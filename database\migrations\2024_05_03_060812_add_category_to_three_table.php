<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->enum('category', ['Sales', 'Service'])->after('id');
        });

        Schema::table('challan', function (Blueprint $table) {
            $table->enum('category', ['Sales', 'Service'])->after('id');
        });

        Schema::table('invoice', function (Blueprint $table) {
            $table->enum('category', ['Sales', 'Service'])->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('three', function (Blueprint $table) {
            //
        });
    }
};
