<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\OrdersDTO;
use Support\Contracts\HasDTO;


class OrderStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'note' => 'nullable|string',
            'selectedProductItem.*.product_id'  => 'required|integer', // Example validation rule
            'selectedProductItem.*.description' => 'required|string',
            'selectedProductItem.*.qty'         => 'required|integer|min:1',
            'selectedProductItem.*.price'       => 'required|numeric',
            'selectedProductItem.*.total_amount'=> 'required|numeric',
            'customer_id'                       => 'required|integer',
            'sales_user_id'                     => 'required|integer',
            'category'                          => 'required',
            'organization_id'                   => 'required|integer',
            'validity'                          => 'required',
            'delivery'                          => 'required',
            'payment_terms'                     => 'required',
        ];
    }

    public function withValidator($validator)
    {
        if (!empty($this->input('order_id'))) {
            $validator->after(function ($validator) {
                $selectedProductItems = $this->input('selectedProductItem');
                if($selectedProductItems){
                    foreach ($selectedProductItems as $key => $product) {
                        if ($product['delivered_qty'] > $product['qty']) {
                            $validator->errors()->add("selectedProductItem.$key.qty1", __(''.$product['delivered_qty'].'Qty Already Delivered'));
                        }
                    }
                }
            });
        }
    }

    public function DTO()
    {
        return OrdersDTO::LazyFromArray($this->input());
    }
}
