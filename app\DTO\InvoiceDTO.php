<?php 

namespace App\DTO;

use App\Traits\ArrayToProps;

class InvoiceDTO
{
    use ArrayToProps;

    public $category;
    public $sales_user_id;
    public $invoice_type;
    public $organization_id;
    public $customer_id;
    public $entity_id;
    public $entity_type;
    public $challan_ids;
    public $order_id;
    public $invoice_no;
    public $date;
    public $sub_total;
    public $igst;
    public $cgst;
    public $sgst;
    public $discount_before_tax;
    public $overall_discount;
    public $total_discount;
    public $total_gst;
    public $total_amount;
    public $paid_amount;
    public $pending_amount;
    public $note;
    public $transport;
    public $dispatch;
    public $patient_name;
    // public $po_date;
    public $customer_po_date;
    public $customer_po_number;
    public $due_days;
    public $selectedProductItem;
    public $eway_bill;
    public $cr_dr_note;
    public $created_by;
    public $updated_by;
    public $purchase_order_id;
    public $purchase_order_receive_id;
    public $invoice_id;
    public $stock_transfer;
    public $serial_number_id;

}