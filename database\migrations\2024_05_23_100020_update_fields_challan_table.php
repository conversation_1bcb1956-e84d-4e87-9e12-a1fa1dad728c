<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `challan` MODIFY `status` ENUM('Open', 'In-process', 'Close', 'Cancelled', 'Foc') DEFAULT 'Open'");

        Schema::table('service_products', function (Blueprint $table) {
            $table->dropColumn('purchase_price');
        });

        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('invoice', function (Blueprint $table) {
            $table->enum('invoice_type', ['Retail', 'Tax'])->after('category')->default('Tax');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE `challan` MODIFY `status` ENUM('Open', 'In-process', 'Close', 'Cancelled') DEFAULT 'Open'");
    }
};
