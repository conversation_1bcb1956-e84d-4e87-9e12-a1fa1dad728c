<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdownNew.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';

const props = defineProps([
    'data',
    'accountType',
    'bankList',
    'bank_id',
    'accountId'
]);


const form = useForm({});
const organizationId = ref(props.organizationId);
const bankId = ref(props.bank_id);
const from_date = ref('');
const to_date = ref('');

const exportXls = () => {
    const xlsName = 'Account_Type_Transactions_' + props.accountType.name.replace(/\s+/g, '_');
    const params = {
        account_id: props.accountId,
        bank_id: bankId.value || '',
        from_date: from_date.value || '',
        to_date: to_date.value || ''
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-account-type-transactions/${props.accountId}?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${xlsName}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const filteredTransactions = computed(() => {
    let cumulativeBalance = 0;
    props.data.forEach((transaction) => {
        const transactionDate = new Date(transaction.date);
        const fromDate = new Date(from_date.value);
        if (transactionDate < fromDate) {
            if (transaction.payment_type === 'cr') {
                cumulativeBalance += parseFloat(transaction.amount);
            } else if (transaction.payment_type === 'dr') {
                cumulativeBalance -= parseFloat(transaction.amount);
            }
        }
    });

    let filteredData = props.data.filter((transaction) => {
        const transactionDate = new Date(transaction.date);
        const fromDate = new Date(from_date.value);
        const toDate = new Date(to_date.value);
        if (from_date.value && to_date.value) {
            return transactionDate >= fromDate && transactionDate <= toDate;
        }
        const isAfterFromDate = !from_date.value || transactionDate >= fromDate;
        const isBeforeToDate = !to_date.value || transactionDate <= toDate;
        return isAfterFromDate && isBeforeToDate;
    });

    filteredData = filteredData.map((transaction) => {
        if (transaction.payment_type === 'cr') {
            cumulativeBalance  += parseFloat(transaction.amount);
        } else if (transaction.payment_type === 'dr') {
            cumulativeBalance  -= parseFloat(transaction.amount);
        }
        let prefix = cumulativeBalance >= 0 ? 'cr' : 'dr';
        let formattedBalance = formatAmountNew(Math.abs(cumulativeBalance)) + ' ' + prefix;
        return { ...transaction, balance: formattedBalance };
    });
    return filteredData;
});



const formatAmountNew = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const handleDateChange = () => {
};

const handleSearchChange = (bankId) => {
    form.get(route('account-type.transactions',{id: props.accountId, bank_id: bankId}),  {
        preserveState: true,
    });
};

const setBank = (id, name) => {
    bankId.value = id;
    handleSearchChange(bankId.value);
};

</script>


<template>
    <Head title="View transactions"/>
    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">{{ props.accountType.name }}</h1>
                </div>
                <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex items-center space-x-4">
                        <div class="flex justify-end w-20">
                            <div class="flex justify-end w-20">
                                <CreateButton :href="route('account-type.index')">
                                        Back
                                </CreateButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                    <div class="inline-flex items-center space-x-4 justify-end w-full">
                        <button @click="exportXls">
                            <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="Export XLS">
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center">
                    <div class="sm:col-span-4">
                        <InputLabel for="bank_id" value="Bank Name" />
                        <div class="relative mt-2">
                            <SearchableDropdown
                                :options="props.bankList"
                                option-label="bank_name"
                                option-value="id"
                                v-model="bankId"
                                @onchange="setBank"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="date" value="From Date" />
                        <input
                            v-model="from_date"
                            class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"
                            @change="handleDateChange"
                            :class="{ 'error rounded-md': form.errors.from_date }"
                        />
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="date" value="To Date" />
                        <input
                            v-model="to_date"
                            class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"
                            @change="handleDateChange"
                            :class="{ 'error rounded-md': form.errors.to_date }"
                        />
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg" >
                <div class="shadow sm:rounded-lg" v-if="filteredTransactions && (filteredTransactions.length > 0)">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;" v-if="filteredTransactions && (filteredTransactions.length > 0)">
                            <tr class="border-b-2">
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    DATE
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    NARRATION
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    DEBIT (₹)
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    CREDIT (₹)
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    BALANCE (₹)
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="filteredTransactions && (filteredTransactions.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(transaction, index) in filteredTransactions" :key="transaction.id">
                                <td class="px-4 py-2.5 min-w-24">
                                    {{ formatDate(transaction.date) ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 text-sm flex flex-col font-medium text-gray-900">
                                     {{ transaction.bank?.bank_name ?? '-' }} - {{ transaction.bank?.account_number ?? '-' }}
                                    <span class="tooltiptext text-xs">
                                            {{ transaction.note ?? '-' }}
                                    </span>
                                </td>
                                <td class="px-4 py-2.5 min-w-28">
                                    {{ (transaction.payment_type == 'dr') ? formatAmountNew(transaction.amount) : '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-28">
                                    {{ (transaction.payment_type == 'cr') ? formatAmountNew(transaction.amount) : '-'}}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ (transaction.balance) }}
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

