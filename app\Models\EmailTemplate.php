<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\ActivityTrait;


class EmailTemplate extends Model
{
    use HasFactory;

    use ActivityTrait;

    protected $table = 'email_template';

    protected static $logName = 'Email-Template';

    public function getLogDescription(string $event): string
    {
        return "E-mail Template has been {$event} for <strong>{$this->email_subject}<strong> by";
    }
    protected static $logAttributes = [
        'email_subject',
        'template_name',
        'content',
    ];
    protected $fillable = [
        'email_subject',
        'template_name',
        'content',
    ];

    // public function EmailTemplate(){
    //     return $this->belongsTo(EmailTemplate::class,'created_by','id');
    // }
}
