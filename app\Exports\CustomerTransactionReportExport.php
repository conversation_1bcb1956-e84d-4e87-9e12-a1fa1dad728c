<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class CustomerTransactionReportExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize, WithColumnWidths
{
    protected $transactions;
    protected $credits;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;

    public function __construct($transactions, $credits, $organizationName = null, $fromDate = null, $toDate = null)
    {
        $this->transactions = $transactions;
        $this->credits = $credits;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
    }

    public function headings(): array
    {
        $fromDateFormatted = $this->fromDate ? Carbon::parse($this->fromDate)->format('Y-m-d') : 'N/A';
        $toDateFormatted = $this->toDate ? Carbon::parse($this->toDate)->format('Y-m-d') : 'N/A';

        return [
            [$this->organizationName ?? 'Organization Name'],
            ['Customer Wise Report'],
            ['From: ' . $fromDateFormatted . '  To: ' . $toDateFormatted],
            ['Party Name', 'Bill Date', 'Bill No.', 'Bill Amount', 'Received', 'Balance'],
        ];
    }

    public function array(): array
    {
        $data = [];
        $grouped = $this->transactions->groupBy(fn($t) => $t->customer->customer_name ?? 'N/A');
    
        foreach ($grouped as $customerName => $transactions) {
            $totalBalance = $transactions->sum(fn($t) => $t->pending_amount ?? 0);
    
            $data[] = [
                $customerName,
                '',
                '',
                '',
                '',
                $totalBalance,
            ];
    
            foreach ($transactions as $transaction) {
                $billAmount = $transaction->total_amount   ?? 0;
                $received   = $transaction->paid_amount     ?? 0;
                $balance    = $transaction->pending_amount  ?? 0;
    
                $data[] = [
                    '',
                    $transaction->date 
                        ? Carbon::parse($transaction->date)->format('Y-m-d') 
                        : 'N/A',
                    $transaction->invoice_no ?? 'N/A',
                    $billAmount,
                    $received,
                    $balance,
                ];
            }
        }
    
        return $data;
    }
        
    public function styles(Worksheet $sheet)
    {
        $transactionCount = count($this->transactions);
        $lastRow = 4 + $transactionCount; 

        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');
        $sheet->mergeCells('A3:F3');

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(13);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(11);
        $sheet->getStyle('A1:A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A4:F4')->getFont()->setBold(true);
        $sheet->getStyle('A4:F4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFFFCC');

        $sheet->getStyle("A4:F{$lastRow}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        $sheet->getStyle("A4:F{$lastRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $currentRow = 5;  
        foreach ($this->transactions->groupBy(fn($t) => $t->customer->customer_name ?? 'N/A') as $customerName => $transactions) {
            $sheet->getStyle("A{$currentRow}:F{$currentRow}")->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setRGB('D9E1F2');   
            $sheet->getStyle("A{$currentRow}")->getFont()->setBold(true);
            $sheet->getStyle("A{$currentRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

            $currentRow += count($transactions) + 1;
        }

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 40,    
            'B' => 15,  
            'C' => 25,  
            'D' => 18, 
            'E' => 18, 
            'F' => 18,  
        ];
    }
}
