<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_transaction', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained( table: 'organizations', indexName: 'orgcus_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('customer_id');
            $table->enum('payment_type', ['cr', 'dr']);
            $table->double('amount', 16, 2);
            $table->date('date');
            $table->double('balance', 16, 2)->nullable();
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_transaction');
    }
};
