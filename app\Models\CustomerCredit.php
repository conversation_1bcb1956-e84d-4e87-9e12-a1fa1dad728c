<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class CustomerCredit extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'customer_credit';

    protected static $logName = 'Customer Credit';

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customer ? $this->customer->customer_name : 'Unknown Customer';

        return "<strong>{$customerName}</strong> Customer credit has been {$event} by";
    }

    protected static $logAttributes = [
        'payment_receive_id',
        'organization_id',
        'customer_id',
        'amount',
        'unused_amount',
        'date',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'payment_receive_id',
        'organization_id',
        'customer_id',
        'amount',
        'unused_amount',
        'date',
        'created_by',
        'updated_by'
    ];

    public function paymentreceive(){
        return $this->belongsTo(PaymentReceive::class,'payment_receive_id','id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function creditDetail()
    {
        return $this->hasMany(CustomerCreditDetails::class,'credit_id','id');
    }
}
