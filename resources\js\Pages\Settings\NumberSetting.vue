<script setup>
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , router, useForm, usePage} from '@inertiajs/vue3';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';


const props = defineProps({
    data: Array,
    permissions: Array
});

const editingIndex = ref(null);
const editedNumber = ref('');

const startEditing = (index, number) => {
    editingIndex.value = index;
    editedNumber.value = number;
};

const saveNumber = (item) => {
    if (!editedNumber.value || isNaN(editedNumber.value)) {
        alert("Please enter a valid number.");
        return;
    }

    router.post(route('setting.updateNumber', item.id), { number: editedNumber.value }, {
        preserveScroll: true,
        onSuccess: () => {
            router.reload({ only: ['data'] });

            editingIndex.value = null;
            editedNumber.value = null;
        },
        onError: (errors) => {
            console.error("Update failed:", errors);
            alert("Failed to update number. Please try again.");
        }
    });
};



const columns = ref([
    { label: 'Organization Name', field: 'organization_name', sortable: true },
    { label: 'Type', field: 'type', sortable: true },
    { label: 'Number', field: 'number', sortable: true },
]); 

</script>

<template>
    <Head title="Number Setting" />

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Number Setting</h1>
                <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('setting')">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>

            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" 
                                    class="px-4 py-4 text-sm font-semibold text-gray-900 cursor-pointer">
                                    {{ column.label }}
                                </th>
                                <th class="px-4 py-4 text-sm font-semibold text-gray-900">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="props.data?.length">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" 
                                v-for="(item, index) in props.data" :key="item.id">
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ item.organization?.name ?? 'N/A' }}
                                </td>
                                <td class="px-4 py-2.5">
                                    {{ item.type }}
                                </td>
                                <td  class="px-4 py-2.5">
                                    <input 
                                        v-if="editingIndex === index" 
                                        v-model="editedNumber"
                                        type="text"
                                        class="border rounded px-2 py-1 w-24"
                                    />
                                    <span v-else>{{ item.number }}</span>
                                </td>
                                <td class="px-4 py-2.5">
                                    <button 
                                        v-if="editingIndex === index" 
                                        @click="saveNumber(item)" 
                                        class="text-green-600 font-semibold hover:underline">
                                        Save
                                    </button>

                                    <button 
                                        v-else-if="permissions.canEditRoles"  
                                        @click="startEditing(index, item.number)" 
                                        class="text-blue-600 font-semibold hover:underline">
                                        Edit
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                                <td colspan="4" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                    No data found.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
