import{K as j,o as n,c,a as x,u as r,w as _,F as y,Z as L,b as t,g as v,t as i,k as M,v as S,N as T,i as A,e as D,f as H}from"./app-2ecbacfc.js";import{s as E}from"./sortAndSearch-94154e09.js";import{_ as B}from"./AdminLayout-42d5bb92.js";import{_ as F}from"./CreateButton-1fa2a774.js";import{_ as V}from"./Pagination-56593f88.js";const O={key:0,class:"animate-top"},P={class:"flex justify-between items-center"},U={class:"items-start"},z={class:"text-2xl font-semibold leading-7 text-gray-900"},K={class:"text-blue-700"},G={class:"flex justify-end"},J={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},R={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},q=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Z={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Q={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},W={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},X={class:"sm:col-span-4"},Y={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},tt={class:"inline-flex items-center justify-start w-full space-x-2"},et=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Product Name:",-1),st={class:"text-sm leading-6 text-gray-700"},ot={class:"inline-flex items-center justify-start w-full space-x-2"},lt=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Product Code:",-1),at={class:"text-sm leading-6 text-gray-700"},it={class:"inline-flex items-center justify-start w-full space-x-2"},dt=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Company:",-1),nt={class:"text-sm leading-6 text-gray-700"},ct={class:"sm:col-span-4"},rt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},pt={class:"inline-flex items-center justify-start w-full space-x-2"},mt=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Price (₹)",-1),ft={class:"text-sm leading-6 text-gray-700"},ut={class:"inline-flex items-center justify-start w-full space-x-2"},xt=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"GST (%)",-1),gt={class:"text-sm leading-6 text-gray-700"},ht={class:"inline-flex items-center justify-start w-full space-x-2"},_t=t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"HSN Code",-1),yt={class:"text-sm leading-6 text-gray-700"},vt={class:"mt-6 flow-root"},wt={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},bt={class:"overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px","margin-bottom":"80px"}},kt={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Nt={class:"min-w-full divide-y divide-gray-300"},It={class:"bg-gray-50"},$t=t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer"},"DESCRIPTION",-1),Ct={key:0,class:"divide-y divide-gray-300 bg-white"},jt=["innerHTML"],Lt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Mt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},St=["innerHTML"],Tt={key:1},At=t("tr",{class:"bg-white"},[t("td",{colspan:"4",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Dt=[At],Ht={key:1,class:"animate-top"},Et=t("div",{class:"flex justify-between items-center"},[t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Invalid product ID ")])],-1),Bt=[Et],zt={__name:"Logs",props:["data","productInfo"],setup(l){const w=j().props.productInfo.id,{search:m,sort:f,fetchData:g}=E("product.logs",{id:w}),b=o=>{const e=new Date(o),s={year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"numeric",hour12:!0};return e.toLocaleDateString("en-US",s)},u=o=>o!=null?o.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()).replace(/\b(Gst|Sgst|Cgst|Igst)\b/g,e=>e.toUpperCase()):"NA",h={created:"green",updated:"blue",deleted:"red"},k=o=>{const e=h[o]||"blue";return`<div class="items-center justify-center px-3 py-1 w-fit border-none leading-normal bg-${e}-100 text-${e}-700 font-semibold rounded-md">
                ${u(o)}
            </div>`},N=o=>{let e=o.action,s=JSON.parse(o.description),a="";if(a+='<div class="space-y-4">',e=="updated"||e=="received"||e=="accepted")for(const[p,d]of Object.entries(s))a+=$(p,d.old,d.new);else a+=C(e,s);return a+="</div>",a},I=o=>o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"),$=(o,e,s)=>`
        <div class="p-3 mb-4 rounded-lg shadow-md">
            <div class="flex gap-4">
                <div class="flex-1">
                    <div class="text-sm font-semibold text-blue-700">
                        ${u(o)}
                    </div>
                </div>
                <div class="flex-2">
                    <div class="flex gap-2">
                        <div class="bg-red-100 text-red-700 rounded-md px-3 py-1 text-xs">
                            <pre style="white-space: pre-wrap; word-wrap: break-word;">${e??"NA"}</pre>
                        </div>
                        <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill="currentColor" d="M10.837 3.13a.5.5 0 0 0-.674.74L16.33 9.5H2.5a.5.5 0 0 0 0 1h13.828l-6.165 5.628a.5.5 0 0 0 .674.739l6.916-6.314a.747.747 0 0 0 0-1.108z"></path>
                        </svg>
                        <div class="bg-green-100 text-green-700 rounded-md px-3 py-1 text-xs">
                            <pre style="white-space: pre-wrap; word-wrap: break-word;">${s??"NA"}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>`,C=(o,e)=>{let s=u(e.status),a=e.attributes,p="NA";a!=null&&(p=typeof a=="object"?I(JSON.stringify(a,null,2)):a);const d=h[o]||"blue";return`
        <div class="p-3 mb-4 bg-${d}-100 rounded-lg shadow-md">
            <div class="flex gap-4">
                <div class="flex-1">
                    <div class="text-sm font-semibold text-${d}-700">
                        ${s}
                    </div>
                </div>
                <div class="flex-2">
                    <div class="bg-${d}-100 text-${d}-700 rounded-md px-3 py-1 text-xs">
                        <pre style="white-space: pre-wrap; word-wrap: break-word;">${p}</pre>
                    </div>
                </div>
            </div>
        </div>`};return(o,e)=>(n(),c(y,null,[x(r(L),{title:"Product Logs"}),x(B,null,{default:_(()=>[l.productInfo?(n(),c("div",O,[t("div",P,[t("div",U,[t("h1",z,[v(" All Activity Log for "),t("span",K,i(l.productInfo.name),1)])]),t("div",G,[t("div",J,[t("div",R,[q,M(t("input",{id:"search-field","onUpdate:modelValue":e[0]||(e[0]=s=>T(m)?m.value=s:null),onInput:e[1]||(e[1]=(...s)=>r(g)&&r(g)(...s)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[S,r(m)]])])]),t("div",Z,[x(F,{href:o.route("products.show",{id:l.productInfo.company_id})},{default:_(()=>[v(" Back ")]),_:1},8,["href"])])])]),t("div",Q,[t("div",W,[t("div",X,[t("div",Y,[t("div",tt,[et,t("p",st,i(l.productInfo.name),1)]),t("div",ot,[lt,t("p",at,i(l.productInfo.item_code),1)]),t("div",it,[dt,t("p",nt,i(l.productInfo.company.name),1)])])]),t("div",ct,[t("div",rt,[t("div",pt,[mt,t("p",ft,i(Number(l.productInfo.price).toFixed(2)),1)]),t("div",ut,[xt,t("p",gt,i(Number(l.productInfo.gst).toFixed(2)),1)]),t("div",ht,[_t,t("p",yt,i(l.productInfo.hsn_code),1)])])])])]),t("div",vt,[t("div",wt,[t("div",bt,[t("div",kt,[t("table",Nt,[t("thead",It,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer",onClick:e[2]||(e[2]=s=>r(f)("action"))},"EVENT"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer",onClick:e[3]||(e[3]=s=>r(f)("log_name"))},"NAME"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900 cursor-pointer",onClick:e[4]||(e[4]=s=>r(f)("created_at"))},"DATE TIME"),$t])]),l.data.data&&l.data.data.length>0?(n(),c("tbody",Ct,[(n(!0),c(y,null,A(l.data.data,(s,a)=>(n(),c("tr",{key:s.id,class:""},[t("td",{class:"whitespace-nowrap px-3 py-3 text-sm",innerHTML:k(s.action)},null,8,jt),t("td",Lt,i(s.log_name),1),t("td",Mt,i(b(s.created_at)??"-"),1),t("td",{class:"whitespace-nowrap px-3 py-3 text-sm",innerHTML:N(s)},null,8,St)]))),128))])):(n(),c("tbody",Tt,Dt))])]),l.data.data&&l.data.data.length>0?(n(),D(V,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):H("",!0)])])])])):(n(),c("div",Ht,Bt))]),_:1})],64))}};export{zt as default};
