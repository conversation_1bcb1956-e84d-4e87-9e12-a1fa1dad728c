<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ActivityTrait;

class JobChecklist extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'job_card_checklist';

    protected static $logName = 'Jobcard Checklist';

    public function getLogDescription(string $event): string
    {

        return "<strong>{$this->type}</strong> Jobcard checklist has been {$event} by";
    }

    protected static $logAttributes = [
        'type',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'type',
        'created_by',
        'updated_by'
    ];
}
