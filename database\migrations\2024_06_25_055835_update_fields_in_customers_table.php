<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->enum('is_organization', ['yes', 'no'])->after('type')->default('no');
            $table->integer('organization_id')->nullable()->after('is_organization');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->enum('is_organization', ['yes', 'no'])->after('type')->dafault('no');
            $table->integer('organization_id')->nullable()->after('is_organization');
        });
    }
};
