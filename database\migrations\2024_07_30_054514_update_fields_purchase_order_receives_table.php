<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->enum('status', ['Paid', 'Partially Paid', 'Unpaid'])->default('Unpaid')->after('total_amount');
            $table->double('paid_amount', 16, 2)->nullable()->default(0)->after('status');
            $table->double('pending_amount', 16, 2)->nullable()->default(0)->after('paid_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
