<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_report_detail', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_report_id')->constrained( table: 'service_reports', indexName: 'srd_id')->onDelete('cascade')->onUpdate('no action');
            $table->date('date');
            $table->string('type');
            $table->string('document_name');
            $table->string('original_document_name');
            $table->integer('service_engineer_id');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_report_detail');
    }
};
