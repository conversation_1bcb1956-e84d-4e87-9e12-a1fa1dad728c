<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderDeliver extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'order_delivers';
    protected static $logName = 'Order-Deliver';

    public function getLogDescription(string $event): string
    {
        return "<strong>{$this->order_deliver_number}</strong> Order has been deliver by";
    }

    protected static $logAttributes = [
        'order_id',
        'order_details_id',
        'order_deliver_number',
        'delivered_qty',
        'deliver_date',
        'invoice_number',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'order_id',
        'order_details_id',
        'order_deliver_number',
        'delivered_qty',
        'deliver_date',
        'invoice_number',
        'created_by',
        'updated_by'
    ];

    public function orderDetails()
    {
        return $this->belongsTo(OrderDetails::class ,'order_details_id','id');
    }
}
