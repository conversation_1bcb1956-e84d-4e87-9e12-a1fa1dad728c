<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import RadioButton from '@/Components/RadioButton.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { defineProps, ref, computed, watch } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['paymentType', 'bankinfo', 'organization', 'customers', 'companies', 'invoices', 'customerCredit', 'companyCredit']);

const checkedValues = ref([]);

const form = useForm('post', '/receipt', {
    entity_type: 'customer', // 'customer' or 'company'
    organization_id: '',
    customer_id: '',
    company_id: '',
    payment_type: '',
    date:'',
    note:'',
    amount: 0,
    tds_amount: 0,
    discount_amount: 0,
    round_off: 0,
    check_number: '',
    bank_name: '',
    org_bank_id: '',
    invoice:[],
    settled_amount: '',
    advance_amount: '',
    is_credit: '',
    credit_data:[],
    total_unused_amount: ''
});

const payment_type = ref('');
//form.invoice = checkedValues.value;
//form.invoice = invoice.value;
const submit = () => {
    form.settled_amount = settledAmount.value;
    form.advance_amount = advanceAmount.value;
    form.total_unused_amount = totalUnusedAmount.value;
    form.is_credit = creditOption.value,
    form.invoice = invoice.value;
    form.credit_data = creditData.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setPaymentType = (id, name) => {
    payment_type.value = name;
    form.payment_type = id;
    form.errors.payment_type = null;
    if (name === "Cash") {
        form.note = "Cash";
    } else if (form.note === "Cash") {
        form.note = "";
    }
};

const pendinginvoice = ref([]);
const creditData = ref([]);
const totalUnusedAmount = ref('');
const bankinfo = ref([]);

const setOrganization = (id, name) => {
    const bank = props.bankinfo.filter(bank  => bank.organization_id === id);
    bankinfo.value = bank;

    // Filter invoices based on entity type
    let finalInvoice = [];
    if (form.entity_type === 'customer') {
        finalInvoice = props.invoices.filter(product =>
            product.organization_id === id &&
            product.entity_type === 'customer' &&
            (!form.customer_id || product.customer_id === form.customer_id)
        );
    } else {
        finalInvoice = props.invoices.filter(product =>
            product.organization_id === id &&
            product.entity_type === 'company' &&
            (!form.company_id || product.company_id === form.company_id)
        );
    }
    pendinginvoice.value = finalInvoice;

    // Filter credits based on entity type
    let credit = [];
    if (form.entity_type === 'customer') {
        credit = props.customerCredit.filter(credit =>
            credit.organization_id === id &&
            (!form.customer_id || credit.customer_id === form.customer_id)
        );
    } else {
        credit = props.companyCredit.filter(credit =>
            credit.organization_id === id &&
            (!form.company_id || credit.company_id === form.company_id)
        );
    }
    creditData.value = credit;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setCustomer = (id, name) => {
    const finalInvoice = props.invoices.filter(product =>
        product.customer_id === id &&
        product.organization_id === form.organization_id &&
        product.entity_type === 'customer'
    );
    pendinginvoice.value = finalInvoice;
    const credit = props.customerCredit.filter(credit =>
        credit.customer_id === id &&
        credit.organization_id === form.organization_id
    );
    creditData.value = credit;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
    form.customer_id = id;
    form.errors.customer_id = null;
};

const setCompany = (id, name) => {
    const finalInvoice = props.invoices.filter(product =>
        product.company_id === id &&
        product.organization_id === form.organization_id &&
        product.entity_type === 'company'
    );
    pendinginvoice.value = finalInvoice;
    const credit = props.companyCredit.filter(credit =>
        credit.company_id === id &&
        credit.organization_id === form.organization_id
    );
    creditData.value = credit;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
    form.company_id = id;
    form.errors.company_id = null;
};

const setBankInfo = (id, name) => {
    form.org_bank_id = id;
    form.errors.org_bank_id = null;
};

const updateChecked = (newCheckedValues) => {
    const filteredInvoices = props.invoices.filter(invoice => newCheckedValues.includes(invoice.id));
    const totalAmount = filteredInvoices.reduce((sum, invoice) => sum + invoice.total_amount, 0);
    settledAmount.value = totalAmount;
    console.log('Total Amount:', totalAmount);
    checkedValues.value = newCheckedValues;
};

const settledAmount = computed(() => {
    return invoice.value.reduce((total, product) => {
        return total + (product.check ? (product.amount ? parseFloat(product.amount) : 0) : 0);
    }, 0);
});

const advanceAmount = computed(() => {
    const totalEntered = parseFloat(form.amount || 0) + parseFloat(form.discount_amount || 0) + parseFloat(form.tds_amount || 0);
    const roundOff = parseFloat(form.round_off || 0);
    const settled = settledAmount.value;
    return (totalEntered - settled - roundOff);
});

const updateAmount = () => {
    // const amount = parseFloat(form.amount || 0);
    // const tds = parseFloat(form.tds_amount || 0);
    // const roundOff = parseFloat(form.round_off || 0);
    // const settled = settledAmount.value;
    // const discount = settled - (amount + tds + roundOff);
    // const roundAmount = amount + tds - settled;
    // form.discount_amount = discount > 0 ? discount.toFixed(2) : 0;
    // form.round_off = roundAmount > 0 ? roundAmount.toFixed(2) : 0;
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const creditOption = ref('No');

const radioOptions = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
];

const changeEvent = (startIndex) => {
    // updateAmount();

    // Calculate total available amount
    const totalAvailableAmount =
        creditOption.value === 'Yes'
            ? parseFloat(totalUnusedAmount.value || 0)
            : (parseFloat(form.amount || 0) + parseFloat(form.round_off || 0) + parseFloat(form.discount_amount || 0) + parseFloat(form.tds_amount || 0));

    // If unchecked, reset amount to 0
    if (!invoice.value[startIndex].check) {
        invoice.value[startIndex].amount = 0;
        return;
    }

    // Calculate remaining amount after accounting for already allocated amounts
    let remainingAmount = totalAvailableAmount;

    // Subtract amounts already allocated to other checked invoices
    invoice.value.forEach((item, index) => {
        if (item.check && index !== startIndex) {
            remainingAmount -= parseFloat(item.amount || 0);
        }
    });

    // Apply remaining amount to the current invoice
    const pending = parseFloat(invoice.value[startIndex].pending_amount || 0);
    const applied = Math.min(pending, remainingAmount);
    invoice.value[startIndex].amount = applied.toFixed(2);
};

const invoice = ref([
    {
        id: '',
        date: '',
        invoice_no: '',
        total_amount: '',
        pending_amount: '',
        check: false,
        amount: 0,
    }
])

// watch(() => pendinginvoice.value, () => {
//     invoice.value = pendinginvoice.value.map(detail => ({
//         id: detail.id,
//         date: detail.date,
//         invoice_no: detail.invoice_no,
//         total_amount: parseFloat(detail.total_amount).toFixed(2),
//         pending_amount: parseFloat(detail.pending_amount).toFixed(2),
//         check: false,
//         amount: detail.pending_amount
//     }));
// });

const setupInvoiceList = () => {
    invoice.value = pendinginvoice.value.map(detail => ({
        id: detail.id,
        date: detail.date,
        invoice_no: detail.invoice_no,
        total_amount: parseFloat(detail.total_amount || 0).toFixed(2),
        pending_amount: parseFloat(detail.pending_amount || 0).toFixed(2),
        check: false,
        amount: '0.00',
    }));
};


watch(pendinginvoice, () => {
    setupInvoiceList();
});

watch(creditOption, () => {
    setupInvoiceList();
});

watch(() => form.amount, () => {
    if (creditOption.value === 'No') {
        setupInvoiceList();
    }
});

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
    form.errors.settled_amount = null;
};

const resetEntitySelection = () => {
    form.customer_id = '';
    form.company_id = '';
    pendinginvoice.value = [];
    creditData.value = [];
    totalUnusedAmount.value = 0;
    setupInvoiceList();
};



</script>

<template>
    <Head title="Receipt" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">
                        {{ form.entity_type === 'customer' ? 'Receive Payment' : 'Pay to Company' }}
                    </h1>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-base font-semibold leading-6 text-gray-900" v-if="creditData.length > 0">
                        Credits Available:  ₹{{ formatAmount(totalUnusedAmount) }}
                    </div>
                </div>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">
                        <!-- Entity Type Selection -->
                        <div class="sm:col-span-8">
                            <InputLabel value="Transaction Type" />
                            <div class="mt-2 flex space-x-6">
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        v-model="form.entity_type"
                                        value="customer"
                                        class="mr-2"
                                        @change="resetEntitySelection"
                                    />
                                    Customer Payment (Receive)
                                </label>
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        v-model="form.entity_type"
                                        value="company"
                                        class="mr-2"
                                        @change="resetEntitySelection"
                                    />
                                    Company Payment (Pay)
                                </label>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Organization"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="organization"
                                    v-model="form.organization_id"
                                    @onchange="setOrganization"
                                    :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                            </div>
                        </div>

                        <!-- Customer Selection (when entity_type is customer) -->
                        <div class="sm:col-span-3" v-if="form.entity_type === 'customer'">
                            <InputLabel for="customer" value="Customer"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="customers"
                                    v-model="form.customer_id"
                                    @onchange="setCustomer"
                                    :class="{ 'error rounded-md': form.errors.customer_id }"
                                />
                            </div>
                        </div>

                        <!-- Company Selection (when entity_type is company) -->
                        <div class="sm:col-span-3" v-if="form.entity_type === 'company'">
                            <InputLabel for="company" value="Company"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="companies"
                                    v-model="form.company_id"
                                    @onchange="setCompany"
                                    :class="{ 'error rounded-md': form.errors.company_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="role_id" value="Payment Through Credit ?" />
                            <div class="relative mt-2">
                                <RadioButton
                                    v-model="creditOption"
                                    :options="radioOptions"
                                />
                            </div>
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Payment Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="paymentType"
                                    v-model="form.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors.payment_type }"
                                />
                            </div>
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="date" value="Payment Date" />
                            <input
                                v-model="form.date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                @change="clearError('date')"
                                :class="{ 'error rounded-md': form.errors.date }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-2">
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-1">
                            <InputLabel for="tds_amount" value="TDS Amount" />
                            <TextInput
                                type="text"
                                @change="clearError('tds_amount')"
                                 @input="updateAmount()"
                                v-model="form.tds_amount"
                                :class="{ 'error rounded-md': form.errors.tds_amount }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-1">
                            <InputLabel for="discount_amount" value="Discount Amount" />
                            <TextInput
                                type="text"
                                @change="clearError('discount_amount')"
                                 @input="updateAmount()"
                                v-model="form.discount_amount"
                                :class="{ 'error rounded-md': form.errors.discount_amount }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-1">
                            <InputLabel for="round_off" value="Round Off" />
                            <TextInput
                                type="text"
                                @change="clearError('round_off')"
                                v-model="form.round_off"
                                :class="{ 'error rounded-md': form.errors.round_off }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="amount" value="Amount" />
                            <TextInput
                                id="amount"
                                type="text"
                                @change="clearError('amount')"
                                @input="updateAmount()"
                                v-model="form.amount"
                                :class="{ 'error rounded-md': form.errors.amount }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="advance" value="Advance(Ref) Amount" />
                            <div class="mt-4 flex justify-start">
                                <p class="text-base font-semibold">{{ formatAmount(advanceAmount) }}</p>
                            </div>
                        </div>
                        <div v-if="payment_type == 'Cheque' && creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="check_number" value="Cheque Number" />
                            <TextInput
                                id="check_number"
                                type="text"
                                v-model="form.check_number"
                                :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                            />
                        </div>
                        <div v-if="payment_type == 'Cheque' && creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="bank_name" value="Bank Name" />
                            <TextInput
                                id="bank_name"
                                type="text"
                                v-model="form.bank_name"
                                :class="{ 'error rounded-md': form.errors[`data.bank_name`] }"
                            />
                        </div>
                        <!-- <div v-if="payment_type == 'Cash'" class="sm:col-span-3">
                        </div> -->
                        <div v-if="payment_type != 'Cash' && creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="org_bank_id" value="Our Bank" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="bankinfo"
                                v-model="form.org_bank_id"
                                @onchange="setBankInfo"
                                :class="{ 'error rounded-md': form.errors.org_bank_id }"
                                />
                            </div>
                        </div>
                        <div v-if="payment_type != 'Cash' && creditOption =='No'" class="sm:col-span-3">
                        </div>
                        <div class="sm:col-span-6">
                            <table class="overflow-x-auto divide-y divide-gray-300 w-full">
                                <div class="w-full">
                                    <thead class="w-full">
                                        <tr class="">
                                            <th scope="col" class="">
                                            </th>
                                            <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"> Invoice No </th>
                                            <th scope="col" class="whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"> Total Amount (₹) </th>
                                            <th scope="col" class="whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"> Pending Amount (₹) </th>
                                            <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"> Amount (₹)</th>
                                            <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"> Date</th>
                                        </tr>
                                    </thead>
                                </div>
                                <div style="overflow-y: auto; max-height: 318px;">
                                <tbody class="divide-y divide-gray-300 bg-white">
                                    <tr v-for="(product, index)  in invoice" :key="index">
                                        <td class="whitespace-nowrap px-2 text-sm text-gray-900">
                                            <div class="text-sm text-gray-900 leading-6 py-1.5">
                                                <Checkbox name="check" v-model:checked="product.check" @change="changeEvent(index)"/>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.invoice_no }}</td>
                                        <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.total_amount }}</td>
                                        <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.pending_amount }}</td>
                                        <td class="whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36">
                                            <TextInput
                                                id="amount"
                                                type="text"
                                                v-model="product.amount"
                                                @change="clearError('invoice.' + index + '.amount')"
                                                :class="{ 'error': form.errors[`invoice.${index}.amount`] }"
                                            />
                                            <p v-if="form.errors[`invoice.${index}.amount`]" class="text-red-500 text-xs absolute">
                                                {{ form.errors[`invoice.${index}.amount`] }}
                                            </p>
                                        </td>
                                        <td class="whitespace-nowrap px-2 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                    </tr>
                                </tbody>
                                </div>
                            </table>
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="note" value="Total Settled Amount" />
                            <div class="mt-4 flex justify-start">
                                <p class="text-base font-semibold">{{ formatAmount(settledAmount) }}</p>
                            </div>
                            <p v-if="form.errors.settled_amount" class="text-red-500 text-xs absolute">
                                {{ form.errors.settled_amount }}
                            </p>
                        </div>
                        <div class="sm:col-span-6" v-if="creditOption =='No'">
                            <InputLabel for="note" value="Note" />
                            <TextArea
                                id="note"
                                type="text"
                                :rows="2"
                                v-model="form.note"
                            />
                        </div>
                    </div>
                    <table class="mt-5 overflow-x-auto divide-y divide-gray-300 w-full"  v-if="creditData.length > 0 && creditOption =='Yes'">
                        <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Date</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Bank</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Credit Amount (₹)</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Available Credit (₹)</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-for="(product, index)  in creditData" :key="index">
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">
                                    <div class="flex flex-col">
                                        <div class="text-sm text-gray-900">
                                            {{ product.paymentreceive?.bank_info?.bank_name ? product.paymentreceive?.bank_info?.bank_name  : 'Cash' }} -
                                            {{ product.paymentreceive?.bank_info?.account_number ? product.paymentreceive?.bank_info?.account_number  : '' }}
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.amount) }}</td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.unused_amount) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('receipt.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>
                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

<style scoped>
    .error {
        border: 1px solid red;
    }
</style>

