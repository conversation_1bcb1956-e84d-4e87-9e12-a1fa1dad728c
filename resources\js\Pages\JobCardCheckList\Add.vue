<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const form = useForm('post', '/jobcard-checklist', {
    type:'',
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

</script>

<template>
    <Head title="Jobcard Checklists" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Create Checklist</h2>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-6">
                            <InputLabel for="type" value="Type" />
                            <TextInput
                                id="type"
                                type="text"
                                v-model="form.type"
                                autocomplete="type"
                                @change="form.validate('type')"
                            />
                            <InputError  v-if="form.invalid('type')" class="" :message="form.errors.type" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('jobcard-checklist.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

