<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Challan extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'challan';

    protected static $logName = 'Challan';

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customers ? $this->customers->customer_name : 'Unknown Customer';
        return "<strong>{$customerName}'s</strong> Challan <strong>{$this->challan_number}</strong> has been {$event} by";
    }


    protected static $logAttributes = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'challan_number',
        'date',
        'status',
        'note',
        'created_by',
        'updated_by',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_discount',
        'total_amount',
        'transport',
        'dispatch'
    ];
    protected $fillable = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'challan_number',
        'date',
        'status',
        'note',
        'created_by',
        'updated_by',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_discount',
        'total_amount',
        'transport',
        'dispatch'
    ];

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function users(){
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function challanDetail()
    {
        return $this->hasMany(ChallanDetail::class)->where('is_receive', NULL);
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'challan');
    }

    public function invoice()
    {
        return $this->hasMany(Invoice::class, 'entity_id')->where('entity_type', 'challan');
    }


    public function closeChallanData()
    {
        return $this->hasMany(ChallanDetail::class)->where('is_receive', NULL)->whereRaw('(qty - (invoiced_qty + return_qty)) > 0');
    }

    public static function boot()
    {
        parent::boot();

        static::updating(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Challan: " . $model->challan_number;
        foreach ($model->challanDetail as $detail) {
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
    }

}
