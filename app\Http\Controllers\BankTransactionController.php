<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\BankTransaction;
use App\Models\Organization;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\BankTransactionRequest;
use App\Http\Requests\InternalBankTransferRequest;
use App\Models\AccountType;
use App\Models\BankInfo;
use Illuminate\Support\Facades\DB;
use App\Traits\QueryTrait;
use Config;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\BankTransactionExport;

class BankTransactionController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Bank Transaction')->only(['index']);
        $this->middleware('permission:Create Bank Transaction')->only(['create', 'store']);
        $this->middleware('permission:Edit Bank Transaction')->only(['edit', 'update']);
        $this->middleware('permission:Delete Bank Transaction')->only('destroy');
    }

    use QueryTrait;
    public function index(Request $request)
    {
        $search = $request->input('search');
        $query = BankInfo::with('organization');
        $searchableFields = ['bank_name', 'account_number', 'ifsc_code', 'organization.name'];
        $this->searchAndSort($query, $request, $searchableFields);
        $data = $query->orderBy('id', 'desc')->paginate(10);

        // Calculate and format the current balance for each bank account
        $data->getCollection()->transform(function ($bank) {
            // Get the current balance
            $currentBalance = $this->calculateCurrentBalance($bank->id);

            // Add the formatted balance to the bank object
            $bank->formatted_balance = $currentBalance['formatted_balance'];

            return $bank;
        });

        return Inertia::render('BankTransaction/List', compact('data'));
    }

    private function formatAmount($amount)
    {
        $amountStr = number_format(abs($amount), 2, '.', '');
        list($integerPart, $decimalPart) = explode('.', $amountStr);

        $lastThree = substr($integerPart, -3);
        $otherNumbers = substr($integerPart, 0, -3);

        $formattedIntegerPart = $otherNumbers !== ''
            ? preg_replace('/\B(?=(\d{2})+(?!\d))/', ',', $otherNumbers) . ',' . $lastThree
            : $lastThree;

        return $formattedIntegerPart . '.' . $decimalPart;
    }

    /**
     * Calculate the current balance for a bank account
     *
     * @param int $bankId The bank ID
     * @param string|null $toDate Optional date to calculate balance up to
     * @return array The calculated balance information
     */
    private function calculateCurrentBalance($bankId, $toDate = null)
    {
        // Get the bank information
        $bank = BankInfo::find($bankId);
        if (!$bank) {
            return [
                'balance' => 0,
                'formatted_balance' => '0.00 cr'
            ];
        }

        // Start with the initial balance
        $initialBalance = (float)$bank->balance;
        $bankAmountType = $bank->amount_type;

        // Get all transactions for this bank
        $query = BankTransaction::where('org_bank_id', $bankId)
                    ->orderBy('date', 'asc');

        // If a date is provided, only include transactions up to that date
        if ($toDate) {
            $query->where('date', '<=', $toDate);
        }

        $transactions = $query->get();

        // Calculate the current balance
        $currentBalance = $initialBalance;
        foreach ($transactions as $transaction) {
            $amount = (float)$transaction->amount;
            $isCreditBank = $bankAmountType === 'cr';
            $isCreditTransaction = $transaction->payment_type === 'cr';

            if ($isCreditBank) {
                $currentBalance = $isCreditTransaction ? $currentBalance - $amount : $currentBalance + $amount;
            } else {
                $currentBalance = $isCreditTransaction ? $currentBalance + $amount : $currentBalance - $amount;
            }
        }

        // Format the balance
        $prefix = $currentBalance >= 0 ? 'cr' : 'dr';
        $formattedBalance = $this->formatAmount(abs($currentBalance)) . ' ' . $prefix;

        return [
            'balance' => $currentBalance,
            'formatted_balance' => $formattedBalance
        ];
    }

    public function show(Request $request, $id)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $search = $request->input('search');

        $query = BankTransaction::with('paymentReceive.customers', 'accounttype', 'paymentPaid.company')
                    ->where('org_bank_id', $id);

        // if ($fromDate && $toDate) {
        //     $query->whereBetween('date', [$fromDate, $toDate]);
        // }

        $data = $query->orderBy('date', 'asc')->get();
        $bank = BankInfo::with('organization')->find($id);
        $bank_id = $id;

        // Calculate the current balance
        $currentBalance = $this->calculateCurrentBalance($id, $toDate);

        $permissions = [
            'canCreateBankTransaction' => auth()->user()->can('Create Bank Transaction'),
            'canEditBankTransaction'   => auth()->user()->can('Edit Bank Transaction'),
            'canDeleteBankTransaction' => auth()->user()->can('Delete Bank Transaction')
        ];

        return Inertia::render('BankTransaction/View', [
            'data' => $data,
            'bank' => $bank,
            'bank_id' => $bank_id,
            'current_balance' => $currentBalance,
            'permissions' => $permissions
        ]);
    }

    public function create(Request $request)
    {
        $bank = BankInfo::find($request->id);
        $paymentType = Config::get('constants.paymentType');
        $accounttype  = AccountType::select('id', 'name')->get();
        return Inertia::render('BankTransaction/Add', compact('accounttype', 'paymentType', 'bank'));
    }

    public function store(BankTransactionRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            BankTransaction::create($data);
            DB::commit();
            return Redirect::to("/banktransaction/$request->org_bank_id")->with('success', 'Transaction Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = BankTransaction::with('accounttype')->where('id', $id)->get();
        $paymentType = Config::get('constants.paymentType');
        $accounttype  = AccountType::select('id', 'name')->get();
        return Inertia::render('BankTransaction/Edit', compact('data', 'accounttype', 'paymentType'));
    }

    public function update(BankTransactionRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();;
            $user = BankTransaction::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to("/banktransaction/$request->org_bank_id")->with('success', 'Transaction Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $transaction = BankTransaction::find($id);
            if ($transaction->entity_type === 'internal_transfer') {
                $createdAt = $transaction->created_at;
                $query = BankTransaction::where('id', '!=', $id)
                    ->where('entity_type', 'internal_transfer')
                    ->where('date', $transaction->date)
                    ->where('amount', $transaction->amount);
                if ($createdAt) {
                    $startTime = (clone $createdAt)->subSeconds(5);
                    $endTime = (clone $createdAt)->addSeconds(5);
                    $query->whereBetween('created_at', [$startTime, $endTime]);
                }
                $relatedTransaction = $query->first();
                if ($relatedTransaction) {
                    $relatedTransaction->delete();
                }
            }
            $transaction->delete();
            DB::commit();
            return Redirect::back()->with('success', 'Transaction Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function internalTransfer(Request $request)
    {
        $accounttype  = AccountType::select('id', 'name')->get();
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"))->get();
        return Inertia::render('BankTransaction/InterTransfer', compact('accounttype', 'bankinfo'));
    }

    public function editInternalTransfer($id)
    {
        $transaction = BankTransaction::find($id);

        if (!$transaction || $transaction->entity_type !== 'internal_transfer') {
            return Redirect::back()->with('error', 'Internal transfer not found');
        }

        $createdAt = $transaction->created_at;

        $relatedTransaction = BankTransaction::where('id', '!=', $id)
            ->where('entity_type', 'internal_transfer')
            ->where('date', $transaction->date)
            ->where('amount', $transaction->amount);

        if ($createdAt) {
            $startTime = (clone $createdAt)->subSeconds(5);
            $endTime = (clone $createdAt)->addSeconds(5);
            $relatedTransaction->whereBetween('created_at', [$startTime, $endTime]);
        }

        $relatedTransaction = $relatedTransaction->first();
        $fromTransaction = $transaction->payment_type === 'dr' ? $transaction : $relatedTransaction;
        $toTransaction = $transaction->payment_type === 'cr' ? $transaction : $relatedTransaction;

        $fromBank = BankInfo::find($fromTransaction->org_bank_id);
        $toBank = BankInfo::find($toTransaction->org_bank_id);

        if (!$fromBank || !$toBank) {
            return Redirect::back()->with('error', 'Bank information not found');
        }

        $transferData = [
            'id' => $id,
            'related_id' => $relatedTransaction->id,
            'from_bank' => $fromTransaction->org_bank_id,
            'from_bank_name' => $fromBank->bank_name . ' - ' . $fromBank->account_number,
            'to_bank' => $toTransaction->org_bank_id,
            'to_bank_name' => $toBank->bank_name . ' - ' . $toBank->account_number,
            'account_type' => $transaction->account_type,
            'date' => $transaction->date,
            'amount' => $transaction->amount
        ];
        $accounttype = AccountType::select('id', 'name')->get();
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"))->get();
        return Inertia::render('BankTransaction/EditInterTransfer', compact('transferData', 'accounttype', 'bankinfo'));
    }

    public function saveInternalTransfer(InternalBankTransferRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['entity_type'] = 'internal_transfer';
            $data['org_bank_id'] = $data['from_bank'];
            $data['payment_type'] = 'dr';
            $data['note'] = $data['to_bank_name'];
            $data['created_by'] = $data['updated_by'] = auth()->id();
            BankTransaction::create($data);
            $data['payment_type'] = 'cr';
            $data['org_bank_id'] = $data['to_bank'];
            $data['note'] = $data['from_bank_name'];
            BankTransaction::create($data);
            DB::commit();
            return Redirect::to("/banktransaction/$request->org_bank_id")->with('success', 'Transaction Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function updateInternalTransfer(InternalBankTransferRequest $request)
    {
        DB::beginTransaction();
        $data = (array) $request->DTO();
        $data['created_by'] = $data['updated_by'] = auth()->id();
        // dd($data['id']);
        try {

            $transaction1 = BankTransaction::find($data['id']);
            $transaction2 = BankTransaction::find($data['related_id']);

            if (!$transaction1 || !$transaction2 ||
                $transaction1->entity_type !== 'internal_transfer' ||
                $transaction2->entity_type !== 'internal_transfer') {
                return Redirect::back()->with('error', 'Internal transfer transactions not found');
            }

            $fromTransaction = $transaction1->payment_type === 'dr' ? $transaction1 : $transaction2;
            $toTransaction = $transaction1->payment_type === 'cr' ? $transaction1 : $transaction2;

            // Update from transaction
            $fromTransaction->org_bank_id = $request->from_bank;
            $fromTransaction->account_type = $request->account_type;
            $fromTransaction->date = $request->date;
            $fromTransaction->amount = $request->amount;
            $fromTransaction->note = $request->to_bank_name;
            $fromTransaction->updated_by = auth()->id();
            $fromTransaction->save();

            // Update to transaction
            $toTransaction->org_bank_id = $request->to_bank;
            $toTransaction->account_type = $request->account_type;
            $toTransaction->date = $request->date;
            $toTransaction->amount = $request->amount;
            $toTransaction->note = $request->from_bank_name;
            $toTransaction->updated_by = auth()->id();
            $toTransaction->save();
            DB::commit();
            return Redirect::to("/banktransaction/{$request->from_bank}")->with('success', 'Internal Transfer Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            // dd($e->getMessage());
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }
    public function exportBankTransaction(Request $request, $id)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $query = BankTransaction::with('paymentReceive.customers', 'accounttype', 'paymentPaid.company')
                    ->where('org_bank_id', $id);

        if ($fromDate && $toDate) {
            $query->whereBetween('date', [$fromDate, $toDate]);
        }

        $transactions = $query->orderBy('date', 'asc')->get();
        $bank = BankInfo::with('organization')->find($id);

        $organizationName = $bank->organization->name ?? 'Organization';
        $bankName = $bank->bank_name . ' - ' . $bank->account_number;
        $bankAmountType = $bank->amount_type;
        $initialBalance = (float)$bank->balance;

        if ($fromDate) {
            $earlierTransactions = BankTransaction::where('org_bank_id', $id)
                ->where('date', '<', $fromDate)
                ->orderBy('date', 'asc')
                ->get();

            foreach ($earlierTransactions as $transaction) {
                $amount = (float)$transaction->amount;
                $isCreditBank = $bankAmountType === 'cr';
                $isCreditTransaction = $transaction->payment_type === 'cr';

                if ($isCreditBank) {
                    $initialBalance = $isCreditTransaction ? $initialBalance - $amount : $initialBalance + $amount;
                } else {
                    $initialBalance = $isCreditTransaction ? $initialBalance + $amount : $initialBalance - $amount;
                }
            }
        }
        return Excel::download(
            new BankTransactionExport($transactions,$organizationName,$bankName,$fromDate,$toDate,$bankAmountType,$initialBalance),
            'bank_transactions.xlsx'
        );
    }
}








