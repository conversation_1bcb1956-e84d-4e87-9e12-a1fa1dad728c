<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained( table: 'orders', indexName: 'ordd_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('product_id')->constrained( table: 'products', indexName: 'orddp_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('hsn_code');
            $table->integer('qty');
            $table->integer('delivered_qty')->default(0);
            $table->double('price', 16, 2);
            $table->double('total_price', 16, 2);
            $table->integer('gst');
            $table->double('gst_amount', 16, 2);
            $table->double('total_gst_amount', 16, 2);
            $table->double('total_amount', 16, 2);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_details');
    }
};
