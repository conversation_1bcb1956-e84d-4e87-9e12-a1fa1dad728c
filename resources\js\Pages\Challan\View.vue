<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['data']);

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

</script>

<template>
    <Head title="Challan"/>

    <AdminLayout>
        <div class="animate-top h-screen">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Challan Detail</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('challan.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Customer:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.customer_name ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.gst_no  ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.email ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.contact_no ?? '-'}}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Challan Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].challan_number ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Challan Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Category:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].category ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Sales Person:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name ?? '-'}} {{ data[0].users.last_name ?? '-'}}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Sr No</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Product Code</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Product Description</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">HSN Code</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Batch</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Qty</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Invoiced Qty</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Return Qty</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(product, index)  in data[0].challan_detail" :key="index">
                        <th class="px-4 py-2.5 min-w-20">{{ index + 1 }}</th>
                        <th class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">{{ product.viewserialnumbers.product.item_code ?? '-' }}</th>
                        <th class="px-4 py-2.5 font-medium text-gray-900 whitespace-normal min-w-60">{{ product.viewserialnumbers.product.name ?? '-' }}</th>
                        <th class="px-4 py-2.5 font-medium  whitespace-nowrap min-w-32">{{ product.viewserialnumbers.product.hsn_code ?? '-' }}</th>
                        <th class="px-4 py-2.5 min-w-22">{{ product.viewserialnumbers.batch ?? '-' }}</th>
                        <th class="px-4 py-2.5 min-w-24">{{ product.qty ?? '-' }}</th>
                        <th class="px-4 py-2.5 min-w-36">{{ product.invoiced_qty ?? '-' }}</th>
                        <th class="px-4 py-2.5 min-w-32">{{ product.return_qty ?? '-' }}</th>
                    </tr>
                 </tbody>
            </table>
        </div>
        </div>
        <div class="mt-6 sm:flex sm:items-center">
            <h1 class="text-2xl font-semibold leading-7 text-gray-900" v-if="(data[0].invoice && data[0].invoice.length != 0)">Invoice List</h1>
        </div>
        <div class="flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden" style="min-height:500px">
                <div class="p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg" v-if="(data[0].invoice && data[0].invoice.length > 0)">
                    <table class="min-w-full divide-y divide-gray-300" >
                        <thead class="bg-gray-50 border">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">INVOICE NO</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total Amount (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">STATUS</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">DATE</th>
                        </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-for="(receive, index) in data[0].invoice" :key="index" class="">
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ receive.invoice_no}}</td>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ parseFloat(receive.total_amount).toFixed(2)}}</td>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ receive.status }}</td>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ formatDate(receive.date) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            </div>
        </div>
        </form>
        </div>
    </AdminLayout>

</template>
