<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['organization', 'customers']);

const form = useForm('post', '/email-tag', {
    // name:'',
    // description:'',
    organization_id:'',
    customer_id:'',
    invoice_type:'',
    sales_user_id: 22,
    category: 'Sales',
    entity_type: 'invoice',
    invoice_no: '',
    cgst: 0,
    sgst: 0,
    igst: 0,
    date: '',
    status: 'Unpaid',
    total_gst: 0,
    sub_total: '',
    total_amount: '',
    discount_before_tax: 0,
    total_discount: 0,

});

const setOrganization = (id, name) => {
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setCustomer = (id, name) => {
    form.customer_id = id;
    form.errors.customer_id = null;
};

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

</script>

<template>
    <Head title="Email Tags" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Create Tag</h2>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-4">
                            <InputLabel for="company_name" value="Organization" />
                                <div class="relative mt-2">
                                <SearchableDropdown :options="organization"
                                v-model="form.organization_id"
                                @onchange="setOrganization"
                                :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                                </div>
                        </div>
                        <div class="sm:col-span-6">
                        <InputLabel for="customer_id" value="Customer Name" />
                                <div class="relative mt-2">
                                <SearchableDropdown :options="customers"
                                v-model="form.customer_id"
                                @onchange="setCustomer"
                                :class="{ 'error rounded-md': form.errors.customer_id }"
                                />
                                </div>
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="customer_id" value="Date" />
                            <div class="flex space-x-2 items-center">
                                    <input
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"  v-model="form.date"   @change="form.validate('date')"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-4">
                             <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="total_amount"
                                    type="text"
                                    v-model="form.total_amount"
                                />
                        </div>
                        <div class="sm:col-span-4">
                             <InputLabel for="customer_id" value="Invoice No" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    v-model="form.invoice_no"
                                />
                        </div>
                        <div class="sm:col-span-4">
                             <InputLabel for="customer_id" value="Invoice Type" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    v-model="form.invoice_type"
                                />
                        </div>
                        <!-- <div class="sm:col-span-6">
                            <InputLabel for="name" value="Tag Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                                autocomplete="name"
                                @change="form.validate('name')"
                            />
                            <InputError  v-if="form.invalid('name')" class="" :message="form.errors.name" />
                        </div>
                        <div class="sm:col-span-6">
                            <InputLabel for="description" value="Description"/>
                            <TextArea
                                id="description"
                                type="text"
                                v-model="form.description"
                                :rows="2"
                                @change="form.validate('description')"
                            />
                            <InputError v-if="form.invalid('description')" class="" :message="form.errors.description"/>
                        </div> -->
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('email-tag.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                    Cancel
                                </button>
                            </template>
                        </SvgLink>

                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

