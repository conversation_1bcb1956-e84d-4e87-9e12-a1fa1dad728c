<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrderReceiveDetails extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'purchase_order_receive_details';

    protected static $logName = 'Company-PO-Receive';

    public function getLogDescription(string $event): string
{
    $poNumber = $this->purchaseOrderDetail && $this->purchaseOrderDetail->purchaseOrder
                ? $this->purchaseOrderDetail->purchaseOrder->po_number
                : 'Unknown PO Number';
    $productName = $this->product ? $this->product->name : 'Unknown Product';
    $userName = $this->users ? $this->users->name : 'Unknown User';
    return "PO receive details has been {$event} for <strong>{$productName}</strong> : {$poNumber} by";
}

    protected static $logAttributes = [
        'purchase_order_receive_id',
        'purchase_order_detail_id',
        'organization_id',
        'product_id',
        'receive_qty',
        'old_record',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'purchase_order_receive_id',
        'purchase_order_detail_id',
        'organization_id',
        'product_id',
        'receive_qty',
        // 'purchase_price',
        'old_record',
        'created_by',
        'updated_by'
    ];

    public function purchaseOrderDetail()
    {
        return $this->belongsTo(PurchaseOrderDetail::class);
    }

    public function purchaseOrderReceives()
    {
        return $this->belongsTo(PurchaseOrderReceives::class, 'purchase_order_receive_id', 'id');
    }

    public function serialNumbers()
    {
        return $this->hasMany(SerialNumbers::class, 'purchase_order_receive_detail_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Company PO Received details for : " . $model->purchaseOrderDetail->purchaseOrder->po_number;
        $mergedAttributes = [];

        if($event == 'created' || $event == 'deleted'){
            $mergedAttributes = [
                'poReceive'         => $model->purchaseOrderDetail->getAttributes(),
                'poReceiveDetail'   => $model->getAttributes()
            ];
            //$mergedAttributes = array_merge($model->getAttributes(), $model->purchaseOrder->getAttributes());
        }

        self::addCustomLogEntry($model, $event, $logName, $mergedAttributes);
    }
}
