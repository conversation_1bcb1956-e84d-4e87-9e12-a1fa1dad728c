<script setup>
import { ref, onMounted } from 'vue';
import { Link , useForm} from '@inertiajs/vue3';

const logoSrc = ref('/uploads/companyprofile/defaultimg.png');
const visionlogo = ref('/uploads/companyprofile/visionlogo.png');
const form = useForm({});
const updateLogoSrc = (value) => {
    logoSrc.value = value;
};

onMounted(async () => {
    try {
        const response = await fetch('/api/logo');
        if (response.ok) {
            const data = await response.json();
            if (data.logoUrl) {
                updateLogoSrc('/uploads/companyprofile/'+data.logoUrl);
            } else {
                updateLogoSrc('/uploads/companyprofile/defaultimg.png');
            }
        }
    } catch (error) {
        console.error('Error fetching logo:', error);
    }
});

</script>


<template>
   <img :src="visionlogo" alt="LOGO">
</template>
