import{K as g,h as V,o as p,c as _,a as s,u as t,w as i,F as b,Z as x,b as a,d as h,g as q,T as U,f as O}from"./app-2ecbacfc.js";import{_ as T,a as $}from"./AdminLayout-42d5bb92.js";import{_ as m}from"./InputError-aa79d601.js";import{_ as r}from"./InputLabel-f62a278f.js";import{P as w}from"./PrimaryButton-0d76f021.js";import{_ as d}from"./TextInput-73b24943.js";import{_ as N}from"./TextArea-00d3f05d.js";import{_ as y}from"./SearchableDropdown-6058bf5f.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top h-screen"},j={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},E=a("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Funnel",-1),S={class:"border-b border-gray-900/10 pb-12"},B={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},F={class:"sm:col-span-3"},M={class:"sm:col-span-3"},P={class:"sm:col-span-3"},k={class:"sm:col-span-3"},D={class:"sm:col-span-3"},I={class:"sm:col-span-3"},L={class:"sm:col-span-3"},K={class:"sm:col-span-3"},Z={class:"relative mt-2"},z={class:"sm:col-span-3"},A={class:"relative mt-2"},G={class:"sm:col-span-6"},H={class:"flex mt-6 items-center justify-between"},J={class:"ml-auto flex items-center justify-end gap-x-6"},Q=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),R={key:0,class:"text-sm text-gray-600"},ne={__name:"Edit",props:{data:{type:Object},inquiryType:{type:Object},months:{type:Object}},setup(c){const n=g().props.data,e=V({id:n.id,customer_name:n.customer_name,dr_name:n.dr_name,place:n.place,mobile_number:n.mobile_number,company:n.company,product:n.product,order_value:n.order_value,order_month:n.order_month,inquiry_type:n.inquiry_type,close_date:n.close_date,status:n.status}),f=(u,o)=>{e.inquiry_type=u,e.errors.inquiry_type=null},v=(u,o)=>{e.order_month=u};return(u,o)=>(p(),_(b,null,[s(t(x),{title:"Edit Funnel"}),s(T,null,{default:i(()=>[a("div",C,[a("div",j,[E,a("form",{onSubmit:o[10]||(o[10]=h(l=>t(e).patch(u.route("funnel.update")),["prevent"]))},[a("div",S,[a("div",B,[a("div",F,[s(r,{for:"customer_name",value:"Customer Name"}),s(d,{id:"customer_name",type:"text",modelValue:t(e).customer_name,"onUpdate:modelValue":o[0]||(o[0]=l=>t(e).customer_name=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.customer_name},null,8,["message"])]),a("div",M,[s(r,{for:"dr_name",value:"Doctor Name"}),s(d,{id:"dr_name",type:"text",modelValue:t(e).dr_name,"onUpdate:modelValue":o[1]||(o[1]=l=>t(e).dr_name=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.dr_name},null,8,["message"])]),a("div",P,[s(r,{for:"place",value:"Place/City"}),s(d,{id:"place",type:"text",modelValue:t(e).place,"onUpdate:modelValue":o[2]||(o[2]=l=>t(e).place=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.place},null,8,["message"])]),a("div",k,[s(r,{for:"mobile_number",value:"Mobile Number"}),s(d,{id:"mobile_number",type:"text",numeric:!0,maxLength:"10",modelValue:t(e).mobile_number,"onUpdate:modelValue":o[3]||(o[3]=l=>t(e).mobile_number=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.mobile_number},null,8,["message"])]),a("div",D,[s(r,{for:"company",value:"Company"}),s(d,{id:"company",type:"text",modelValue:t(e).company,"onUpdate:modelValue":o[4]||(o[4]=l=>t(e).company=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.company},null,8,["message"])]),a("div",I,[s(r,{for:"product",value:"Products"}),s(d,{id:"product",type:"text",modelValue:t(e).product,"onUpdate:modelValue":o[5]||(o[5]=l=>t(e).product=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.product},null,8,["message"])]),a("div",L,[s(r,{for:"order_value",value:"Order Value"}),s(d,{id:"order_value",type:"text",modelValue:t(e).order_value,"onUpdate:modelValue":o[6]||(o[6]=l=>t(e).order_value=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.order_value},null,8,["message"])]),a("div",K,[s(r,{for:"customer_id",value:"Estimated Month Of Order"}),a("div",Z,[s(y,{options:c.months,modelValue:t(e).order_month,"onUpdate:modelValue":o[7]||(o[7]=l=>t(e).order_month=l),onOnchange:v},null,8,["options","modelValue"])])]),a("div",z,[s(r,{for:"customer_id",value:"Inquiry Type"}),a("div",A,[s(y,{options:c.inquiryType,modelValue:t(e).inquiry_type,"onUpdate:modelValue":o[8]||(o[8]=l=>t(e).inquiry_type=l),onOnchange:f},null,8,["options","modelValue"])])]),a("div",G,[s(r,{for:"status",value:"Latest Status"}),s(N,{id:"status",type:"text",rows:3,modelValue:t(e).status,"onUpdate:modelValue":o[9]||(o[9]=l=>t(e).status=l)},null,8,["modelValue"]),s(m,{class:"",message:t(e).errors.status},null,8,["message"])])])]),a("div",H,[a("div",J,[s($,{href:u.route("funnel.index")},{svg:i(()=>[Q]),_:1},8,["href"]),s(w,{disabled:t(e).processing},{default:i(()=>[q("Save")]),_:1},8,["disabled"]),s(U,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:i(()=>[t(e).recentlySuccessful?(p(),_("p",R,"Saved.")):O("",!0)]),_:1})])])],32)])])]),_:1})],64))}};export{ne as default};
