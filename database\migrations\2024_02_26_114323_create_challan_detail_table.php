<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('challan_detail', function (Blueprint $table) {
            $table->id();
            $table->foreignId('challan_id')->constrained( table: 'challan', indexName: 'chd_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('product_id')->constrained( table: 'products', indexName: 'chdp_id')->onDelete('cascade')->onUpdate('no action');
            // $table->longText('description')->nullable();
            $table->string('hsn_code');
            $table->integer('qty');
            $table->integer('return_qty')->default(0);
            $table->integer('accepted_qty')->default(0);
            $table->double('price', 16, 2);
            $table->double('total_price', 16, 2);
            $table->integer('gst');
            $table->double('gst_amount', 16, 2);
            $table->double('total_gst_amount', 16, 2);
            $table->double('total_amount', 16, 2);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('challan_detail');
    }
};
