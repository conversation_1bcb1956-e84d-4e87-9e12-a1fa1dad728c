<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotation', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained( table: 'customers', indexName: 'quc_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('sales_user_id')->constrained( table: 'users', indexName: 'qus_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('quotation_number');
            $table->date('date');
            $table->enum('status', ['Received', 'Accepted', 'Rejected', 'Completed']);
            $table->double('total_amount', 16, 2);
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotation');
    }
};
