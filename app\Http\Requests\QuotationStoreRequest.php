<?php

namespace App\Http\Requests;

use App\Models\Quotation;
use App\DTO\QuotationDTO;
use Support\Contracts\HasDTO;
use App\Models\QuotationDetail;
use Illuminate\Foundation\Http\FormRequest;

class QuotationStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'note' => 'nullable|string',
            'selectedProductItem.*.product_id'  => 'required|integer',
            'selectedProductItem.*.description' => 'required|string',
            'selectedProductItem.*.qty'         => 'required|integer|min:1',
            'selectedProductItem.*.price'       => 'required|numeric',
            'selectedProductItem.*.total_amount'=> 'required|numeric',
            'customer_id'                       => 'required',
            'sales_user_id'                     => 'required|integer',
            'category'                          => 'required',
            'organization_id'                   => 'required|integer',
            'validity'                          => 'required',
            'delivery'                          => 'required',
            'payment_terms'                     => 'required',
        ];

        if ($this->input('is_customer') == 'No') {
            $rules['customer_name'] = 'required';
            $rules['address'] = 'required';
            $rules['city'] = 'required';
            $rules['gst_type'] = 'required';
            $rules['type'] = 'required';
        }

        return $rules;
    }

    // public function withValidator($validator)
    // {
    //     $validator->after(function ($validator) {
    //         $selectedProductItems = $this->input()['selectedProductItem'];
    //         if(isset($selectedProductItems)){
    //             foreach ($selectedProductItems as $key => $product) {
    //                 if ($product['stock'] < $product['qty']) {
    //                     $validator->errors()->add("selectedProductItem.$key.qty1", __('Quantity is not available.'));
    //                 }
    //             }
    //         }
    //     });
    // }

    public function DTO()
    {
        return QuotationDTO::LazyFromArray($this->input());
    }
}
