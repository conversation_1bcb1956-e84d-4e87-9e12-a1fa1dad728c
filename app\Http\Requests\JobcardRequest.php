<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\JobcardDTO;
use Support\Contracts\HasDTO;

class JobcardRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'hospital_name' => 'required|string',
            'engineer_id' => 'required',
            'city' => 'required',
            'product_name' => 'required|string',
            'warranty_status' => 'required|string|in:warranty,out_of_warranty,amc,cmc',

            // 'problem_description' => 'required|string',
        ];
    }

    public function DTO()
    {
        return JobcardDTO::LazyFromArray($this->input());
    }

}
