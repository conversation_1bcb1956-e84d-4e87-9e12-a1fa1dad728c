<?php

namespace App\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class GstPurchaseDataExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $fromDate;
    protected $toDate;
    protected $organizationName;

    public function __construct(array $allData, string $fromDate, string $toDate, string $organizationName)
    {
        $this->allData = $allData;
        $this->fromDate = Carbon::parse($fromDate)->format('d/m/Y');
        $this->toDate = Carbon::parse($toDate)->format('d/m/Y');
        $this->organizationName = $organizationName;
    }

    public function title(): string
    {
        return 'GST Purchase Data as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        return [
            "S.No",
            "Desc",
            "GSTIN",
            "Invoice Date",
            "Invoice No",
            "Invoice Value (₹)",
            "Local/ Central",
            "Invoice Type",
            "HSN Code",
            "Quantity",
            "Amount (₹)",
            "Taxable Amount (₹)",
            "SGST %",
            "SGST Amount (₹)",
            "CGST %",
            "CGST Amount (₹)",
            "IGST %",
            "IGST Amount (₹)",
            "Cess",
            "Total GST (₹)"
        ];
    }

    public function collection()
    {
        $columns = [];
        $sNo = 1;

        // Initialize totals for all required columns
        $totalAmount = 0;
        $totalQuantity = 0;
        $totalTaxableAmount = 0;
        $totalSgstAmount = 0;
        $totalCgstAmount = 0;
        $totalIgstAmount = 0;
        $totalCess = 0;
        $totalGst = 0;

        $columns[] = ["", "B2B"];

        foreach ($this->allData as $d) {
            $formattedInvoice = $this->formatInvoice($d, $sNo++);
            $columns = array_merge($columns, $formattedInvoice);

            // Calculate totals from each invoice row
            foreach ($formattedInvoice as $row) {
                // Amount (₹) - index 10
                if (isset($row[10]) && is_numeric($row[10])) {
                    $totalAmount += $row[10];
                }

                // Quantity - index 9
                if (isset($row[9]) && is_numeric($row[9])) {
                    $totalQuantity += $row[9];
                }

                // Taxable Amount (₹) - index 11
                if (isset($row[11]) && is_numeric($row[11])) {
                    $totalTaxableAmount += $row[11];
                }

                // SGST Amount (₹) - index 13
                if (isset($row[13]) && is_numeric($row[13])) {
                    $totalSgstAmount += $row[13];
                }

                // CGST Amount (₹) - index 15
                if (isset($row[15]) && is_numeric($row[15])) {
                    $totalCgstAmount += $row[15];
                }

                // IGST Amount (₹) - index 17
                if (isset($row[17]) && is_numeric($row[17])) {
                    $totalIgstAmount += $row[17];
                }

                // Cess - index 18
                if (isset($row[18]) && is_numeric($row[18])) {
                    $totalCess += $row[18];
                }

                // Total GST (₹) - index 19
                if (isset($row[19]) && is_numeric($row[19])) {
                    $totalGst += $row[19];
                }
            }
        }

        // Add additional rows before the total
        // Nil Rated/Exempted row
        $nilRatedRow = array_fill(0, 20, '');
        $nilRatedRow[1] = 'Import of Goods Invoices';
        $nilRatedRow[9] = '0';
        $nilRatedRow[10] = '0.00';
        $nilRatedRow[11] = '0.00';
        $nilRatedRow[12] = '0.00';
        $nilRatedRow[13] = '0.00';
        $nilRatedRow[14] = '0.00';
        $nilRatedRow[15] = '0.00';
        $nilRatedRow[16] = '0.00';
        $nilRatedRow[17] = '0.00';
        $nilRatedRow[18] = '0.00';
        $nilRatedRow[19] = '0.00';
        $columns[] = $nilRatedRow;

        // Import Invoices row
        $importInvoicesRow = array_fill(0, 20, '');
        $importInvoicesRow[1] = 'Import of Services Invoices';
        $importInvoicesRow[9] = '0';
        $importInvoicesRow[10] = '0.00';
        $importInvoicesRow[11] = '0.00';
        $importInvoicesRow[12] = '0.00';
        $importInvoicesRow[13] = '0.00';
        $importInvoicesRow[14] = '0.00';
        $importInvoicesRow[15] = '0.00';
        $importInvoicesRow[16] = '0.00';
        $importInvoicesRow[17] = '0.00';
        $importInvoicesRow[18] = '0.00';
        $importInvoicesRow[19] = '0.00';
        $columns[] = $importInvoicesRow;

        $nilratedInvoicesRow = array_fill(0, 20, '');
        $nilratedInvoicesRow[1] = 'Nil rated Invoices';
        $nilratedInvoicesRow[9] = '0';
        $nilratedInvoicesRow[10] = '0.00';
        $nilratedInvoicesRow[11] = '0.00';
        $nilratedInvoicesRow[12] = '0.00';
        $nilratedInvoicesRow[13] = '0.00';
        $nilratedInvoicesRow[14] = '0.00';
        $nilratedInvoicesRow[15] = '0.00';
        $nilratedInvoicesRow[16] = '0.00';
        $nilratedInvoicesRow[17] = '0.00';
        $nilratedInvoicesRow[18] = '0.00';
        $nilratedInvoicesRow[19] = '0.00';
        $columns[] = $nilratedInvoicesRow;

        $itcReversalRow = array_fill(0, 20, '');
        $itcReversalRow[1] = 'ITC Reversal';
        $itcReversalRow[9] = '0';
        $itcReversalRow[10] = '0.00';
        $itcReversalRow[11] = '0.00';
        $itcReversalRow[12] = '0.00';
        $itcReversalRow[13] = '0.00';
        $itcReversalRow[14] = '0.00';
        $itcReversalRow[15] = '0.00';
        $itcReversalRow[16] = '0.00';
        $itcReversalRow[17] = '0.00';
        $itcReversalRow[18] = '0.00';
        $itcReversalRow[19] = '0.00';
        $columns[] = $itcReversalRow;

        $taxPaidonReverseChargeslRow = array_fill(0, 20, '');
        $taxPaidonReverseChargeslRow[1] = 'Tax Paid on Reverse Charges';
        $taxPaidonReverseChargeslRow[9] = '0';
        $taxPaidonReverseChargeslRow[10] = '0.00';
        $taxPaidonReverseChargeslRow[11] = '0.00';
        $taxPaidonReverseChargeslRow[12] = '0.00';
        $taxPaidonReverseChargeslRow[13] = '0.00';
        $taxPaidonReverseChargeslRow[14] = '0.00';
        $taxPaidonReverseChargeslRow[15] = '0.00';
        $taxPaidonReverseChargeslRow[16] = '0.00';
        $taxPaidonReverseChargeslRow[17] = '0.00';
        $taxPaidonReverseChargeslRow[18] = '0.00';
        $taxPaidonReverseChargeslRow[19] = '0.00';
        $columns[] = $taxPaidonReverseChargeslRow;

        $taxPaidunderReverseChargeRow = array_fill(0, 20, '');
        $taxPaidunderReverseChargeRow[1] = ' Tax Paid under Reverse Charge on Advance';
        $taxPaidunderReverseChargeRow[9] = '0';
        $taxPaidunderReverseChargeRow[10] = '0.00';
        $taxPaidunderReverseChargeRow[11] = '0.00';
        $taxPaidunderReverseChargeRow[12] = '0.00';
        $taxPaidunderReverseChargeRow[13] = '0.00';
        $taxPaidunderReverseChargeRow[14] = '0.00';
        $taxPaidunderReverseChargeRow[15] = '0.00';
        $taxPaidunderReverseChargeRow[16] = '0.00';
        $taxPaidunderReverseChargeRow[17] = '0.00';
        $taxPaidunderReverseChargeRow[18] = '0.00';
        $taxPaidunderReverseChargeRow[19] = '0.00';
        $columns[] = $taxPaidunderReverseChargeRow;

        // Add total row with all calculated totals
        $totalRow = array_fill(0, 20, '');
        $totalRow[1] = ' Gross Total';
        $totalRow[9] = number_format($totalQuantity, 0);
        $totalRow[10] = number_format($totalAmount, 2, '.', ',');
        $totalRow[11] = number_format($totalTaxableAmount, 2, '.', ',');
        $totalRow[13] = number_format($totalSgstAmount, 2, '.', ',');
        $totalRow[15] = number_format($totalCgstAmount, 2, '.', ',');
        $totalRow[17] = number_format($totalIgstAmount, 2, '.', ',');
        $totalRow[18] = number_format($totalCess, 2, '.', ',');
        $totalRow[19] = number_format($totalGst, 2, '.', ',');
        $columns[] = $totalRow;

        return collect($columns);
    }

    private function formatInvoice($invoice, $sNo)
    {
        $formattedData = [];
        $lastInvoiceNo = null;
        $companyName = $invoice['purchase_order']['company']['name'] ?? 'N/A';
        $gstin = $invoice['purchase_order']['company']['gst_no'] ?? 'N/A';
        $gstType = $invoice['purchase_order']['company']['gst_type'] ?? 'N/A';
        $localOrCentral = ($gstType === 'IGST') ? 'Central' : 'Local';
        $invoiceDate = $invoice['customer_invoice_date'] ?? 'N/A';
        $invoiceNo = $invoice['customer_invoice_no'] ?? 'N/A';
        $invoiceValue = $invoice['total_amount'] ?? 0;


        $hsnGrouped = [];

        // Group by HSN code and sum quantities and amounts
        foreach ($invoice['purchase_order_receive_details'] as $detail) {
            $hsnCode = $detail['product']['hsn_code'] ?? 'N/A';
            $quantity = $detail['receive_qty'] ?? 0;
            $gstPercent = $detail['purchase_order_detail']['gst'] ?? 0;

            // Calculate purchase price from serial numbers
            $totalPurchasePrice = 0;
            $totalSerialQty = 0;

            foreach ($detail['serial_numbers'] as $serial) {
                $serialQty = $serial['receive_qty'] ?? 0;
                $purchasePrice = $serial['purchase_price'] ?? 0;
                $totalPurchasePrice += ($purchasePrice * $serialQty);
                $totalSerialQty += $serialQty;
            }

            // Calculate GST amounts
            $gstAmount = ($totalPurchasePrice * $gstPercent) / 100;
            $totalAmount = $totalPurchasePrice + $gstAmount;

            if (!isset($hsnGrouped[$hsnCode])) {
                $hsnGrouped[$hsnCode] = [
                    'quantity' => 0,
                    'total_price' => 0,
                    'sgst_amount' => 0,
                    'cgst_amount' => 0,
                    'igst_amount' => 0,
                    'sgst' => 0,
                    'cgst' => 0,
                    'igst' => 0,
                    'total_gst' => 0,
                ];
            }

            // Sum the quantities and other relevant details
            $hsnGrouped[$hsnCode]['quantity'] += $quantity;
            $hsnGrouped[$hsnCode]['total_price'] += $totalPurchasePrice;

            // Calculate GST amounts based on purchase order GST type
            if ($invoice['purchase_order']['igst'] > 0) {
                $hsnGrouped[$hsnCode]['igst_amount'] += $gstAmount;
                $hsnGrouped[$hsnCode]['igst'] = $gstPercent;
            } else {
                $hsnGrouped[$hsnCode]['sgst_amount'] += $gstAmount / 2;
                $hsnGrouped[$hsnCode]['cgst_amount'] += $gstAmount / 2;
                $hsnGrouped[$hsnCode]['sgst'] = $gstPercent / 2;
                $hsnGrouped[$hsnCode]['cgst'] = $gstPercent / 2;
            }

            $hsnGrouped[$hsnCode]['total_gst'] += $gstAmount;
        }



        // Now loop through the grouped HSN data
        foreach ($hsnGrouped as $hsnCode => $group) {
            // Check if this is the first row for this invoice
            if ($invoiceNo !== $lastInvoiceNo) {
                $formattedData[] = [
                    $sNo,
                    $companyName,
                    $gstin,
                    $invoiceDate,
                    $invoiceNo,
                    $invoiceValue,
                    $localOrCentral,
                    'Inventory',
                    $hsnCode,
                    $group['quantity'],
                    $invoiceValue,
                    !empty($group['total_price']) ? $group['total_price'] : '0.00',
                    !empty($group['sgst']) ? $group['sgst'] : '0.00',
                    !empty($group['sgst_amount']) ? $group['sgst_amount'] : '0.00',
                    !empty($group['cgst']) ? $group['cgst'] : '0.00',
                    !empty($group['cgst_amount']) ? $group['cgst_amount'] : '0.00' ,
                    !empty($group['igst']) ? $group['igst'] : '0.00',
                    !empty($group['igst_amount']) ? $group['igst_amount'] : '0.00',
                    '0.00',
                    !empty($group['total_gst']) ? $group['total_gst'] : '0.00'
                ];
                // Save the last invoice number
                $lastInvoiceNo = $invoiceNo;
            } else {
                // For subsequent rows with the same invoice number, set company name and invoice value to null
                $formattedData[] = [
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    $hsnCode,
                    $group['quantity'],
                    '0.00',
                    !empty($group['total_price']) ? $group['total_price'] : '0.00',
                    !empty($group['sgst']) ? $group['sgst'] : '0.00',
                    !empty($group['sgst_amount']) ? $group['sgst_amount'] : '0.00',
                    !empty($group['cgst']) ? $group['cgst'] : '0.00',
                    !empty($group['cgst_amount']) ? $group['cgst_amount'] : '0.00' ,
                    !empty($group['igst']) ? $group['igst'] : '0.00',
                    !empty($group['igst_amount']) ? $group['igst_amount'] : '0.00',
                    '0.00',
                    !empty($group['total_gst']) ? $group['total_gst'] : '0.00'
                ];
            }
        }

        return $formattedData;
    }

    public function startCell(): string
    {
        return 'A5';
    }

    public function styles(Worksheet $sheet)
    {
        // Merge cells for the header information
        $sheet->mergeCells('A1:T1');
        $sheet->mergeCells('A2:T2');
        $sheet->mergeCells('A3:T3');
        $sheet->mergeCells('A4:T4');

        $sheet->setCellValue('A1', strtoupper($this->organizationName));
        $sheet->setCellValue('A2', '1301/1302, 13TH FLOOR, LINK INSPIRED WORKSPACE, OPP.VISHNUDHARA GARDENS, NR.JLR SHOWROOM, OFF.S.G.ROAD, GOTA, AHMEDABAD-382481');
        $sheet->setCellValue('A3', 'GSTIN : 24AAGCV8377E1Z1');
        $sheet->setCellValue('A4', 'GSTR2 DETAILS FOR THE PERIOD ' . $this->fromDate . ' TO ' . $this->toDate);

        $styleArray = [
            'font' => [
                'bold' => true,
                'size' => 14,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet->getStyle('A1')->applyFromArray($styleArray);

        $sheet->getStyle('A2:A3')->applyFromArray([
            'font' => [
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        $sheet->getStyle('A4')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        $sheet->getStyle('A5:T5')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'FFFFCC',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);

        $highestRow = $sheet->getHighestRow();

        foreach (range(6, $highestRow) as $row) {
            $sheet->getStyle("A$row:T$row")->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ]);

            if ($row % 2 == 0) {
                $sheet->getStyle("A$row:T$row")->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('F9F9F9');
            } else {
                $sheet->getStyle("A$row:T$row")->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('FFFFFF');
            }
        }

        // Style the additional rows (Nil Rated, Import Invoices)
        $additionalRowsStartRow = $highestRow - 6; // 2 additional rows before total

        for ($row = $additionalRowsStartRow; $row < $highestRow; $row++) {
            $sheet->getStyle("A{$row}:T{$row}")->applyFromArray([
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'F0F0F0', // Light gray background
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT,
                ],
            ]);

            // Format number cells and set right alignment for numeric values
            $sheet->getStyle("J{$row}:T{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle("J{$row}")->getNumberFormat()->setFormatCode('#,##0'); // Quantity as whole number

            // Set right alignment for all numeric cells
            $sheet->getStyle("J{$row}:T{$row}")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        }

        // Style the total row
        $sheet->getStyle("A{$highestRow}:T{$highestRow}")->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'FFFF00',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        // Format the total cells with proper number formatting
        // Quantity - column J
        $sheet->getStyle("J{$highestRow}")->getNumberFormat()->setFormatCode('#,##0');

        // Amount (₹) - column K
        $sheet->getStyle("K{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Taxable Amount (₹) - column L
        $sheet->getStyle("L{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // SGST Amount (₹) - column N
        $sheet->getStyle("N{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // CGST Amount (₹) - column P
        $sheet->getStyle("P{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // IGST Amount (₹) - column R
        $sheet->getStyle("R{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Cess - column S
        $sheet->getStyle("S{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Total GST (₹) - column T
        $sheet->getStyle("T{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        return [];
    }
}
