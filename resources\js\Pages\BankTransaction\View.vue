<script setup>
import { ref, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps(['data', 'bank_id', 'bank', 'current_balance', 'permissions']);

const from_date = ref('');
const to_date = ref('');

const form = useForm({});
const modalVisible = ref(false);
const selectedUserId = ref(null);

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('banktransaction.destroy', { id: selectedUserId.value }), {
        onSuccess: () => closeModal()
    });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmountNew = (amount) => {
     const amountStr = amount.toFixed(2).toString();
    const [integerPart, decimalPart] = amountStr.split('.');
    const lastThree = integerPart.substring(integerPart.length - 3);
    const otherNumbers = integerPart.substring(0, integerPart.length - 3);
    const formattedIntegerPart = otherNumbers !== ''
        ? otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + ',' + lastThree
        : lastThree;
    return `${formattedIntegerPart}.${decimalPart}`;
};

 const updateBalance = (balance, transaction, bankAmountType) => {
    const amount = parseFloat(transaction.amount);
    const isCreditBank = bankAmountType === 'cr';
    const isCreditTransaction = transaction.payment_type === 'cr';

    if (isCreditBank) {
        return isCreditTransaction ? balance - amount : balance + amount;
    } else {
        return isCreditTransaction ? balance + amount : balance - amount;
    }
};

const filteredTransactions = computed(() => {
     let cumulativeBalance = parseFloat(props.bank.balance);
    const bankAmountType = props.bank.amount_type;

     if (from_date.value) {
        const fromDate = new Date(from_date.value);
        props.data.forEach((transaction) => {
            const transactionDate = new Date(transaction.date);
            if (transactionDate < fromDate) {
                cumulativeBalance = updateBalance(cumulativeBalance, transaction, bankAmountType);
            }
        });
    }

     let filteredData = props.data.filter((transaction) => {
        const transactionDate = new Date(transaction.date);

        if (from_date.value && to_date.value) {
            return transactionDate >= new Date(from_date.value) &&
                   transactionDate <= new Date(to_date.value);
        }

        return (!from_date.value || transactionDate >= new Date(from_date.value)) &&
               (!to_date.value || transactionDate <= new Date(to_date.value));
    });

     return filteredData.map((transaction) => {
        cumulativeBalance = updateBalance(cumulativeBalance, transaction, bankAmountType);
        const prefix = cumulativeBalance >= 0 ? 'cr' : 'dr';
        const formattedBalance = formatAmountNew(Math.abs(cumulativeBalance)) + ' ' + prefix;
        // const formattedBalance = formatAmountNew(Math.abs(cumulativeBalance));
        return { ...transaction, balance: formattedBalance };
    });
});

const handleDateChange = () => {};


const exportXls = () => {
    const xlsName = 'Bank_Transactions_' + props.bank.bank_name.replace(/\s+/g, '_');
    const params = {};

    if (from_date.value && to_date.value) {
        params.from_date = from_date.value;
        params.to_date = to_date.value;
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-bank-transaction/${props.bank.id}?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${xlsName}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

</script>

<template>
    <Head title="Bank Transaction"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">{{ bank.bank_name }} - {{ bank.account_number }}</h1>

            </div>
            <p class="text-sm font-semibold text-gray-900">{{ bank.organization.name }}</p>
            <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('banktransaction.index')">
                            Back
                    </CreateButton>
                </div>
                <div class="flex justify-end" v-if="permissions.canCreateBankTransaction">
                    <CreateButton :href="route('banktransaction.create',{id:bank.id})">
                            Create Transaction
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3 6h18M4 10h16M5 14h14M6 18h12"/>
                    </svg>
                    <InputLabel for="company_id" value="Filters"/>
                </div>
                <div class="inline-flex items-center space-x-4">
                    <div class="mt-2 flex items-center">
                        <span class="text-sm text-gray-600 mr-2">Current Balance:</span>
                        <span class="text-base font-bold text-gray-900">₹{{ current_balance.formatted_balance }}</span>
                    </div>
                    <button @click="exportXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="Export XLS">
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-4">
                    <InputLabel for="date" value="From Date"/>
                    <input
                        v-model="from_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleDateChange"
                        :class="{ 'error rounded-md': form.errors.from_date }"
                    />
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="date" value="To Date"/>
                    <input
                        v-model="to_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleDateChange"
                        :class="{ 'error rounded-md': form.errors.to_date }"
                    />
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                DATE
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                TYPE
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                NARRATION
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                RECEIPT
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                PAYMENT (₹)
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                BALANCE (₹)
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                ACTION
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="filteredTransactions && (filteredTransactions.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="transaction in filteredTransactions" :key="transaction.id">
                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatDate(transaction.date) ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-20">
                                {{ (transaction.payment_type == 'cr') ? 'Rcpt' : 'Pymt' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52 text-sm flex flex-col font-medium text-gray-900">
                                {{ (transaction.accounttype) ? `${transaction.accounttype.name} : ${transaction.note}` :  transaction.note}}
                                <span class="tooltiptext text-xs">
                                    <template v-if="transaction.entity_type === 'payment_receive'">
                                        {{ transaction?.payment_receive?.customers?.customer_name ?? '' }}
                                    </template>
                                    <template v-else-if="transaction.entity_type === 'payment_paid'">
                                        {{ transaction?.payment_paid?.company?.name ?? '' }}
                                    </template>
                                    <template v-if="transaction.entity_type === 'payment_receive' && transaction?.payment_receive">
                                        <div class="text-xs text-gray-700" v-if="transaction.payment_receive.tds_amount > 0">
                                            TDS: ₹{{ transaction.payment_receive.tds_amount }}
                                        </div>
                                        <div class="text-xs text-gray-700" v-if="transaction.payment_receive.discount_amount > 0">
                                            Discount: ₹{{ transaction.payment_receive.discount_amount }}
                                        </div>
                                    </template>
                                </span>
                            </td>
                            <td class="px-4 py-2.5 min-w-36">
                                {{ (transaction.payment_type == 'cr') ? formatAmountNew(transaction.amount) : '' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-36">
                                {{ (transaction.payment_type == 'dr') ? formatAmountNew(transaction.amount) : ''}}
                            </td>
                            <td class="px-4 py-2.5 min-w-36">
                                {{ transaction.balance }}
                            </td>
                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                    <Dropdown :align="'right'" width="48" v-if="transaction.entity_type == NULL || transaction.entity_type == 'internal_transfer'">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                             <ActionLink v-if="permissions.canEditBankTransaction && transaction.entity_type == NULL" :href="route('banktransaction.edit',{id:transaction.id})">
                                                <template #svg>
                                                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                           />
                                                     </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                             </ActionLink>
                                            <ActionLink v-if="permissions.canEditBankTransaction && transaction.entity_type === 'internal_transfer'" :href="route('editinternalbanktransfer',{id:transaction.id})">
                                                <template #svg>
                                                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                           />
                                                     </svg>
                                                </template>
                                                <template #text>
                                                     <span class="text-sm text-gray-700 leading-5">
                                                        Edit Internal Transfer
                                                     </span>
                                                </template>
                                            </ActionLink>
                                            <button v-if="permissions.canDeleteBankTransaction && (transaction.entity_type == NULL || transaction.entity_type == 'internal_transfer')" type="button" @click="openDeleteModal(transaction.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>

        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: rgb(50 68 210);
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%; /* Position the tooltip above the text */
  left: 20%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

</style>







