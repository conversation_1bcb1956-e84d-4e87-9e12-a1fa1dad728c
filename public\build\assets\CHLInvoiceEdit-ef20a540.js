import{K as L,r as S,C as $t,j as q,l as Gt,o as u,c as _,a as r,u as a,w as P,F as N,Z as Dt,b as t,t as h,k as Ut,v as Nt,d as Mt,i as j,f as x,g as T,e as qt,n as w,s as jt,x as At}from"./app-2ecbacfc.js";import{_ as Et,a as Bt}from"./AdminLayout-42d5bb92.js";import{_ as Lt}from"./InputError-aa79d601.js";import{_ as I}from"./InputLabel-f62a278f.js";import{P as it}from"./PrimaryButton-0d76f021.js";import{_ as y}from"./TextInput-73b24943.js";import{_ as Ot}from"./TextArea-00d3f05d.js";import{D as Z}from"./DangerButton-3e1103de.js";import{_ as O}from"./SecondaryButton-be49842d.js";import{M as z}from"./Modal-54f7c77a.js";import{_ as zt}from"./FileViewer-f3ce6629.js";import{_ as Ht}from"./MultipleFileUpload-7d40cdc3.js";import{_ as rt}from"./Checkbox-3bb6de23.js";import{_ as dt}from"./SearchableDropdown-6058bf5f.js";import{u as Rt}from"./index-35fd125b.js";import{_ as Qt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=G=>(jt("data-v-273aff1c"),G=G(),At(),G),Wt={class:"animate-top"},Kt={class:"sm:flex sm:items-center"},Xt=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Challan Invoice Edit")],-1)),Zt={class:"w-auto"},Jt={class:"flex space-x-2 items-center"},Yt={class:"text-sm font-semibold text-gray-900"},te={class:"flex space-x-2 items-center"},ee=["onSubmit"],se={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},oe={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},le={class:"sm:col-span-4"},ae={class:"inline-flex items-center justify-start w-full space-x-2"},ne=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Customer Name:",-1)),ce={class:"text-sm text-gray-700 leading-6"},ie={class:"inline-flex items-center justify-start w-full space-x-2"},re=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Email:",-1)),de={class:"text-sm leading-6 text-gray-700"},ue={class:"inline-flex items-center justify-start w-full space-x-2"},_e=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Contact No:",-1)),me={class:"text-sm leading-6 text-gray-700"},pe={class:"sm:col-span-8"},he={class:"inline-flex items-center justify-start w-full space-x-2"},xe=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Invoice No:",-1)),ye={class:"text-sm text-gray-700 leading-6"},ge={class:"inline-flex items-start justify-start w-full space-x-2"},ve=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Challan Number:",-1)),fe={class:"grid grid-cols-1 gap-x-2 gap-y-1 sm:grid-cols-12"},we={class:"text-sm text-gray-700 leading-6"},be={class:"inline-flex items-start justify-start w-full space-x-2"},Ve=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Challan Date :",-1)),Pe={class:"grid grid-cols-1 gap-x-2 gap-y-1 sm:grid-cols-12"},ke={class:"text-sm text-gray-700 leading-6"},Se={key:0,class:"mt-6"},Ce=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Invoiced Products")],-1)),Ie=[Ce],Fe={key:1,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Te={class:"overflow-x-auto w-full"},$e={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},Ge=d(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),De=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Ue=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Ne=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Me=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),qe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),je=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Ae=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Ee=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Be=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Le={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Oe={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ze={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},He={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Re={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Qe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),We=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Ke=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Xe={class:"divide-y divide-gray-300 bg-white"},Ze={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},Je={class:"relative mt-2"},Ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},ts={class:"relative mt-2"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ss={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},os={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},ls={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},as={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ns={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},cs={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},is={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},rs={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ds={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},us={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},_s={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ms={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ps={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},hs={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},xs={class:"px-3 py-3 text-sm text-gray-900"},ys=["onClick"],gs=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),vs=[gs],fs={class:"flex items-center justify-between"},ws={class:"ml-auto flex items-center justify-end gap-x-6"},bs={key:2,class:"mt-6"},Vs=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Challan Products")],-1)),Ps=[Vs],ks={key:3,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto w-full"},Ss={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{width:"110%"}},Cs={scope:"col",class:""},Is=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Code",-1)),Fs={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ts=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),$s=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Gs=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Ds=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Us=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Ns=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Ms=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),qs={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},js={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},As={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Es={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Bs={key:5,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ls=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),Os=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),zs=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Hs=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Rs={class:"divide-y divide-gray-300 bg-white"},Qs={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ws={class:"text-sm text-gray-900 leading-6"},Ks={key:0,class:"text-red-500 text-xs absolute"},Xs={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Zs={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},Js={class:"whitespace-normal px-3 py-3 text-sm text-gray-900 min-w-48"},Ys={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},to={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},eo={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},so={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},oo={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},lo={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ao={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},no={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},co={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},io={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ro={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},uo={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},_o={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-48"},mo={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},po={key:4,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},ho={class:"min-w-full divide-y divide-gray-300"},xo=d(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),yo={class:"divide-y divide-gray-300 bg-white"},go={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},vo={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},fo=["onClick"],wo=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),bo=[wo],Vo=["onClick"],Po=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ko=[Po],So=["onClick"],Co=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Io=[Co],Fo={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},To={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},$o={class:"sm:col-span-3 space-y-4"},Go={class:"flex space-x-4"},Do={class:"w-full"},Uo={class:"flex space-x-4"},No={class:"w-full"},Mo={class:"w-full"},qo={class:"w-full"},jo={class:"flex space-x-4"},Ao={class:"w-full"},Eo={class:"w-full"},Bo={class:"w-full"},Lo={class:"flex space-x-4"},Oo={class:"w-full"},zo={class:"w-full"},Ho={class:"sm:col-span-3"},Ro={class:"inline-flex flex-col space-y-1 items-center justify-end w-full p-4"},Qo={class:"inline-flex items-center justify-end w-full space-x-3"},Wo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Ko={class:"text-base font-semibold text-gray-900 w-20"},Xo={class:"inline-flex items-center justify-end w-full space-x-3"},Zo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Jo={class:"w-40"},Yo={class:"inline-flex items-center justify-end w-full space-x-3"},tl=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),el={class:"w-40"},sl={class:"inline-flex items-center justify-end w-full space-x-3"},ol=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),ll={class:"text-base font-semibold text-gray-900 w-20"},al={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},nl=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),cl={class:"text-base font-semibold text-gray-900 w-20"},il={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},rl=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),dl={class:"text-base font-semibold text-gray-900 w-20"},ul={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},_l=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ml={class:"text-base font-semibold text-gray-900 w-20"},pl={class:"inline-flex items-center justify-end w-full space-x-3"},hl=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),xl={class:"text-base font-semibold text-gray-900 w-20"},yl={class:"flex mt-6 items-center justify-between"},gl={class:"ml-auto flex items-center justify-end gap-x-6"},vl=d(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),fl={class:"p-6"},wl=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),bl={class:"mt-6 flex justify-end"},Vl={class:"p-6"},Pl=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to remove this product? ",-1)),kl={class:"mt-6 flex justify-end"},Sl={class:"p-6"},Cl=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to remove this Product? ",-1)),Il={class:"mt-6 flex justify-end"},Fl={class:"p-6"},Tl={class:"mt-6 px-4 flex justify-end"},$l={__name:"CHLInvoiceEdit",props:["data","invoicedata","customers","serialno","products","filepath","challanData","invoice"],setup(G){const A=G,J=L().props.filepath.view;L().props.data[0];const i=L().props.invoicedata[0],Y=S([]),ut=A.products.filter(s=>s.serial_numbers.some(e=>e.organization_id==i.organization_id));Y.value=ut;const n=Rt("post","/editchallaninvoice",{category:i.category,note:i.note,date:i.date,invoicedProduct:[],customer_id:i.customer_id,organization_id:i.organization_id,invoice_id:i.id,total_amount:i.total_amount,invoice_no:i.invoice_no,sales_user_id:i.sales_user_id,document:i.documents,cgst:i.cgst,sgst:i.sgst,igst:i.documents,total_gst:i.igst,sub_total:i.sub_total,total_discount:i.total_discount,discount_before_tax:i.discount_before_tax,dispatch:i.dispatch,transport:i.transport,patient_name:i.patient_name,customer_po_date:i.customer_po_date,customer_po_number:i.customer_po_number,eway_bill:i.eway_bill,due_days:i.due_days,cr_dr_note:i.cr_dr_note,overall_discount:i.overall_discount,selectedProductItem:[],challans:A.challanData}),_t=()=>{n.sub_total=st.value,n.cgst=i.customers.gst_type=="CGST/SGST"?D.value/2:"0",n.sgst=i.customers.gst_type=="CGST/SGST"?D.value/2:"0",n.igst=i.customers.gst_type=="IGST"?D.value:"0",n.total_gst=D.value,n.total_amount=et.value,n.total_discount=ot.value,n.invoice_type=i.customers.customer_type,n.selectedProductItem=v.value,n.invoicedProduct=m.value,n.submit({preserveScroll:!0,onSuccess:()=>n.reset()})},v=S([{product_id:"",item_code:"",product_name:"",hsn_code:"",qty:"",challan_qty:"",return_qty:"",invoiced_qty:"",price:"",sell_price:"",discount:"",discount_amount:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",challan_detail_id:"",check:!1,description:""}]),m=S([{invoice_detail_id:"",challan_detail_id:"",editmode:"",serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",expiry_date:"",mrp:"",description:""}]);$t(()=>{m.value=i.invoice_detail.map(s=>({invoice_detail_id:s.id,challan_detail_id:s.challan_detail_id,editmode:"editMode",serial_number_id:s.serial_number_id,product_name:s.serialnumbers.product.name,hsn_code:s.serialnumbers.product.hsn_code,item_code:s.serialnumbers.product.item_code,discount:parseFloat(s.discount).toFixed(2),price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),product_id:s.serialnumbers.product.id,qty:s.qty,expiry_date:s.serialnumbers.expiry_date,mrp:s.serialnumbers.mrp?parseFloat(s.serialnumbers.mrp).toFixed(2):"-",description:s.description,sell_price:parseFloat(s.price).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0"})),v.value=L().props.data.map(s=>({product_id:s.viewserialnumbers.product.id,serial_number_id:s.viewserialnumbers.id,serial_no:s.viewserialnumbers.serial_no,batch:s.viewserialnumbers.unique_id,item_code:s.viewserialnumbers.product.item_code,product_name:s.viewserialnumbers.product.name,challan_detail_id:s.id,qty:s.qty-s.invoiced_qty,challan_qty:s.qty,return_qty:s.return_qty,invoiced_qty:s.invoiced_qty,discount:"0.00",description:"",hsn_code:s.viewserialnumbers.product.hsn_code,price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),total_price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),gst:parseFloat(s.viewserialnumbers.product.gst).toFixed(2),sgst:parseFloat(s.viewserialnumbers.product.gst/2).toFixed(2),gst_amount:"",total_gst_amount:"",total_amount:"",sell_price:"",check:!1}))});const mt=()=>{m.value.push({invoice_detail_id:"",challan_detail_id:"",serial_number_id:"",product_name:"",hsn_code:""})},pt=(s,c,e)=>{m.value[e].product_id=s},ht=(s,c,e)=>{const o=A.serialno.find(l=>l.id===s);o&&(m.value[e].qty="",m.value[e].serial_number_id=o.id,m.value[e].product_name=o.product.name,m.value[e].item_code=o.product.item_code,m.value[e].expiry_date=o.expiry_date,m.value[e].mrp=o.mrp?parseFloat(o.mrp).toFixed(2):"-",m.value[e].price=parseFloat(o.purchase_price).toFixed(2),m.value[e].hsn_code=o.product.hsn_code,m.value[e].discount="0.00",m.value[e].sell_price="",m.value[e].total_price=parseFloat(o.purchase_price).toFixed(2),m.value[e].gst=parseFloat(o.product.gst).toFixed(2),m.value[e].sgst=parseFloat(o.product.gst/2).toFixed(2),m.value[e].gst_amount="",m.value[e].total_gst_amount="",m.value[e].total_amount="",m.value[e].description="",n.errors[`invoicedProduct.${e}.serial_number_id`]=null)},xt=(s,c,e)=>A.serialno.filter(l=>l.product_id===s&&l.organization_id===i.organization_id),H=S(!1),R=S(!1),Q=S(null),yt=S(null),gt=(s,c,e)=>{console.log(e),c!==void 0&&c!=""?(Q.value=c,yt.value=s,e!=null?R.value=!0:H.value=!0):m.value.splice(s,1)},E=()=>{H.value=!1},tt=()=>{R.value=!1},vt=()=>{n.get(route("removeproduct",{id:Q.value,model:"InvoiceDetail"}),{onSuccess:()=>{E(),v.value.splice(index,1)}})},ft=()=>{n.get(route("removeinvoicedetails",{id:Q.value}),{onSuccess:()=>{E(),v.value.splice(index,1)}})},wt=(s,c)=>{const e=parseFloat(s.sell_price),o=parseFloat(s.discount_before_tax_product)||0,l=parseFloat(s.discount)||0,g=i.customers.gst_type=="IGST"?s.gst:parseFloat(s.sgst*2),F=parseFloat(s.qty);let C=0,$=0;l>0||o>0?C=e*F:C=e*F*(1+g/100);const p=C*(l/100)||0,M=e*1*(g/100),V=(e*F-p-o)*(g/100);l>0||o>0?$=C-p-o+V:$=C-p;const f=e*F;return s.total_price=isNaN(f)?"":parseFloat(f).toFixed(2),s.gst_amount=isNaN(M)?"":parseFloat(M).toFixed(2),s.total_gst_amount=isNaN(V)?"":parseFloat(V).toFixed(2),s.discount_amount=isNaN(p)?"":parseFloat(p).toFixed(2),s.gst=g,isNaN($)?"":parseFloat($).toFixed(2)},b=(s,c)=>{ct(),s.total_amount=wt(s)},et=q(()=>{const s=Math.round(v.value.reduce((o,l)=>o+(l.check&&l.total_amount?parseFloat(l.total_amount):0),0)),c=Math.round(m.value.reduce((o,l)=>o+(l.total_amount?parseFloat(l.total_amount):0),0)),e=n.overall_discount?parseFloat(n.overall_discount):0;return s+c-e}),D=q(()=>{const s=v.value.reduce((e,o)=>e+(o.check&&o.total_gst_amount?parseFloat(o.total_gst_amount):0),0),c=m.value.reduce((e,o)=>e+(o.total_gst_amount?parseFloat(o.total_gst_amount):0),0);return s+c}),st=q(()=>{const s=v.value.reduce((e,o)=>e+(o.check&&o.total_price?parseFloat(o.total_price):0),0),c=m.value.reduce((e,o)=>e+(o.total_price?parseFloat(o.total_price):0),0);return s+c}),ot=q(()=>{const s=v.value.reduce((l,g)=>l+(g.check&&g.discount_amount?parseFloat(g.discount_amount):0),0),c=m.value.reduce((l,g)=>l+(g.discount_amount?parseFloat(g.discount_amount):0),0),e=n.overall_discount?parseFloat(n.overall_discount):0,o=n.discount_before_tax?parseFloat(n.discount_before_tax):0;return s+c+e+o}),k=s=>{n.errors[s]=null},bt=s=>{const c=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return c.toLocaleDateString("en-US",e)},Vt=s=>{n.document=s},W=S(!1),lt=S(null),Pt=s=>{lt.value=s,W.value=!0},kt=()=>{n.get(route("removedocument",{id:lt.value,name:"challanDocument"}),{onSuccess:()=>{K()}})},K=()=>{W.value=!1},X=S(!1),at=S(null),St=S("custom"),Ct=s=>{at.value=s,X.value=!0},nt=()=>{X.value=!1},It=s=>{const c=window.location.origin+J+s,e=document.createElement("a");e.href=c,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},U=s=>{if(s==null||isNaN(s))return"0.00";let c=Number(s).toFixed(2),[e,o]=c.split("."),l=e.substring(e.length-3),g=e.substring(0,e.length-3);return g!==""&&(l=","+l),`${g.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${o}`},Ft=q(()=>v.value.length>0&&v.value.every(s=>s.check)),Tt=s=>{v.value.map(c=>{c.check=s})},B=(s,c,e)=>{const o=c.length+e.length,l=c.reduce((V,f)=>V+(f.total_price?parseFloat(f.total_price):0),0),g=e.reduce((V,f)=>V+(f.check&&f.total_price?parseFloat(f.total_price):0),0),F=l+g,C=m.value.reduce((V,f)=>V+(f.total_price?parseFloat(f.total_price):0),0),$=v.value.reduce((V,f)=>V+(f.check&&f.total_price?parseFloat(f.total_price):0),0),p=C+$,M=s*F/p/o;c.forEach(V=>{V.discount_before_tax_product=M}),e.forEach(V=>{V.discount_before_tax_product=M})},ct=()=>{const s=parseFloat(n.discount_before_tax)||0,c=m.value.filter(p=>p.gst==5&&p.total_price>0),e=m.value.filter(p=>p.gst==12&&p.total_price>0),o=m.value.filter(p=>p.gst==18&&p.total_price>0),l=m.value.filter(p=>p.gst==28&&p.total_price>0),g=v.value.filter(p=>p.gst==5&&p.total_price>0&&p.check),F=v.value.filter(p=>p.gst==12&&p.total_price>0&&p.check),C=v.value.filter(p=>p.gst==18&&p.total_price>0&&p.check),$=v.value.filter(p=>p.gst==28&&p.total_price>0&&p.check);B(s,c,g),B(s,e,F),B(s,o,C),B(s,l,$)};return Gt(()=>n.discount_before_tax,s=>{ct(),m.value.forEach(c=>{b(c)}),v.value.forEach(c=>{b(c)})}),(s,c)=>(u(),_(N,null,[r(a(Dt),{title:"Invoice"}),r(Et,null,{default:P(()=>[t("div",Wt,[t("div",Kt,[Xt,t("div",Zt,[t("div",Jt,[t("p",Yt,h(a(i).organization.name),1),t("div",te,[Ut(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":c[0]||(c[0]=e=>a(n).date=e),onChange:c[1]||(c[1]=e=>a(n).validate("date"))},null,544),[[Nt,a(n).date]])])])])]),t("form",{onSubmit:Mt(_t,["prevent"]),class:""},[t("div",se,[t("div",oe,[t("div",le,[t("div",ae,[ne,t("span",ce,h(a(i).customers.customer_name??"-"),1)]),t("div",ie,[re,t("p",de,h(a(i).customers.email??"-"),1)]),t("div",ue,[_e,t("p",me,h(a(i).customers.contact_no??"-"),1)])]),t("div",pe,[t("div",he,[xe,t("span",ye,h(a(i).invoice_no),1)]),t("div",ge,[ve,t("div",fe,[(u(!0),_(N,null,j(G.challanData,(e,o)=>(u(),_("div",{class:"sm:col-span-3",key:o},[t("span",we,h(e.challan_number)+",",1)]))),128))])]),t("div",be,[Ve,t("div",Pe,[(u(!0),_(N,null,j(G.challanData,(e,o)=>(u(),_("div",{class:"sm:col-span-2",key:o},[t("span",ke,h(bt(e.date))+",",1)]))),128))])])])])]),m.value.length>0?(u(),_("div",Se,Ie)):x("",!0),m.value.length>0?(u(),_("div",Fe,[t("div",Te,[t("table",$e,[t("thead",null,[t("tr",null,[Ge,De,Ue,Ne,Me,qe,je,Ae,Ee,Be,a(i).customers.gst_type=="IGST"?(u(),_("th",Le,"IGST (%)")):x("",!0),a(i).customers.gst_type=="IGST"?(u(),_("th",Oe,"IGST (₹)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",ze,"CGST (%)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",He,"SGST (%)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",Re,"Total GST (₹)")):x("",!0),Qe,We,Ke])]),t("tbody",Xe,[(u(!0),_(N,null,j(m.value,(e,o)=>(u(),_("tr",{key:o},[t("td",Ze,[t("div",Je,[r(dt,{options:Y.value,modelValue:e.product_id,"onUpdate:modelValue":l=>e.product_id=l,onOnchange:(l,g)=>pt(l,g,o),onChange:c[2]||(c[2]=l=>a(n).validate("product_id")),class:w({"error rounded-md":a(n).errors[`invoicedProduct.${o}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Ye,[t("div",ts,[r(dt,{options:xt(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":l=>e.serial_number_id=l,onOnchange:(l,g)=>ht(l,g,o),onChange:c[3]||(c[3]=l=>a(n).validate("serial_number_id")),class:w({"error rounded-md":a(n).errors[`invoicedProduct.${o}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",es,h(e.hsn_code??"-"),1),t("td",ss,h(e.expiry_date??"-"),1),t("td",os,[r(y,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".description"),class:w({error:a(n).errors[`invoicedProduct.${o}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ls,h(e.mrp??"-"),1),t("td",as,h(e.price),1),t("td",ns,[r(y,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".qty"),class:w({error:a(n).errors[`invoicedProduct.${o}.qty`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])]),t("td",cs,[r(y,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":l=>e.sell_price=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".sell_price"),class:w({error:a(n).errors[`invoicedProduct.${o}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",is,h(e.total_price),1),a(i).customers.gst_type=="IGST"?(u(),_("td",rs,[r(y,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".gst"),class:w({error:a(n).errors[`invoicedProduct.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("td",ds,[r(y,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".gst"),class:w({error:a(n).errors[`invoicedProduct.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("td",us,[r(y,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".gst"),class:w({error:a(n).errors[`invoicedProduct.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),t("td",_s,h(e.total_gst_amount),1),t("td",ms,[r(y,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>b(e,o),onChange:l=>k("invoicedProduct."+o+".discount"),class:w({error:a(n).errors[`invoicedProduct.${o}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ps,h(e.discount_amount),1),t("td",hs,[t("div",xs,h(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:l=>gt(o,e.invoice_detail_id,e.challan_detail_id)},vs,8,ys)])]))),128))])])]),t("div",fs,[t("div",ws,[r(it,{onClick:mt,type:"button"},{default:P(()=>[T("Add Product")]),_:1})])])])):x("",!0),v.value.length>0?(u(),_("div",bs,Ps)):x("",!0),v.value.length>0?(u(),_("div",ks,[t("table",Ss,[t("thead",null,[t("tr",null,[t("th",Cs,[r(rt,{checked:Ft.value,"onUpdate:checked":Tt},null,8,["checked"])]),Is,a(i).category=="Service"?(u(),_("th",Fs,"Part No")):x("",!0),Ts,$s,Gs,Ds,Us,Ns,Ms,a(i).customers.gst_type=="IGST"?(u(),_("th",qs,"IGST(%)")):x("",!0),a(i).customers.gst_type=="IGST"?(u(),_("th",js,"IGST (₹)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",As,"CGST(%)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",Es,"SGST(%)")):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("th",Bs,"Total GST (₹)")):x("",!0),Ls,Os,zs,Hs])]),t("tbody",Rs,[(u(!0),_(N,null,j(v.value,(e,o)=>(u(),_("tr",{key:o},[t("td",Qs,[t("div",Ws,[r(rt,{name:"check",checked:e.check,"onUpdate:checked":l=>e.check=l},null,8,["checked","onUpdate:checked"])]),a(n).errors[`selectedProductItem.${o}.check`]?(u(),_("p",Ks,h(a(n).errors[`selectedProductItem.${o}.check`]),1)):x("",!0)]),t("td",Xs,h(e.item_code??"-"),1),a(i).category=="Service"?(u(),_("td",Zs,h(e.item_code??"-"),1)):x("",!0),t("td",Js,h(e.product_name??"-"),1),t("td",Ys,h(e.hsn_code??"-"),1),t("td",to,h(e.batch??"-"),1),t("td",eo,[r(y,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".qty"),class:w({error:a(n).errors[`selectedProductItem.${o}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",so,h(e.price??"-"),1),t("td",oo,[r(y,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":l=>e.sell_price=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".sell_price"),class:w({error:a(n).errors[`selectedProductItem.${o}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",lo,h(e.total_price??"-"),1),a(i).customers.gst_type=="IGST"?(u(),_("td",ao,[r(y,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".gst"),class:w({error:a(n).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("td",no,[r(y,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".gst"),class:w({error:a(n).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("td",co,[r(y,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".gst"),class:w({error:a(n).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),t("td",io,h(e.total_gst_amount),1),t("td",ro,[r(y,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>b(e,o),onChange:l=>k("selectedProductItem."+o+".discount"),class:w({error:a(n).errors[`selectedProductItem.${o}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",uo,h(e.discount_amount??"-"),1),t("td",_o,h(e.total_amount??"-"),1),t("td",mo,[r(y,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,onChange:l=>k("selectedProductItem."+o+".description"),class:w({error:a(n).errors[`selectedProductItem.${o}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128))])])])):x("",!0),a(i).documents&&a(i).documents.length>0?(u(),_("div",po,[t("table",ho,[xo,t("tbody",yo,[(u(!0),_(N,null,j(a(i).documents,(e,o)=>(u(),_("tr",{key:a(i).id,class:""},[t("td",go,h(e.orignal_name),1),t("td",vo,[t("button",{type:"button",onClick:l=>Pt(e.id)},bo,8,fo),t("button",{type:"button",onClick:l=>Ct(e.name)},ko,8,Vo),t("button",{type:"button",onClick:l=>It(e.name)},Io,8,So)])]))),128))])])])):x("",!0),t("div",Fo,[t("div",To,[t("div",$o,[t("div",Go,[t("div",Do,[r(I,{for:"note",value:"Upload Documents"}),r(Ht,{inputId:"document",inputName:"document",onFiles:Vt})])]),t("div",Uo,[t("div",No,[r(I,{for:"company_name",value:"Transport"}),r(y,{id:"gst",type:"text",modelValue:a(n).dispatch,"onUpdate:modelValue":c[4]||(c[4]=e=>a(n).dispatch=e)},null,8,["modelValue"])]),t("div",Mo,[r(I,{for:"company_name",value:"Dispatch"}),r(y,{id:"transport",type:"text",modelValue:a(n).transport,"onUpdate:modelValue":c[5]||(c[5]=e=>a(n).transport=e)},null,8,["modelValue"])]),t("div",qo,[r(I,{for:"eway_bill",value:"Eway Bill"}),r(y,{id:"eway_bill",type:"text",modelValue:a(n).eway_bill,"onUpdate:modelValue":c[6]||(c[6]=e=>a(n).eway_bill=e)},null,8,["modelValue"])])]),t("div",jo,[t("div",Ao,[r(I,{for:"company_name",value:"PO Number"}),r(y,{id:"gst",type:"text",modelValue:a(n).customer_po_number,"onUpdate:modelValue":c[7]||(c[7]=e=>a(n).customer_po_number=e)},null,8,["modelValue"])]),t("div",Eo,[r(I,{for:"company_name",value:"PO Date"}),r(y,{id:"customer_po_date",type:"date",modelValue:a(n).customer_po_date,"onUpdate:modelValue":c[8]||(c[8]=e=>a(n).customer_po_date=e)},null,8,["modelValue"])]),t("div",Bo,[r(I,{for:"due_days",value:"Due Days"}),r(y,{id:"due_days",type:"text",modelValue:a(n).due_days,"onUpdate:modelValue":c[9]||(c[9]=e=>a(n).due_days=e)},null,8,["modelValue"])])]),t("div",Lo,[t("div",Oo,[r(I,{for:"patient_name",value:"Patient Name"}),r(y,{id:"patient_name",type:"text",modelValue:a(n).patient_name,"onUpdate:modelValue":c[10]||(c[10]=e=>a(n).patient_name=e)},null,8,["modelValue"])]),t("div",zo,[r(I,{for:"cr_dr_note",value:"CR DR Note"}),r(y,{id:"cr_dr_note",type:"text",modelValue:a(n).cr_dr_note,"onUpdate:modelValue":c[11]||(c[11]=e=>a(n).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[r(I,{for:"note",value:"Note"}),r(Ot,{id:"note",type:"text",modelValue:a(n).note,"onUpdate:modelValue":c[12]||(c[12]=e=>a(n).note=e),onChange:c[13]||(c[13]=e=>a(n).validate("note"))},null,8,["modelValue"]),a(n).invalid("note")?(u(),qt(Lt,{key:0,class:"",message:a(n).errors.note},null,8,["message"])):x("",!0)])]),t("div",Ho,[t("div",Ro,[t("div",Qo,[Wo,t("p",Ko,h(U(st.value)),1)]),t("div",Xo,[Zo,t("div",Jo,[r(y,{id:"discount_before_tax",type:"text",modelValue:a(n).discount_before_tax,"onUpdate:modelValue":c[14]||(c[14]=e=>a(n).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",Yo,[tl,t("div",el,[r(y,{id:"overall_discount",type:"text",modelValue:a(n).overall_discount,"onUpdate:modelValue":c[15]||(c[15]=e=>a(n).overall_discount=e)},null,8,["modelValue"])])]),t("div",sl,[ol,t("p",ll,h(U(ot.value)),1)]),a(i).customers.gst_type=="IGST"?(u(),_("div",al,[nl,t("p",cl,h(U(D.value)),1)])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("div",il,[rl,t("p",dl,h(U(D.value/2)),1)])):x("",!0),a(i).customers.gst_type=="CGST/SGST"?(u(),_("div",ul,[_l,t("p",ml,h(U(D.value/2)),1)])):x("",!0),t("div",pl,[hl,t("p",xl,h(U(et.value)),1)])])])])]),t("div",yl,[t("div",gl,[r(Bt,{href:s.route("invoice.index")},{svg:P(()=>[vl]),_:1},8,["href"]),r(it,{disabled:a(n).processing},{default:P(()=>[T("Submit")]),_:1},8,["disabled"])])])],40,ee)]),r(z,{show:W.value,onClose:K},{default:P(()=>[t("div",fl,[wl,t("div",bl,[r(O,{onClick:K},{default:P(()=>[T(" Cancel ")]),_:1}),r(Z,{class:"ml-3",onClick:kt},{default:P(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(z,{show:H.value,onClose:E},{default:P(()=>[t("div",Vl,[Pl,t("div",kl,[r(O,{onClick:E},{default:P(()=>[T(" Cancel ")]),_:1}),r(Z,{class:"ml-3",onClick:vt},{default:P(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(z,{show:R.value,onClose:tt},{default:P(()=>[t("div",Sl,[Cl,t("div",Il,[r(O,{onClick:tt},{default:P(()=>[T(" Cancel ")]),_:1}),r(Z,{class:"ml-3",onClick:ft},{default:P(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(z,{show:X.value,onClose:nt,maxWidth:St.value},{default:P(()=>[t("div",Fl,[r(zt,{fileUrl:a(J)+at.value},null,8,["fileUrl"]),t("div",Tl,[r(O,{onClick:nt},{default:P(()=>[T(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Kl=Qt($l,[["__scopeId","data-v-273aff1c"]]);export{Kl as default};
