import{o as l,c as f,a as n,u as a,w as c,F as V,Z as x,b as t,d as h,n as b,e as m,f as r,g as z,T as C}from"./app-497d70e1.js";import{_ as $,a as B}from"./AdminLayout-f002e683.js";import{_ as d}from"./InputError-9708b76b.js";import{_ as u}from"./InputLabel-5f63a3d9.js";import{P as S}from"./PrimaryButton-8958b93e.js";import{_}from"./TextInput-affa926c.js";import{_ as g}from"./SearchableDropdown-1fe89ae6.js";import{u as w}from"./index-25f94e24.js";import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"animate-top h-screen"},T={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},U=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Bank",-1),A=["onSubmit"],O={class:"border-b border-gray-900/10 pb-12"},F={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},j={class:"sm:col-span-3"},E={class:"relative mt-2"},I={class:"sm:col-span-3"},P={class:"sm:col-span-3"},M={class:"sm:col-span-3"},Z={class:"sm:col-span-3"},q={class:"sm:col-span-3"},D={class:"relative mt-2"},G={class:"flex mt-6 items-center justify-between"},H={class:"ml-auto flex items-center justify-end gap-x-6"},J=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),K={key:0,class:"text-sm text-gray-600"},ne={__name:"Add",props:["organization","amounttype"],setup(p){const e=w("post","/bankinfo",{organization_id:"",bank_name:"",account_number:"",balance:"",amount_type:"",ifsc_code:""}),v=()=>{e.submit({preserveScroll:!0,onSuccess:()=>{},onError:i=>{i.account_number&&(e.errors.account_number="This account number already exists in the bank.")}})},y=(i,o)=>{e.organization_id=i,e.errors.organization_id=null},k=(i,o)=>{e.amount_type=i,e.errors.amount_type=null};return(i,o)=>(l(),f(V,null,[n(a(x),{title:"Bank Info"}),n($,null,{default:c(()=>[t("div",N,[t("div",T,[U,t("form",{onSubmit:h(v,["prevent"]),class:""},[t("div",O,[t("div",F,[t("div",j,[n(u,{for:"company_name",value:"Organization"}),t("div",E,[n(g,{options:p.organization,modelValue:a(e).organization_id,"onUpdate:modelValue":o[0]||(o[0]=s=>a(e).organization_id=s),onOnchange:y,class:b({"error rounded-md":a(e).errors.organization_id})},null,8,["options","modelValue","class"]),a(e).invalid("organization_id")?(l(),m(d,{key:0,message:a(e).errors.organization_id},null,8,["message"])):r("",!0)])]),t("div",I,[n(u,{for:"bank_name",value:"Bank Name"}),n(_,{id:"bank_name",type:"text",modelValue:a(e).bank_name,"onUpdate:modelValue":o[1]||(o[1]=s=>a(e).bank_name=s),autocomplete:"bank_name",onChange:o[2]||(o[2]=s=>a(e).validate("bank_name"))},null,8,["modelValue"]),a(e).invalid("bank_name")?(l(),m(d,{key:0,message:a(e).errors.bank_name},null,8,["message"])):r("",!0)]),t("div",P,[n(u,{for:"account_number",value:"Account Number"}),n(_,{id:"account_number",type:"text",modelValue:a(e).account_number,"onUpdate:modelValue":o[3]||(o[3]=s=>a(e).account_number=s),autocomplete:"account_number",onChange:o[4]||(o[4]=s=>a(e).validate("account_number"))},null,8,["modelValue"]),a(e).invalid("account_number")?(l(),m(d,{key:0,message:a(e).errors.account_number},null,8,["message"])):r("",!0)]),t("div",M,[n(u,{for:"ifsc_code",value:"IFSC Code"}),n(_,{id:"ifsc_code",type:"text",modelValue:a(e).ifsc_code,"onUpdate:modelValue":o[5]||(o[5]=s=>a(e).ifsc_code=s),autocomplete:"ifsc_code",onChange:o[6]||(o[6]=s=>a(e).validate("ifsc_code"))},null,8,["modelValue"]),a(e).invalid("ifsc_code")?(l(),m(d,{key:0,message:a(e).errors.ifsc_code},null,8,["message"])):r("",!0)]),t("div",Z,[n(u,{for:"balance",value:"Opening Balance"}),n(_,{id:"balance",type:"text",modelValue:a(e).balance,"onUpdate:modelValue":o[7]||(o[7]=s=>a(e).balance=s),autocomplete:"balance",onChange:o[8]||(o[8]=s=>a(e).validate("balance"))},null,8,["modelValue"]),a(e).invalid("balance")?(l(),m(d,{key:0,message:a(e).errors.balance},null,8,["message"])):r("",!0)]),t("div",q,[n(u,{for:"company_name",value:"Amount Type"}),t("div",D,[n(g,{options:p.amounttype,modelValue:a(e).amount_type,"onUpdate:modelValue":o[9]||(o[9]=s=>a(e).amount_type=s),onOnchange:k,class:b({"error rounded-md":a(e).errors.amount_type})},null,8,["options","modelValue","class"]),a(e).invalid("amount_type")?(l(),m(d,{key:0,message:a(e).errors.amount_type},null,8,["message"])):r("",!0)])])])]),t("div",G,[t("div",H,[n(B,{href:i.route("bankinfo.index")},{svg:c(()=>[J]),_:1},8,["href"]),n(S,{disabled:a(e).processing},{default:c(()=>[z("Save")]),_:1},8,["disabled"]),n(C,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[a(e).recentlySuccessful?(l(),f("p",K,"Saved.")):r("",!0)]),_:1})])])],40,A)])])]),_:1})],64))}};export{ne as default};
