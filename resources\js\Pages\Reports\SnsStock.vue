<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'organization', 'search', 'company', 'organizationId', 'companyId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('sns-stock.report', {
    organization_id: props.organizationId,
    company_id: props.companyId,
    from_date: props.from_date,
    to_date: props.to_date,
});

const activeLink = usePage().props.data.links.find(link => link.active === true);

// const form = useForm({
// });

const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const companyName = ref('ALL COMPANY');
const from_date = ref('');
const to_date = ref('');
const searchValue = ref('');

watch([organizationId, companyId, from_date, to_date ], () => {
    updateParams({
        organization_id: organizationId.value,
        conpany_id: companyId.value,
        from_date: from_date.value,
        to_date: to_date.value
    });
});

const columns = [
    { field: 'item_code',           label: 'PRODUCT CODE',       sortable: true },
    { field: 'name',                label: 'PRODUCT NAME',       sortable: true },
    { field: 'company.name',        label: 'COMPANY',            sortable: true },
    { field: 'min_qty',             label: 'STOCK',              sortable: true },
];



const handleSearchChange = (value, organizationId, companyId, from_date, to_date) => {
    searchValue.value = value;
    form.get(route('sns-stock.report',{search:value , organization_id: organizationId, company_id: companyId, from_date:from_date , to_date:to_date}),  {
        preserveState: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, companyId.value , from_date.value, to_date.value);
};

const setCompany = (id, name) => {
    companyId.value = id;
    companyName.value = name;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, from_date.value, to_date.value);
};

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};

const exportXls = () => {
    let organizationName = '';

    switch (organizationId.value) {
        case 1:
            organizationName = 'MC';
            break;
        case 2:
            organizationName = 'HC';
            break;
        case 3:
            organizationName = 'NOX';
            break;
        default:
            organizationName = 'All_Organizations';
            break;
    }

    const cleanedCompanyName = companyName.value.replace(/\s+/g, '_');
    const fileName = `SNS_Stock_Report_${organizationName}_${cleanedCompanyName}`;
    const params = {
        organization_id: organizationId.value,
        company_id: companyId.value,
        from_date: from_date.value,
        to_date: to_date.value,
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-sns-stock-report?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${fileName}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const truncateCompanyName = (name) => {
    return name && name.length > 27 ? name.substring(0, 27) + '...' : name;
};

const handleStartDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, companyId.value, from_date.value, to_date.value);
};

const handleToDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, companyId.value, from_date.value, to_date.value);
};

const calculateTotalStock = (salesProducts) => {
    if (salesProducts && salesProducts.length > 0) {
        return salesProducts.reduce((total, product) => total + (product.receive_qty - product.sell_qty), 0);
    } else {
        return '-';
    }
};

</script>

<template>
    <Head title="Stock Report"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="items-start">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">SNS STOCK REPORT</h1>
            </div>
            <div class="flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, companyId, from_date, to_date)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                            <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                </div>
                <div class="flex ml-6">
                    <CreateButton :href="route('reports')">
                            Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="inline-flex items-center space-x-4 justify-end w-full ">
                    <button @click="exportXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Company Name" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="company"
                          v-model="companyId"
                        @onchange="setCompany"
                        />
                    </div>
                </div>
                 <div class="sm:col-span-3">
                    <InputLabel for="date" value="From Date" />
                    <input
                        v-model="from_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                            @change="handleStartDate"
                        :class="{ 'error rounded-md': form.errors.from_date }"
                    />
                 </div>
                 <div class="sm:col-span-3">
                    <InputLabel for="date" value="To Date" />
                    <input
                        v-model="to_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleToDate"
                        :class="{ 'error rounded-md': form.errors.to_date }"
                    />
                 </div>
            </div>
        </div>

        <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(userData, index) in data.data" :key="userData.id">
                                <td scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ userData.item_code ?? '-' }}
                                </td>
                                <th scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ userData.name ?? '-' }}
                                </th>
                                <td class="px-4 py-2.5 min-w-52">
                                    {{ userData.company.name ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ calculateTotalStock(userData.sales_products) ?? '-' ?? '-' }}
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
    </AdminLayout>

</template>
