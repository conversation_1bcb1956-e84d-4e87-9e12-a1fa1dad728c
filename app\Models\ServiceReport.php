<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceReport extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'service_reports';

    protected static $logName = 'Service Report';

    public function getLogDescription(string $event): string
    {
        $customerName = optional($this->customers)->customer_name ?? 'Unknown Customer';
        return "Service report has been {$event} for <strong>{$customerName}</strong> by";
    }

    protected static $logAttributes = [
        'customer_id',
        'company_id',
        'product_code',
        'product_name',
        'serial_no',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'customer_id',
        'company_id',
        'product_code',
        'product_name',
        'serial_no',
        'created_by',
        'updated_by'
    ];

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function company(){
        return $this->belongsTo(Company::class,'company_id','id');
    }

    public function reportDetail()
    {
        return $this->hasMany(ServiceReportDetail::class);
    }
}
