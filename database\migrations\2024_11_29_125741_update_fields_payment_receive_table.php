<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_receive', function (Blueprint $table) {
            $table->json('invoice_data')->nullable()->after('invoice_id');
        });

        Schema::table('payment_paid', function (Blueprint $table) {
            $table->json('invoice_data')->nullable()->after('purchase_order_receive_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
