<script setup>
import { onBeforeMount, ref, watch, computed  } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SvgLink from '@/Components/ActionLink.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import Modal from '@/Components/Modal.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import FileViewer from '@/Components/FileViewer.vue';
import { Head, usePage  } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['companies', 'category', 'type', 'po_number', 'organization', 'filepath', 'products', 'salesuser']);
const file = usePage().props.filepath.view;

const finalProducts = ref([]);
const poData = usePage().props.data[0];

const filterProducts = props.products.filter(product => product.company_id === poData.company_id);
finalProducts.value =  filterProducts;

const gst_type = ref(poData.company.gst_type);

const form = useForm('post', '/companypo', {
    note: poData.note,
    po_date:  poData.date,
    selectedProductItem: [],
    company_id: poData.company_id,
    sales_user_id: poData.sales_user_id,
    total_amount: poData.total_amount,
    category: poData.category,
    type: poData.type,
    cgst: poData.cgst,
    sgst: poData.sgst,
    igst: poData.igst,
    total_gst: poData.total_gst,
    sub_total: poData.sub_total,
    po_number: poData.po_number,
    purchase_order_id:  poData.id,
    total_discount: poData.total_discount,
    overall_discount: poData.overall_discount,
    document: poData.documents,
    organization_id: poData.organization_id,
    sales_order_no: poData.sales_order_no,
    sales_order_date: poData.sales_order_date,
});

const submit = () => {
    form.total_amount = totalAmount.value;
    form.total_discount = totalDiscountAmount.value;
    form.sub_total = totalPrice.value;
    form.cgst = (gst_type.value =='CGST/SGST') ? totalGstAmount.value/2 : '0';
    form.sgst = (gst_type.value =='CGST/SGST') ? totalGstAmount.value/2 : '0';
    form.igst = (gst_type.value =='IGST') ? totalGstAmount.value : '0';
    form.total_gst = totalGstAmount.value;
    form.selectedProductItem = selectedProductItem.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setOrganization = (id, name) => {
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setSalesUser = (id, name) => {
    form.sales_user_id = id;
};

const selectedProductItem = ref([
    {
        product_id: '',
        editmode: '',
        item_code: '',
        hsn_code: '',
        pkg_of_qty: '',
        qty: '',
        receive_qty: '',
        price: '',
        gst: '',
        sgst: '',
        discount: '',
        discount_amount: '',
        total_price: '',
        gst_amount: '',
        total_gst_amount: '',
        total_amount: '',
        purchase_order_detail_id: '',
        description: ''
    }
])

onBeforeMount(() => {
    selectedProductItem.value = poData.purchase_order_detail.map(detail => ({
        product_id: detail.product_id,
        editmode: 'editMode',
        receive_qty: detail.receive_qty,
        purchase_order_detail_id: detail.id,
        hsn_code: detail.product.hsn_code,
        item_code: detail.product.item_code,
        pkg_of_qty: detail.pkg_of_qty,
        qty: detail.qty,
        description: detail.description,
        price: parseFloat(detail.price).toFixed(2),
        total_price: parseFloat(detail.total_price).toFixed(2),
        gst: parseFloat(detail.gst).toFixed(2),
        sgst: parseFloat(detail.gst/2).toFixed(2),
        gst_amount: parseFloat(detail.gst_amount).toFixed(2),
        total_gst_amount: parseFloat(detail.total_gst_amount).toFixed(2),
        total_amount: parseFloat(detail.total_amount).toFixed(2),
        discount:  parseFloat(detail.discount).toFixed(2),
        discount_amount: parseFloat(detail.discount_amount).toFixed(2) ?? '0' ,
    }));
});

const setProductInfo = (id, name, index, product) => {
    const selectedProduct = finalProducts.value.find(product => product.id === id);
    if(selectedProduct) {
        selectedProductItem.value[index].product_id = selectedProduct.id;
        selectedProductItem.value[index].price = parseFloat(selectedProduct.price).toFixed(2);
        selectedProductItem.value[index].hsn_code = selectedProduct.hsn_code;
        selectedProductItem.value[index].item_code = selectedProduct.item_code;
        selectedProductItem.value[index].gst = parseFloat(selectedProduct.gst).toFixed(2);
        selectedProductItem.value[index].sgst = parseFloat(selectedProduct.gst/2).toFixed(2);
        selectedProductItem.value[index].discount = '0.00';
        form.errors[`selectedProductItem.${index}.product_id`] = null;
        form.errors[`selectedProductItem.${index}.price`] = null;
        updateAmount(product, index);
    }
};

const addProduct = () => {
    selectedProductItem.value.push({
        product_id: '',
        item_code: '',
        receive_qty: '',
        hsn_code: '',
        pkg_of_qty: '',
        qty: '',
        price: '',
        gst: '',
        sgst: '',
        total_price: '',
        gst_amount: '',
        total_gst_amount: '',
        total_amount: '',
        purchase_order_detail_id: '',
        description: ''
    });
};

const productDeleteModal = ref(false);
const selectedProductId = ref(null);
const selectedIndexId = ref(null);

const closeProductModal = () => {
    productDeleteModal.value = false;
};

const deleteProduct = () => {
    form.get(route('removeproduct',{id:selectedProductId.value, model:'PurchaseOrderDetail'}), {
        onSuccess: () => {
        closeProductModal()
        selectedProductItem.value.splice(index, 1);
        }
    });
};

const removeProduct = (index, id) => {
    if(id !== undefined && id != ''){
        selectedProductId.value = id;
        selectedIndexId.value = index;
        productDeleteModal.value = true;
    } else {
        selectedProductItem.value.splice(index, 1);
    }
};

const calculateAmount = (product, index) => {
    // const price = parseFloat(product.price);
    // const gst = (gst_type.value =='IGST') ? parseFloat(product.gst) : parseFloat(product.sgst*2);
    // const qty = parseFloat(product.qty);
    // const amount = price * qty * (1 + gst / 100);
    // const total_price = price * qty;
    // const gst_amount = price * 1 * (gst / 100);
    // const total_gst_amount = price * qty * (gst / 100);
    // product.total_price      = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    // product.gst_amount       = isNaN(gst_amount) ? '' : parseFloat(gst_amount).toFixed(2);
    // product.total_gst_amount = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    // return isNaN(amount) ? '' :  parseFloat(amount).toFixed(2);

    const price = parseFloat(product.price);
    const discount = parseFloat(product.discount) || 0;
    const gst = (gst_type.value =='IGST') ? parseFloat(product.gst) : parseFloat(product.sgst*2);
    const qty = parseFloat(product.qty);
    let amount = 0;
    let totalAmount = 0;
    if(discount > 0){
      amount = price * qty;
    } else {
      amount = price * qty * (1 + gst / 100);
    }
    const discountAmount = (amount * (discount / 100)) || 0;
    const gst_amount = price * 1 * (gst / 100);
    const total_gst_amount = ((price * qty) - discountAmount) * (gst / 100);
     if(discount > 0){
      totalAmount = amount - discountAmount + total_gst_amount;
    } else {
      totalAmount = amount - discountAmount;
    }
    const total_price = price * qty;
    product.total_price      = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    product.gst_amount       = isNaN(gst_amount) ? '' : parseFloat(gst_amount).toFixed(2);
    product.total_gst_amount       = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    product.discount_amount = isNaN(discountAmount) ? '' : parseFloat(discountAmount).toFixed(2);
    return isNaN(totalAmount) ? '' :  parseFloat(totalAmount).toFixed(2);
};

const updateAmount = (product, index) => {
    product.total_amount = calculateAmount(product, index);
};

const totalAmount = computed(() => {
    const totalAmount = Math.round(selectedProductItem.value.reduce((total, product) => {
        return total + (product.total_amount ? parseFloat(product.total_amount) : 0);
    }, 0));
    const overallDiscountAmount = form.overall_discount ? parseFloat(form.overall_discount) : 0;
    return totalAmount - overallDiscountAmount;
});

const totalGstAmount = computed(() => {
    return selectedProductItem.value.reduce((total, product) => {
        return total +  (product.total_gst_amount ? parseFloat(product.total_gst_amount) : 0);
    }, 0);
});

const totalPrice = computed(() => {
    return selectedProductItem.value.reduce((total, product) => {
        return total +  (product.total_price ? parseFloat(product.total_price) : 0);
    }, 0);
});

const totalDiscountAmount = computed(() => {
    const totalDiscountFromProducts = selectedProductItem.value.reduce((total, product) => {
        return total + (product.discount_amount ? parseFloat(product.discount_amount) : 0);
    }, 0);
    const overallDiscountAmount = form.overall_discount ? parseFloat(form.overall_discount) : 0;
    return totalDiscountFromProducts + overallDiscountAmount;
});

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const handleDocument = (file) => {
    form.document = file;
};

const documentDeleteModal = ref(false);
const selectedDocumentId = ref(null);

const openDeleteModal = (id) => {
  selectedDocumentId.value = id;
  documentDeleteModal.value = true;
};

const deleteDocument = () => {
    form.get(route('removedocument',{id:selectedDocumentId.value, name:'purchaseOrderDocument'}), {
        onSuccess: () => {
        closeDocumentModal()
        }
    });
};

const closeDocumentModal = () => {
    documentDeleteModal.value = false;
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
  selectedDocument.value = name;
  documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin+ file+ name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const selectedOptionRemove = (index) => {
    // return index.filter(option => !selectedProductItem.value.some(item => item.product_id === option.id));
    return index;
};

const setCategory = (id, name) => {
    form.category = id;
    form.errors.category = null;
};

const setType = (id, name) => {
    form.type = id;
    form.errors.type = null;
};

</script>

<template>
    <Head title="Comapany PO" />
    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Edit Purchase Order</h1>
                </div>
                <div class="w-auto">
                    <div class="flex space-x-6 items-center">
                        <p class="text-sm font-semibold text-gray-900">{{  poData.company.name ?? '-' }}</p>
                        <div>
                            <div class="flex space-x-2 items-center">
                                <span class="text-sm font-semibold text-gray-900 leading-6">PO Number:</span>
                                <span class="text-sm font-semibold text-gray-900 leading-6">{{ poData.po_number }}</span>
                            </div>
                            <div class="flex space-x-2 items-center">
                                <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Date :</span>
                                <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"  v-model="form.po_date"   @change="form.validate('date')"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <form @submit.prevent="submit" class="mt-6 space-y-6">
                <div class="shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
                    <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-4">
                            <InputLabel for="sales_order_no" value="Sales Order No" />
                            <TextInput
                                id="sales_order_no"
                                type="text"
                                v-model="form.sales_order_no"
                                @change="form.validate('sales_order_no')"
                                maxLength="30"
                            />
                            <InputError class="" :message="form.errors.sales_order_no" />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="sales_order_date" value="Sales Order Date" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"  @change="form.validate('sales_order_date')"
                            v-model="form.sales_order_date"
                            />
                            <InputError v-if="form.invalid('sales_order_date')" class="" :message="form.errors.sales_order_date" />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="organization_id" value="Organization" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="organization"
                                v-model="form.organization_id"
                                @onchange="setOrganization"
                                :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="category" value="Category" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="category"
                                v-model="form.category"
                                @onchange="setCategory"
                                :class="{ 'error rounded-md': form.errors.category }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="type" value="Purchase Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="type"
                                v-model="form.type"
                                @onchange="setType"
                                :class="{ 'error rounded-md': form.errors.type }"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full">
                    <div class="overflow-x-auto w-full">
                      <table class="overflow-x-auto divide-y divide-gray-300" style="margin-bottom: 160px;">
                            <thead>
                                <tr>
                            <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Product</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">HSN</th>
                            <!--<th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Description</th> -->
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Pkg Of Qty</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">QTY</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Price (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Rec. QTY</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total Price (₹)</th>
                            <th v-if="gst_type =='IGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">IGST (%)</th>
                            <th v-if="gst_type =='IGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">IGST (₹)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">CGST (%)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">SGST (%)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text- text-sm font-semibold text-gray-900">Total GST (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Discount (%)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Discount (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total Amount (₹)</th>
                            <th scope="col"></th>
                        </tr>
                         </thead>
                            <tbody class="divide-y divide-gray-300 bg-white">
                                <tr v-for="(product, index)  in selectedProductItem" :key="index">
                                    <td class="whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80">
                                        <div class="relative mt-2">
                                            <SearchableDropdown
                                                :options="finalProducts"
                                                v-model="product.product_id"
                                                @onchange="(id, name) => setProductInfo(id, name, index, product)"
                                                @change="form.validate('product_id')"
                                                :class="{ 'error rounded-md': form.errors[`selectedProductItem.${index}.product_id`] }"
                                                :editMode="product.editmode"
                                                />
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">{{ product.hsn_code ?? '-' }}</td>
<!--                                     <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40">
                                        <TextInput
                                            id="description"
                                            type="text"
                                            v-model="product.description"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.description')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.description`] }"
                                        />
                                    </td>-->

                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                        <TextInput
                                            id="pkg_of_qty"
                                            type="text"
                                            :numeric="true"
                                            v-model="product.pkg_of_qty"
                                        />
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                        <TextInput
                                            id="qty"
                                            type="text"
                                            :numeric="true"
                                            v-model="product.qty"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.qty')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.qty`] }"
                                        />
                                        <p v-if="form.errors[`selectedProductItem.${index}.qty1`]" class="text-red-500 text-xs">
                                            {{ form.errors[`selectedProductItem.${index}.qty1`] }}
                                        </p>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32">
                                        <TextInput
                                            id="price"
                                            type="text"
                                            v-model="product.price"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.price')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.price`] }"
                                        />
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"> {{ product.receive_qty }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"> {{ product.total_price }}</td>
                                    <td  v-if="gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                        <TextInput
                                            id="gst"
                                            type="text"
                                            v-model="product.gst"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.gst')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                        />
                                    </td>
                                    <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                        <TextInput
                                            id="gst"
                                            type="text"
                                            v-model="product.sgst"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.gst')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                        />
                                    </td>
                                    <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                        <TextInput
                                            id="gst"
                                            type="text"
                                            v-model="product.sgst"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.gst')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                        />
                                    </td>
                                    <td  v-if="gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">{{ product.total_gst_amount }}</td>
                                    <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">{{ product.total_gst_amount }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">
                                        <TextInput
                                            id="discount"
                                            type="text"
                                            v-model="product.discount"
                                            @input="updateAmount(product, index)"
                                            @change="clearError('selectedProductItem.' + index + '.discount')"
                                            :class="{ 'error': form.errors[`selectedProductItem.${index}.discount`] }"
                                        />
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"> {{ product.discount_amount }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 flex space-x-2 min-w-36">
                                        <div class="px-3 py-3 text-sm text-gray-900">
                                            {{ product.total_amount }}
                                        </div>
                                    </td>
                                    <td>
                                        <button type="button" v-if="product.receive_qty == 0" class="mt-1" @click="removeProduct(index, product.purchase_order_detail_id)">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                      </table>
                      </div>
                    <div class="flex items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <PrimaryButton @click="addProduct" type="button">Add Product</PrimaryButton>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="poData.documents && (poData.documents.length > 0)">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">UPLOADED DOCUMENT</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-for="(files, index) in poData.documents" :key="poData.id" class="">
                                <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ files.orignal_name }}
                                </td>
                                <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                    <button type="button" @click="openDeleteModal(files.id)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                            />
                                        </svg>
                                    </button>
                                    <button type="button"  @click="openPreviewModal(files.name)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>                            </button>
                                    <button type="button"  @click="downloadDocument(files.name)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path></svg>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
                    <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-3 space-y-2">
                            <div class="flex space-x-4">
                                <div class="w-full">
                                    <InputLabel for="note" value="Upload Documents" />
                                    <MultipleFileUpload
                                        inputId="document"
                                        inputName="document"
                                        :uploadedFiles="form.document"
                                        @files="handleDocument"
                                    />
                                </div>
                                <div class="w-full">
                                    <InputLabel for="company_name" value="Person Name" />
                                    <div class="relative mt-2">
                                        <SearchableDropdown :options="salesuser"
                                        v-model="form.sales_user_id"
                                        @onchange="setSalesUser"
                                        :class="{ 'error rounded-md': form.errors.sales_user_id }"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    v-model="form.note"
                                    @change="form.validate('note')"
                                />
                                <InputError  v-if="form.invalid('note')" class="" :message="form.errors.note" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                                <div class="inline-flex items-center justify-end w-full space-x-3">
                                    <p class="text-sm font-semibold text-gray-700">Sub Total (₹):</p>
                                    <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                                </div>
                                <div class="inline-flex items-center justify-end w-full space-x-3">
                                    <p class="text-sm font-semibold text-gray-700 mt-2">Overall Discount (₹):</p>
                                    <div class="w-40">
                                        <TextInput
                                        id="overall_discount"
                                        type="text"
                                        v-model="form.overall_discount"
                                    />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-end w-full space-x-3">
                                    <p class="text-sm font-semibold text-gray-700">Total Discount Amount (₹):</p>
                                    <p class="text-base font-semibold text-gray-900 w-w-32">{{ formatAmount(totalDiscountAmount) }}</p>
                                </div>
                                <div class="inline-flex items-center justify-end w-full space-x-3">
                                    <p class="text-sm font-semibold text-gray-700">Total GST (₹):</p>
                                    <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                                </div>
                                <div class="inline-flex items-center justify-end w-full space-x-3">
                                    <p class="text-sm font-semibold text-gray-700">Total Amount (₹):</p>
                                    <p class="text-base font-semibold text-gray-900 w-32">{{  formatAmount(totalAmount) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="flex items-center justify-between">
                <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('companypo.index')">
                        <template #svg>
                            <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                Cancel
                            </button>
                        </template>
                    </SvgLink>
                    <PrimaryButton :disabled="form.processing">Submit</PrimaryButton>
                </div>
            </div>
            </form>
        </div>
        <Modal :show="productDeleteModal" @close="closeProductModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this product?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeProductModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteProduct"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="documentDeleteModal" @close="closeDocumentModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this document?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeDocumentModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteDocument"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                 <FileViewer :fileUrl="file+ selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal"> Cancel </SecondaryButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>

<style scoped>
.error {
  border: 1px solid red;
}
</style>
