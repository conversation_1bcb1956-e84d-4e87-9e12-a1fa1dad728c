<?php

namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Customer;
use App\Models\Organization;
use App\Models\CustomerTransaction;
use App\Models\BankInfo;
use App\Models\PurchaseTransaction;
use App\Models\Company;
use App\Http\Requests\CustomerStoreRequest;
use App\Exports\CustomerTransactionExports;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\PaymentReceive;
use App\Models\BankTransaction;
use App\Models\CustomerCredit;
use App\Http\Requests\paymentRequest;
use Config;

class CustomerController extends Controller
{
    use QueryTrait;

    /**
     * Format a number according to the Indian Rupees format (e.g., 1,00,000.00)
     *
     * @param float $amount The amount to format
     * @return string The formatted amount
     */

    public function __construct()
    {
        $this->middleware('permission:List Customer')->only(['index']);
        $this->middleware('permission:Create Customer')->only(['create', 'store']);
        $this->middleware('permission:Edit Customer')->only(['edit', 'update']);
        $this->middleware('permission:Delete Customer')->only('destroy');
        $this->middleware('permission:Customer Activation')->only('activation');
        $this->middleware('permission:Customer Transaction')->only('customerTransaction');
    }

    public function index(Request $request)
    {
        $query = Customer::with('credit');

        $query->withSum(['transactions as credit_sum' => function ($q) {
            $q->where('payment_type', 'cr'  );
        }], 'amount');

        $query->withSum(['transactions as debit_sum' => function ($q) {
            $q->where('payment_type', 'dr');
        }], 'amount');

        $searchableFields = ['customer_name', 'contact_no', 'email', 'city', 'type'];
        $this->searchAndSort($query, $request, $searchableFields);
        // $data = $this->getResult($query);
        $data = $query->orderByRaw('customer_name')->paginate(20);

        $partyIds = $data->pluck('party_id')->filter()->unique()->toArray();
        $companiesByParty = Company::whereIn('party_id', $partyIds)->get()->groupBy('party_id');
        $companyIds = $companiesByParty->flatten()->pluck('id')->unique();

        $companyCreditSums = PurchaseTransaction::selectRaw('company_id, SUM(amount) as credit_sum')
            ->whereIn('company_id', $companyIds)
            ->where('payment_type', 'cr')
            ->groupBy('company_id')
            ->pluck('credit_sum', 'company_id');

        $companyDebitSums = PurchaseTransaction::selectRaw('company_id, SUM(amount) as debit_sum')
            ->whereIn('company_id', $companyIds)
            ->where('payment_type', 'dr')
            ->groupBy('company_id')
            ->pluck('debit_sum', 'company_id');

        $data->getCollection()->transform(function ($customer) use ( $companiesByParty, $companyCreditSums, $companyDebitSums){
            $credit = $customer->credit_sum ?? 0;
            $debit = $customer->debit_sum ?? 0;
            $purchaseCr = 0;
            $purchaseDr = 0;

            if ($customer->party_id && isset($companiesByParty[$customer->party_id])) {
                foreach ($companiesByParty[$customer->party_id] as $company) {
                    $purchaseCr += $companyCreditSums[$company->id] ?? 0;
                    $purchaseDr += $companyDebitSums[$company->id] ?? 0;
                }
            }

            $balance = ($purchaseCr - $purchaseDr ) + ($credit - $debit);
            $prefix = $balance >= 0 ? 'cr' : 'dr';
            $customer->balance = $this->formatIndianRupees(abs($balance)) . ' ' . $prefix;
            $customer->balance_value = $balance;
            return $customer;
        });

        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $organization  = Organization::select('id', 'name')->get();
        $permissions = [
            'canCreateCustomer'      => auth()->user()->can('Create Customer'),
            'canEditCustomer'        => auth()->user()->can('Edit Customer'),
            'canDeleteCustomer'      => auth()->user()->can('Delete Customer'),
            'canTransactionCustomer' => auth()->user()->can('Customer Transaction'),
        ];

        return Inertia::render('Customer/List', compact('data', 'permissions', 'bankinfo', 'organization', 'paymentType'));
    }

    public function create()
    {
        $types = Config::get('constants.occupationType');
        $customer_type = Config::get('constants.customerType');
        $gst_type = Config::get('constants.gstType');
        $is_organization = Config::get('constants.isOrganization');
        $organization  = Organization::select('id', 'name')->get();
        return Inertia::render('Customer/Add', compact('types', 'customer_type', 'gst_type', 'is_organization', 'organization'));
    }


    public function store(CustomerStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            Customer::create($data);
            DB::commit();
            return Redirect::to('/customers')->with('success','Customer Add Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Customer::find($id);
        $types = Config::get('constants.occupationType');
        $customer_type = Config::get('constants.customerType');
        $gst_type = Config::get('constants.gstType');
        $is_organization = Config::get('constants.isOrganization');
        $organization  = Organization::select('id', 'name')->get();
        return Inertia::render('Customer/Edit', compact('data', 'types', 'customer_type', 'gst_type', 'is_organization', 'organization'));
    }

    public function update(CustomerStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();
            $user = Customer::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to('/customers')->with('success','Customer Info Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $customer = Customer::findOrFail($id);
            if ($customer->invoices()->count() > 0 || $customer->challans()->count() > 0 || $customer->quotations()->count() > 0) {
                return Redirect::to('/customers')->with('error', 'Customer cannot be deleted');
            }
            $customer->delete();
            DB::commit();
            return Redirect::to('/customers')->with('success','Customer Info Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/customers')->with('error', $e->getMessage());
        }
    }


    public function customerCredit($id, Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $organizationId = $request->input('organization_id');
        $query = CustomerCredit::with('paymentreceive.bankInfo', 'customer', 'creditDetail.invoice')->where('customer_id', $id);
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        $data = $query->paginate(20);
        $customer = Customer::find($id);
        $customerId = $id;
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $organizations  = Organization::select('id', 'name')->get();
        return Inertia::render('Customer/Credit', compact('data','organization', 'customer', 'customerId', 'bankinfo', 'organizations', 'paymentType'));
    }

    public function activation(Request $request)
    {
        DB::beginTransaction();
        try {
            $customer = Customer::findOrFail($request->id);
            $customer->status = $request->status;
            $customer->save();
            DB::commit();
            return Redirect::to('/customers')->with('success','Customer Status Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/customers')->with('error', $e->getMessage());
        }
    }

    public function advancePayment(paymentRequest $request)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {
            $data = $data['data'];
            if(isset($data['id'])){
                $creditInfo = CustomerCredit::find($data['id']);
                $data['updated_by'] = Auth::user()->id;
                $updatePaymentReceive = PaymentReceive::findOrFail($creditInfo->payment_receive_id);
                $updatePaymentReceive->update($data);
                $bankTransactionUpdate = BankTransaction::where(['entity_type' => 'payment_receive', 'entity_id' => $creditInfo->payment_receive_id])->first();
                $data['unused_amount'] = $data['amount'];
                if($data['payment_type'] == 'check'){
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Advance Payment';
                    $bankTransactionUpdate->update($data);
                    $creditInfo->update($data);
                } else if($data['payment_type'] == 'NEFT'){
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'NEFT'. ' ' .'Advance Payment';
                    $bankTransactionUpdate->update($data);
                    $creditInfo->update($data);
                } else if($data['payment_type'] == 'cash'){
                    $data['payment_type'] = 'cr';
                    $data['note'] = $data['note'].' '.'Advance Payment';
                    $creditInfo->update($data);
                }
                DB::commit();
                return Redirect::back()->with('success','Payment Updated Successfully');
                dd('update');
            } else {
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                $receivePayment = PaymentReceive::create($data);
                $data['entity_id'] = $receivePayment->id;
                $data['payment_receive_id'] = $receivePayment->id;
                $data['entity_type'] = 'payment_receive';
                $data['unused_amount'] = $data['amount'];

                if($data['payment_type'] == 'check'){
                $data['payment_type'] = 'cr';
                $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Advance Payment';
                $bankTransaction = BankTransaction::create($data);
                CustomerCredit::create($data);
                } else if($data['payment_type'] == 'NEFT'){
                $data['payment_type'] = 'cr';
                $data['note'] = 'NEFT'. ' ' .'Advance Payment';
                $bankTransaction = BankTransaction::create($data);
                CustomerCredit::create($data);
                } else if($data['payment_type'] == 'cash'){
                $data['payment_type'] = 'cr';
                $data['note'] = $data['note'].' '.'Advance Payment';
                CustomerCredit::create($data);
                }
                CustomerTransaction::create($data);
                DB::commit();
                return Redirect::to('/customers')->with('success','Payment Received Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/customers')->with('error', $e->getMessage());
        }
    }

    public function customerTransaction($id, Request $request)
    {
        $organization = Organization::select('id', 'name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);

        $organizationId = $request->input('organization_id');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $customer = Customer::findOrFail($id);
        $customerId = $id;
        $partyId = $customer->party_id;

        // Sales transactions (CustomerTransaction)
        $salesQuery = CustomerTransaction::with('customer', 'organization', 'paymentReceive')
            ->where('customer_id', $id);

        // Customer Credit
        $creditData = CustomerCredit::where('customer_id', $id)
            ->where('unused_amount', '>', 0);

        if ($organizationId) {
            $salesQuery->where('organization_id', $organizationId);
            $creditData->where('organization_id', $organizationId);
        }

        $sales = $salesQuery->get();

        // Check if party_id exists and fetch related purchase transactions
        $purchases = collect();

        if ($partyId) {
            $companyIds = Company::where('party_id', $partyId)->pluck('id');

            $purchaseQuery = PurchaseTransaction::with('company', 'organization')
                ->whereIn('company_id', $companyIds);

            if($organizationId) {
                $purchaseQuery->where('organization_id', $organizationId);
            }
            $purchases = $purchaseQuery->get();
        }
        // Merge both collections
        $data = $sales->concat($purchases)->sortBy('date')->values();
        $creditdata = $creditData->get()->toArray();

        return Inertia::render('Customer/Transaction', compact(
            'data',
            'organization',
            'organizationId',
            'customer',
            'customerId',
            'creditdata'
        ));
    }


    public function exportCustomerTransaction(Request $request)

    {
        $validated = $request->validate([
            'customer_id' => 'required|integer',
            'organization_id' => 'nullable|integer',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
        ]);

        $customerId = $validated['customer_id'];
        $organizationId = $validated['organization_id'] ?? null;
        $fromDate = $validated['from_date'] ?? null;
        $toDate = $validated['to_date'] ?? null;

        $customer = Customer::findOrFail($customerId);
        $partyId = $customer->party_id;

        $salesQuery  = CustomerTransaction::with('customer', 'organization')->where('customer_id', $customerId);

        if ($organizationId) {
            $salesQuery->where('organization_id', $organizationId);
        }

        if ($fromDate && $toDate) {
            $salesQuery->whereBetween('date', [$fromDate, $toDate]);
        }

        // $transactions = $query->get();
        $sales = $salesQuery->get();

        $purchases = collect();
        if ($partyId) {
            $companyIds = Company::where('party_id', $partyId)->pluck('id');

            $purchaseQuery = PurchaseTransaction::with('company', 'organization')
                ->whereIn('company_id', $companyIds);

            if ($organizationId) {
                $purchaseQuery->where('organization_id', $organizationId);
            }

            if ($fromDate && $toDate) {
                $purchaseQuery->whereBetween('date', [$fromDate, $toDate]);
            }
            $purchases = $purchaseQuery->get();
        }

        $transactions = $sales->concat($purchases)->sortBy('date')->values();

        $organizationName = $organizationId
            ? Organization::find($organizationId)->name
            : 'All Organizations';

        $customerName = $customer->customer_name ?? 'Unknown Customer';
        // $customerName = Customer::find($customerId)->customer_name ?? 'Unknown Customer';

        return Excel::download(
            new CustomerTransactionExports($transactions, $organizationName, $fromDate, $toDate, $customerName),
            'customer_transactions.xlsx'
        );
    }
}
