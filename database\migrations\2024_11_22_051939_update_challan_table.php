<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challan', function (Blueprint $table) {
            $table->integer('purchase_order_id')->nullable()->after('transport');
            $table->integer('purchase_order_receive_id')->nullable()->after('purchase_order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('challan', function (Blueprint $table) {
            $table->dropColumn('purchase_order_id');
            $table->dropColumn('purchase_order_receive_id');
        });
    }
};
