<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BaseModel extends Model
{
    use SoftDeletes, ActivityTrait;

    protected static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            static::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                $event = static::getUpdateEvent($model);
                static::handleLogEntry($model, $event);
            }
        });

        static::deleted(function ($model) {
            static::handleLogEntry($model, 'deleted');
        });
    }

    protected static function getUpdateEvent($model)
    {
        // Override in child models if a custom event name is needed based on certain conditions.
        return 'updated';
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = static::getLogName($model, $event);

        $modelClones = static::getModelClonesForLogging($model);

        foreach ($modelClones as $modelClone) {
            static::addCustomLogEntry($modelClone, $event, $logName, static::getMergedAttributes($model, $event));
        }
    }

    protected static function getLogName($model, $event)
    {
        // Override in child models for custom log names
        return static::$logName ?? class_basename($model) . " has been $event";
    }

    protected static function getModelClonesForLogging($model)
    {
        // Override in child models to clone and attach related models for logging if needed
        return [$model];
    }

    protected static function getMergedAttributes($model, $event)
    {
        // Override in child models to merge specific attributes for logging if needed
        return [];
    }

}
