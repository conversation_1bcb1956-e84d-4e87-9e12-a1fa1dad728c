<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('proforma_invoice', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('proforma_invoice', function (Blueprint $table) {
            $table->enum('status',['Open','Close'])->after('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
