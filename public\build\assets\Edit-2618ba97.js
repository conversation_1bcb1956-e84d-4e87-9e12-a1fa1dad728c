import{K as C,r as S,o as u,c as g,a as i,u as s,w as v,F as x,Z as k,b as o,t as E,k as p,v as $,d as U,e as f,f as b,n as j,P as y,i as q,g as N,T as M}from"./app-8a557454.js";import{_ as B,a as D}from"./AdminLayout-301d54ca.js";import{_ as d}from"./InputError-ccd7f9dc.js";import{_ as r}from"./InputLabel-07f3a6e8.js";import{P}from"./PrimaryButton-9d9bcdd8.js";import{_ as c}from"./TextInput-ab168ee4.js";import{_ as T}from"./TextArea-3588e81e.js";import{_ as W}from"./SearchableDropdown-51a69527.js";import{C as A}from"./CheckboxWithLabel-f345f79b.js";import{u as F}from"./index-62ab7306.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                                          */const J={class:"animate-top h-screen"},L={class:"sm:flex sm:items-center"},O=o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Jobcard")],-1),R={class:"w-auto"},z={class:"flex space-x-2"},H=o("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Jobcard Number:",-1),K={class:"text-sm font-semibold text-gray-900 leading-6"},Z={class:"flex space-x-2 items-center"},G=o("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1),I=["disabled"],Q={class:"mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},X=["onSubmit"],Y={class:"border-b border-gray-900/10 pb-12"},ee={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},se={class:"sm:col-span-4"},te={class:"sm:col-span-2"},ae={class:"sm:col-span-2"},oe={class:"sm:col-span-4"},ie={class:"sm:col-span-3"},ne={class:"sm:col-span-3"},le={class:"sm:col-span-3"},re={class:"sm:col-span-3"},de={class:"sm:col-span-6"},ce={class:"sm:col-span-6"},me={key:0,class:"sm:col-span-3"},ue={class:"relative mt-2"},pe={class:"sm:col-span-3"},_e={class:"mt-2 space-y-2"},ge={class:"flex items-center space-x-2"},ve=["disabled"],be=o("span",null,"Warranty",-1),ye={class:"flex items-center space-x-2"},fe=["disabled"],xe=o("span",null,"Out of Warranty",-1),Ve={class:"flex items-center space-x-2"},he=["disabled"],we=o("span",null,"AMC",-1),Ce={class:"flex items-center space-x-2"},Se=["disabled"],ke=o("span",null,"CMC",-1),Ee={class:"sm:col-span-6"},$e={class:"grid sm:grid-cols-6 relative mt-2"},Ue={class:"flex mt-6 items-center justify-between"},je={class:"ml-auto flex items-center justify-end gap-x-6"},qe=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ne={key:0,class:"text-sm text-gray-600"},ze={__name:"Edit",props:["data","checklist","permissions","engineer"],setup(n){const l=C().props.data,_=S([]);_.value=l.job_card_checks.map(m=>m.job_card_checklist_id);const e=F("post","/jobcard",{id:l.id,type:l.type,job_card_number:l.job_card_number,engineer_id:l.engineer_id,hospital_name:l.hospital_name,address:l.address,city:l.city,contact_no:l.contact_no,product_name:l.product_name,product_code:l.product_code,serial_no:l.serial_no,accessories:l.accessories,problem_description:l.problem_description,parts_required:l.parts_required,warranty_status:l.warranty_status,jobchecks:[],date:l.date}),V=m=>{_.value=m},h=()=>{e.jobchecks=_.value,e.put(route("jobcard.update",e.id),{preserveScroll:!0,onSuccess:()=>e.reset()})},w=(m,t)=>{e.engineer_id=m,e.errors.engineer_id=null};return(m,t)=>(u(),g(x,null,[i(s(k),{title:"Jobcard Checklist"}),i(B,null,{default:v(()=>[o("div",J,[o("div",L,[O,o("div",R,[o("div",z,[H,o("span",K,E(s(l).job_card_number),1)]),o("div",Z,[G,p(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[0]||(t[0]=a=>s(e).date=a),onChange:t[1]||(t[1]=a=>s(e).validate("date")),disabled:n.permissions.isServiceEngineer},null,40,I),[[$,s(e).date]])])])]),o("div",Q,[o("form",{onSubmit:U(h,["prevent"]),class:""},[o("div",Y,[o("div",ee,[o("div",se,[i(r,{for:"hospital_name",value:"Hospital Name"}),i(c,{id:"hospital_name",hospital_name:"text",modelValue:s(e).hospital_name,"onUpdate:modelValue":t[2]||(t[2]=a=>s(e).hospital_name=a),autocomplete:"hospital_name",onChange:t[3]||(t[3]=a=>s(e).validate("hospital_name")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.hospital_name},null,8,["message"])]),o("div",te,[i(r,{for:"contact_no",value:"Contact No"}),i(c,{id:"contact_no",contact_no:"text",modelValue:s(e).contact_no,"onUpdate:modelValue":t[4]||(t[4]=a=>s(e).contact_no=a),numeric:!0,autocomplete:"contact_no",onChange:t[5]||(t[5]=a=>s(e).validate("contact_no")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.contact_no},null,8,["message"])]),o("div",ae,[i(r,{for:"city",value:"City"}),i(c,{id:"city",city:"text",modelValue:s(e).city,"onUpdate:modelValue":t[6]||(t[6]=a=>s(e).city=a),autocomplete:"city",onChange:t[7]||(t[7]=a=>s(e).validate("city")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.city},null,8,["message"])]),o("div",oe,[i(r,{for:"address",value:"Address"}),i(c,{id:"address",type:"text",modelValue:s(e).address,"onUpdate:modelValue":t[8]||(t[8]=a=>s(e).address=a),onChange:t[9]||(t[9]=a=>s(e).validate("address")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.address},null,8,["message"])]),o("div",ie,[i(r,{for:"product_name",value:"Equipment"}),i(c,{id:"product_name",product_name:"text",modelValue:s(e).product_name,"onUpdate:modelValue":t[10]||(t[10]=a=>s(e).product_name=a),autocomplete:"product_name",onChange:t[11]||(t[11]=a=>s(e).validate("product_name")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.product_name},null,8,["message"])]),o("div",ne,[i(r,{for:"product_code",value:"Model"}),i(c,{id:"product_code",product_code:"text",modelValue:s(e).product_code,"onUpdate:modelValue":t[12]||(t[12]=a=>s(e).product_code=a),autocomplete:"product_code",onChange:t[13]||(t[13]=a=>s(e).validate("product_code")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.product_code},null,8,["message"])]),o("div",le,[i(r,{for:"serial_no",value:"Serial No"}),i(c,{id:"serial_no",serial_no:"text",modelValue:s(e).serial_no,"onUpdate:modelValue":t[14]||(t[14]=a=>s(e).serial_no=a),autocomplete:"serial_no",onChange:t[15]||(t[15]=a=>s(e).validate("serial_no")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),i(d,{class:"",message:s(e).errors.serial_no},null,8,["message"])]),o("div",re,[i(r,{for:"accessories",value:"Accessories"}),i(c,{id:"accessories",accessories:"text",modelValue:s(e).accessories,"onUpdate:modelValue":t[16]||(t[16]=a=>s(e).accessories=a),autocomplete:"accessories",onChange:t[17]||(t[17]=a=>s(e).validate("accessories")),disabled:n.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),s(e).invalid("accessories")?(u(),f(d,{key:0,class:"",message:s(e).errors.accessories},null,8,["message"])):b("",!0)]),o("div",de,[i(r,{for:"problem_description",value:"Description"}),i(T,{id:"problem_description",type:"text",rows:3,modelValue:s(e).problem_description,"onUpdate:modelValue":t[18]||(t[18]=a=>s(e).problem_description=a),onChange:t[19]||(t[19]=a=>s(e).validate("problem_description"))},null,8,["modelValue"]),i(d,{class:"",message:s(e).errors.problem_description},null,8,["message"])]),o("div",ce,[i(r,{for:"parts_required",value:"Parts Required"}),i(c,{id:"parts_required",type:"text",modelValue:s(e).parts_required,"onUpdate:modelValue":t[20]||(t[20]=a=>s(e).parts_required=a),autocomplete:"parts_required",onChange:t[21]||(t[21]=a=>s(e).validate("parts_required"))},null,8,["modelValue"]),i(d,{class:"",message:s(e).errors.parts_required},null,8,["message"])]),n.permissions.isServiceEngineer?b("",!0):(u(),g("div",me,[i(r,{for:"engineer_id",value:"Engineer Name"}),o("div",ue,[i(W,{options:n.engineer,modelValue:s(e).engineer_id,"onUpdate:modelValue":t[22]||(t[22]=a=>s(e).engineer_id=a),onOnchange:w,class:j({"error rounded-md":s(e).errors.engineer_id}),disabled:n.permissions.isServiceEngineer},null,8,["options","modelValue","class","disabled"])])])),o("div",pe,[i(r,{for:"warranty_status",value:"Warranty Status"}),o("div",_e,[o("label",ge,[p(o("input",{type:"radio",id:"warranty",value:"warranty","onUpdate:modelValue":t[23]||(t[23]=a=>s(e).warranty_status=a),onChange:t[24]||(t[24]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:n.permissions.isServiceEngineer},null,40,ve),[[y,s(e).warranty_status]]),be]),o("label",ye,[p(o("input",{type:"radio",id:"out_of_warranty",value:"out_of_warranty","onUpdate:modelValue":t[25]||(t[25]=a=>s(e).warranty_status=a),onChange:t[26]||(t[26]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:n.permissions.isServiceEngineer},null,40,fe),[[y,s(e).warranty_status]]),xe]),o("label",Ve,[p(o("input",{type:"radio",id:"amc",value:"amc","onUpdate:modelValue":t[27]||(t[27]=a=>s(e).warranty_status=a),onChange:t[28]||(t[28]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:n.permissions.isServiceEngineer},null,40,he),[[y,s(e).warranty_status]]),we]),o("label",Ce,[p(o("input",{type:"radio",id:"cmc",value:"cmc","onUpdate:modelValue":t[29]||(t[29]=a=>s(e).warranty_status=a),onChange:t[30]||(t[30]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:n.permissions.isServiceEngineer},null,40,Se),[[y,s(e).warranty_status]]),ke])]),s(e).invalid("warranty_status")?(u(),f(d,{key:0,message:s(e).errors.warranty_status},null,8,["message"])):b("",!0)]),o("div",Ee,[i(r,{for:"engineer_id",value:"Checklist"}),o("div",$e,[(u(!0),g(x,null,q(n.checklist,a=>(u(),f(A,{key:a.id,checked:_.value,value:a.id,label:a.type,"onUpdate:checked":V,disabled:n.permissions.isServiceEngineer},null,8,["checked","value","label","disabled"]))),128))])])])]),o("div",Ue,[o("div",je,[i(D,{href:m.route("jobcard.index")},{svg:v(()=>[qe]),_:1},8,["href"]),i(P,{disabled:s(e).processing},{default:v(()=>[N("Save")]),_:1},8,["disabled"]),i(M,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:v(()=>[s(e).recentlySuccessful?(u(),g("p",Ne,"Saved.")):b("",!0)]),_:1})])])],40,X)])])]),_:1})],64))}};export{ze as default};
