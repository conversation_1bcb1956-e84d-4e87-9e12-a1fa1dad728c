<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `challan` MODIFY `status` ENUM('Open', 'In-process', 'Close', 'Cancelled', 'Foc', 'Demo Close') DEFAULT 'Open'");

        Schema::table('payment_paid', function (Blueprint $table) {
            $table->string('bank_name')->nullable()->after('check_number');
        });

        Schema::table('payment_receive', function (Blueprint $table) {
            $table->string('bank_name')->nullable()->after('check_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
