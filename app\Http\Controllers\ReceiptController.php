<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\BankTransaction;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\Company;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\ReceiptRequest;
use App\Models\AccountType;
use App\Models\BankInfo;
use App\Models\Invoice;
use App\Models\PurchaseOrderReceives;
use App\Models\CustomerTransaction;
use App\Models\PurchaseTransaction;
use App\Models\PaymentReceive;
use App\Models\PaymentPaid;
use App\Models\CustomerCredit;
use App\Models\CompanyCredit;
use App\Models\CustomerCreditDetails;
use App\Models\CompanyCreditDetails;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Traits\QueryTrait;

class ReceiptController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Bank Transaction')->only(['index']);
        $this->middleware('permission:Create Bank Transaction')->only(['create', 'store']);
        $this->middleware('permission:Edit Bank Transaction')->only(['edit', 'update']);
        $this->middleware('permission:Delete Bank Transaction')->only('destroy');
    }

    use QueryTrait;

    /**
     * Determine entity type and create appropriate transaction
     */
    private function createTransaction($data, $entityType, $entityId, $paymentType, $note, $amount, $date)
    {
        $transactionData = [
            'organization_id' => $data['organization_id'],
            'entity_id' => $entityId,
            'entity_type' => $paymentType === 'payment_receive' ? 'payment_receive' : 'payment_paid',
            'payment_type' => $paymentType === 'payment_receive' ? 'cr' : 'dr',
            'amount' => $amount,
            'date' => $date,
            'note' => $note,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        if ($entityType === 'customer') {
            $transactionData['customer_id'] = $data['customer_id'];
            return CustomerTransaction::create($transactionData);
        } else {
            $transactionData['company_id'] = $data['company_id'];
            return PurchaseTransaction::create($transactionData);
        }
    }

    /**
     * Create appropriate credit record
     */
    private function createCredit($data, $entityType, $paymentId, $amount)
    {
        $creditData = [
            'organization_id' => $data['organization_id'],
            'amount' => $amount,
            'unused_amount' => $amount,
            'date' => $data['date'],
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        if ($entityType === 'customer') {
            $creditData['customer_id'] = $data['customer_id'];
            $creditData['payment_receive_id'] = $paymentId;
            return CustomerCredit::create($creditData);
        } else {
            $creditData['company_id'] = $data['company_id'];
            $creditData['payment_paid_id'] = $paymentId;
            return CompanyCredit::create($creditData);
        }
    }

    /**
     * Create linked transaction for party_id relationships
     */
    private function createLinkedTransaction($data, $entityType, $partyId, $paymentId, $paymentType, $note, $amount, $date)
    {
        if ($entityType === 'customer') {
            // For customer payments, create corresponding company transactions
            $companyIds = Company::where('party_id', $partyId)->pluck('id');
            foreach ($companyIds as $companyId) {
                PurchaseTransaction::create([
                    'organization_id' => $data['organization_id'],
                    'company_id' => $companyId,
                    'entity_id' => $paymentId,
                    'entity_type' => $paymentType,
                    'payment_type' => 'cr', // Credit for company when customer pays
                    'amount' => $amount,
                    'date' => $date,
                    'note' => $note . ' (Linked from Customer)',
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                ]);
            }
        } else {
            // For company payments, create corresponding customer transactions
            $customerIds = Customer::where('party_id', $partyId)->pluck('id');
            foreach ($customerIds as $customerId) {
                CustomerTransaction::create([
                    'organization_id' => $data['organization_id'],
                    'customer_id' => $customerId,
                    'entity_id' => $paymentId,
                    'entity_type' => $paymentType,
                    'payment_type' => 'dr', // Debit for customer when company receives payment
                    'amount' => $amount,
                    'date' => $date,
                    'note' => $note . ' (Linked from Company)',
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                ]);
            }
        }
    }

    /**
     * Update invoice status based on entity type
     */
    private function updateInvoiceStatus($invoiceId, $amount, $entityType, $operation = 'add')
    {
        if ($entityType === 'customer') {
            $invoice = Invoice::find($invoiceId);
        } else {
            $invoice = PurchaseOrderReceives::find($invoiceId);
        }

        if (!$invoice) return;

        $multiplier = $operation === 'add' ? 1 : -1;
        $updateData = [
            'paid_amount' => $invoice->paid_amount + ($amount * $multiplier),
            'pending_amount' => $invoice->pending_amount - ($amount * $multiplier)
        ];

        if ($updateData['pending_amount'] <= 0) {
            $updateData['status'] = 'Paid';
        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
            $updateData['status'] = 'Partially Paid';
        } else {
            $updateData['status'] = 'Unpaid';
        }

        $invoice->update($updateData);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $query = PaymentReceive::with('customers', 'bankInfo');

        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allOption2);

        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        $searchableFields = ['customers.customer_name', 'invoice.invoice_no', 'payment_type', 'bank_info.bank_name', 'date', 'amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);
        $data->withQueryString()->links();
        return Inertia::render('Receipt/List', compact('data', 'organization', 'customers', 'customerId', 'organizationId'));
    }

    public function create(Request $request)
    {
        $organization = Organization::select('id', 'name')->get();

        // Get customers and companies with party_id information
        $customers = Customer::where('status', '1')
            ->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 35), " - ", city) as name, id, party_id')
            ->orderByRaw('customer_name')->get();

        $companies = Company::where('status', '1')
            ->selectRaw('CONCAT(SUBSTRING(name, 1, 35), " - ", city) as name, id, party_id')
            ->orderByRaw('name')->get();

        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Customer invoices (for receiving payments)
        $customerInvoices = Invoice::where('status', '!=', 'Paid')
            ->select(DB::raw("CONCAT('CUST-', invoice_no, '  Date: ', DATE_FORMAT(date, '%d %b %Y'), '  Amount: ₹', FORMAT(total_amount, 2)) AS name"),
                'id', 'organization_id', 'customer_id', 'total_amount', 'invoice_no', 'date', 'pending_amount')
            ->orderBy('date', 'asc')->get();

        // Company invoices (Purchase Order Receives - for paying to companies)
        $companyInvoices = PurchaseOrderReceives::with('purchaseOrder')
            ->where('status', '!=', 'Paid')
            ->where('total_amount', '>', 0)
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->select(DB::raw("CONCAT('COMP-', customer_invoice_no, '  Date: ', DATE_FORMAT(customer_invoice_date, '%d %b %Y'), '  Amount: ₹', FORMAT(total_amount, 2)) AS name"),
                'id', 'purchase_order_id', 'total_amount', 'customer_invoice_no as invoice_no', 'customer_invoice_date as date', 'pending_amount')
            ->orderBy('customer_invoice_date', 'asc')->get();

        // Add entity type and additional info to invoices
        $customerInvoices->each(function($invoice) {
            $invoice->entity_type = 'customer';
            $invoice->entity_id = $invoice->customer_id;
        });

        $companyInvoices->each(function($invoice) {
            $invoice->entity_type = 'company';
            $invoice->organization_id = $invoice->purchaseOrder->organization_id ?? null;
            $invoice->company_id = $invoice->purchaseOrder->company_id ?? null;
            $invoice->entity_id = $invoice->company_id;
        });

        // Merge all invoices
        $invoices = $customerInvoices->concat($companyInvoices)->sortBy('date');

        // Get credits for both customers and companies
        $customerCredit = CustomerCredit::with('paymentreceive.bankInfo')
            ->where('unused_amount', '>', 0)->get();
        $companyCredit = CompanyCredit::with('paymentpaid.bankInfo')
            ->where('unused_amount', '>', 0)->get();

        return Inertia::render('Receipt/Add', compact(
            'paymentType', 'organization', 'bankinfo', 'customers', 'companies',
            'invoices', 'customerCredit', 'companyCredit'
        ));
    }

    public function store(ReceiptRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();

            // Determine entity type based on request data
            $entityType = isset($data['customer_id']) && $data['customer_id'] ? 'customer' : 'company';
            $entityId = $entityType === 'customer' ? $data['customer_id'] : $data['company_id'];

            // Get party_id for linked transactions
            $partyId = null;
            if ($entityType === 'customer') {
                $customer = Customer::find($entityId);
                $partyId = $customer->party_id ?? null;
            } else {
                $company = Company::find($entityId);
                $partyId = $company->party_id ?? null;
            }

            //payment edit
            if(isset($data['id'])){
                $paymentId = $data['id'];
                // Revert the effects of the original payment
                // Find the original payment
                $originalPayment = PaymentReceive::with('credit', 'credit.creditDetail')->find($paymentId);

                // 1. Revert invoice payments
                foreach ($originalPayment->invoice_data as $invoice) {
                    $invoiceDetail = Invoice::find($invoice['id']);
                    if ($invoiceDetail) {
                        $updateData['paid_amount'] = $invoiceDetail->paid_amount - $invoice['amount'];
                        $updateData['pending_amount'] = $invoiceDetail->pending_amount + $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $invoiceDetail->update($updateData);
                    }
                }

                // 2. Revert credit usage not required now
                // if ($originalPayment->credit) {
                //     $credit = $originalPayment->credit;
                //     foreach ($credit->creditDetail as $detail) {
                //         $invoice = Invoice::find($detail->invoice_id);
                //         if ($invoice) {
                //             $invoice->paid_amount -= $detail->amount;
                //             $invoice->pending_amount += $detail->amount;
                //             if ($invoice->paid_amount <= 0) {
                //                 $invoice->status = 'Unpaid';
                //             } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                //                 $invoice->status = 'Partially Paid';
                //             } else {
                //                 $invoice->status = 'Paid';
                //             }
                //             $invoice->save();
                //         }
                //         $detail->delete();
                //     }

                //     // Don't delete the credit record, just update it
                //     if ($originalPayment->advance_amount > 0) {
                //         $credit->unused_amount = $originalPayment->advance_amount;
                //         $credit->save();
                //     } else {
                //         $credit->delete();
                //     }
                // }

                // 3. Delete related bank transaction and customer transaction
                if ($originalPayment->payment_type == 'check' || $originalPayment->payment_type == 'NEFT') {
                    BankTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_receive'])->delete();
                }
                CustomerTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_receive'])->delete();

                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                    ]);
                }, $filteredInvoiceData));
                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));

                $data['created_by'] = $data['updated_by'] = auth()->id();
                $originalPayment->update($data);

                if ($originalPayment) {
                    foreach ($data['invoice_data'] as $invoice) {
                        $InvoiceDetail = Invoice::find($invoice['id']);
                        $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $invoice['amount'];
                        $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $InvoiceDetail->update($updateData);
                    }
                }

                // Create customer transaction and bank transaction
                $data['entity_id'] = $originalPayment->id;
                $data['entity_type'] = 'payment_receive';
                if ($data['payment_type'] == 'check') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'Cheque No:' . $data['check_number'] . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'NEFT') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'NEFT' . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'cash') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = $data['note'] . ' ' . 'Invoice No:' . $invoice_nos;
                }

                if ($data['advance_amount'] > 0) {
                    // Check if there's an existing credit record
                    $existingCredit = CustomerCredit::where('payment_receive_id', $originalPayment->id)->first();
                    if ($existingCredit) {
                        $existingCredit->update([
                            'unused_amount' => $data['advance_amount'],
                            'amount' => $data['advance_amount']
                        ]);
                    } else {
                        $data['payment_receive_id'] = $originalPayment->id;
                        $data['unused_amount'] = $data['amount'] = $data['advance_amount'];
                        CustomerCredit::create($data);
                    }
                }
                $data['amount'] = $data['settled_amount'] + $data['advance_amount'];
                CustomerTransaction::create($data);
                DB::commit();
                return Redirect::to('/receipt')->with('success', 'Payment Updated Successfully');
            } else {
            //payment create
                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                    ]);
                }, $filteredInvoiceData));
                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));
                    //for advance payment settle
                if($data['is_credit'] == 'Yes'){
                    $credits = $data['credit_data'];
                    $invoiceData = $data['invoice_data'];
                    foreach ($invoiceData as $invoice) {
                        $remainingAmount = $invoice['amount'];
                        foreach ($credits as &$creditEntry) {
                            if ($remainingAmount <= 0) break;

                            $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);

                            if ($usableAmount > 0) {
                                // 1. Update invoice
                                $InvoiceDetail = Invoice::find($invoice['id']);
                                $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
                                $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
                                if ($updateData['pending_amount'] <= 0) {
                                    $updateData['status'] = 'Paid';
                                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                    $updateData['status'] = 'Partially Paid';
                                } else {
                                    $updateData['status'] = 'Unpaid';
                                }
                                $InvoiceDetail->update($updateData);
                                // 2. Create credit details record
                                $creditDetail = [
                                    'invoice_id' => $invoice['id'],
                                    'credit_id' => $creditEntry['id'],
                                    'amount' => $usableAmount,
                                    'date' => date('Y-m-d'),
                                    'created_by' => auth()->id(),
                                    'updated_by' => auth()->id(),
                                ];
                                CustomerCreditDetails::create($creditDetail);
                                // 3. Deduct from credit's unused amount
                                $creditEntry['unused_amount'] -= $usableAmount;
                                CustomerCredit::where('id', $creditEntry['id'])->update([
                                    'unused_amount' => $creditEntry['unused_amount']
                                ]);
                                $remainingAmount -= $usableAmount;
                            }
                            $customerTransaction = CustomerTransaction::where(['entity_id' => $creditEntry['payment_receive_id'], 'entity_type' => 'payment_receive'])->first();
                            if ($customerTransaction) {
                                $existingNote = trim($customerTransaction->note ?? '');
                                $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
                                $customerTransaction->update(['note' => $updatedNote]);
                            }
                        }
                    }
                } else {
                    //for receive payment
                    $data['created_by'] = $data['updated_by'] = auth()->id();

                    // Create payment record based on entity type
                    if ($entityType === 'customer') {
                        $payment = PaymentReceive::create($data);
                        $paymentType = 'payment_receive';
                    } else {
                        $payment = PaymentPaid::create($data);
                        $paymentType = 'payment_paid';
                    }

                    if($payment){
                        foreach($payment->invoice_data as $invoice){
                            $this->updateInvoiceStatus($invoice['id'], $invoice['amount'], $entityType, 'add');
                        }
                    }

                    // Create transaction record
                    $data['entity_id'] = $payment->id;
                    $data['entity_type'] = $paymentType;

                    // Create bank transaction if needed and prepare note
                    $notePrefix = '';
                    if($data['payment_type'] == 'check'){
                        $notePrefix = 'Cheque No:' .$data['check_number'] .' ';
                        $bankData = $data;
                        $bankData['payment_type'] = $entityType === 'customer' ? 'cr' : 'dr';
                        $bankData['note'] = $notePrefix . 'Invoice No:' .$invoice_nos;
                        BankTransaction::create($bankData);
                    } else if($data['payment_type'] == 'NEFT'){
                        $notePrefix = 'NEFT ';
                        $bankData = $data;
                        $bankData['payment_type'] = $entityType === 'customer' ? 'cr' : 'dr';
                        $bankData['note'] = $notePrefix . 'Invoice No:' .$invoice_nos;
                        BankTransaction::create($bankData);
                    } else if($data['payment_type'] == 'cash'){
                        $notePrefix = $data['note'] . ' ';
                    }

                    // Create credit if advance amount exists
                    if($data['advance_amount'] > 0){
                        $this->createCredit($data, $entityType, $payment->id, $data['advance_amount']);
                    }

                    // Create transaction record
                    $totalAmount = $data['settled_amount'] + $data['advance_amount'];
                    $note = $notePrefix . 'Invoice No:' .$invoice_nos;
                    $this->createTransaction($data, $entityType, $payment->id, $paymentType, $note, $totalAmount, $data['date']);

                    // Create linked transaction for party_id if exists
                    if ($partyId) {
                        $this->createLinkedTransaction($data, $entityType, $partyId, $payment->id, $paymentType, $note, $totalAmount, $data['date']);
                    }
                }
                DB::commit();
                return Redirect::to('/receipt')->with('success', 'Payment Received Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit($id)
    {
        $payment = PaymentReceive::with('bankInfo')->find($id);

        if (!$payment) {
            return Redirect::to('/receipt')->with('error', 'Payment not found');
        }

        $organization = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 35), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Get invoices that are either paid in this payment or still have pending amounts
        $invoices = Invoice::where(function($query) use ($payment) {
                // Include invoices that are not fully paid
                $query->where('status', '!=', 'Paid')
                      ->where('customer_id', $payment->customer_id)
                      ->where('organization_id', $payment->organization_id);
            })
            ->orWhereIn('id', collect($payment->invoice_data)->pluck('id')) // Include invoices paid in this payment
            ->select(DB::raw("CONCAT(invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(date, '%d %b %Y'), '  ',  'Total Amount : ', total_amount) AS name"),
                    'id', 'organization_id', 'customer_id', 'total_amount', 'invoice_no', 'date', 'pending_amount')
            ->orderBy('date', 'asc')
            ->get();

        // Get the original pending amount for each invoice by adding back the amount paid in this payment
        foreach ($invoices as $invoice) {
            foreach ($payment->invoice_data as $paidInvoice) {
                if ($invoice->id == $paidInvoice['id']) {
                    $invoice->original_pending_amount = $invoice->pending_amount + $paidInvoice['amount'];
                    $invoice->paid_in_this_payment = $paidInvoice['amount'];
                    break;
                }
            }
            if (!isset($invoice->original_pending_amount)) {
                $invoice->original_pending_amount = $invoice->pending_amount;
                $invoice->paid_in_this_payment = 0;
            }
        }

        // Get all credits for this customer
        $credit = CustomerCredit::with('paymentreceive.bankInfo')
            ->where('customer_id', $payment->customer_id)
            ->where('organization_id', $payment->organization_id)
            ->where('unused_amount', '>', 0)
            ->get();

        // Add the payment's own credit if it exists
        if ($payment->advance_amount > 0) {
            $ownCredit = CustomerCredit::with('paymentreceive.bankInfo')
                ->where('payment_receive_id', $payment->id)
                ->first();

            if ($ownCredit) {
                $credit->push($ownCredit);
            }
        }

        return Inertia::render('Receipt/Edit', compact('payment', 'paymentType', 'organization', 'bankinfo', 'customers', 'invoices', 'credit'));
    }

    public function update(ReceiptRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            if ($data['is_credit'] == 'Yes') {
                $credits = $data['credit_data'];
                $invoiceData = $data['invoice_data'];
                foreach ($invoiceData as $invoice) {
                    $remainingAmount = $invoice['amount'];
                    foreach ($credits as &$creditEntry) {
                        if ($remainingAmount <= 0) break;

                        $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);

                        if ($usableAmount > 0) {
                            // 1. Update invoice
                            $InvoiceDetail = Invoice::find($invoice['id']);
                            $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
                            $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $InvoiceDetail->update($updateData);

                            // 2. Create credit details record
                            $creditDetail = [
                                'invoice_id' => $invoice['id'],
                                'credit_id' => $creditEntry['id'],
                                'amount' => $usableAmount,
                                'date' => date('Y-m-d'),
                                'created_by' => auth()->id(),
                                'updated_by' => auth()->id(),
                            ];
                            CustomerCreditDetails::create($creditDetail);

                            // 3. Deduct from credit's unused amount
                            $creditEntry['unused_amount'] -= $usableAmount;
                            CustomerCredit::where('id', $creditEntry['id'])->update([
                                'unused_amount' => $creditEntry['unused_amount']
                            ]);
                            $remainingAmount -= $usableAmount;
                        }
                        $customerTransaction = CustomerTransaction::where(['entity_id' => $creditEntry['payment_receive_id'], 'entity_type' => 'payment_receive'])->first();
                        if ($customerTransaction) {
                            $existingNote = trim($customerTransaction->note ?? '');
                            $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
                            $customerTransaction->update(['note' => $updatedNote]);
                        }
                    }
                }
            } else {
            }
            DB::commit();
            return Redirect::to('/receipt')->with('success', 'Payment Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $receivePayment = PaymentReceive::with('credit', 'credit.creditDetail')->find($id);
            foreach($receivePayment->invoice_data as $invoice){
                $InvoiceDetail = Invoice::find($invoice['id']);
                $updateData['paid_amount'] = $InvoiceDetail->paid_amount - $invoice['amount'];
                $updateData['pending_amount'] = $InvoiceDetail->pending_amount + $invoice['amount'];
                if ($updateData['pending_amount'] <= 0) {
                    $updateData['status'] = 'Paid';
                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                    $updateData['status'] = 'Partially Paid';
                } else {
                    $updateData['status'] = 'Unpaid';
                }
                $InvoiceDetail->update($updateData);
            }

            if($receivePayment->credit) {
                $credit = $receivePayment->credit;
                foreach ($credit->creditDetail as $detail) {
                    $invoice = Invoice::find($detail->invoice_id);
                    if ($invoice) {
                        $invoice->paid_amount -= $detail->amount;
                        $invoice->pending_amount += $detail->amount;
                        if ($invoice->paid_amount <= 0) {
                            $invoice->status = 'Unpaid';
                        } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                            $invoice->status = 'Partially Paid';
                        } else {
                            $invoice->status = 'Paid';
                        }
                        $invoice->save();
                    }
                    $detail->delete();
                }
                $credit->delete();
            }
            if($receivePayment->payment_type == 'check' || $receivePayment->payment_type== 'NEFT'){
                $bankTransaction = BankTransaction::where(['entity_id' => $receivePayment->id , 'entity_type' => 'payment_receive'])->delete();
            }
            $customerTransaction = CustomerTransaction::where(['entity_id' => $receivePayment->id , 'entity_type' => 'payment_receive'])->delete();
            $receivePayment->delete();
            DB::commit();
            return Redirect::back()->with('success','Transaction Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
