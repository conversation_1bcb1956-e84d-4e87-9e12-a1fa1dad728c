<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import Modal from '@/Components/Modal.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import {onBeforeMount,  defineProps , ref} from 'vue';
import { Head, usePage } from '@inertiajs/vue3';

const props = defineProps(['product', 'organization', 'page']);

const stockData = usePage().props.data[0];


const form = useForm('post', '/savestock', {
    stockItems: [],
    company_id: props.product.company_id,
    product_id: stockData.id,
    page: props.page
});

const setOrganization = (id, name, index) => {
    stockItems.value[index].organization_id = id;
};

const stockItems = ref([
    {
        product_id: props.product.id,
        organization_id: '',
        batch: '',
        expiry_date: '',
        mrp: '',
        purchase_price: '',
        receive_qty: '',
        sell_qty: '',
        serial_number_id: ''
    }
])

onBeforeMount(() => {
    stockItems.value = stockData.serial_numbers.map(detail => ({
        serial_number_id: detail.id,
        product_id: detail.product_id,
        organization_id: detail.organization_id,
        batch: detail.batch,
        expiry_date: detail.expiry_date,
        mrp: detail.mrp,
        purchase_price: detail.purchase_price,
        receive_qty: detail.receive_qty,
        sell_qty: detail.sell_qty
    }));
});

const selectedProductId = ref(null);
const productDeleteModal = ref(false);

const closeProductModal = () => {
    productDeleteModal.value = false;
};

const deleteProduct = () => {
    form.get(route('removeproduct',{id:selectedProductId.value,  model:'SerialNumbers'}), {
        onSuccess: () => {
        closeProductModal()
        stockItems.value.splice(index, 1);
        }
    });
};

const removeProduct = (index, id) => {
    selectedProductId.value = id;
    productDeleteModal.value = true;
};

const submit = () =>{
    form.stockItems = stockItems.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

</script>

<template>
    <Head title="Stock Update" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Stock</h2>
            <form @submit.prevent="submit" class="">
                <table class="overflow-x-auto divide-y divide-gray-300">
                    <thead>
                        <tr>
                            <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Organization</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Batch</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Expiry Date</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">MRP (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Purchase Price (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Stock</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white">
                        <tr v-for="(product, index)  in stockItems" :key="index">
                            <td class="whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-60">
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="organization"
                                    v-model="product.organization_id"
                                    @onchange="(id, name) => setOrganization(id, name, index)"
                                    :class="{ 'error': form.errors[`stockItems.${index}.organization_id`] }"
                                    />
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60">
                                <TextInput
                                    id="batch"
                                    type="text"
                                    v-model="product.batch"
                                    :class="{ 'error': form.errors[`stockItems.${index}.batch`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20">
                                <input
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"  v-model="product.expiry_date"
                                    :class="{ 'error': form.errors[`stockItems.${index}.expiry_date`] }"
                                    />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20">
                                <TextInput
                                    id="mrp"
                                    type="text"
                                    v-model="product.mrp"
                                    :class="{ 'error': form.errors[`stockItems.${index}.mrp`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20">
                                <TextInput
                                    id="purchase_price"
                                    type="text"
                                    v-model="product.purchase_price"
                                    :class="{ 'error': form.errors[`stockItems.${index}.purchase_price`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-16">
                                <TextInput
                                    id="receive_qty"
                                    type="text"
                                    :numeric="true"
                                    v-model="product.receive_qty"
                                    :class="{ 'error': form.errors[`stockItems.${index}.receive_qty`] }"
                                     :disabled="product.sell_qty > 0"
                                />
                            </td>
                            <td>
                                <button v-if="product.sell_qty == 0" type="button" class="mt-1" @click="removeProduct(index, product.serial_number_id)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="product.category === 'Sales' ? route('salesstock') : route('servicestock')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Update</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        <Modal :show="productDeleteModal" @close="closeProductModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this stock?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeProductModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteProduct"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>






<style scoped>
.error {
  border: 1px solid red;
}
</style>
