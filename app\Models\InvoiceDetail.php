<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceDetail extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'invoice_details';

    protected static $logName = 'Invoice-Detail';

    public function getLogDescription(string $event): string
{
    $invoiceNumber = $this->invoice->invoice_no;

    $productName = $this->product?->name ?? 'Unknown Product';

    return "Invoice details has been {$event} for <strong>{$productName}</strong> : {$invoiceNumber} by";
}

    protected static $logAttributes = [
        'invoice_id',
        'product_id',
        'is_receive',
        'credit_note_id',
        'serial_number_id',
        'challan_detail_id',
        'hsn_code',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by',
        'description'
    ];
    protected $fillable = [
        'invoice_id',
        'product_id',
        'is_receive',
        'credit_note_id',
        'serial_number_id',
        'challan_detail_id',
        'hsn_code',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by',
        'description'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function creditnote()
    {
        return $this->belongsTo(CreditNote::class, 'credit_note_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function serialnumbers(){
        return $this->belongsTo(SerialNumbers::class, 'serial_number_id', 'id');
    }

      public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

}
