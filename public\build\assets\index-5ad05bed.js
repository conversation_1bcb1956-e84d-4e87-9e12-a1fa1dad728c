import{_ as M,b as D,a as N}from"./AdminLayout-f002e683.js";import{_ as p}from"./CreateButton-400e96c7.js";import{_ as $}from"./SecondaryButton-98872fc5.js";import{D as S}from"./DangerButton-fc55f8d0.js";import{M as T}from"./Modal-e8ed59aa.js";import{r as g,o as a,c as l,a as o,u as d,w as s,F as _,Z as E,b as e,g as n,i as x,t as u,e as V,f as y}from"./app-497d70e1.js";import{_ as z}from"./ArrowIcon-92b60770.js";import{s as A}from"./sortAndSearch-07be74d0.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const H={class:"animate-top"},O={class:"flex justify-between items-center"},q=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Email Template")],-1),F={class:"flex justify-end"},I=e("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},null,-1),K={class:"flex justify-end w-20"},U={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Z={class:"flex justify-end"},G={class:"mt-8 overflow-x-auto sm:rounded-lg"},J={class:"shadow sm:rounded-lg"},P={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Q={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},R={class:"border-b-2"},W=["onClick"],X={key:0,class:"divide-y divide-gray-300 bg-white"},Y={class:"border-gray-300 px-4 py-2 min-w-40"},ee={class:"border-gray-300 px-4 py-2 min-w-40"},te=["innerHTML"],se={class:"items-center px-4 py-2.5"},oe={class:"flex items-center justify-start gap-4"},ae={key:0,type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},le=e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})],-1),ie=[le],ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),re=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),de=["onClick"],ce=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),me=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),_e=[ce,me],ue={key:1,class:"bg-white"},fe=e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ",-1),he=[fe],pe={class:"p-6"},ge=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),xe={class:"mt-6 flex justify-end"},Se={__name:"index",props:{templates:{type:Object,required:!0},id:{type:[String,Number],required:!1}},setup(r){const{form:b,search:ye,sort:v,fetchData:be,sortKey:w,sortDirection:k}=A("emailtemplates.index"),C=[{field:"email_subject",label:"Email Subject",sortable:!1},{field:"template_name",label:"Template Name",sortable:!0},{field:"content",label:"Content",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],j=i=>{f.value=i,m.value=!0},c=()=>{m.value=!1},B=()=>{b.delete(route("emailtemplates.destroy",{id:f.value}),{onSuccess:()=>c()})},m=g(!1),f=g(null);return(i,ve)=>(a(),l(_,null,[o(d(E),{title:"Email Template"}),o(M,null,{default:s(()=>[e("div",H,[e("div",O,[q,e("div",F,[I,e("div",K,[o(p,{href:i.route("setting")},{default:s(()=>[n(" Back ")]),_:1},8,["href"])]),e("div",U,[e("div",Z,[o(p,{href:i.route("emailtemplates.create")},{default:s(()=>[n(" Create ")]),_:1},8,["href"])])])])]),e("div",G,[e("div",J,[e("table",P,[e("thead",Q,[e("tr",R,[(a(),l(_,null,x(C,(t,h)=>e("th",{key:h,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:L=>d(v)(t.field,t.sortable)},[n(u(t.label)+" ",1),t.sortable?(a(),V(z,{key:0,isSorted:d(w)===t.field,direction:d(k)},null,8,["isSorted","direction"])):y("",!0)],8,W)),64))])]),r.templates.data&&r.templates.data.length?(a(),l("tbody",X,[(a(!0),l(_,null,x(r.templates.data,(t,h)=>(a(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Y,u(t.email_subject),1),e("td",ee,u(t.template_name),1),e("td",{class:"border-gray-300 px-4 py-2",innerHTML:t.content},null,8,te),e("td",se,[e("div",oe,[o(D,{align:"right",width:"48"},{trigger:s(()=>[r.templates.data?(a(),l("button",ae,ie)):y("",!0)]),content:s(()=>[o(N,{href:i.route("emailtemplates.edit",{id:t.id})},{svg:s(()=>[ne]),text:s(()=>[re]),_:2},1032,["href"]),e("button",{type:"button",onClick:L=>j(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},_e,8,de)]),_:2},1024)])])]))),128))])):(a(),l("tr",ue,he))])])])]),o(T,{show:m.value,onClose:c},{default:s(()=>[e("div",pe,[ge,e("div",xe,[o($,{onClick:c},{default:s(()=>[n(" Cancel ")]),_:1}),o(S,{class:"ml-3",onClick:B},{default:s(()=>[n(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Se as default};
