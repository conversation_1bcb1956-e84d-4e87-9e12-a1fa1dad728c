<?php

namespace App\DTO;

use App\Traits\ArrayToProps;

class QuotationDTO
{
    use ArrayToProps;

    public $category;
    public $organization_id;
    public $customer_id;
    public $sales_user_id;
    public $quotation_number;
    public $date;
    public $overall_discount;
    public $total_discount;
    public $total_amount;
    public $note;
    public $igst;
    public $sgst;
    public $cgst;
    public $sub_total;
    public $total_gst;
    public $validity;
    public $delivery;
    public $payment_terms;
    public $warranty;
    public $is_customer;
    public $customer_name;
    public $address;
    public $city;
    public $gst_type;
    public $type;
    public $selectedProductItem;
    public $id;
    public $entity_id;
    public $quotation_id;
    public $created_by;
    public $updated_by;

}
