<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Organization extends Model
{
    use HasFactory;

    use ActivityTrait;

    protected $table = 'organizations';

    protected static $logName = 'Organization';

    public function getLogDescription(string $event): string
    {

        return "<strong>{$this->name}</strong> Organization has been {$event} by";
    }

    protected static $logAttributes = [
        'name',
        'address_line_1',
        'address_line_2',
        'pincode',
        'city',
        'state',
        'gst_no',
        'pan_no',
        'drug_licence_no',
        'email',
        'website',
        'contact_no',
        'logo',
        'signature',
        'remarks',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'name',
        'address_line_1',
        'address_line_2',
        'pincode',
        'city',
        'state',
        'gst_no',
        'pan_no',
        'drug_licence_no',
        'email',
        'website',
        'contact_no',
        'logo',
        'signature',
        'remarks',
        'created_by',
        'updated_by'
    ];
}
