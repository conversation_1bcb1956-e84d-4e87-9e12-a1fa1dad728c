<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challan_detail', function (Blueprint $table) {
            $table->dropColumn('qty');
            $table->dropForeign('chdp_id');
            $table->dropColumn('product_id');
            $table->dropColumn('hsn_code');
            $table->bigInteger('serial_number_id')->unsigned()->nullable()->after('challan_id');
            $table->dropColumn('return_qty');
            $table->dropColumn('accepted_qty');
        });

        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->dropColumn('challan_id');
            $table->dropColumn('invoice_id');
            $table->enum('status', ['Stock', 'Challan', 'Sold'])->after('serial_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('challan_detail', function (Blueprint $table) {
            //
        });
    }
};
