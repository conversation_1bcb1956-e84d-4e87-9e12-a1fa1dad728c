import{r as w,h as R,j as O,o as l,c,a as p,u as L,w as m,F as v,Z as X,b as e,t as i,g,f as h,k as S,v as j,n as A,i as Y,e as b,s as q,x as G}from"./app-2ecbacfc.js";import{_ as H,b as Z,a as I}from"./AdminLayout-42d5bb92.js";import{_ as U}from"./CreateButton-1fa2a774.js";import{_ as J}from"./SecondaryButton-be49842d.js";import{D as K}from"./DangerButton-3e1103de.js";import{M as Q}from"./Modal-54f7c77a.js";import{_ as W}from"./Pagination-56593f88.js";import{_ as E}from"./InputLabel-f62a278f.js";import{_ as ee}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const u=r=>(q("data-v-68b652de"),r=r(),G(),r),te={class:"animate-top"},se={class:"sm:flex sm:items-center"},oe={class:"sm:flex-auto"},ae={class:"text-2xl font-semibold leading-7 text-gray-900"},ne={class:"text-sm font-semibold text-gray-900"},re={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},le={class:"flex justify-end w-20"},ie={key:0,class:"flex justify-end"},de={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ce={class:"flex justify-between mb-2"},me={class:"flex items-center"},ue=u(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),_e={class:"inline-flex items-center space-x-4"},pe={class:"mt-2 flex items-center"},he=u(()=>e("span",{class:"text-sm text-gray-600 mr-2"},"Current Balance:",-1)),ye={class:"text-base font-bold text-gray-900"},fe=["src"],ge={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},xe={class:"sm:col-span-4"},ve={class:"sm:col-span-4"},we={class:"mt-8 overflow-x-auto sm:rounded-lg"},be={class:"shadow sm:rounded-lg"},ke={class:"w-full text-sm text-left rtl:text-right text-gray-500"},De=u(()=>e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," TYPE "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," RECEIPT "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," PAYMENT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1)),Ce={key:0},Be={class:"px-4 py-2.5 min-w-32"},Te={class:"px-4 py-2.5 min-w-20"},Le={class:"px-4 py-2.5 min-w-52 text-sm flex flex-col font-medium text-gray-900"},Ee={class:"tooltiptext text-xs"},Ne={key:0,class:"text-xs text-gray-700"},Me={key:1,class:"text-xs text-gray-700"},$e={class:"px-4 py-2.5 min-w-36"},Se={class:"px-4 py-2.5 min-w-36"},je={class:"px-4 py-2.5 min-w-36"},Ae={class:"items-center px-4 py-2.5"},Ie={class:"flex items-center justify-start gap-4"},Ue=u(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Ve=u(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Pe=u(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),ze=u(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Fe=u(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit Internal Transfer ",-1)),Re=["onClick"],Oe=u(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Xe=u(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Ye=[Oe,Xe],qe={key:1},Ge=u(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),He=[Ge],Ze={class:"p-6"},Je=u(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Ke={class:"mt-6 flex justify-end"},Qe={__name:"View",props:["data","bank_id","bank","current_balance","permissions"],setup(r){const x=r,_=w(""),f=w(""),k=R({}),D=w(!1),N=w(null),V=s=>{N.value=s,D.value=!0},C=()=>{D.value=!1},P=()=>{k.delete(route("banktransaction.destroy",{id:N.value}),{onSuccess:()=>C()})},z=s=>{const a=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",t)},B=s=>{const a=s.toFixed(2).toString(),[t,n]=a.split("."),o=t.substring(t.length-3),d=t.substring(0,t.length-3);return`${d!==""?d.replace(/\B(?=(\d{2})+(?!\d))/g,",")+","+o:o}.${n}`},M=(s,a,t)=>{const n=parseFloat(a.amount),o=t==="cr",d=a.payment_type==="cr";return o?d?s-n:s+n:d?s+n:s-n},T=O(()=>{let s=parseFloat(x.bank.balance);const a=x.bank.amount_type;if(_.value){const n=new Date(_.value);x.data.forEach(o=>{new Date(o.date)<n&&(s=M(s,o,a))})}return x.data.filter(n=>{const o=new Date(n.date);return _.value&&f.value?o>=new Date(_.value)&&o<=new Date(f.value):(!_.value||o>=new Date(_.value))&&(!f.value||o<=new Date(f.value))}).map(n=>{s=M(s,n,a);const o=s>=0?"cr":"dr",d=B(Math.abs(s))+" "+o;return{...n,balance:d}})}),$=()=>{},F=()=>{const s="Bank_Transactions_"+x.bank.bank_name.replace(/\s+/g,"_"),a={};_.value&&f.value&&(a.from_date=_.value,a.to_date=f.value);const t=new URLSearchParams(a).toString(),n=`/export-bank-transaction/${x.bank.id}?${t}`;fetch(n,{method:"GET"}).then(o=>{if(!o.ok)throw new Error("Network response was not ok");return o.blob()}).then(o=>{const d=window.URL.createObjectURL(new Blob([o])),y=document.createElement("a");y.href=d,y.setAttribute("download",`${s}.xlsx`),document.body.appendChild(y),y.click(),document.body.removeChild(y)}).catch(o=>{console.error("Error exporting data:",o)})};return(s,a)=>(l(),c(v,null,[p(L(X),{title:"Bank Transaction"}),p(H,null,{default:m(()=>[e("div",te,[e("div",se,[e("div",oe,[e("h1",ae,i(r.bank.bank_name)+" - "+i(r.bank.account_number),1)]),e("p",ne,i(r.bank.organization.name),1),e("div",re,[e("div",le,[p(U,{href:s.route("banktransaction.index")},{default:m(()=>[g(" Back ")]),_:1},8,["href"])]),r.permissions.canCreateBankTransaction?(l(),c("div",ie,[p(U,{href:s.route("banktransaction.create",{id:r.bank.id})},{default:m(()=>[g(" Create Transaction ")]),_:1},8,["href"])])):h("",!0)])]),e("div",de,[e("div",ce,[e("div",me,[ue,p(E,{for:"company_id",value:"Filters"})]),e("div",_e,[e("div",pe,[he,e("span",ye,"₹"+i(r.current_balance.formatted_balance),1)]),e("button",{onClick:F},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"Export XLS"},null,8,fe)])])]),e("div",ge,[e("div",xe,[p(E,{for:"date",value:"From Date"}),S(e("input",{"onUpdate:modelValue":a[0]||(a[0]=t=>_.value=t),class:A(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":L(k).errors.from_date}]),type:"date",onChange:$},null,34),[[j,_.value]])]),e("div",ve,[p(E,{for:"date",value:"To Date"}),S(e("input",{"onUpdate:modelValue":a[1]||(a[1]=t=>f.value=t),class:A(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":L(k).errors.to_date}]),type:"date",onChange:$},null,34),[[j,f.value]])])])]),e("div",we,[e("div",be,[e("table",ke,[De,T.value&&T.value.length>0?(l(),c("tbody",Ce,[(l(!0),c(v,null,Y(T.value,t=>{var n,o,d,y;return l(),c("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Be,i(z(t.date)??"-"),1),e("td",Te,i(t.payment_type=="cr"?"Rcpt":"Pymt"),1),e("td",Le,[g(i(t.accounttype?`${t.accounttype.name} : ${t.note}`:t.note)+" ",1),e("span",Ee,[t.entity_type==="payment_receive"?(l(),c(v,{key:0},[g(i(((o=(n=t==null?void 0:t.payment_receive)==null?void 0:n.customers)==null?void 0:o.customer_name)??""),1)],64)):t.entity_type==="payment_paid"?(l(),c(v,{key:1},[g(i(((y=(d=t==null?void 0:t.payment_paid)==null?void 0:d.company)==null?void 0:y.name)??""),1)],64)):h("",!0),t.entity_type==="payment_receive"&&(t!=null&&t.payment_receive)?(l(),c(v,{key:2},[t.payment_receive.tds_amount>0?(l(),c("div",Ne," TDS: ₹"+i(t.payment_receive.tds_amount),1)):h("",!0),t.payment_receive.discount_amount>0?(l(),c("div",Me," Discount: ₹"+i(t.payment_receive.discount_amount),1)):h("",!0)],64)):h("",!0)])]),e("td",$e,i(t.payment_type=="cr"?B(t.amount):""),1),e("td",Se,i(t.payment_type=="dr"?B(t.amount):""),1),e("td",je,i(t.balance),1),e("td",Ae,[e("div",Ie,[t.entity_type==s.NULL||t.entity_type=="internal_transfer"?(l(),b(Z,{key:0,align:"right",width:"48"},{trigger:m(()=>[Ue]),content:m(()=>[r.permissions.canEditBankTransaction&&t.entity_type==s.NULL?(l(),b(I,{key:0,href:s.route("banktransaction.edit",{id:t.id})},{svg:m(()=>[Ve]),text:m(()=>[Pe]),_:2},1032,["href"])):h("",!0),r.permissions.canEditBankTransaction&&t.entity_type==="internal_transfer"?(l(),b(I,{key:1,href:s.route("editinternalbanktransfer",{id:t.id})},{svg:m(()=>[ze]),text:m(()=>[Fe]),_:2},1032,["href"])):h("",!0),r.permissions.canDeleteBankTransaction&&(t.entity_type==s.NULL||t.entity_type=="internal_transfer")?(l(),c("button",{key:2,type:"button",onClick:We=>V(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ye,8,Re)):h("",!0)]),_:2},1024)):h("",!0)])])])}),128))])):(l(),c("tbody",qe,He))])])]),r.data.data&&r.data.data.length>0?(l(),b(W,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):h("",!0)]),p(Q,{show:D.value,onClose:C},{default:m(()=>[e("div",Ze,[Je,e("div",Ke,[p(J,{onClick:C},{default:m(()=>[g(" Cancel ")]),_:1}),p(K,{class:"ml-3",onClick:P},{default:m(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},ct=ee(Qe,[["__scopeId","data-v-68b652de"]]);export{ct as default};
