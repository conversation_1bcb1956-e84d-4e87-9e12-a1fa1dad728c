<?php
namespace App\Mail;

use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Traits\ActivityTrait;
use App\Models\MailConfig; // Make sure to import the MailConfig model
use App\Models\User;

class SendInvoiceEmail extends Mailable
{
    use SerializesModels, ActivityTrait;

    public $emailData;
    public $pdfPath;

    protected static $logName = 'Send Email';

    public function __construct($emailData, $pdfPath)
    {
        $this->emailData = $emailData;
        $this->pdfPath = $pdfPath;
    }

    public function build()
    {
        $this->logActivity();

        $email = $this->subject($this->emailData['subject'])
                    ->view('emails.sendInvoiceEmail')
                    ->with([
                        'to' => $this->emailData['to'],
                        'from' => $this->emailData['from'],
                        'content' => $this->emailData['content'],
                    ])->attach($this->pdfPath, [
                        'as' =>  $this->emailData['pdf'],
                        'mime' => 'application/pdf',
                    ]);
        $toEmails = is_array($this->emailData['to']) ? $this->emailData['to'] : [$this->emailData['to']];
        $email->to($toEmails);

        if (!empty($this->emailData['cc']) && is_array($this->emailData['cc'])) {
            $email->cc(array_map('trim', $this->emailData['cc']));
        }
        return $email;
    }


    private function logActivity()
    {
        $user = User::find($this->emailData['user_id']);
    
        $subjectClass = $this->emailData['subject_type'];
        $subject = new $subjectClass();
        $subject->id = $this->emailData['subject_id'];
    
        $logDescription = $this->getLogDescription('sent');
    
        // Merge both "To" and "CC" addresses
        $toEmails = is_array($this->emailData['to']) ? $this->emailData['to'] : [$this->emailData['to']];
        $ccEmails = !empty($this->emailData['cc']) && is_array($this->emailData['cc']) ? $this->emailData['cc'] : [];
        $allRecipients = array_merge($toEmails, $ccEmails); // Combine "To" and "CC"
    
        activity(static::$logName)
            ->causedBy($user)
            ->performedOn($subject)
            ->event('sent')
            ->withProperties([
                'to' => $toEmails,
                'cc' => $ccEmails,
                'from' => $this->emailData['from'],
                'subject' => $this->emailData['subject'],
                'content' => $this->emailData['content'],
                'all_recipients' => $allRecipients, // Store all recipients
            ])
            ->log($logDescription);
    }
    

    public function getLogDescription(string $event): string
    {
        $toEmails = is_array($this->emailData['to']) ? $this->emailData['to'] : [$this->emailData['to']];
        $ccEmails = !empty($this->emailData['cc']) && is_array($this->emailData['cc']) ? $this->emailData['cc'] : [];
    
        // Combine all recipients (To + CC)
        $allRecipients = array_merge($toEmails, $ccEmails);
    
        $firstToEmail = $toEmails[0];
        $otherEmails = array_slice($allRecipients, 1); 
        $otherCount = count($otherEmails);
    
        // Create tooltip text for other emails
        $tooltipText = !empty($otherEmails) ? implode(', ', $otherEmails) : '';
    
        $othersDisplay = $otherCount > 0 ? " and <span title='{$tooltipText}'>other {$otherCount} </span>" : "";
    
        return "E-mail has been sent to <strong><span title='{$tooltipText}'>{$firstToEmail}</span></strong>{$othersDisplay} 
                from <strong>{$this->emailData['from']}</strong> by";
    }     
}
