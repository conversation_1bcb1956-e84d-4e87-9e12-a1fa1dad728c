<?php 

namespace App\DTO;

use App\Traits\ArrayToProps;

class JobcardDTO
{
    use ArrayToProps;

    public $job_card_number;
    public $engineer_id;
    public $hospital_name;
    public $address;
    public $city;
    public $contact_no;
    public $product_name;
    public $product_code;
    public $serial_no;
    public $accessories;
    public $problem_description;
    public $parts_required;
    public $date;
    public $warranty_status;
    public $close_note;
    public $close_by;
    public $close_date;
    public $customer_id;
    public $jobchecks;
    public $job_card_id;
    public $id;
    public $job_card_checklist_id;
    // public $created_by;
    public $updated_by;

}