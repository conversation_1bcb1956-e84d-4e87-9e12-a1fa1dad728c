<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\User;
use App\Models\Product;
use App\Models\ProformaInvoice;
use App\Models\Organization;
use App\Models\ProformaInvoiceDetails;
use App\Http\Requests\PiStoreRequest;
use App\Models\Document;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Traits\CommonTrait;
use App\Traits\QueryTrait;
use Config;
use PDF;

class ProformaInvoiceController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $salesUserId = $request->input('sales_user_id');
        $categoryId = $request->input('category');
        $createdBy = $request->input('created_by');
        $statusId = $request->input('status');
        $query  = ProformaInvoice::with('proformaInvoiceDetails.product.serialNumbers','customers', 'users', 'organization', 'quotation');
        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if($createdBy) {
            $query->where('created_by', $createdBy);
        }
        if($statusId) {
            $query->where('status', $statusId);
        }

        //for engineers only
        if(auth()->user()->can('Create Orders') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }

        if(!empty($search)){
            if(!empty($search)){
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })->orWhereHas('users', function ($subquery) use ($search) {
                    $subquery->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%$search%"]);
                })
                ->orWhere('order_number', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            }
        }

        $searchableFields = ['order_number', 'quotation.quotation_number', 'customers.customer_name', 'users.first_name', 'date', 'total_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Open', 'Close'];
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate(20);
        $administrationArr =  Organization::get()->toArray();
        $administration =  (!empty($administrationArr)) ? $administrationArr[0] : $administrationArr;
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $piStatus = Config::get('constants.proformaStatus');
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $category = Config::get('constants.quotationList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $organization->prepend($allOrganization);
        $customers->prepend($allCustomers);
        $salesuser->prepend($allSalesuser);
        $pagetypes = collect(Config::get('constants.pageTypes'));
        $data->withQueryString()->links();

        $permissions = [
            'canCreateOrders'      => auth()->user()->can('Create Orders'),
            'canEditOrders'        => auth()->user()->can('Edit Orders'),
            'canDeleteOrders'      => auth()->user()->can('Delete Orders'),
            'canViewOrders'        => auth()->user()->can('View Orders')
        ];
        return Inertia::render('ProformaInvoice/List', compact('data', 'permissions', 'administration', 'filepath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank', 'organization', 'customers', 'organizationId', 'customerId', 'salesuser', 'category', 'salesUserId', 'categoryId', 'createdBy', 'statusId', 'piStatus', 'pagetypes' ));
    }

    public function create()
    {
        $order_number = $this->generatePINumber();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.productCategoryList');
        $terms = Config::get('constants.termsAndConditions');
        return Inertia::render('ProformaInvoice/Add', compact( 'order_number','customers', 'salesuser', 'products', 'organization', 'category', 'terms'));
    }

    public function store(PiStoreRequest $request)
    {
        // dd($request);
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            if(isset($data['pi_id'])){
                $proformaInvoice = ProformaInvoice::findOrFail($data['pi_id']);
                $proformaInvoice->update([
                    'customer_id'       => $data['customer_id'],
                    'date'              => $data['date'],
                    'organization_id'   => $data['organization_id'],
                    'category'          => $data['category'],
                    'sales_user_id'     => $data['sales_user_id'],
                    'overall_discount'  => $data['overall_discount'],
                    'total_discount'    => $data['total_discount'],
                    'total_amount'      => $data['total_amount'],
                    'igst'              => $data['igst'],
                    'sgst'              => $data['sgst'],
                    'cgst'              => $data['cgst'],
                    'sub_total'         => $data['sub_total'],
                    'total_gst'         => $data['total_gst'],
                    'validity'          => $data['validity'],
                    'delivery'          => $data['delivery'],
                    'payment_terms'     => $data['payment_terms'],
                    'warranty'          => $data['warranty'],
                    'note'              => $data['note'],
                ]);
                foreach ($data['selectedProductItem'] as $piDetails) {
                    $piDetails['pi_id'] = $data['pi_id'];
                    $piDetails['gst'] = isset($piDetails['gst']) ? $piDetails['gst'] : 0;
                    if (!empty($piDetails['pi_details_id'])) {
                        $piDetails['updated_by'] = Auth::user()->id;
                        $quotationDetail = ProformaInvoiceDetails::find($piDetails['pi_details_id']);
                        $quotationDetail->update($piDetails);
                    } else {
                        $piDetails['created_by'] = $piDetails['updated_by'] = Auth::user()->id;
                        $piDetail = ProformaInvoiceDetails::create($piDetails);
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['pi_id']);
                }
                DB::commit();
                return Redirect::to('/proforma-invoice')->with('success','Proforma-Invoice Updated Successfully');
            } else {
                $data['status'] = "Open";
                $data['overall_discount'] = $data['overall_discount'] ?? 0;

                $piNumberByOrganization = $this->generatePINumber();
                $pi_number = $piNumberByOrganization[$data['organization_id']] ?? '';
                $data['order_number'] = $pi_number;

                // $this->updatePINumber($data['order_number'], $data['organization_id']);
                $proformaInvoice = ProformaInvoice::create($data);
                if($proformaInvoice){
                    foreach ($data['selectedProductItem'] as $piDetails) {
                        $piDetails['gst'] = isset($piDetails['gst']) ? $piDetails['gst'] : 0;
                        $piDetails['pi_id'] = $proformaInvoice->id;
                        $piDetails['created_by'] = $piDetails['updated_by'] = Auth::user()->id;
                        $piDetail = ProformaInvoiceDetails::create($piDetails);
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $proformaInvoice->id);
                }
                
                $this->updatePINumber($pi_number, $data['organization_id']);

                DB::commit();
                return Redirect::to('/proforma-invoice')->with('success','Proforma-Invoice Created Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/proforma-invoice')->with('error',$e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data     = ProformaInvoice::where('id', $id)->with('proformaInvoiceDetails.product.serialNumbers', 'customers', 'documents', 'organization')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.piDocument');
        $customers= Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser= User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price")->get();
        $category = Config::get('constants.productCategoryList');
        $organization  = Organization::select('id', 'name')->get();
        return Inertia::render('ProformaInvoice/Edit', compact('data', 'filepath', 'customers', 'salesuser', 'products', 'category', 'organization'));
    }

    public function view(Request $request, $id)
    {
        $data = ProformaInvoice::where('id', $id)->with('proformaInvoiceDetails.product.serialNumbers', 'customers', 'organization',  'documents', 'users')->get()->toArray();
        // $orderDeliver = OrderDeliver::where('order_id', $id)->with('proformaInvoiceDetails.product')->get()->groupBy('order_deliver_number')->toArray();
        $filepath = Config::get('constants.uploadFilePath.piDocument');
        return Inertia::render('ProformaInvoice/View', compact('data',  'filepath'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $proformaInvoice = ProformaInvoice::find($id);
            if($proformaInvoice) {
                $piDetail = ProformaInvoiceDetails::where('pi_id', $id)->get();
                foreach ($piDetail as $detail) {
                    $detail->delete();
                }
                $proformaInvoice->delete();
            }
            DB::commit();
            return Redirect::back()->with('message','Proforma Invoice Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('message','Something want wrong');
        }
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.piDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "proforma_invoice";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

        public function downloadPI($id, $type)
        {
            $data = ProformaInvoice::with('proformaInvoiceDetails.product.serialNumbers', 'customers', 'documents', 'organization')->where('id', $id)->get();
            $filepath = Config::get('constants.uploadFilePath.companyDocument');
            $quotationbank = Config::get('constants.quotationBankinfo');
            $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
            $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
            $pdf = PDF::loadView('pdf.proformaInvoice', compact('data', 'filepath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank'))->setPaper('A4', $type);
            $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
            return $pdf->download("Proforma_invoice_{$sanitizedFilename}.pdf");
            // return $pdf->stream();
        }

        public function rejectPi($id)
        {
            $quotation = ProformaInvoice::findOrFail($id);
            $quotation->status = 'Close';
            $quotation->save();
            return redirect()->back()->with('success', 'Status Change successfully');
        }
}
