<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    const STATUS_ACTIVE = 1;

    protected $table = 'companies';

    protected static $logName = 'Company';

    public function getLogDescription(string $event): string
    {
        return "<strong>{$this->name}</strong> Company has been {$event} by";
    }

    protected static $logAttributes = [
        'name',
        'address',
        'city',
        'state',
        'gst_no',
        'email',
        'drug_licence_no',
        'website',
        'contact_no',
        'status',
        'company_type',
        'gst_type',
         'party_id'
    ];

    protected $fillable = [
        'name',
        'address',
        'city',
        'state',
        'gst_no',
        'email',
        'drug_licence_no',
        'website',
        'contact_no',
        'description',
        'status',
        'company_type',
        'gst_type',
        'created_by',
        'updated_by',
        'party_id'
    ];

    public function products()
    {
        return $this->hasMany(Product::class,'company_id','id');
    }

    public function companyTransactions()
    {
        return $this->hasMany(PurchaseTransaction::class, 'company_id', 'id');
    }

}
