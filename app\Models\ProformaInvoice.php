<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProformaInvoice extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'proforma_invoice';

    protected static $logName = 'Proforma-Invoice';

    public function getLogDescription(string $event): string
    {
        $customerName = optional($this->customer)->customer_name ?? 'Unknown Customer';

        return "<strong>{$customerName}'s</strong> Proforma invoice <strong>{$this->order_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_id',
        'order_number',
        'date',
        'status',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_id',
        'order_number',
        'date',
        'status',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];


    public function organization()
    {
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function quotation()
    {
        return $this->belongsTo(Quotation::class,'quotation_id','id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id'); // Adjust 'customer_id' if your foreign key is named differently
    }

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function users(){
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function proformaInvoiceDetails()
    {
        return $this->hasMany(ProformaInvoiceDetails::class,'pi_id','id');
    }
    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'proforma_invoice');
    }

}
