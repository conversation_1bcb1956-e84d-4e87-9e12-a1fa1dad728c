<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head , Link, useForm, usePage} from '@inertiajs/vue3';

const userData = usePage().props.data;

defineProps({
    data: {
        type: Object,
    },
    inquiryType: {
        type: Object,
    },
    months: {
        type: Object,
    }
});

const form = useForm({
    id: userData.id,
    customer_name: userData.customer_name,
    dr_name: userData.dr_name,
    place: userData.place,
    mobile_number: userData.mobile_number,
    company: userData.company,
    product: userData.product,
    order_value: userData.order_value,
    order_month: userData.order_month,
    inquiry_type: userData.inquiry_type,
    close_date: userData.close_date,
    status: userData.status,
});

const setInquiryType = (id, name) => {
    form.inquiry_type = id;
    form.errors.inquiry_type = null;
};

const setMonth = (id, name) => {
    form.order_month = id;
};

</script>

<template>
    <Head title="Edit Funnel" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Funnel</h2>
            <form @submit.prevent="form.patch(route('funnel.update'))">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-3">
                            <InputLabel for="customer_name" value="Customer Name" />
                            <TextInput
                                id="customer_name"
                                type="text"
                                v-model="form.customer_name"
                            />
                            <InputError  class="" :message="form.errors.customer_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="dr_name" value="Doctor Name" />
                            <TextInput
                                id="dr_name"
                                type="text"
                                v-model="form.dr_name"
                            />
                            <InputError  class="" :message="form.errors.dr_name" />
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="place" value="Place/City" />
                            <TextInput
                                id="place"
                                type="text"
                                v-model="form.place"
                            />
                            <InputError  class="" :message="form.errors.place" />
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="mobile_number" value="Mobile Number" />
                            <TextInput
                                id="mobile_number"
                                type="text"
                                :numeric="true"
                                maxLength="10"
                                v-model="form.mobile_number"
                            />
                            <InputError  class="" :message="form.errors.mobile_number" />
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="company" value="Company" />
                            <TextInput
                                id="company"
                                type="text"
                                v-model="form.company"
                            />
                            <InputError  class="" :message="form.errors.company" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product" value="Products" />
                            <TextInput
                                id="product"
                                type="text"
                                v-model="form.product"
                            />
                            <InputError class="" :message="form.errors.product" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="order_value" value="Order Value" />
                            <TextInput
                                id="order_value"
                                type="text"
                                v-model="form.order_value"
                            />
                            <InputError  class="" :message="form.errors.order_value" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="customer_id" value="Estimated Month Of Order" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="months"
                                    v-model="form.order_month"
                                    @onchange="setMonth"
                                    />
                                </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="customer_id" value="Inquiry Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="inquiryType"
                                    v-model="form.inquiry_type"
                                    @onchange="setInquiryType"
                                    />
                            </div>
                        </div>


                        <div class="sm:col-span-6">
                            <InputLabel for="status" value="Latest Status" />
                            <TextArea
                                id="status"
                                type="text"
                                :rows="3"
                                v-model="form.status"
                            />
                            <InputError  class="" :message="form.errors.status" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('funnel.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>



                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>
