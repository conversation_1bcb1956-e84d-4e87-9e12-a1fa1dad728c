<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Jobcard extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'job_cards';

    protected static $logName = 'Jobcard';

    public function getLogDescription(string $event): string
    {
        return "Jobcard <strong>{$this->job_card_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'job_card_number',
        'engineer_id',
        'hospital_name',
        'address',
        'city',
        'contact_no',
        'product_name',
        'product_code',
        'serial_no',
        'accessories',
        'problem_description',
        'parts_required',
        'warranty_status',
        'date',
        'job_status',
        'status',
        'close_note',
        'close_by',
        'close_date',
        'created_by',
        'updated_by',
    ];
    protected $fillable = [
        'job_card_number',
        'engineer_id',
        'hospital_name',
        'address',
        'city',
        'contact_no',
        'product_name',
        'product_code',
        'serial_no',
        'accessories',
        'problem_description',
        'parts_required',
        'date',
        'job_status',
        'warranty_status',
        'status',
        'close_note',
        'close_by',
        'close_date',
        'created_by',
        'updated_by',
    ];

    public function jobCardChecks()
    {
        return $this->hasMany(JobcardCheck::class ,'job_card_id','id');
    }

    public function users(){
        return $this->belongsTo(User::class,'engineer_id','id');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'jobcard');
    }

    public function quotationDocuments()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'jobcard_quotation');
    }

    public function poDocuments()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'jobcard_po');
    }

    public const DOCUMENT_TYPE = 'jobcard';
    public const QUOTATION_DOCUMENT_TYPE = 'jobcard_quotation';
    public const PO_DOCUMENT_TYPE = 'jobcard_po';
}
