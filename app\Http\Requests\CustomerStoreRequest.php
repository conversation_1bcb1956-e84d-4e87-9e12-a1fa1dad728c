<?php

namespace App\Http\Requests;

use App\DTO\CustomerDTO;
use Support\Contracts\HasDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use App\Models\Customer;

class CustomerStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // return [
        //     'customer_name'     => ['required','string','max:255'],
        //     //'email'             => ['string','email'],
        //     // 'contact_no'        => ['required','numeric', 'digits:10'],
        //     'address'           => ['required','string', 'max:500'],
        //     'customer_type'     => ['required','string'],
        //     'gst_type'          => ['required','string'],
        //     'is_organization'   => ['required','string'],
        //     // 'organization_id'   => ['required','string'],
        //     //'person_name'       => ['string','max:255'],
        //     'city'              => ['required'],
        //     //'drug_licence_no'   => ['string','max:255'],
        //     'gst_no'          => ['required', 'string', 'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$/'], // GST validation
        //     //'description'       => ['string','max:500'],
        //     'type'              => ['required','max:255']
        // ];



        $rules = [
            'customer_name'     => ['required','string','max:255'],
            //'email'             => ['string','email'],
            // 'contact_no'        => ['required','numeric', 'digits:10'],
            'address'           => ['required','string', 'max:500'],
            'customer_type'     => ['required','string'],
            'gst_type'          => ['required','string'],
            'is_organization'   => ['required','string'],
            // 'organization_id'   => ['required','string'],
            //'person_name'       => ['string','max:255'],
            'city'              => ['required'],
            //'drug_licence_no'   => ['string','max:255'],
            // 'gst_no'          => ['required', 'string', 'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$/'], // GST validation
            //'description'       => ['string','max:500'],
            'type'              => ['required','max:255']
        ];

        if($this->input('customer_type') == 'Tax') {
            $rules['gst_no'] = ['required', 'string', 'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[A-Z]{1}[A-Z0-9]{1}$/'];

        }

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $name = $this->input('customer_name');
            $normalized_name = Str::lower(preg_replace('/\s+/', '', $name));
            $city = $this->input('city');

            if (empty($this->input('id'))) {
                $existingProduct = Customer::where(function ($query) use ($normalized_name, $city) {
                    $query->whereRaw("LOWER(REPLACE(customer_name, ' ', '')) = ?", [$normalized_name])
                          ->where('city', $city);
                })
                ->exists();

                if ($existingProduct) {
                    $validator->errors()->add('customer_name', __('Customer Already Exists'));
                }
            } else {
                $existingProduct = Customer::where(function ($query) use ($normalized_name, $city) {
                    $query->whereRaw("LOWER(REPLACE(customer_name, ' ', '')) = ?", [$normalized_name])
                          ->where('city', $city);
                })
                ->where('id', '!=', $this->input('id'))
                ->exists();

                if ($existingProduct) {
                    $validator->errors()->add('customer_name', __('Customer Already Exists'));
                }
            }
        });

    }

    public function DTO()
    {
        return CustomerDTO::LazyFromArray($this->input());
    }

}
