<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import Modal from '@/Components/Modal.vue';
import { Head , useForm} from '@inertiajs/vue3';

const props = defineProps(['data']);

const form = useForm({});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    // let amountStr = amount.toFixed(2).toString();
    if (amount == null || isNaN(amount)) {
        return "0.00";  // Default value
    }
    let amountStr = Number(amount).toFixed(2); // Ensure it's a number before calling toFixed
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const deleteTransactionModal = ref(false);
const selectedTransactionId = ref(null);

const openDeleteModal = (id) => {
  selectedTransactionId.value = id;
  deleteTransactionModal.value = true;
};

const deleteTransaction = () => {
    // form.get(route('removetransaction',{id:selectedTransactionId.value}), {
    //     onSuccess: () => {
    //     closeTransactionModal()
    //     }
    // });
};

const closeTransactionModal = () => {
    deleteTransactionModal.value = false;
};

const backRoute = computed(() => {
    const source = new URLSearchParams(window.location.search).get('source');
    if (source === 'dashboard') {
        return route('dashboard');
    }
    return route('invoice.index');
});
</script>

<template>
    <Head title="Invoice"/>

    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Invoice Detail</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                     <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="backRoute">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Customer Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.customer_name }} - {{ data[0].customers.city }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.gst_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Email:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.email ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.contact_no ?? '-' }}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Invoice Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].invoice_no }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Invoice Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Category:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].category }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-32">Sales Person:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name }} {{ data[0].users.last_name }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Sr No</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Code</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Product Name</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">HSN</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Qty</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">batch</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Price (₹)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Price (₹)</th>
                        <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (%)</th>
                        <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (₹)</th>
                        <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">CGST (%)</th>
                        <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">SGST (%)</th>
                        <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total GST (₹)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Discount (%)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Discount (₹)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Amount (₹)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr  class="odd:bg-white even:bg-gray-50 border-b" v-for="(product, index)  in data[0].invoice_detail" :key="index">
                        <td class="px-4 py-2.5 min-w-20">{{ index + 1 }}</td>
                        <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-30">{{ product.product.item_code ?? '-'  }}</td>
                        <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-normal min-w-60">{{ product.product.name }}</td>
                        <td class="px-4 py-2.5 font-medium whitespace-nowrap min-w-32">{{ product.hsn_code }}</td>
                        <td class="px-4 py-2.5">{{ product.qty }}</td>
                        <td class="px-4 py-2.5 min-w-36">{{product.serialnumbers.batch ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-24">{{ parseFloat(product.price).toFixed(2) }}</td>
                        <td class="px-4 py-2.5 min-w-24">{{ parseFloat(product.total_price).toFixed(2) }}</td>
                        <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ product.gst }}</td>
                        <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ formatAmount(product.total_gst_amount) }}</td>
                        <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                        <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                        <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-32">{{ formatAmount(product.total_gst_amount) }}</td>
                        <td class="px-4 py-2.5 min-w-36">{{ product.discount }}</td>
                        <td class="px-4 py-2.5 min-w-32">{{ formatAmount(product.discount_amount) }}</td>
                        <td class="px-4 py-2.5 min-w-44">{{ formatAmount(product.total_amount) }}</td>
                    </tr>
                    <tr class="bg-white border-b">
                        <th class="px-4 py-2.5 min-w-20"></th>
                        <th class="px-4 py-2.5 min-w-30"></th>
                        <th class="px-4 py-2.5 min-w-60"></th>
                        <th class="px-4 py-2.5 min-w-32"></th>
                        <th class="px-4 py-2.5"></th>
                        <th class="px-4 py-2.5 min-w-36"></th>
                        <th class="px-4 py-2.5 min-w-24"></th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-36">{{ formatAmount(data[0].sub_total) }}</th>
                        <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24"></th>
                        <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-30">{{ formatAmount(data[0].total_gst) }}</th>
                        <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24"></th>
                        <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24"></th>
                        <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-22">{{ formatAmount(data[0].total_gst) }}</th>
                        <th class="px-4 py-2.5 min-w-32"></th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-22">{{ formatAmount(data[0].total_discount) }}</th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-44">{{  formatAmount(data[0].total_amount) }}</th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
        <!-- <div class="mt-6 sm:flex sm:items-center">
            <h1 class="text-2xl font-semibold leading-7 text-gray-900" v-if="(data[0].payment_receive && data[0].payment_receive.length != 0)">Payment History List</h1>
        </div>
        <div class="mt-8 flow-root" v-if="data[0].payment_receive.length > 0">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8" style="min-height:500px">
                    <div class="p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        <table  class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50 border">
                                <tr>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Bank</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Payment Type</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Check No</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Amount (₹)</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Action</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-300 bg-white" v-if="data[0].payment_receive">
                                <tr v-for="(poData, index) in data[0].payment_receive" :key="poData.id" class="">
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ formatDate(poData.date) }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ poData.bank_info.bank_name  ?? '' }} - {{ poData.bank_info.account_number ?? ''}}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ poData.payment_type }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ poData.check_number ?? '-' }}</td>
                                    <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900">{{ formatAmount(poData.amount) }}</td>
                                    <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                        <button type="button" @click="openDeleteModal(poData.id)">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div> -->
        </div>
        <Modal :show="deleteTransactionModal" @close="closeTransactionModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this transaction?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeTransactionModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteTransaction"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>
