<?php

namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Funnel;
use App\Models\User;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\FunnelRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Config;

class FunnelController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Funnel')->only(['index']);
        $this->middleware('permission:Create Funnel')->only(['create', 'store']);
        $this->middleware('permission:Edit Funnel')->only(['edit', 'update']);
        $this->middleware('permission:Delete Funnel')->only('destroy');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $userId = $request->input('user_id');
        $inquiry_type = $request->input('inquiry_type');
        $userInfo = Auth()->user();

        $query = Funnel::with('users');;


        if(auth()->user()->can('Filter Funnel') != true){
            $query->where('created_by', $userInfo->id);
        }

        if($inquiry_type) {
            $query->where('inquiry_type', $inquiry_type);
        }

        if($userId) {
            $query->where('created_by', $userId);
        }

        $user = User::where(['status' => '1'])->whereIn('role_id', [6,7,8])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        // $searchableFields = ['customer_name', 'dr_name', 'company', 'product'];

        $searchableFields = [
            'users.first_name','customer_name', 'dr_name', 'place', 'mobile_number', 'company',
            'product', 'inquiry_type', 'order_value', 'order_month', 'status'
        ];
        $this->searchAndSort($query, $request, $searchableFields);

        $searchText = $request->input('search');
        if (!empty($searchText)) {
            $query->where(function ($subQuery) use ($searchableFields, $searchText) {
                foreach ($searchableFields as $field) {
                    if ($field === 'users.first_name') {
                        $subQuery->orWhereHas('users', function ($q) use ($searchText) {
                            $q->where('first_name', 'like', "%$searchText%");
                        });
                    } else {
                        $subQuery->orWhere($field, 'like', "%$searchText%");
                    }
                }
            });
        }
        $statusOrder = ['Hot', 'Cold', 'Warm', 'Close'];
        $query->orderByRaw("FIELD(inquiry_type, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate(20);
        $data->withQueryString()->links();
        $inquiryTypes = Config::get('constants.inquiryTypes');
        $allUsers = ['id' => null, 'name' => 'ALL USERS'];
        $user->prepend($allUsers);
        $permissions = [
            'canCreateFunnel' => auth()->user()->can('Create Funnel'),
            'canEditFunnel'   => auth()->user()->can('Edit Funnel'),
            'canDeleteFunnel' => auth()->user()->can('Delete Funnel'),
            'canFilterFunnel' => auth()->user()->can('Filter Funnel')
        ];
        return Inertia::render('Funnel/List', compact('data', 'user', 'inquiryTypes','inquiry_type', 'userId', 'userInfo', 'permissions'));
    }

    public function create()
    {
        $inquiryType = Config::get('constants.funnelStatus');
        $months = Config::get('constants.months');
        return Inertia::render('Funnel/Add', compact('inquiryType', 'months'));
    }

    public function store(FunnelRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::id();
            Funnel::create($data);
            DB::commit();
            return Redirect::to('/funnel')->with('success', 'Funnel Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Funnel::findOrFail($id);
        $inquiryType = Config::get('constants.funnelStatus');
        $months = Config::get('constants.months');
        session(key: ['funnel_list_page_url' => url()->previous()]);
        return Inertia::render('Funnel/Edit', compact('data', 'inquiryType', 'months'));
    }

    public function update(FunnelRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = Auth::id();
            $company = Funnel::findOrFail($request->id);
            $company->update($data);
            DB::commit();
            $redirectUrl = session('funnel_list_page_url', route('funnel.index'));
            session()->forget('funnel_list_page_url');
            return Redirect::to($redirectUrl)->with('success', 'Funnel Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $company = Funnel::findOrFail($id);
            $company->delete();
            DB::commit();
            return Redirect::to('/funnel')->with('success', 'Funnel Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/funnel')->with('error', $e->getMessage());
        }
    }

    public function changeStatus(Request $request)
    {
        DB::beginTransaction();
        try {
            $company = Funnel::findOrFail($request->id);
            $company->close_date = date('Y-m-d');
            $company->inquiry_type = 'Close';
            $company->save();
            DB::commit();
            return Redirect::to('/funnel')->with('success', 'funnel Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/funnel')->with('error', $e->getMessage());
        }
    }

}
