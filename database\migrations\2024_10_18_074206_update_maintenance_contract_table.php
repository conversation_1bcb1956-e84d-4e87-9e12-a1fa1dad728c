<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('maintenance_contract', function (Blueprint $table) {
            $table->string('pm_date_3')->nullable()->after('pm_date_2');
            $table->string('pm_date_4')->nullable()->after('pm_date_3');
            $table->string('price')->nullable()->after('product_name');
            $table->dropColumn('serial_no');
            $table->dropColumn('product_code');
            $table->dropColumn('qty');
            $table->string('company_name')->nullable()->change();
            $table->string('invoice_number')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('maintenance_contract', function (Blueprint $table) {
            $table->dropColumn(['pm_date_3', 'pm_date_4', 'price']);
            $table->string('serial_no')->nullable()->after('product_name');
            $table->string('product_code')->nullable()->after('serial_no');
            $table->string('qty')->nullable()->after('product_code');

        });

    }
};
