<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AccountType extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'account_types';

    protected static $logName = 'Account Type';

    public function getLogDescription(string $event): string
    {
        return "Account type has been {$event} for <strong>{$this->name}</strong> by";
    }

    protected static $logAttributes = [
        'name',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'name',
        'created_by',
        'updated_by'
    ];

}
