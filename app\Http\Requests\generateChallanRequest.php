<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class generateChallanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            //
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $selectedProductItem  = $this->input('selectedProductItem');
            // dd($selectedProductItem);
            $acceptedQtys = 0;
            foreach ($selectedProductItem  as $key => $product) {
                if($product['check']){
                    $acceptedQtys++;
                }
                if ($product['check'] && $product['sell_price'] == '') {
                    $validator->errors()->add("selectedProductItem.$key.sell_price", __('price is Required.'));
                }
                if ($product['qty'] + $product['invoiced_qty'] + $product['return_qty']> $product['challan_qty']) {
                    $validator->errors()->add("selectedProductItem.$key.qty", __('qty not match with challan qty'));
                }
            }
            if ($acceptedQtys == 0) {
                $validator->errors()->add("selectedProductItem.$key.check", __('At least one qty must be Checked.'));
            }
        });
    }
}
