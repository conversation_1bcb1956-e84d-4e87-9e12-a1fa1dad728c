<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class CompanyTransactionExport implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles
{
    protected $transactions;
    protected $organizationName;
    protected $companyName;
    protected $fromDate;
    protected $toDate;

    public function __construct($transactions, $organizationName = null, $companyName, $fromDate, $toDate)
    {
        $this->transactions = $transactions;
        $this->organizationName = $organizationName;
        $this->companyName = $companyName;
        $this->fromDate = $fromDate ? Carbon::parse($fromDate)->format('d-m-Y') : 'N/A';
        $this->toDate = $toDate ? Carbon::parse($toDate)->format('d-m-Y') : 'N/A';
    }

    public function collection()
    {
        $data = [];
        $balance = 0;
        $sno = 1;
        $totalBalance = 0;

        foreach ($this->transactions as $transaction) {
            $amount = $transaction->amount;
            $isCredit = $transaction->payment_type === 'cr';
            $debitAmount = $isCredit ? '' : $amount;
            $creditAmount = $isCredit ? abs($amount) : '';
            $balance = $isCredit ? $balance + abs($amount) : $balance - $amount;
            $totalBalance = $balance;

            $data[] = [
                'Sno'       => $sno++,
                'Date'      => Carbon::parse($transaction->date)->format('d-m-Y'),
                'Narration' => $transaction->note,
                'Debit (₹)' => $debitAmount,
                'Credit (₹)'=> $creditAmount,
                'Balance (₹)' => number_format(abs($balance), 2, '.', ''),
            ];
        }

        $data[] = [
            'Sno'         => 'TOTAL',
            'Date'        => '',
            'Narration'   => '',
            'Debit (₹)'   => '',
            'Credit (₹)'  => '',
            'Balance (₹)' => number_format(abs($totalBalance), 2, '.', ''),
        ];

        return collect($data);
    }

    public function headings(): array
    {
        return [
            ["{$this->organizationName}"],
            ["Date: {$this->fromDate} To: {$this->toDate}"],
            ['company: ' . ($this->companyName ?? 'Unknown company')],
            ['Sno', 'Date', 'Narration', 'Debit (₹)', 'Credit (₹)', 'Balance (₹)'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_XLSX15,
            'D' => NumberFormat::FORMAT_NUMBER_00,
            'E' => NumberFormat::FORMAT_NUMBER_00,
            'F' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function styles($sheet)
    {
        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');
        $sheet->mergeCells('A3:F3');

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(13);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A4:F4')->getFont()->setBold(true);
        $sheet->getStyle('A4:F4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4:F4')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');

        $totalRows = count($this->transactions) + 5;

        $sheet->getStyle("A4:F$totalRows")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle("A5:F$totalRows")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        foreach (['A', 'B', 'C', 'D', 'E', 'F'] as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $sheet->getStyle("A$totalRows:F$totalRows")->getFont()->setBold(true);
        $sheet->getStyle("A$totalRows:F$totalRows")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFFF00');
        $sheet->getStyle("A$totalRows:F$totalRows")->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        return $sheet;
    }
}
