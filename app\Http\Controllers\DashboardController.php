<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Jobcard;
use App\Models\Orders;
use App\Models\Quotation;
use App\Models\Organization;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderReceives;
use Illuminate\Support\Facades\DB;


class DashboardController extends Controller
{

    public function dashboard(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $permissions = auth()->user()->getAllPermissions()->pluck('name');
        $permissions = json_encode($permissions);
        $user = auth()->user();

        $currentDate = now();

        // Determine the current fiscal year based on the current month
        if ($currentDate->month < 4) {
            $startDate = $currentDate->copy()->subYear()->startOfYear()->addMonths(3); // April 1st of the previous year
            $endDate = $currentDate->copy()->endOfYear()->addMonths(3); // March 31st of the current year
        } else {
            $startDate = $currentDate->copy()->startOfYear()->addMonths(3); // April 1st of the current year
            $endDate = $currentDate->copy()->startOfYear()->addYear()->addMonths(2)->endOfMonth(); // March 31st of the next year
        }

        $startDateString = $startDate->toDateString();
        $endDateString = $endDate->toDateString();

        $quotationCount = Quotation::whereBetween('date', [$startDateString, $endDateString])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })->count();

        $ordersCount = Orders::whereBetween('date', [$startDateString, $endDateString])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })->count();

        $purchaseCount = PurchaseOrder::whereBetween('date', [$startDateString, $endDateString])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })->count();

        $jobCardCount = Jobcard::whereBetween('date', [$startDateString, $endDateString])->count();


        $taxInvoiceCount = Invoice::when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })
            ->where('invoice_type', 'tax')
            ->whereBetween('date', [$startDateString, $endDateString])
            ->count();

        $retailInvoiceCount = Invoice::when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })
            ->where('invoice_type', 'retail')
            ->whereBetween('date', [$startDateString, $endDateString])
            ->count();

        $companyCount = Company::count();
        $productCount = Product::count();
        $customerCount = Customer::count();


        $recentInvoices = Invoice::with('customers')->when($organizationId, function($query) use ($organizationId) {
            return $query->where('organization_id', $organizationId);
        })->latest()->limit(10)->get();

        $recentQuotations = Quotation::with('customers')->when($organizationId, function($query) use ($organizationId) {
            return $query->where('organization_id', $organizationId);
        })->latest()->limit(10)->get();

        $recentPurchaseOrder = PurchaseOrderReceives::with('purchaseOrder.company')
        ->when($organizationId, function($query) use ($organizationId) {
            return $query->whereHas('purchaseOrder', function($q) use ($organizationId) {
                $q->where('organization_id', $organizationId);
            });
        })->latest()->limit(10)->get();

        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);

        $salesDataByMonth = array_fill(0, 12, 0);
        $salesByMonth = Invoice::selectRaw('MONTH(date) as month, SUM(total_amount) as total')
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })
            ->whereBetween('date', [$startDateString, $endDateString])
            ->groupBy('month')
            ->pluck('total', 'month');
        foreach ($salesByMonth as $month => $total) {
            $adjustedMonth = ($month + 8) % 12;
            $salesDataByMonth[$adjustedMonth] = $total;
        }

        $purchaseDataByMonth = array_fill(0, 12, 0);
        $purchaseByMonth = PurchaseOrderReceives::selectRaw('MONTH(po_receive_date) as month, SUM(total_amount) as total')
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('purchaseOrder', function($q) use ($organizationId) {
                    $q->where('organization_id', $organizationId);
                });
            })
            ->whereBetween('customer_invoice_date', [$startDateString, $endDateString])
            ->groupBy('month')
            ->pluck('total', 'month');
        foreach ($purchaseByMonth as $month => $total) {
            $adjustedMonth = ($month + 8) % 12;
            $purchaseDataByMonth[$adjustedMonth] = $total;
        }

        $salesData = Invoice::with('users')->whereNotNull('sales_user_id')->whereBetween('date', [$startDateString, $endDateString])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })->select('sales_user_id', DB::raw('SUM(sub_total) as total_sales'))->groupBy('sales_user_id')->get()->toArray();

        $pieChartData = [
            'labels' => [],
            'datasets' => []
        ];
        $pieChartData['datasets'][0]['data'] = [];

        foreach ($salesData as $sale) {
            $pieChartData['labels'][] = $sale['users']['first_name'].' '.$sale['users']['last_name'];
            $pieChartData['datasets'][0]['data'][] = $sale['total_sales'];
        }

        $productsWithLowStock = Product::join(DB::raw('(SELECT product_id, SUM(receive_qty) - SUM(sell_qty) AS stock
            FROM serial_numbers GROUP BY product_id) AS sn'), 'products.id', '=', 'sn.product_id')
            ->leftJoin('companies', 'companies.id', 'products.company_id')
            ->whereColumn('sn.stock', '<', 'products.min_qty')
            ->select('products.*', 'companies.name as company_name', 'sn.stock');

        $productsWithLowStock = $productsWithLowStock->orderBy('products.id', 'desc')->paginate(10);

        // Query sales data by engineer (sales_user_id) and month
        $salesEngData = Invoice::with('users')->selectRaw('sales_user_id, MONTH(date) as month, SUM(sub_total) as total_sales')
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->where('organization_id', $organizationId);
            })
            ->whereBetween('date', [$startDateString, $endDateString])
            ->groupBy('sales_user_id', 'month')
            ->get();

        $salesEngdatasets = [];
        $colors = [
            'rgba(255, 99, 132, 1)',     // Red
            'rgba(54, 162, 235, 1)',     // Blue
            'rgba(255, 206, 86, 1)',     // Yellow
            'rgba(75, 192, 192, 1)',     // Green
            'rgba(153, 102, 255, 1)',    // Purple
            'rgba(201, 203, 207, 1)',    // Gray
            'rgba(255, 159, 64, 1)',     // Orange
            'rgba(100, 149, 237, 1)',    // Cornflower Blue
            'rgba(255, 105, 180, 1)',    // Hot Pink
            'rgba(60, 179, 113, 1)',     // Medium Sea Green
            'rgba(147, 112, 219, 1)',    // Medium Purple
            'rgba(0, 191, 255, 1)',      // Deep Sky Blue
            'rgba(220, 20, 60, 1)',      // Crimson
            'rgba(255, 140, 0, 1)',      // Dark Orange
            'rgba(70, 130, 180, 1)',     // Steel Blue
            'rgba(199, 21, 133, 1)'      // Medium Violet Red
        ];
        $colorIndex = 0;
        $salesEngDataGrouped = $salesEngData->groupBy('sales_user_id');
        // dd($salesEngDataGrouped);
        foreach ($salesEngDataGrouped as  $salesEntries) {
            $name = $salesEntries[0] ? $salesEntries[0]->users->first_name . ' ' . $salesEntries[0]->users->last_name : 'Engineer ';
            $monthlySales = array_fill(0, 12, 0);
            foreach ($salesEntries as $entry) {
                $month = (int) $entry->month;
                $adjustedMonth = ($month + 8) % 12;
                $monthlySales[$adjustedMonth] = (float) $entry->total_sales;
            }
            $monthlySales = array_values(array_filter($monthlySales, fn($val) => $val != 0));
            $salesEngdatasets[] = [
                'label' => $name,
                'data' => $monthlySales,
                'borderColor' => $colors[$colorIndex % count($colors)], // Assign color
                'backgroundColor' => $colors[$colorIndex % count($colors)], // Assign color
                'fill' => false,
                'tension' => 0.4
            ];
            $colorIndex++;  // Increment color index for next engineer
        }
        return Inertia::render('Dashboard', compact(
            'organizationId',
            'permissions',
            'productsWithLowStock',
            'pieChartData',
            'salesDataByMonth',
            'purchaseDataByMonth',
            'user',
            'recentInvoices',
            'recentQuotations',
            'recentPurchaseOrder',
            'productCount',
            'customerCount',
            'ordersCount',
            'purchaseCount',
            'jobCardCount',
            'taxInvoiceCount',
            'retailInvoiceCount',
            'companyCount',
            'quotationCount',
            'organization',
            'salesEngdatasets'
        ));
    }
    }
