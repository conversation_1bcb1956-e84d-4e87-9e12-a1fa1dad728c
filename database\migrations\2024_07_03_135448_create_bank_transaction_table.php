<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_transaction', function (Blueprint $table) {
            $table->id();
            $table->integer('org_bank_id');
            $table->enum('payment_type', ['cr', 'dr']);
            $table->double('amount', 16, 2);
            $table->integer('account_type')->nullable();
            $table->longText('note')->nullable();
            $table->date('date');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_transaction');
    }
};
