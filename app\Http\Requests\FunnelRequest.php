<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\FunnelDTO;
use Support\Contracts\HasDTO;

class FunnelRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name' => ['required','string','max:255'],
            'place'         => ['required','string','max:500'],
            'product'       => ['required','string','max:255'],
            'company'       => ['required'],
            'status'        => ['required'],
            'inquiry_type'  => ['required'],
            'order_value'   => ['required','numeric'],
        ];
    }

    public function DTO()
    {
        return FunnelDTO::LazyFromArray($this->input());
    }

}
