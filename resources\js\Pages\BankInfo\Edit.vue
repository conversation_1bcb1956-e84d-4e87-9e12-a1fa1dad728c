<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';

const props = defineProps(['data', 'organization', 'amounttype']);

const userData = usePage().props.data;

const form = useForm({
    id: userData.id,
    organization_id: userData.organization_id,
    bank_name: userData.bank_name,
    account_number: userData.account_number,
    balance: userData.balance,
    ifsc_code: userData.ifsc_code,
    amount_type: userData.amount_type,
});

const submit = () => {
    form.patch(route('bankinfo.update', { id: form.id }), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
        },
        onError: (errors) => {
            if (errors.account_number) {
                form.errors.account_number =
                    'This account number already exists in the bank.';
            }
        },
    });
};

const setOrganization = (id) => {
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setAmountType = (id) => {
    form.amount_type = id;
    form.errors.amount_type = null;
};
</script>

<template>
    <Head title="Edit Bank Info" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Bank</h2>
            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">
                        <!-- Organization -->
                        <div class="sm:col-span-3">
                            <InputLabel for="organization" value="Organization" />
                            <div class="relative mt-2">
                                <SearchableDropdown
                                    :options="organization"
                                    v-model="form.organization_id"
                                    @onchange="setOrganization"
                                    :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                                <InputError :message="form.errors.organization_id" />
                            </div>
                        </div>

                        <!-- Bank Name -->
                        <div class="sm:col-span-3">
                            <InputLabel for="bank_name" value="Bank Name" />
                            <TextInput
                                id="bank_name"
                                type="text"
                                v-model="form.bank_name"
                                autocomplete="bank_name"
                            />
                            <InputError :message="form.errors.bank_name" />
                        </div>

                        <!-- Account Number -->
                        <div class="sm:col-span-3">
                            <InputLabel for="account_number" value="Account Number" />
                            <TextInput
                                id="account_number"
                                type="text"
                                v-model="form.account_number"
                                autocomplete="account_number"
                            />
                            <InputError :message="form.errors.account_number" />
                        </div>

                        <!-- IFSC Code -->
                        <div class="sm:col-span-3">
                            <InputLabel for="ifsc_code" value="IFSC Code" />
                            <TextInput
                                id="ifsc_code"
                                type="text"
                                v-model="form.ifsc_code"
                                autocomplete="ifsc_code"
                            />
                            <InputError :message="form.errors.ifsc_code" />
                        </div>

                        <!-- Opening Balance -->
                        <div class="sm:col-span-3">
                            <InputLabel for="balance" value="Opening Balance" />
                            <TextInput
                                id="balance"
                                type="text"
                                v-model="form.balance"
                                autocomplete="balance"
                            />
                            <InputError :message="form.errors.balance" />
                        </div>

                        <!-- Amount Type -->
                        <div class="sm:col-span-3">
                            <InputLabel for="amount_type" value="Amount Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown
                                    :options="amounttype"
                                    v-model="form.amount_type"
                                    @onchange="setAmountType"
                                    :class="{ 'error rounded-md': form.errors.amount_type }"
                                />
                                <InputError :message="form.errors.amount_type" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <Link :href="route('bankinfo.index')">
                            <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                Cancel
                            </button>
                        </Link>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
