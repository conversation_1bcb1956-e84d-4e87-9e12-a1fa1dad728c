<?php

namespace App\Http\Requests;
use App\Models\InvoiceDetail;
use App\Models\SerialNumbers;

use Illuminate\Foundation\Http\FormRequest;

class ChallanInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'note'          => 'nullable|string',
            'invoicedProduct.*.product_id'  => 'required|integer',
            'invoicedProduct.*.serial_number_id'  => 'required|integer',
            'invoicedProduct.*.sell_price'  => 'required',
            'invoicedProduct.*.qty'  => 'required|integer'
        ];

        if (empty($this->input('invoice_id'))) {
            $rules['sales_user_id'] = 'required|integer';
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $selectedProductItem  = $this->input('selectedProductItem');
            // dd($selectedProductItem);
            $acceptedQtys = 0;
            foreach ($selectedProductItem  as $key => $product) {
                if($product['check']){
                    $acceptedQtys++;
                }
                if ($product['check'] && $product['sell_price'] == '') {
                    $validator->errors()->add("selectedProductItem.$key.sell_price", __('price is Required.'));
                }
                if ($product['qty'] + $product['invoiced_qty'] + $product['return_qty'] > $product['challan_qty']) {
                    $validator->errors()->add("selectedProductItem.$key.qty", __('qty not match with challan qty'));
                }
            }
            // if ($acceptedQtys == 0) {
            //     $validator->errors()->add("selectedProductItem.$key.check", __('At least one qty must be Checked.'));
            // }

            //for invoiced qty
            $invoicedProducts = $this->input('invoicedProduct');
            $serialNumberQuantities = [];

            foreach ($invoicedProducts as $key => $product) {

                $serialNumberId = $product['serial_number_id'];
                $qty = $product['qty'];

                if (!isset($serialNumberQuantities[$serialNumberId])) {
                    $serialNumberQuantities[$serialNumberId] = 0;
                }
                $serialNumberQuantities[$serialNumberId] += $qty;

                if (empty($product['invoice_detail_id'])) {
                    $productDetails = SerialNumbers::find($product['serial_number_id']);
                    $availableQty = $productDetails->receive_qty - $productDetails->sell_qty;
                    if ($serialNumberQuantities[$serialNumberId] > $availableQty) {
                        $validator->errors()->add("invoicedProduct.$key.qty", __('Qty not available for this batch.'));
                    }
                } else {
                    $productDetails = SerialNumbers::find($product['serial_number_id']);
                    $invoiceDetails = InvoiceDetail::find($product['invoice_detail_id']);
                    $availableQty = $productDetails->receive_qty - $productDetails->sell_qty + $invoiceDetails->qty;
                    if ($serialNumberQuantities[$serialNumberId] > $availableQty && $product['qty'] != $invoiceDetails->qty) {
                        $validator->errors()->add("invoicedProduct.$key.qty", __('Qty not available for this batch.'));
                    }
                }
                if ($productDetails && ($productDetails->mrp != '' || $productDetails->mrp != 0) && $product['sell_price'] > ($productDetails->mrp)) {
                    $validator->errors()->add("invoicedProduct.$key.sell_price", __('Price exceeds mrp'));
                }
            }
        });
    }
}
