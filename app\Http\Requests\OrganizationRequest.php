<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\OrganizationDTO;
use Support\Contracts\HasDTO;

class OrganizationRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name'          => ['required','string','max:255'],
            'address_line_1'=> ['required','string','max:255'],
            'pincode'       => ['required','numeric', 'digits:6'],
            'city'          => ['required','string','max:30'],
            'state'         => ['required','string','max:30'],
            'gst_no'          => ['required', 'string', 'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$/'], // GST validation
            'drug_licence_no'=> ['required','string','max:255'],
            'email'         => ['required','string','email'],
            'contact_no'    => ['required','numeric', 'digits:10'],
            'pan_no'         => ['required', 'string', 'regex:/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/'],

        ];
    }

    public function messages(): array
    {
        return [
            'pincode.digits' => 'The pincode must be exactly 6 digits.',
            'gst_no.regex'   => 'The GST number format is invalid.',
            'pan_no.regex'  => 'The PAN card number must be a valid 10-character alphanumeric value.',
        ];
    }

    
    public function DTO()
    {
        return OrganizationDTO::LazyFromArray($this->input());
    }

}
