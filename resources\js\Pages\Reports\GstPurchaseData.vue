<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';

const props = defineProps(['purchaseOrders', 'organization', 'organizationId', 'from_date', 'to_date']);

const form = useForm({
});

const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const companyName = ref('ALL COMPANY');
const from_date = ref('');
const to_date = ref('');
const searchValue = ref('');

const handleSearchChange = (searchValue, organizationId, from_date, to_date) => {
    form.get(route('gst-purchase-data', {
        search: searchValue,  // Include search if needed, otherwise remove
        organization_id: organizationId,
        from_date: from_date,
        to_date: to_date
    }), {
        preserveState: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, from_date.value, to_date.value);
};

const exportXls = () => {
    let organizationName = '';

    switch (organizationId.value) {
        case 1:
            organizationName = 'MC';
            break;
        case 2:
            organizationName = 'HC';
            break;
        case 3:
            organizationName = 'NOX';
            break;
        default:
            organizationName = 'All_Organizations';
            break;
    }

    const fileName = `GST_Purchase_Data_${organizationName}`;
    const params = {
        organization_id: organizationId.value || '',
        from_date: from_date.value || '',
        to_date: to_date.value || ''
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-gst-purchase-data?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${fileName}.xlsx`);
        document.body.appendChild(link)
;
        link.click();
        document.body.removeChild(link)
;
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const handleStartDate = () => {
    handleSearchChange(searchValue.value, organizationId.value, from_date.value, to_date.value);
};

const handleToDate = () => {
    handleSearchChange(searchValue.value, organizationId.value, from_date.value, to_date.value);
};

</script>

<template>
    <Head title="GST Purchase Data"/>

    <AdminLayout>
        <div class="animate-top h-screen">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">GST Purchase Data</h1>
                </div>
                <div class="flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex ml-6">
                        <CreateButton :href="route('reports')">
                                Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                    <div class="inline-flex items-center space-x-4 justify-end w-full ">
                        <button @click="exportXls">
                            <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                        </button>
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <div class="grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center">
                        <div class="sm:col-span-4">
                            <InputLabel for="customer_id" value="Organization Name" />
                            <div class="relative mt-2">
                                <SimpleDropdown :options="organization"
                                    v-model="organizationId"
                                    @onchange="setOrganization"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="date" value="From Date" />
                            <input
                                v-model="from_date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                @change="handleStartDate"
                                :class="{ 'error rounded-md': form.errors.from_date }"
                            />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="date" value="To Date" />
                            <input
                                v-model="to_date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                @change="handleToDate"
                                :class="{ 'error rounded-md': form.errors.to_date }"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>

</template>
