<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\InternalTransferDTO;
use Support\Contracts\HasDTO;

class InternalBankTransferRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'account_type' => 'required',
            'from_bank' => 'required',
            'to_bank' => 'required',
            'amount' => 'required',
            'date' => 'required|date'
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (($this->input('from_bank') == ($this->input('to_bank')))) {
                $validator->errors()->add("from_bank", __('Can not transfer to same bank'));
            }
        });
    }

    public function DTO()
    {
        return InternalTransferDTO::LazyFromArray($this->input());
    }

}
