import{_ as bt,b as kt,a as j}from"./AdminLayout-f002e683.js";import{_ as St}from"./CreateButton-400e96c7.js";import{_ as L}from"./SecondaryButton-98872fc5.js";import{D as Ct}from"./DangerButton-fc55f8d0.js";import{P as Y}from"./PrimaryButton-8958b93e.js";import{M as E}from"./Modal-e8ed59aa.js";import{_ as Tt}from"./Pagination-24879c4b.js";import{_ as $}from"./SimpleDropdown-acec1cc7.js";import{_ as zt}from"./SearchableDropdown-1fe89ae6.js";import{_ as R}from"./SearchableDropdownNew-c21d519e.js";import{K as It,r as v,l as Ot,o as a,c as d,a as c,u as B,w as _,F as A,Z as Bt,b as t,g as T,f as u,i as H,e as N,t as o,n as Z,s as Nt,x as Mt}from"./app-497d70e1.js";import"./html2canvas.esm-d15ba981.js";import{_ as z}from"./InputLabel-5f63a3d9.js";import{_ as Pt}from"./ArrowIcon-92b60770.js";import{s as jt}from"./sortAndSearch-07be74d0.js";import{_ as At}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const s=i=>(Nt("data-v-a5fc0e38"),i=i(),Mt(),i),Vt={class:"animate-top"},Gt={class:"flex justify-between items-center"},qt=s(()=>t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1)),Ut={class:"flex justify-end"},Lt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},Et={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},$t=s(()=>t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Rt={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Ht={class:"flex justify-end"},Ft={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Kt={class:"flex justify-between mb-2"},Qt={class:"flex"},Wt=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),Xt=["src"],Yt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Zt={class:"sm:col-span-4"},Jt={class:"relative mt-2"},Dt={class:"sm:col-span-4"},te={class:"relative mt-2"},ee={key:0,class:"sm:col-span-4"},se={class:"relative mt-2"},oe={class:"sm:col-span-4"},le={class:"relative mt-2"},ne={class:"sm:col-span-4"},ae={class:"relative mt-2"},ie={class:"sm:col-span-4"},re={class:"relative mt-2"},de={class:"mt-8 overflow-x-auto sm:rounded-lg"},ue={class:"shadow sm:rounded-lg"},ce={class:"w-full text-sm text-left rtl:text-right text-gray-500"},_e={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},he={class:"border-b-2"},me=["onClick"],ve={key:0},ge={class:"px-4 py-2.5 min-w-36"},pe={class:"px-4 py-2.5 min-w-44"},ye={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},fe={class:"px-4 py-2.5 min-w-52"},xe={class:"px-4 py-2.5 min-w-32"},we={class:"px-4 py-2.5 min-w-32"},be={class:"flex flex-1 items-center px-4 py-2.5"},ke={class:"items-center px-4 py-2.5"},Se={class:"flex items-center justify-start gap-4"},Ce=s(()=>t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Te=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),ze=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Ie=["onClick"],Oe=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Be=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Ne=[Oe,Be],Me=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Pe=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Deliver Order ",-1)),je=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ae=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," View Order ",-1)),Ve=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-width":"1.5","stroke-linejoin":"round",d:"M3 7.5v9a1.5 1.5 0 001.5 1.5h15a1.5 1.5 0 001.5-1.5v-9H3z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 6V3.75A1.5 1.5 0 017.5 2h9A1.5 1.5 0 0118 3.75V6m-15 0h18"})],-1)),Ge=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"},"Generate Invoice",-1)),qe=["onClick"],Ue=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),Le=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),Ee=[Ue,Le],$e={key:1},Re=s(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),He=[Re],Fe={class:"p-6"},Ke=s(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Qe={class:"mt-6 flex justify-end"},We={class:"p-6"},Xe=s(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you want to convert this quotation in to order? ",-1)),Ye={class:"mt-6 flex justify-end space-x-3"},Ze={class:"w-32"},Je={class:"p-6"},De={id:"pdf-content"},ts={class:"container1"},es={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},ss=["src"],os=s(()=>t("p",null,[t("strong",{style:{"font-size":"20px"}},"Order")],-1)),ls=s(()=>t("h1",null,[t("div",{style:{width:"120px"}})],-1)),ns={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},as=["src"],is=s(()=>t("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[t("p",{style:{"font-size":"20px"}},[t("strong",null,"Order")])],-1)),rs={style:{display:"flex","justify-content":"space-between"}},ds={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},us={style:{"font-size":"14px","margin-top":"10px"}},cs=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),_s=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),hs={style:{display:"flex"}},ms=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Phone")],-1)),vs={style:{display:"flex"}},gs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Email")],-1)),ps={style:{display:"flex"}},ys=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"GST")],-1)),fs={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},xs={style:{display:"flex"}},ws=s(()=>t("p",{style:{width:"120px"}},[t("strong",null,"Invoice Number")],-1)),bs={style:{display:"flex"}},ks=s(()=>t("p",{style:{width:"120px"}},[t("strong",null,"Invoice Date")],-1)),Ss=s(()=>t("p",{style:{"margin-bottom":"10px"}},[t("strong")],-1)),Cs={style:{"font-size":"14px","margin-top":"10px"}},Ts=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),zs={style:{display:"flex"}},Is=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Phone")],-1)),Os={style:{display:"flex"}},Bs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"GST")],-1)),Ns={style:{"overflow-x":"auto"}},Ms=s(()=>t("th",null,"SN",-1)),Ps=s(()=>t("th",null,"MODEL",-1)),js=s(()=>t("th",null,"HSN",-1)),As=s(()=>t("th",null,"DESCRIPTION",-1)),Vs=s(()=>t("th",null,"MRP",-1)),Gs=s(()=>t("th",null,"PRICE (₹)",-1)),qs=s(()=>t("th",null,"QTY",-1)),Us=s(()=>t("th",null,"TOTAL PRICE (₹)",-1)),Ls={key:0},Es={key:1},$s={key:2},Rs=s(()=>t("th",null,"GST (₹)",-1)),Hs=s(()=>t("th",null,"DIS.(₹)",-1)),Fs=s(()=>t("th",null,"TOTAL AMOUNT",-1)),Ks=["innerHTML"],Qs={key:0},Ws={key:1},Xs={key:2},Ys={class:"",style:{"margin-bottom":"10px","justify-items":"start",width:"400"}},Zs=s(()=>t("p",null,[t("strong")],-1)),Js={style:{display:"flex","justify-content":"space-between"}},Ds={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},to=s(()=>t("p",null,[t("strong",null,"OUR BANK DETAILS")],-1)),eo={key:0,style:{display:"flex"}},so=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),oo={key:1,style:{display:"flex"}},lo=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),no={key:2,style:{display:"flex"}},ao=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),io={key:3,style:{display:"flex"}},ro=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),uo={key:4,style:{display:"flex"}},co=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),_o={key:5,style:{display:"flex"}},ho=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),mo={key:6,style:{display:"flex"}},vo=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),go={key:7,style:{display:"flex"}},po=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),yo={key:8,style:{display:"flex"}},fo=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),xo={key:9,style:{display:"flex"}},wo=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),bo={key:10,style:{display:"flex"}},ko=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),So={key:11,style:{display:"flex"}},Co=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),To={style:{display:"flex"}},zo=s(()=>t("p",null,[t("strong",null,"GSTIN")],-1)),Io={style:{display:"flex"}},Oo=s(()=>t("p",null,[t("strong",null,"PAN")],-1)),Bo={style:{display:"flex"}},No=s(()=>t("p",null,[t("strong",null,"Payment Terms")],-1)),Mo={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Po={style:{display:"flex"}},jo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Sub Total (₹)")],-1)),Ao={style:{display:"flex"}},Vo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total Discount (₹)")],-1)),Go={key:0,style:{display:"flex"}},qo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total IGST (₹):")],-1)),Uo={key:1,style:{display:"flex"}},Lo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total CGST (₹):")],-1)),Eo={key:2,style:{display:"flex"}},$o=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total SGST (₹):")],-1)),Ro={style:{display:"flex"}},Ho=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total Amount (₹)")],-1)),Fo={style:{display:"flex","justify-content":"space-between"}},Ko=s(()=>t("div",{class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400px"}},null,-1)),Qo={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Wo=s(()=>t("p",null,[t("strong",null,"FOR,")],-1)),Xo=["src"],Yo={class:"mt-6 px-4 flex justify-end"},Zo={class:"flex flex-col justify-end space-y-6"},Jo={class:"flex items-center space-x-2"},Do={class:"flex justify-end"},tl={class:"w-36"},el={__name:"List",props:["data","permissions","administration","quotationbank","quotationHealthCareBankinfo","quotationNoxBank","organization","customers","salesuser","category","organizationId","customerId","salesUserId","categoryId","createdBy","statusId","ordersStatus","pagetypes"],setup(i){const g=i,{form:V,search:sl,sort:J,fetchData:ol,sortKey:D,sortDirection:tt,updateParams:et}=jt("orders.index",{organization_id:g.organizationId,customer_id:g.customerId,sales_user_id:g.salesUserId,category:g.categoryId,created_by:g.createdBy,status:g.status}),G=It().props.filepath.view,l=v([]),q=v(!1),F=v(!1),K=v(null),st=v(null),ot=[{field:"order_number",label:"ORDER NUMBER",sortable:!0},{field:"quotation.quotation_number",label:"QUOTATION NUMBER",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"users.first_name",label:"SALES PERSON",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],lt=n=>{K.value=n,q.value=!0},M=()=>{q.value=!1},Q=()=>{F.value=!1},nt=()=>{V.delete(route("orders.destroy",{id:K.value}),{onSuccess:()=>M()})},at=()=>{V.get(route("orders.convertorder",{id:st.value}),{onSuccess:()=>M()})},p=v(g.organizationId),y=v(g.customerId),f=v(g.salesUserId),x=v(g.categoryId),w=v(g.createdBy),b=v(g.statusId),I=v("");Ot([p,y,f,x,w,b],()=>{et({organization_id:p.value,customer_id:y.value,sales_user_id:f.value,category:x.value,created_by:w.value,status:b.value})});const O=(n,r,e,k,h,S,C)=>{I.value=n,V.get(route("orders.index",{search:n,organization_id:r,customer_id:e,sales_user_id:k,category:h,created_by:S,status:C}),{preserveState:!0})},it=(n,r)=>{p.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},rt=(n,r)=>{y.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},dt=(n,r)=>{x.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},ut=(n,r)=>{f.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},ct=(n,r)=>{w.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},_t=(n,r)=>{b.value=n,O(I.value,p.value,y.value,f.value,x.value,w.value,b.value)},ht=()=>{const n="Orders_Report_"+new Date().toISOString().split("T")[0],r={organization_id:p.value,customer_id:y.value,sales_user_id:f.value,category_id:x.value,created_by:w.value,status:b.value},k=`/export-orders?${new URLSearchParams(r).toString()}`;fetch(k,{method:"GET"}).then(h=>{if(!h.ok)throw new Error("Network response was not ok");return h.blob()}).then(h=>{const S=window.URL.createObjectURL(new Blob([h])),C=document.createElement("a");C.href=S,C.setAttribute("download",n+".xlsx"),document.body.appendChild(C),C.click(),document.body.removeChild(C)}).catch(h=>{console.error("Error exporting data:",h)})},U=v(!1),mt=v("custom"),vt=n=>{const r=g.data.data.find(e=>e.id===n);l.value=r,U.value=!0},W=()=>{U.value=!1},gt=n=>{switch(n){case"Pending":return"bg-blue-100";case"Accepted":return"bg-yellow-100";case"Completed":return"bg-green-100";default:return"bg-red-100"}},pt=n=>{switch(n){case"Pending":return"text-blue-600";case"Accepted":return"text-yellow-600";case"Completed":return"text-green-600";default:return"text-red-600"}},P=v("portrait"),yt=(n,r)=>{P.value=n},ft=(n,r)=>{window.open(`/order/download/${n}/${r}`,"_blank")},X=n=>{const r=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return r.toLocaleDateString("en-US",e)},m=n=>{let r=n.toFixed(2).toString(),[e,k]=r.split("."),h=e.substring(e.length-3),S=e.substring(0,e.length-3);return S!==""&&(h=","+h),`${S.replace(/\B(?=(\d{2})+(?!\d))/g,",")+h}.${k}`},xt=n=>n&&n.length>40?n.substring(0,40)+"...":n,wt=n=>n?n.replace(/\n/g,"<br>"):"";return(n,r)=>(a(),d(A,null,[c(B(Bt),{title:"Orders"}),c(bt,null,{default:_(()=>[t("div",Vt,[t("div",Gt,[qt,t("div",Ut,[t("div",Lt,[t("div",Et,[$t,t("input",{id:"search-field",onInput:r[0]||(r[0]=e=>O(e.target.value,p.value,y.value,f.value,x.value,w.value,b.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),i.permissions.canCreateOrders?(a(),d("div",Rt,[t("div",Ht,[c(St,{href:n.route("orders.create")},{default:_(()=>[T(" Add Order ")]),_:1},8,["href"])])])):u("",!0)])]),t("div",Ft,[t("div",Kt,[t("div",Qt,[Wt,c(z,{for:"customer_id",value:"Filters"})]),t("button",{onClick:ht},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,Xt)])]),t("div",Yt,[t("div",Zt,[c(z,{for:"customer_id",value:"Organization Name"}),t("div",Jt,[c($,{options:i.organization,modelValue:p.value,"onUpdate:modelValue":r[1]||(r[1]=e=>p.value=e),onOnchange:it},null,8,["options","modelValue"])])]),t("div",Dt,[c(z,{for:"customer_id",value:"Customer Name"}),t("div",te,[c(R,{options:i.customers,modelValue:y.value,"onUpdate:modelValue":r[2]||(r[2]=e=>y.value=e),onOnchange:rt},null,8,["options","modelValue"])])]),i.permissions.canCreateOrders?(a(),d("div",ee,[c(z,{for:"customer_id",value:"Sales Person"}),t("div",se,[c(R,{options:i.salesuser,modelValue:f.value,"onUpdate:modelValue":r[3]||(r[3]=e=>f.value=e),onOnchange:ut},null,8,["options","modelValue"])])])):u("",!0),t("div",oe,[c(z,{for:"customer_id",value:"Category"}),t("div",le,[c($,{options:i.category,modelValue:x.value,"onUpdate:modelValue":r[4]||(r[4]=e=>x.value=e),onOnchange:dt},null,8,["options","modelValue"])])]),t("div",ne,[c(z,{for:"customer_id",value:"Created By"}),t("div",ae,[c(R,{options:i.salesuser,modelValue:w.value,"onUpdate:modelValue":r[5]||(r[5]=e=>w.value=e),onOnchange:ct},null,8,["options","modelValue"])])]),t("div",ie,[c(z,{for:"customer_id",value:"Status"}),t("div",re,[c($,{options:i.ordersStatus,modelValue:b.value,"onUpdate:modelValue":r[6]||(r[6]=e=>b.value=e),onOnchange:_t},null,8,["options","modelValue"])])])])]),t("div",de,[t("div",ue,[t("table",ce,[t("thead",_e,[t("tr",he,[(a(),d(A,null,H(ot,(e,k)=>t("th",{key:k,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:h=>B(J)(e.field,e.sortable)},[T(o(e.label)+" ",1),e.sortable?(a(),N(Pt,{key:0,isSorted:B(D)===e.field,direction:B(tt)},null,8,["isSorted","direction"])):u("",!0)],8,me)),64))])]),i.data.data&&i.data.data.length>0?(a(),d("tbody",ve,[(a(!0),d(A,null,H(i.data.data,(e,k)=>{var h;return a(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",ge,o(e.order_number),1),t("td",pe,o(((h=e==null?void 0:e.quotation)==null?void 0:h.quotation_number)??"-"),1),t("th",ye,o(xt(e.customers.customer_name)??"-"),1),t("td",fe,o(e.users.first_name)+" "+o(e.users.last_name),1),t("td",xe,o(X(e.date)),1),t("td",we,o(m(e.total_amount)),1),t("td",be,[t("div",{class:Z(["flex rounded-full px-4 py-1",gt(e.status)])},[t("span",{class:Z(["text-sm font-semibold",pt(e.status)])},o(e.status),3)],2)]),t("td",ke,[t("div",Se,[c(kt,{align:"right",width:"48"},{trigger:_(()=>[Ce]),content:_(()=>[(e.status=="Pending"||e.status=="In Process")&&i.permissions.canEditOrders?(a(),N(j,{key:0,href:n.route("orders.edit",{id:e.id})},{svg:_(()=>[Te]),text:_(()=>[ze]),_:2},1032,["href"])):u("",!0),e.status=="Pending"&&i.permissions.canDeleteOrders?(a(),d("button",{key:1,type:"button",onClick:S=>lt(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ne,8,Ie)):u("",!0),e.status!="Completed"&&i.permissions.canCreateOrders?(a(),N(j,{key:2,href:n.route("orders.deliver",{id:e.id})},{svg:_(()=>[Me]),text:_(()=>[Pe]),_:2},1032,["href"])):u("",!0),i.permissions.canViewOrders?(a(),N(j,{key:3,href:n.route("orders.view",{id:e.id})},{svg:_(()=>[je]),text:_(()=>[Ae]),_:2},1032,["href"])):u("",!0),e.status!="Completed"&&i.permissions.canCreateOrders?(a(),N(j,{key:4,href:n.route("orders.generate",{id:e.id})},{svg:_(()=>[Ve]),text:_(()=>[Ge]),_:2},1032,["href"])):u("",!0),t("button",{type:"button",onClick:S=>vt(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ee,8,qe)]),_:2},1024)])])])}),128))])):(a(),d("tbody",$e,He))])])]),i.data.data&&i.data.data.length>0?(a(),N(Tt,{key:0,class:"mt-6",links:i.data.links},null,8,["links"])):u("",!0)]),c(E,{show:q.value,onClose:M},{default:_(()=>[t("div",Fe,[Ke,t("div",Qe,[c(L,{onClick:M},{default:_(()=>[T(" Cancel ")]),_:1}),c(Ct,{class:"ml-3",onClick:nt},{default:_(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(E,{show:F.value,onClose:Q},{default:_(()=>[t("div",We,[Xe,t("div",Ye,[c(L,{onClick:Q},{default:_(()=>[T(" Cancel ")]),_:1}),t("div",Ze,[c(Y,{onClick:at,type:"button"},{default:_(()=>[T("Approve")]),_:1})])])])]),_:1},8,["show"]),c(E,{show:U.value,onClose:W,maxWidth:mt.value},{default:_(()=>[t("div",Je,[t("div",De,[t("div",ts,[l.value.organization.id=="3"?(a(),d("div",es,[t("img",{class:"w-20 h-20",src:B(G)+l.value.organization.logo,alt:"logo"},null,8,ss),os,ls])):u("",!0),l.value.organization.id=="1"||l.value.organization.id=="2"?(a(),d("div",ns,[t("img",{class:"w-full h-10",src:B(G)+l.value.organization.logo,alt:"logo"},null,8,as),is])):u("",!0),t("div",rs,[t("div",ds,[t("p",null,[t("strong",us,o(l.value.organization.name),1)]),cs,t("p",null,o(l.value.organization.address_line_1),1),t("p",null,o(l.value.organization.address_line_2),1),t("p",null,o(l.value.organization.pincode)+" , "+o(l.value.organization.city),1),_s,t("div",hs,[ms,t("p",null,": "+o(l.value.organization.contact_no),1)]),t("div",vs,[gs,t("p",null,": "+o(l.value.organization.email),1)]),t("div",ps,[ys,t("p",null,": "+o(l.value.organization.gst_no),1)])]),t("div",fs,[t("div",xs,[ws,t("p",null,": "+o(l.value.order_number),1)]),t("div",bs,[ks,t("p",null,": "+o(X(l.value.date)),1)]),Ss,t("p",null,[t("strong",Cs,o(l.value.customers.customer_name),1)]),t("p",null,o(l.value.customers.address),1),Ts,t("div",zs,[Is,t("p",null,": "+o(l.value.customers.contact_no??"-"),1)]),t("div",Os,[Bs,t("p",null,": "+o(l.value.customers.gst_no??"-"),1)])])]),t("div",Ns,[t("table",null,[t("thead",null,[t("tr",null,[Ms,Ps,js,As,Vs,Gs,qs,Us,l.value.customers.gst_type=="IGST"?(a(),d("th",Ls,"IGST (%)")):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("th",Es,"CGST (%)")):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("th",$s,"SGST (%)")):u("",!0),Rs,Hs,Fs])]),t("tbody",null,[(a(!0),d(A,null,H(l.value.order_details,(e,k)=>{var h;return a(),d("tr",{key:e.id},[t("td",null,o(k+1),1),t("td",null,o(e.product.item_code),1),t("td",null,o(e.product.hsn_code??"-"),1),t("td",{innerHTML:wt(e.description)},null,8,Ks),t("td",null,o((h=e.product.serial_numbers[0])!=null&&h.mrp?m(e.product.serial_numbers[0].mrp):"-"),1),t("td",null,o(m(e.price)),1),t("td",null,o(e.qty),1),t("td",null,o(m(e.total_price)),1),l.value.customers.gst_type=="IGST"?(a(),d("td",Qs,o(m(e.gst)??"-"),1)):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("td",Ws,o(m(e.gst/2)??"-"),1)):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("td",Xs,o(m(e.gst/2)??"-"),1)):u("",!0),t("td",null,o(m(e.total_gst_amount)),1),t("td",null,o(m(e.discount_amount)),1),t("td",null,o(m(e.total_amount)),1)])}),128))])])]),t("div",Ys,[Zs,t("p",null,o(l.value.note),1)]),t("div",Js,[t("div",Ds,[to,l.value.organization.id=="1"?(a(),d("div",eo,[so,t("p",null,": "+o(i.quotationbank.bank_name),1)])):u("",!0),l.value.organization.id=="1"?(a(),d("div",oo,[lo,t("p",null,": "+o(i.quotationbank.branch_name),1)])):u("",!0),l.value.organization.id=="1"?(a(),d("div",no,[ao,t("p",null,": "+o(i.quotationbank.account_no),1)])):u("",!0),l.value.organization.id=="1"?(a(),d("div",io,[ro,t("p",null,": "+o(i.quotationbank.ifsc_code),1)])):u("",!0),l.value.organization.id=="2"?(a(),d("div",uo,[co,t("p",null,": "+o(i.quotationHealthCareBankinfo.bank_name),1)])):u("",!0),l.value.organization.id=="2"?(a(),d("div",_o,[ho,t("p",null,": "+o(i.quotationHealthCareBankinfo.branch_name),1)])):u("",!0),l.value.organization.id=="2"?(a(),d("div",mo,[vo,t("p",null,": "+o(i.quotationHealthCareBankinfo.account_no),1)])):u("",!0),l.value.organization.id=="2"?(a(),d("div",go,[po,t("p",null,": "+o(i.quotationHealthCareBankinfo.ifsc_code),1)])):u("",!0),l.value.organization.id=="3"?(a(),d("div",yo,[fo,t("p",null,": "+o(i.quotationNoxBank.bank_name),1)])):u("",!0),l.value.organization.id=="3"?(a(),d("div",xo,[wo,t("p",null,": "+o(i.quotationNoxBank.branch_name),1)])):u("",!0),l.value.organization.id=="3"?(a(),d("div",bo,[ko,t("p",null,": "+o(i.quotationNoxBank.account_no),1)])):u("",!0),l.value.organization.id=="3"?(a(),d("div",So,[Co,t("p",null,": "+o(i.quotationNoxBank.ifsc_code),1)])):u("",!0),t("div",To,[zo,t("p",null,": "+o(l.value.organization.gst_no??""),1)]),t("div",Io,[Oo,t("p",null,": "+o(l.value.organization.pan_no??""),1)]),t("div",Bo,[No,t("p",null,": "+o(l.value.payment_terms),1)])]),t("div",Mo,[t("div",Po,[jo,t("p",null,": "+o(m(l.value.sub_total)),1)]),t("div",Ao,[Vo,t("p",null,": "+o(m(l.value.total_discount)),1)]),l.value.customers.gst_type=="IGST"?(a(),d("div",Go,[qo,t("p",null,": "+o(m(l.value.igst)),1)])):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("div",Uo,[Lo,t("p",null,": "+o(m(l.value.cgst)),1)])):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),d("div",Eo,[$o,t("p",null,": "+o(m(l.value.sgst)),1)])):u("",!0),t("div",Ro,[Ho,t("p",null,": "+o(m(l.value.total_amount)),1)])])]),t("div",Fo,[Ko,t("div",Qo,[Wo,t("p",null,[t("strong",null,o(l.value.organization.name),1)]),t("img",{class:"h-28",src:B(G)+l.value.organization.signature,alt:"logo"},null,8,Xo)])])])]),t("div",Yo,[t("div",Zo,[t("div",Jo,[c(z,{for:"customer_id",value:"Page Type :"}),c(zt,{options:i.pagetypes,modelValue:P.value,"onUpdate:modelValue":r[7]||(r[7]=e=>P.value=e),onOnchange:yt},null,8,["options","modelValue"])]),t("div",Do,[c(L,{onClick:W},{default:_(()=>[T(" Cancel ")]),_:1}),t("div",tl,[c(Y,{class:"ml-3 w-20",onClick:r[8]||(r[8]=e=>ft(l.value.id,P.value))},{default:_(()=>[T(" Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},bl=At(el,[["__scopeId","data-v-a5fc0e38"]]);export{bl as default};
