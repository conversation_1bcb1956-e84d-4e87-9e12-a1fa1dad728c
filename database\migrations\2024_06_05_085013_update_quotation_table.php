<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE quotation CHANGE `status` `status` ENUM('Pending', 'Accepted', 'Rejected', 'Completed')");

        Schema::table('quotation', function (Blueprint $table) {
            $table->enum('category', ['Sales', 'Service'])->after('id');
            $table->integer('organization_id')->after('category');
            $table->string('validity')->after('note');
            $table->string('delivery')->after('validity');
            $table->string('paymentterms')->after('delivery');
            $table->string('warranty')->after('paymentterms');
        });

        Schema::table('quotation_details', function (Blueprint $table) {
            $table->dropColumn('hsn_code');
            $table->longText('description')->after('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
