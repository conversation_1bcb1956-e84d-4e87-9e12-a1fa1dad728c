<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('category', ['Sales', 'Service'])->after('id');
            $table->integer('organization_id')->after('category');
            $table->string('validity')->after('note');
            $table->string('delivery')->after('validity');
            $table->longText('payment_terms')->nullable()->after('delivery');
            $table->string('warranty')->nullable()->after('payment_terms');
            $table->double('igst', 16, 2)->nullable()->after('status');
            $table->double('sgst', 16, 2)->nullable()->after('igst');
            $table->double('cgst', 16, 2)->nullable()->after('sgst');
            $table->double('sub_total', 16, 2)->nullable()->after('cgst');
            $table->double('total_gst', 16, 2)->nullable()->after('sub_total');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
};
