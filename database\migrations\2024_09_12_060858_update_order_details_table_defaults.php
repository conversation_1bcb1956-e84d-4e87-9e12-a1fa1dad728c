<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('invoice', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('quotation', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->default(0)->change();
            $table->double('total_discount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('quotation_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->default(0)->change();
            $table->double('discount_amount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->default(0)->change();
            $table->double('total_discount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->default(0)->change();
            $table->double('discount_amount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->default(0)->change();
            $table->double('total_discount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->default(0)->change();
            $table->double('discount_amount', 16, 2)->nullable()->default(0)->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
