
<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;
use Config;

class InvoiceEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $emailData; // Store email details

    /**
     * Create a new message instance.
     */
    public function __construct($emailData)
    {
        $this->emailData = $emailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: [$this->emailData['from'] ?? $this=> config('mail.from.name')], 
            replyTo: [$this->emailData['reply_to'] ?? $this->emailData['from']], 
            subject: $this->emailData['subject'] 
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice', // Use correct email template
            with: [
                'content' => $this->emailData['content'], // Pass email body
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        // Attach invoice if provided
        if (!empty($this->emailData['attachment'])) {
            return [
                Attachment::fromPath(storage_path('app/' . $this->emailData['attachment']))
                    ->as('invoice.pdf')
                    ->withMime('application/pdf'),
            ];
        }

        return [];
    }
}
