<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_notes', function (Blueprint $table) {
            $table->id();
            $table->enum('invoice_type', ['Retail', 'Tax'])->default('Tax');
            $table->integer('organization_id')->nullable();
            $table->bigInteger('customer_id')->unsigned();
            $table->integer('invoice_id')->nullable();
            $table->string('credit_note_no');
            $table->string('debit_note_number');
            $table->date('date');
            $table->double('igst', 16, 2);
            $table->double('sgst', 16, 2);
            $table->double('cgst', 16, 2);
            $table->double('sub_total', 16, 2);
            $table->double('total_gst', 16, 2);
            $table->double('discount_before_tax', 16, 2);
            $table->double('overall_discount', 16, 2)->default(0.00);
            $table->double('total_discount', 16, 2);
            $table->double('total_amount', 16, 2);
            $table->longText('reason')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_notes');
    }
};
