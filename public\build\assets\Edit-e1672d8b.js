import{K as _,o as u,c as p,a as s,u as o,w as c,F as b,Z as U,b as a,d as S,f as g,g as w,T as F}from"./app-2ecbacfc.js";import{_ as P,a as C}from"./AdminLayout-42d5bb92.js";import{_ as r}from"./InputError-aa79d601.js";import{_ as n}from"./InputLabel-f62a278f.js";import{P as $}from"./PrimaryButton-0d76f021.js";import{_ as d}from"./TextInput-73b24943.js";import{_ as q}from"./TextArea-00d3f05d.js";import{_ as N}from"./SearchableDropdown-6058bf5f.js";import{_ as E}from"./FileUpload-9cdc7ca3.js";import{u as k}from"./index-35fd125b.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},T=a("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Product",-1),D=["onSubmit"],I={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},M={class:"sm:col-span-2"},O={class:"sm:col-span-2"},z=a("div",{class:"sm:col-span-2"},null,-1),G={class:"sm:col-span-2"},H={class:"relative mt-2"},K={class:"sm:col-span-2"},Q=a("div",{class:"sm:col-span-2"},null,-1),R={class:"sm:col-span-1"},Z={class:"sm:col-span-1"},A={class:"sm:col-span-1"},J={class:"sm:col-span-1"},L={class:"sm:col-span-4"},W={class:"sm:col-span-4"},X={class:"flex mt-6 items-center justify-between"},Y={class:"ml-auto flex items-center justify-end gap-x-6"},ee=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),se={key:0,class:"text-sm text-gray-600"},pe={__name:"Edit",props:["data","category","organization","filepath"],setup(f){const i=_().props.data[0],y=_().props.filepath.view,e=k("post","/products",{id:i.id,company_id:i.company_id,name:i.name,min_qty:i.min_qty,item_code:i.item_code,description:i.description,price:parseFloat(i.price).toFixed(2),rating:i.rating,gst:parseFloat(i.gst).toFixed(2),hsn_code:i.hsn_code,prefix:i.prefix,category:i.category,image:i?y+i.image:"/uploads/product-images/defaultimg.png"}),v=(m,t)=>{e.category=t},x=m=>{e.image=m},V=()=>{e.get(route("productimage.remove",{id:e.id}),{onSuccess:()=>{e.image=null},onError:m=>{console.error("Error removing the document:",m)}})},h=()=>e.submit({preserveScroll:!0,resetOnSuccess:!1});return(m,t)=>(u(),p(b,null,[s(o(U),{title:"Product Edit"}),s(P,null,{default:c(()=>[a("div",B,[T,a("form",{onSubmit:S(h,["prevent"]),class:""},[a("div",I,[a("div",j,[a("div",M,[s(n,{for:"name",value:"Product Name"}),s(d,{id:"name",type:"text",modelValue:o(e).name,"onUpdate:modelValue":t[0]||(t[0]=l=>o(e).name=l)},null,8,["modelValue"]),s(d,{id:"name",type:"hidden",modelValue:o(e).company_id,"onUpdate:modelValue":t[1]||(t[1]=l=>o(e).company_id=l)},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.name},null,8,["message"])]),a("div",O,[s(n,{for:"item_code",value:"Product Code"}),s(d,{id:"item_code",type:"text",modelValue:o(e).item_code,"onUpdate:modelValue":t[2]||(t[2]=l=>o(e).item_code=l)},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.item_code},null,8,["message"])]),z,a("div",G,[s(n,{for:"type",value:"Category"}),a("div",H,[s(N,{options:f.category,modelValue:o(e).category,"onUpdate:modelValue":t[3]||(t[3]=l=>o(e).category=l),onOnchange:v},null,8,["options","modelValue"])]),s(r,{class:"",message:o(e).errors.category},null,8,["message"])]),a("div",K,[s(n,{for:"hsn_code",value:"HSN Code"}),s(d,{id:"hsn_code",type:"text",modelValue:o(e).hsn_code,"onUpdate:modelValue":t[4]||(t[4]=l=>o(e).hsn_code=l)},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.hsn_code},null,8,["message"])]),Q,a("div",R,[s(n,{for:"price",value:"Price (₹)"}),s(d,{id:"price",type:"text",modelValue:o(e).price,"onUpdate:modelValue":t[5]||(t[5]=l=>o(e).price=l),min:"1"},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.price},null,8,["message"])]),a("div",Z,[s(n,{for:"gst",value:"GST(%)"}),s(d,{id:"gst",type:"text",modelValue:o(e).gst,"onUpdate:modelValue":t[6]||(t[6]=l=>o(e).gst=l),min:"0",max:"100"},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.gst},null,8,["message"])]),a("div",A,[s(n,{for:"min_qty",value:"Minimum Qty"}),s(d,{id:"min_qty",type:"text",modelValue:o(e).min_qty,"onUpdate:modelValue":t[7]||(t[7]=l=>o(e).min_qty=l)},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.min_qty},null,8,["message"])]),a("div",J,[s(n,{for:"prefix",value:"Prefix"}),s(d,{id:"prefix",type:"text",modelValue:o(e).prefix,"onUpdate:modelValue":t[8]||(t[8]=l=>o(e).prefix=l)},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.prefix},null,8,["message"])]),a("div",L,[s(n,{for:"photo",value:"Upload Image"}),s(E,{label:"Upload Image",inputId:"image",inputName:"image",fileUrl:o(e).image,onFile:x},null,8,["fileUrl"]),o(i).image!=null?(u(),p("button",{key:0,type:"button",class:"mt-2 text-sm text-red-500 hover:underline",onClick:V}," Remove File ")):g("",!0)]),a("div",W,[s(n,{for:"description",value:"Description"}),s(q,{id:"description",type:"text",modelValue:o(e).description,"onUpdate:modelValue":t[9]||(t[9]=l=>o(e).description=l),rows:5},null,8,["modelValue"]),s(r,{class:"",message:o(e).errors.description},null,8,["message"])])])]),a("div",X,[a("div",Y,[s(C,{href:m.route("products.show",{id:o(i).company_id})},{svg:c(()=>[ee]),_:1},8,["href"]),s($,{disabled:o(e).processing},{default:c(()=>[w("Save")]),_:1},8,["disabled"]),s(F,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[o(e).recentlySuccessful?(u(),p("p",se,"Saved.")):g("",!0)]),_:1})])])],40,D)])]),_:1})],64))}};export{pe as default};
