<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head , Link, useForm, usePage} from '@inertiajs/vue3';

const userData = usePage().props.data;

defineProps({
    data: {
        type: Object,
    }
});

const form = useForm({
    id: userData.id,
    customer_name: userData.customer_name,
    dr_name: userData.dr_name,
    place: userData.place,
    company: userData.company,
    product: userData.product,
    date: userData.date,
    brief_discussion: userData.brief_discussion,
});

</script>

<template>
    <Head title="Edit Plan" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Plan</h2>
            <form @submit.prevent="form.patch(route('weeklyplan.update'))">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-3">
                            <InputLabel for="dob" value="Date" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                               type="date"  v-model="form.date"   @change="form.validate('date')"
                            />
                            <InputError class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="customer_name" value="Customer Name" />
                            <TextInput
                                id="customer_name"
                                type="text"
                                v-model="form.customer_name"
                                @change="form.validate('customer_name')"
                            />
                            <InputError  class="" :message="form.errors.customer_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="dr_name" value="Doctor Name" />
                            <TextInput
                                id="dr_name"
                                type="text"
                                v-model="form.dr_name"
                            />
                            <InputError  class="" :message="form.errors.dr_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="place" value="Place/City" />
                            <TextInput
                                id="place"
                                type="text"
                                v-model="form.place"
                                @change="form.validate('place')"
                            />
                            <InputError  class="" :message="form.errors.place" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="company" value="Company" />
                            <TextInput
                                id="company"
                                type="text"
                                v-model="form.company"
                            />
                            <InputError  class="" :message="form.errors.company" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product" value="Products" />
                            <TextInput
                                id="product"
                                type="text"
                                v-model="form.product"
                                 @change="form.validate('product')"
                            />
                            <InputError class="" :message="form.errors.product" />
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel for="brief_discussion" value="Brief Discussion" />
                            <TextArea
                                id="brief_discussion"
                                type="text"
                                :rows="3"
                                v-model="form.brief_discussion"
                                @change="form.validate('brief_discussion')"
                            />
                            <InputError  class="" :message="form.errors.brief_discussion" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('weeklyplan.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>



                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>
