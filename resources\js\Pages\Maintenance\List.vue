<script setup>
import { ref, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import TextArea from '@/Components/TextArea.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions']);
const { form, search, sort, fetchData, sortKey, sortDirection } = sortAndSearch('maintenance-contract.index');

const selectedUserId = ref(null);
const modalVisible = ref(false);
const searchValue = ref('');

const columns = [
    { field: 'hospital_name',           label: 'HOSPITAL NAME',         sortable: true },
    { field: 'product_name',            label: 'PRODUCT',               sortable: true },
    { field: 'price',                   label: 'PRICE (₹)',             sortable: true },
    { field: 'contract_start_date',     label: 'CONTRACT DATE',         sortable: true, multiFieldSort: ['contract_start_date', 'contract_end_date'] },
    { field: 'pm_date_1',               label: 'PM DATE 1',             sortable: true },
    { field: 'pm_date_2',               label: 'PM DATE 2',             sortable: true },
    { field: 'pm_date_3',               label: 'PM DATE 3',             sortable: true },
    { field: 'pm_date_4',               label: 'PM DATE 4',             sortable: true },
    { field: 'maintenance_type',        label: 'TYPE',                  sortable: true },
    { field: 'status',                  label: 'STATUS',                sortable: true },
    { field: 'action',                  label: 'ACTION',                sortable: false}
];

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('maintenance-contract.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Open':
            return 'bg-blue-100';
        case 'Close':
            return 'bg-red-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Open':
            return 'text-blue-600';
        case 'Close':
            return 'text-red-600';
    }
};

const getTypeBgClass = (maintenance_type) => {
    switch (maintenance_type) {
        case 'AMC':
            return 'bg-cyan-100';
        case 'CMC':
            return 'bg-green-100';
    }
};

const getTypeClass = (maintenance_type) => {
    switch (maintenance_type) {
        case 'AMC':
            return 'text-cyan-600';
        case 'CMC':
            return 'text-green-600';
    }
};

const handleSearchChange = (value,statusId) => {
    searchValue.value = value;
    form.get(route('maintenance-contract.index',{search:value, status: statusId}),  {
        preserveState: true,
        // replace: true,
    });
};

const maintenanceContract = ref('MAINTENANCE DATA');

const exportXls = () => {
    const xlsName = maintenanceContract.value.replace(/\s+/g, '_');

    // const queryString = new URLSearchParams(params).toString();
    const url = `/export-maintenance-contract?`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${xlsName}.xlsx`);
        document.body.appendChild(link)
;
        link.click();
        document.body.removeChild(link)
;
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};


const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};


</script>

<template>
    <Head title="Maintenance Contract"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Maintenance Contract</h1>
            </div>
            <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                     <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
                <div class="flex justify-end" v-if="permissions.canCreateMaintenance">
                    <CreateButton :href="route('maintenance-contract.create')">
                            Create Maintenance Contract
                    </CreateButton>
                </div>
                <button @click="exportXls">
                    <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                </button>
            </div>
        </div>

        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                {{ column.label }}
                                <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id"  :class="{ 'bg-yellow-100': poData.highlight }">
                            <td class="whitespace-normal  px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate min-w-48" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.hospital_name ?? '-' }} - {{ poData.city ?? '-' }}
                            </td>
                            <td class="whitespace-normal  px-4 py-2.5 min-w-60 truncate" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.product_name ?? '' }}
                            </td>
                            <td scope="row" class="px-4 py-2.5 min-w-36 truncate" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.price ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-60" :class="{ 'text-yellow-600': poData.highlight }">
                                {{ formatDate(poData.contract_start_date) }} - {{ formatDate(poData.contract_end_date) }}
                            </td>
                            <td class="px-4 py-2.5 min-w-36" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.pm_date_1 ? formatDate(poData.pm_date_1) : '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-36" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.pm_date_2 ? formatDate(poData.pm_date_2) : '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-36" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.pm_date_3 ? formatDate(poData.pm_date_3) : '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-36" :class="{ 'text-yellow-600': poData.highlight }">
                                 {{ poData.pm_date_4 ? formatDate(poData.pm_date_4) : '-' }}
                            </td>
                            <td class="flex-1 items-center px-4 py-2.5" :class="{ 'text-yellow-600': poData.highlight }">
                                <div class="flex rounded-full px-4 py-1" :class="getTypeBgClass(poData.maintenance_type)">
                                    <span class="text-sm font-semibold" :class="getTypeClass(poData.maintenance_type)">{{ poData.maintenance_type }}</span>
                                </div>
                            </td>
                            <td class="flex flex-1 items-center px-4 py-2.5" :class="{ 'text-yellow-600': poData.highlight }">
                                <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                    <span class="text-sm font-semibold" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                </div>
                            </td>
                            <td class="items-center px-4 py-2.5" :class="{ 'text-yellow-600': poData.highlight }">
                                <div class="flex items-center justify-start gap-4">
                                    <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" v-if="poData.status != 'Close'" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink :href="route('maintenance-contract.edit',{id:poData.id})" v-if="permissions.canEditMaintenance">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteMaintenance">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.42.271L12 0l-.057.072A2.25 2.25 0 0010.5 1.085c.002-.001.003-.001.004-.001z"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">Delete</span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>

    </AdminLayout>

</template>



<style scoped>
    .error {
        border: 1px solid red;
    }
    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }
    .container1 input[type="checkbox"] {
        margin-top: 10px !important;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 0px 4px 12px 4px !important;
        text-align: left;
        font-size: 12px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 0px 4px 12px 4px !important;
        text-align: left;
        font-size: 12px;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
</style>
