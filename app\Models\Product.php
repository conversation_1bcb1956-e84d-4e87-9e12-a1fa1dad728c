<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use App\Traits\ProductActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    use ProductActivityTrait;

    const STATUS_ACTIVE = 1;

    protected $table = 'products';

    protected static $logName = 'Product';

    public function getLogDescription(string $event): string
    {
        return "Product {$this->name} <strong>{$this->item_code}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'name',
        'item_code',
        'prefix',
        'description',
        'price',
        'min_qty',
        'gst',
        'hsn_code',
        'part_no',
        'category',
        'company_id',
        'status',
        'image'
    ];

    protected $fillable = [
        'name',
        'item_code',
        'prefix',
        'description',
        'price',
        'min_qty',
        'gst',
        'hsn_code',
        'part_no',
        'category',
        'company_id',
        'status',
        'image',
        'created_by',
        'updated_by'
    ];

    public function serialNumbers()
    {
        return $this->hasMany(SerialNumbers::class);
    }

    public function company(){
        return $this->belongsTo(Company::class,'company_id','id');
    }

    public function salesProducts()
    {
        return $this->hasMany(SerialNumbers::class)->whereRaw('(receive_qty - sell_qty) > 0');
    }

    public function salesProduct()
    {
        return $this->hasMany(SerialNumbers::class)->selectRaw('*, (receive_qty - sell_qty) as qty_difference')->orderByRaw('qty_difference DESC');
    }

    public function purchaseDetails()
    {
        return $this->hasMany(PurchaseOrderDetail::class, 'product_id', 'id');
    }

    public function invoiceDetails()
    {
        return $this->hasMany(InvoiceDetail::class, 'product_id', 'id');
    }

    public function orderDetails()
    {
        return $this->hasMany(OrderDetails::class, 'product_id', 'id');
    }

    public function quotationDetails()
    {
        return $this->hasMany(QuotationDetail::class, 'product_id', 'id');
    }

    public function productActivityLogs()
    {
        return $this->hasMany(ProductActivityLog::class,'product_id','id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Product has been $event for the company : " . $model->company->name;
        $modelClone = clone $model;
        $modelClone->product_id = $model->id;
        self::addCustomLogEntry($modelClone, $event, $logName);
    }
}
