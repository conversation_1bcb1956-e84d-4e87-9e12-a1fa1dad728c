import{h as Y,r as h,j,m as L,o as c,c as m,a as l,u as b,w as C,F as T,Z as O,b as e,t as u,g as R,f as F,k as N,v as A,n as B,i as P}from"./app-2ecbacfc.js";import{_ as q}from"./AdminLayout-42d5bb92.js";import{_ as G}from"./CreateButton-1fa2a774.js";import{_ as X}from"./SimpleDropdown-5dc59147.js";/* empty css                                                              */import{_ as v}from"./InputLabel-f62a278f.js";const Z={class:"animate-top"},H={class:"sm:flex sm:items-center"},J=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Company Transaction")],-1),K={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},Q={class:"flex items-center space-x-4"},W={class:"text-lg font-semibold leading-7 text-gray-900"},tt={class:"flex justify-end w-20"},et={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ot={class:"flex justify-between mb-2"},at={class:"flex"},st=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),nt={class:"inline-flex items-center space-x-4 justify-end w-full"},rt={key:0,class:"p-2 flex justify-end text-base font-semibold leading-6 text-gray-900"},lt=["src"],it={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},dt={class:"sm:col-span-4"},ct={class:"relative mt-2"},mt={class:"sm:col-span-4"},ut={class:"sm:col-span-4"},pt={class:"mt-8 overflow-x-auto sm:rounded-lg"},gt={key:0,class:"shadow sm:rounded-lg"},ht={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ft=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")])],-1),_t={key:0},yt={class:"px-4 py-2.5 min-w-24"},xt={class:"px-4 py-2.5 max-w-[24rem] font-medium text-gray-900"},vt={class:"overflow-x-auto overflow-y-hidden whitespace-nowrap"},wt={class:"px-4 py-2.5 min-w-28"},bt={class:"px-4 py-2.5 min-w-28"},Dt={class:"px-4 py-2.5 min-w-36"},kt={key:1},St=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ct=[St],$t={__name:"Transaction",props:["data","organization","organizationId","organizationId","creditdata","companyId","company"],setup(y){const p=y,w=Y({}),I=h("companies tranasction"),$=()=>{const o=new Date,a=o.getMonth(),t=o.getFullYear();return`${a<3?t-1:t}-04-01`},z=()=>{const o=new Date,a=o.getFullYear(),t=String(o.getMonth()+1).padStart(2,"0"),n=String(o.getDate()).padStart(2,"0");return`${a}-${t}-${n}`},i=h($()),g=h(z());h("");const f=h(p.organizationId),E=o=>{w.get(route("companies.transaction",{id:p.companyId,organization_id:o}),{preserveState:!0})},_=j(()=>{let o=0;p.data.forEach(t=>{const n=new Date(t.date),s=new Date(i.value);n<s&&(t.payment_type==="cr"?o+=parseFloat(t.amount):t.payment_type==="dr"&&(o-=parseFloat(t.amount)))});let a=p.data.filter(t=>{const n=new Date(t.date),s=new Date(i.value),d=new Date(g.value);if(i.value&&g.value)return n>=s&&n<=d;const r=!i.value||n>=s,S=!g.value||n<=d;return r&&S});return a=a.map(t=>{t.payment_type==="cr"?o+=parseFloat(t.amount):t.payment_type==="dr"&&(o-=parseFloat(t.amount));let n=o>=0?"cr":"dr",s=x(Math.abs(o))+" "+n;return{...t,balance:s}}),a}),D=()=>{};L(()=>{});const M=(o,a)=>{f.value=o,E(f.value)},V=()=>{const o=I.value.replace(/\s+/g,"_"),a={company_id:p.companyId||"",organization_id:f.value||"",from_date:i.value||"",to_date:g.value||""},n=`/company/{id}/transactions/export?${new URLSearchParams(a).toString()}`;fetch(n,{method:"GET"}).then(s=>{if(!s.ok)throw new Error("Network response was not ok");return s.blob()}).then(s=>{const d=window.URL.createObjectURL(new Blob([s])),r=document.createElement("a");r.href=d,r.setAttribute("download",`${o}.xlsx`),document.body.appendChild(r),r.click(),document.body.removeChild(r)}).catch(s=>{console.error("Error exporting data:",s)})},x=o=>{let a=o.toFixed(2).toString(),[t,n]=a.split("."),s=t.substring(t.length-3),d=t.substring(0,t.length-3);return d!==""&&(s=","+s),`${d.replace(/\B(?=(\d{2})+(?!\d))/g,",")+s}.${n}`},U=o=>{const a=new Date(o),t={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",t)},k=h("");return k.value=p.creditdata.reduce((o,a)=>o+a.unused_amount,0),(o,a)=>(c(),m(T,null,[l(b(O),{title:"Company Transaction"}),l(q,null,{default:C(()=>[e("div",Z,[e("div",H,[J,e("div",K,[e("div",Q,[e("div",null,[e("h1",W,u(y.company.name),1)]),e("div",tt,[l(G,{href:o.route("companies.index")},{default:C(()=>[R(" Back ")]),_:1},8,["href"])])])])]),e("div",et,[e("div",ot,[e("div",at,[st,l(v,{for:"company_id",value:"Filters"})]),e("div",nt,[y.creditdata.length>0?(c(),m("div",rt," Credits Available: ₹"+u(x(k.value)),1)):F("",!0),e("button",{onClick:V},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,lt)])])]),e("div",it,[e("div",dt,[l(v,{for:"company_id",value:"Organization Name"}),e("div",ct,[l(X,{options:y.organization,modelValue:f.value,"onUpdate:modelValue":a[0]||(a[0]=t=>f.value=t),onOnchange:M},null,8,["options","modelValue"])])]),e("div",mt,[l(v,{for:"date",value:"From Date"}),N(e("input",{"onUpdate:modelValue":a[1]||(a[1]=t=>i.value=t),class:B(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(w).errors.from_date}]),type:"date",onChange:D},null,34),[[A,i.value]])]),e("div",ut,[l(v,{for:"date",value:"To Date"}),N(e("input",{"onUpdate:modelValue":a[2]||(a[2]=t=>g.value=t),class:B(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(w).errors.to_date}]),type:"date",onChange:D},null,34),[[A,g.value]])])])]),e("div",pt,[_.value&&_.value.length>0?(c(),m("div",gt,[e("table",ht,[ft,_.value&&_.value.length>0?(c(),m("tbody",_t,[(c(!0),m(T,null,P(_.value,(t,n)=>(c(),m("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",yt,u(U(t.date)??"-"),1),e("td",xt,[e("div",vt,u(t.note??"-"),1)]),e("td",wt,u(t.payment_type=="dr"?x(t.amount):"-"),1),e("td",bt,u(t.payment_type=="cr"?x(t.amount):"-"),1),e("td",Dt,u(t.balance),1)]))),128))])):(c(),m("tbody",kt,Ct))])])):F("",!0)])])]),_:1})],64))}};export{$t as default};
