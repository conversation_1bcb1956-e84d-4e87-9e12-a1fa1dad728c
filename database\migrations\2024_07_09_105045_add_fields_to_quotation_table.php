<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotation', function (Blueprint $table) {
            $table->double('igst', 16, 2)->nullable()->after('status');
            $table->double('sgst', 16, 2)->nullable()->after('status');
            $table->double('cgst', 16, 2)->nullable()->after('status');
            $table->double('sub_total', 16, 2)->nullable()->after('status');
            $table->double('total_gst', 16, 2)->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotation', function (Blueprint $table) {

        });
    }
};
