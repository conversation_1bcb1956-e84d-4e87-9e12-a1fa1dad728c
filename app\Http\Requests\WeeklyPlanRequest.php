<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\WeeklyPlanDTO;
use Support\Contracts\HasDTO;

class WeeklyPlanRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name' => ['required','string','max:255'],
            'place'         => ['required','string','max:500'],
            'product'       => ['required','string','max:255'],
            'date'          => ['required'],
        ];
    }

    public function DTO()
    {
        return WeeklyPlanDTO::LazyFromArray($this->input());
    }

}
