<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;


class PurchaseOrderDetail extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'purchase_order_details';

    protected static $logName = 'Company-PO-Detail';

    public function getLogDescription(string $event): string
    {
        $poNumber = $this->purchaseOrder->po_number;

        $productNames = $this->product()->pluck('name')->implode(', ');

        if (empty($productNames)) {
            $productNames = 'Unknown Product';
        }

        return "Purchase order detail has been {$event} for <strong>{$productNames}</strong> : {$poNumber} by";
    }

    protected static $logAttributes = [
        'purchase_order_id',
        'product_id',
        'pkg_of_qty',
        'qty',
        'receive_qty',
        'price',
        'total_price',
        'gst',
        'gst_amount',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'description',
        'old_record',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'purchase_order_id',
        'product_id',
        'pkg_of_qty',
        'qty',
        'receive_qty',
        'price',
        'total_price',
        'gst',
        'gst_amount',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'description',
        'old_record',
        'created_by',
        'updated_by'
    ];

    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function purchaseOrderReceiveDetails()
    {
        return $this->hasMany(PurchaseOrderReceiveDetails::class, 'purchase_order_receive_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                $event = $model->isDirty('receive_qty') ? 'received' : 'updated';
                self::handleLogEntry($model, $event);
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Company PO: " . $model->purchaseOrder->po_number;
        $mergedAttributes = [];

        if($event == 'created' || $event == 'deleted'){
            $mergedAttributes = [
                'purchaseOrder'         => $model->purchaseOrder->getAttributes(),
                'purchaseOrderDetail'   => $model->getAttributes()
            ];
            //$mergedAttributes = array_merge($model->getAttributes(), $model->purchaseOrder->getAttributes());
        } elseif ($event == 'updated'){
            /*$mergedAttributes = [
                'mainOriginal'      => $model->purchaseOrder->getDirty(),
                'mainDirty'         => $model->purchaseOrder->getOriginal(),
                'detailOriginal'    => $model->getOriginal(),
                'detailDirty'       => $model->getDirty()
            ];*/
        }

        self::addCustomLogEntry($model, $event, $logName, $mergedAttributes);
    }

}
