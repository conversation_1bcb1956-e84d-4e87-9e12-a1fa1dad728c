<script setup>
import { ref, onMounted, watch , computed} from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import { Head , useForm, usePage } from '@inertiajs/vue3';
import MultipleCheckbox from '@/Components/MultipleCheckbox.vue';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions', 'organization', 'customers', 'organizationId', 'customerId', 'salesuser', 'salesUserId', 'category', 'categoryId', 'pagetypes']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('challan.index', {
    organization_id: props.organizationId,
    customer_id: props.customerId,
    sales_user_id: props.salesUserId,
    category: props.categoryId,
});

const filePath = usePage().props.filepath.view;
const challanData = ref([]);
const challanCloseData = ref([]);

// const form = useForm({});
const modalVisible = ref(false);
const selectedChallanId = ref(null);

const columns = [
    { field: 'challan_number',              label: 'CHALLAN NUMBER',   sortable: true },
    { field: 'category',                    label: 'TYPE',             sortable: true },
    { field: 'customers.customer_name',     label: 'CUSTOMER NAME',    sortable: true },
    { field: 'users.first_name',            label: 'SALES PERSON',     sortable: true },
    { field: 'date',                        label: 'DATE',             sortable: true },
    { field: 'total_amount',                label: 'AMOUNT (₹)',       sortable: true },
    { field: 'status',                      label: 'STATUS',           sortable: true },
    { field: 'action',                      label: 'ACTION',           sortable: false },
];

const openDeleteModal = (userId) => {
  selectedChallanId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deletePO = (category) => {
    form.delete(route('challan.destroy',{id:selectedChallanId.value}), {
        onSuccess: () => closeModal()
    });
};

const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const salesUserId = ref(props.salesUserId);
const categoryId = ref(props.categoryId);
const searchValue = ref('');

watch([organizationId, customerId, salesUserId, categoryId ], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
        sales_user_id: salesUserId.value,
        category: categoryId.value,
    });
});

const handleSearchChange = (value, organizationId, customerId, salesUserId, categoryId) => {
    searchValue.value = value;
    form.get(route('challan.index',{search:value,  organization_id: organizationId ,  customer_id: customerId, sales_user_id: salesUserId ,  category: categoryId}),  {
        preserveState: true,
        // replace: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, salesUserId.value, categoryId.value);
};

const setCustomers = (id, name) => {
    customerId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value);
};

const setType = (id, name) => {
    categoryId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value);
};

const setSalesUser = (id, name) => {
    salesUserId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value);
};

const generateChallanModal = ref(false);
const statusCloseModal = ref(false);
const returnProductModal = ref(false);
const focModal = ref(false);
const demoCloseModal = ref(false);
const modalMaxWidth = ref('custom');

const openPreviewModal = (id) => {
  const challan = props.data.data.find(challan => challan.id === id);

//   const filteredChallanDetail = challan.challan_detail.filter(detail => {
//     return (detail.qty - detail.return_qty) > 0;
//   });

//   challanData.value = { ...challan, challan_detail: filteredChallanDetail };
 challanData.value = challan

  generateChallanModal.value = true;
};

const closeChallanModal = () => {
    generateChallanModal.value = false;
};

const openFocModal = (id) => {
  focModal.value = true;
  selectedChallanId.value = id;
};

const closeFocModal = () => {
    focModal.value = false;
};

const focChallan = (category) => {
    form.get(route('challan.foc',{id:selectedChallanId.value}), {
        onSuccess: () => closeModal()
    });
};

const openDemoCloseModal = (id) => {
  demoCloseModal.value = true;
  selectedChallanId.value = id;
};

const closeDemoCloseModal = () => {
    demoCloseModal.value = false;
};

const closeDemo = (category) => {
    form.get(route('challan.democlose',{id:selectedChallanId.value}), {
        onSuccess: () => closeModal()
    });
};

const openstatusCloseModal = (id) => {
  const challan = props.data.data.find(challan  => challan.id === id);
  if(challan){
    const filteredChallanDetails = challan.challan_detail.filter(detail =>
      (detail.qty - detail.invoiced_qty) > 0 && (detail.qty - detail.return_qty) > 0
    );
    challanCloseData.value = filteredChallanDetails
  }
  statusCloseModal.value = true;
  selectedChallanId.value = id;
};

const closeStatusCloseModal = () => {
    statusCloseModal.value = false;
};

const closeChallan = () => {
    form.get(route('challan.close',{id:selectedChallanId.value }), {
        onSuccess: () => closeStatusCloseModal()
    });
};


const returnProductData = ref([]);

const openReturnProductModal = (id) => {
  const challan = props.data.data.find(challan  => challan.id === id);
  if(challan){
    const filteredChallanDetails = challan.challan_detail.filter(detail =>
      (detail.qty - detail.invoiced_qty) > 0 && (detail.qty - detail.return_qty) > 0
    );
    returnProductData.value = filteredChallanDetails
  }
  returnProductModal.value = true;
  selectedChallanId.value = id;
};

const closeReturnProductModal = () => {
    returnProductModal.value = false;
};


const checkedValues = ref([]); // Array to hold checked values
const selectedChallan = ref([]);

const returnProduct = () => {
    form.get(route('challan.return',{id:selectedChallanId.value, detail: JSON.stringify(checkedValues.value) }), {
        onSuccess: () => closeReturnProductModal()
    });
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Open':
            return 'bg-blue-100';
        case 'In-process':
            return 'bg-yellow-100';
        case 'Close':
            return 'bg-green-100';
        case 'Foc':
            return 'bg-orange-100';
        case 'Demo Close':
            return 'bg-pink-100';
        default:
            return 'bg-red-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Open':
            return 'text-blue-600';
        case 'In-process':
            return 'text-yellow-600';
        case 'Close':
            return 'text-green-600';
        case 'Foc':
            return 'text-orange-600';
        case 'Demo Close':
            return 'text-pink-600';
        default:
            return 'text-red-600';
    }
};

const generatePDF = (filename) => {
    const sanitizedFilename = filename.replace(/\s+/g, '_').replace(/[<>:"./\\|?*]+/g, '');
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');
    html2canvas(pdfContent, { scale: 2 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 196;
        const pageHeight = 297;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let position = 0;
        if (imgHeight <= pageHeight) {
            doc.addImage(imgData, 'PNG', 7, 10, imgWidth, imgHeight);
        } else {
            while (position < imgHeight) {
                doc.addImage(imgData, 'PNG', 7, -position +10 , imgWidth, imgHeight);
                position += pageHeight - 20;
                if (position < imgHeight) {
                    doc.addPage();
                }
            }
        }
        doc.save(sanitizedFilename);
    });
};

const page = ref('portrait');

const setPageType = (id, name) => {
    page.value = id;
};


const downloadPDF = (id, page) => {
    window.open(`/challan/download/${id}/${page}`, '_blank');
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const isAllSelected = computed(() => {
    return selectedChallan.value.length === props.data.data.length;
});

const toggleSelectAll = (event) => {
    if (event.target.checked) {
        selectedChallan.value = props.data.data.map(poData => poData.id);
    } else {
        selectedChallan.value = [];
    }
};

const changeEvent = (event) => {
    console.log(event);
};

const closeMultiBox = (event) => {
    selectedChallan.value = [];
};

const isCustomerSame = computed(() => {
    if (props.data.data.length === 0) return false;
    const sameCustomer = props.data.data[0].customers.id;
    const sameOrganization  = props.data.data[0].organization_id;
    return props.data.data.every(poData => poData.customers.id === sameCustomer && poData.organization_id === sameOrganization);
});

</script>

<template>
    <Head title="Challan"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Challan</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, customerId, salesUserId, categoryId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                                <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateChallan">
                        <div class="flex justify-end">
                            <CreateButton :href="route('challan.create')">
                                Add Challan
                            </CreateButton>
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateChallan">
                        <div class="flex justify-end">
                            <CreateButton :href="route('challantransfer')">
                                Transfer Challan
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">

                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Organization Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Customer Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="customers"
                            v-model="customerId"
                            @onchange="setCustomers"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4" v-if="permissions.canCreateChallan">
                        <InputLabel for="customer_id" value="Sales Person" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="salesuser"
                            v-model="salesUserId"
                            @onchange="setSalesUser"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Category" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="category"
                            v-model="categoryId"
                            @onchange="setType"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th scope="col" class="px-4">
                                    <input type="checkbox"  v-if="isCustomerSame" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" :checked="isAllSelected" @change="toggleSelectAll" />
                                </th>
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                                <td class="">
                                    <div class="pl-3" v-if="poData.status != 'Close'  && permissions.canInvoiceChallan && poData.status != 'Cancelled' && poData.status != 'Foc' && poData.category != 'Demo' && isCustomerSame">
                                        <MultipleCheckbox v-model:checked="selectedChallan" :value="poData.id" @change="changeEvent" />
                                    </div>
                                </td>
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData.challan_number }}
                                </td>
                                <td class="px-4 py-2.5">
                                    {{ poData.category }}
                                </td>
                                <th scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ poData.customers.customer_name }}
                                </th>
                                <td class="px-4 py-2.5 min-w-52">
                                    {{ poData.users.first_name }} {{ poData.users.last_name }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatDate(poData.date) }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatAmount(poData.total_amount)}}
                                </td>
                                <td class="flex flex-1 items-center px-4 py-2.5">
                                    <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                        <span class="text-sm font-semibold whitespace-nowrap" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                    </div>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink v-if="poData.status == 'Open' && permissions.canEditChallan && poData.customers.organization_id == null" :href="route('challan.edit',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink v-if="poData.status == 'Open' && permissions.canEditChallan && poData.customers.organization_id != null" :href="route('challantransfer.edit',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button v-if="poData.status == 'Open' && permissions.canDeleteChallan && poData.customers.organization_id == null" type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                                <button v-if="poData.status == 'Open'  && permissions.canFocChallan" type="button" @click="openFocModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"/>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Convert To Foc
                                                    </span>
                                                </button>
                                                <button v-if="poData.status != 'Close' && poData.status != 'Demo Close' && permissions.canCloseChallan && poData.status != 'Cancelled' && poData.status != 'Foc' && poData.customers.organization_id == null" type="button"  @click="openstatusCloseModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 18L18 6M6 6l12 12"/>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Close Challan
                                                    </span>
                                                </button>
                                                <button v-if="poData.status != 'Close' && poData.status != 'Demo Close' && permissions.canCloseChallan && poData.status != 'Cancelled' && poData.status != 'Foc' && poData.customers.organization_id == null" type="button"  @click="openReturnProductModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Return Product
                                                    </span>
                                                </button>
                                                <ActionLink v-if="poData.status != 'Close'  && permissions.canInvoiceChallan && poData.status != 'Cancelled' && poData.status != 'Foc' && poData.category != 'Demo' && poData.customers.organization_id == null" :href="route('challan.invoice',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"  stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-width="1.5" stroke-linejoin="round" d="M3 7.5v9a1.5 1.5 0 001.5 1.5h15a1.5 1.5 0 001.5-1.5v-9H3z"></path><path stroke-linecap="round" stroke-linejoin="round" d="M6 6V3.75A1.5 1.5 0 017.5 2h9A1.5 1.5 0 0118 3.75V6m-15 0h18"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Generate Invoice
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button v-if="poData.status == 'Open' && poData.category == 'Demo'  && permissions.canCloseChallan && poData.status != 'Cancelled' && poData.status != 'Foc' && poData.customers.organization_id == null"  @click="openDemoCloseModal(poData.id)" type="button"   class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Demo Close
                                                    </span>
                                                </button>
                                                <ActionLink :href="route('challan.view',{id:poData.id})" v-if="permissions.canViewChallan">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View Challan
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button"  @click="openPreviewModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                            Generate PDF
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>

        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deletePO"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="statusCloseModal" @close="closeStatusCloseModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Following quantity are unsold</strong></h1>
                </div>
                <div class="mt-4" id="pdf-content">
                    <table>
                        <thead>
                            <tr>
                                <th>SN</th>
                                <th>Batch</th>
                                <th>Product Code</th>
                                <th>Product Name</th>
                                <th>HSN</th>
                                <th>Qty Received</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(poData, index) in  challanCloseData" :key="poData.id" >
                                <td>{{ index + 1 }}</td>
                                <td>{{ poData.viewserialnumbers.unique_id ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.item_code ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.name ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.hsn_code ?? '-' }}</td>
                                <td>{{ poData.qty - poData.invoiced_qty - poData.return_qty ?? '-' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="total" style=" margin-top: 20px; text-align: right;">
                    </div>
                </div>
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Do You Want to Close this Challan ?</strong></h1>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeStatusCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="closeChallan"
                    >
                        Close Challan
                    </PrimaryButton>
                    </div>
                </div>
            </div>
         </Modal>
         <Modal :show="returnProductModal" @close="closeReturnProductModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Following quantity are in this challan</strong></h1>
                </div>
                <div class="mt-4" id="pdf-content">
                    <table>
                        <thead>
                            <tr>
                                <th>SN</th>
                                <th>Batch</th>
                                <th>Product Code</th>
                                <th>Product Name</th>
                                <th>HSN</th>
                                <th>Qty Received</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(poData, index) in  returnProductData" :key="poData.id" >
                                <td class="">
                                    <div class="p-1">
                                        <MultipleCheckbox v-model:checked="checkedValues" :value="poData.id" />
                                    </div>
                                </td>
                                <td>{{ poData.viewserialnumbers.unique_id ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.item_code ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.name ?? '-' }}</td>
                                <td>{{ poData.viewserialnumbers.product.hsn_code ?? '-' }}</td>
                                <td>{{ poData.qty - poData.invoiced_qty - poData.return_qty?? '-' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="total" style=" margin-top: 20px; text-align: right;">
                    </div>
                </div>
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Do You Want to Return this products ?</strong></h1>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeReturnProductModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="returnProduct"
                    >
                        Confirm
                    </PrimaryButton>
                    </div>
                </div>
            </div>
         </Modal>
         <Modal :show="focModal" @close="closeFocModal">
            <div class="p-6">
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Do You Want to Convert this Challan to FOC ?</strong></h1>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeFocModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3"
                        @click="focChallan"
                    >
                        FOC
                    </PrimaryButton>
                    </div>
                </div>
            </div>
         </Modal>
        <Modal :show="demoCloseModal" @close="closeDemoCloseModal">
            <div class="p-6">
                <div class="header" style="display: flex; align-items: center; justify-content: space-between;">
                    <h1><strong>Do You Want to Close This Demo ?</strong></h1>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDemoCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3"
                        @click="closeDemo"
                    >
                        Demo Close
                    </PrimaryButton>
                    </div>
                </div>
            </div>
        </Modal>
        <Modal :show="generateChallanModal" @close="closeChallanModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
              <div class="container1 p-2" id="pdf-content">
                <div v-if="challanData.organization.id == '3'" class="header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                  <img class="w-20 h-20" :src="filePath + challanData.organization.logo" alt="logo">
                  <p><strong style="font-size: 20px;">Challan</strong></p>
                </div>
                <div v-if="challanData.organization.id == '1' || challanData.organization.id == '2'" class="header" style="align-items: start; justify-content: center; text-align: center;">
                  <img class="w-full h-10" :src="filePath + challanData.organization.logo" alt="logo">
                  <div style="align-items: center; justify-content: space-between; margin-bottom: 10px;">
                    <p style="font-size: 20px;"><strong>Challan</strong></p>
                  </div>
                </div>
                <div style="display:flex; justify-content: space-between;">
                  <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                    <p><strong style="font-size: 14px; margin-top: 10px">{{ challanData.organization.name }}</strong> </p>
                    <p>{{ challanData.organization.address_line_1 }}</p>
                    <p>{{ challanData.organization.address_line_2 }}</p>
                    <p>{{ challanData.organization.pincode }} , {{ challanData.organization.city }}</p>
                    <p><strong>Phone</strong>: {{ challanData.organization.contact_no }}</p>
                    <p><strong>Email</strong>: {{ challanData.organization.email }}</p>
                    <p><strong>GST</strong>: {{ challanData.organization.gst_no }}</p>
                  </div>
                  <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 320px;">
                    <div><strong>Challan Number:</strong> {{ challanData.challan_number }}</div>
                    <div><strong>Challan Date:</strong> {{ formatDate(challanData.date) }}</div>
                    <p><strong>{{ challanData.customers.customer_name }}</strong></p>
                    <p>{{ challanData.customers.address }}</p>
                    <p><strong>Phone</strong>: {{ challanData.customers.contact_no }}</p>
                    <p><strong>Email</strong>: {{ challanData.customers.email }}</p>
                    <p><strong>GST</strong>: {{ challanData.customers.gst_no }}</p>
                  </div>
                </div>

                <table>
                  <thead>
                    <tr>
                      <th>SN</th>
                      <th>Code</th>
                      <th>Product Name</th>
                      <th>HSN</th>
                      <th>Qty</th>
                      <th>Batch</th>
                      <th>Exp</th>
                      <th>Mrp</th>
                      <th>Rate</th>
                      <th>Discount</th>
                      <th v-if="challanData.customers.gst_type == 'IGST'">IGST</th>
                      <th v-if="challanData.customers.gst_type == 'CGST/SGST'">CGST</th>
                      <th v-if="challanData.customers.gst_type == 'CGST/SGST'">SGST</th>
                      <th>Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(poData, index) in challanData.challan_detail" :key="poData.id">
                      <td>{{ index + 1 }}</td>
                      <td>{{ poData.viewserialnumbers.product.item_code }}</td>
                      <td>{{ poData.viewserialnumbers.product.name }}<span style="display: flex">{{ poData.description }}</span></td>
                      <td>{{ poData.viewserialnumbers.product.hsn_code }}</td>
                      <td>{{ poData.qty }}</td>
                      <td>{{ poData.viewserialnumbers.batch ?? '-' }}</td>
                      <td>{{ poData.viewserialnumbers.expiry_date ?? '-' }}</td>
                      <td>{{ poData.viewserialnumbers.mrp ? formatAmount(poData.viewserialnumbers.mrp) : '-' }}</td>
                      <td>{{ formatAmount(poData.price) ?? '-' }}</td>
                      <td>{{ formatAmount(poData.discount) ?? '-' }}</td>
                      <td v-if="challanData.customers.gst_type == 'IGST'">{{ formatAmount(poData.gst) ?? '-' }}</td>
                      <td v-if="challanData.customers.gst_type == 'CGST/SGST'">{{ formatAmount(poData.gst / 2) ?? '-' }}</td>
                      <td v-if="challanData.customers.gst_type == 'CGST/SGST'">{{ formatAmount(poData.gst / 2) ?? '-' }}</td>
                      <td>{{ formatAmount(poData.total_price) ?? '-' }}</td>
                    </tr>
                  </tbody>
                </table>
                <div style="display:flex; justify-content: space-between;">
                  <div style="margin-bottom: 20px; width: 260px;">
                    <p>{{ challanData.note }}</p>
                  </div>
                  <div class="invoice-details" style="margin-bottom: 20px; width: 300px;">
                    <div><strong>Sub Total (₹):</strong> {{ formatAmount(challanData.sub_total) }}</div>
                    <div><strong>Total Discount Amount (₹):</strong> {{ formatAmount(challanData.total_discount) }}</div>
                    <div v-if="challanData.customers.gst_type == 'IGST'"><strong>Total IGST (₹):</strong> {{ formatAmount(challanData.igst) }}</div>
                    <div v-if="challanData.customers.gst_type == 'CGST/SGST'"><strong>Total CGST (₹):</strong> {{ formatAmount(challanData.cgst) }}</div>
                    <div v-if="challanData.customers.gst_type == 'CGST/SGST'"><strong>Total SGST (₹):</strong> {{ formatAmount(challanData.sgst) }}</div>
                    <div><strong>Total Amount (₹):</strong> {{ formatAmount(challanData.total_amount) }}</div>
                  </div>
                </div>
                <div style="display:flex; justify-content: space-between;">
                    <div class="" style="margin-bottom: 20px; justify-items: start; width: 400;">
                    </div>
                    <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                    <p><strong>FOR,</strong></p>
                    <p><strong>{{ challanData.organization.name }}</strong></p>
                        <img class="h-28" :src="filePath + challanData.organization.signature" alt="logo">
                    </div>
                </div>
              </div>
              <div class="mt-6 px-4 flex justify-end">
                <div class="flex flex-col justify-end space-y-6">
                    <div class="flex items-center space-x-2">
                        <InputLabel for="customer_id" value="Page Type :" />
                        <SearchableDropdown :options="pagetypes"
                        v-model="page"
                        @onchange="setPageType"
                    />
                    </div>
                </div>
            </div>
              <div class="mt-6 px-4 flex justify-end">
                <SecondaryButton @click="closeChallanModal"> Cancel </SecondaryButton>
                <div class="w-36">
                  <PrimaryButton
                    class="ml-3 w-20"
                    @click="downloadPDF(challanData.id, page)"
                  >
                    Generate Pdf
                  </PrimaryButton>
                </div>
              </div>
            </div>
        </Modal>
        <div v-if="selectedChallan.length > 0" style="transition: height 0.2s;" id="action" class="relative overflow-visible bottomaction flex items-center justify-between px-8 bg-white shadow border-gray-300 h-16" @change="toggleSelectAll">
            <div class="flex gap-2 items-center justify-start">
                <div class="">
                </div>
            </div>
            <div class="flex gap-3 items-center justify-end">
                <CreateButton :href="route('challan.combine-invoice',{detail: JSON.stringify(selectedChallan) })">
                    Generate Combine Invoice
                </CreateButton>
            </div>
            <button type="button" title="Cancel" @click="closeMultiBox">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 13L13 1M1 1L13 13" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </button>
        </div>
    </AdminLayout>

</template>


<style scoped>
    .error {
        border: 1px solid red;
    }
    .bottomaction {
        position: fixed;
        bottom: 0px;
        z-index: 1000;
        width: 100%;
        left: 0;
        box-shadow: 0px -4px 6px -1px rgba(0, 0, 0, 0.1), 0px -2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 4px 4px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 6px 4px !important;
        text-align: left;
        font-size: 12px;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
</style>


