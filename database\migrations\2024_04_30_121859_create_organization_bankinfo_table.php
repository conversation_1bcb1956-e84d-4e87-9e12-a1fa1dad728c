<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_bankinfo', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained( table: 'organizations', indexName: 'orgb_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('bank_name');
            $table->string('account_number')->nullable();
            $table->double('balance', 16, 2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_bankinfo');
    }
};
