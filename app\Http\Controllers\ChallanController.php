<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\User;
use App\Models\Product;
use App\Models\CustomerTransaction;
use App\Models\Challan;
use App\Models\Organization;
use App\Models\ChallanDetail;
use App\Models\Document;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\Company;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderReceives;
use App\Models\PurchaseTransaction;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseOrderReceiveDetails;
use App\Http\Requests\ChallanStoreRequest;
use App\Http\Requests\generateChallanRequest;
use App\Models\SerialNumbers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Traits\CommonTrait;
use App\Traits\QueryTrait;
use Config;
use PDF;

class ChallanController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Challan')->only(['index']);
        $this->middleware('permission:Create Challan')->only(['create', 'store']);
        $this->middleware('permission:Edit Challan')->only(['edit', 'store']);
        $this->middleware('permission:Delete Challan')->only('destroy');
        $this->middleware('permission:View Challan')->only('view');
        $this->middleware('permission:Challan Invoice')->only('generateChallan', 'saveInvoice');
        $this->middleware('permission:Challan Foc')->only('focChallan');
        $this->middleware('permission:Close Challan')->only('closeChallan');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $salesUserId = $request->input('sales_user_id');
        $categoryId = $request->input('category');
        $query  = Challan::with('challanDetail.viewserialnumbers.product', 'customers', 'users', 'organization', 'challanDetail.product');
        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        //for engineers only
        if(auth()->user()->can('Create Challan') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }
        if(!empty($search)){
            $query->where(function ($query) use ($search) {
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })->orWhereHas('users', function ($subquery) use ($search) {
                    $subquery->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%$search%"]);
                })
                ->orWhere('challan_number', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%");
            });
        }

        $searchableFields = ['challan_number', 'category', 'customers.customer_name', 'users.first_name', 'date', 'total_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Open', 'In-process', 'Foc','Demo Close','Close', 'Cancelled'];
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate(20);
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $category = Config::get('constants.challanCategory');
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allOption2);
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $salesuser->prepend($allSalesuser);
        $pagetypes = collect(Config::get('constants.pageTypes'));
        $data->withQueryString()->links();

        $permissions = [
            'canCreateChallan'      => auth()->user()->can('Create Challan'),
            'canEditChallan'        => auth()->user()->can('Edit Challan'),
            'canDeleteChallan'      => auth()->user()->can('Delete Challan'),
            'canViewChallan'        => auth()->user()->can('View Challan'),
            'canInvoiceChallan'     => auth()->user()->can('Challan Invoice'),
            'canFocChallan'         => auth()->user()->can('Challan Foc'),
            'canCloseChallan'       => auth()->user()->can('Close Challan'),
        ];

        return Inertia::render('Challan/List', compact('data', 'permissions', 'filepath','organization', 'customers', 'organizationId', 'customerId', 'salesuser', 'salesUserId', 'category', 'categoryId', 'pagetypes'));
    }

    public function create()
    {
        $challan_number = $this->generateCHLNumber();
        $demo_challan_number = $this->generateDemoCHLNumber();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', null)->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, lot_no, purchase_price, mrp, expiry_date, organization_id')->whereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('salesProducts')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.challanCategoryList');
        return Inertia::render('Challan/Add', compact('challan_number', 'demo_challan_number',  'customers', 'salesuser', 'serialno', 'products', 'organization', 'category'));
    }

    public function challanTransfer()
    {
        $challan_number = $this->generateCHLNumber();
        $demo_challan_number = $this->generateDemoCHLNumber();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', '!=', '')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, lot_no, purchase_price, mrp, expiry_date, organization_id')->whereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('salesProducts')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.challanCategoryList');
        return Inertia::render('Challan/ChallanTransferAdd', compact('challan_number', 'demo_challan_number',  'customers', 'salesuser', 'serialno', 'products', 'organization', 'category'));
    }

    public function challanTransferEdit(string $id)
    {
        $data  = Challan::where('id', $id)->with('challanDetail.viewserialnumbers.product', 'customers', 'documents', 'organization', 'challanDetail.product', 'users')->get()->toArray();
        $getSelectedNo = ChallanDetail::where('challan_id', $id)->with('viewserialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['viewserialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['viewserialnumbers']['id'];
            }
        }
        $filepath  = Config::get('constants.uploadFilePath.challanDocument');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $category = Config::get('constants.challanCategoryList');
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, lot_no, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        return Inertia::render('Challan/ChallanTransaferEdit', compact('data', 'customers', 'category', 'filepath', 'salesuser', 'serialno', 'products'));
    }

    public function store(ChallanStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;

            if(isset($data['challan_id'])){
                $updateChallan = Challan::where('id', $data['challan_id'])->update([
                    'note' => $data['note'],
                    'date' => $data['date'],
                    'category' => $data['category'],
                    'sales_user_id' => $data['sales_user_id'],
                    'customer_id' => $data['customer_id'],
                    'cgst' => $data['cgst'],
                    'sgst' => $data['sgst'],
                    'igst' => $data['igst'],
                    'sub_total' => $data['sub_total'],
                    'total_amount' => $data['total_amount'],
                    'total_discount' => $data['total_discount'],
                    'dispatch' => $data['dispatch'],
                    'transport' => $data['transport']
                ]);

                if(isset($data['stock_transfer']) && $data['stock_transfer'] == 'yes'){
                    $updatePurchaseOrder = PurchaseOrder::where('id', $data['purchase_order_id'])->update([
                        'igst'        => $data['igst'],
                        'sgst'        => $data['sgst'],
                        'cgst'        => $data['cgst'],
                        'date'        => $data['date'],
                        'total_gst'   => $data['total_gst'],
                        'sub_total'   => $data['sub_total'],
                        'total_amount'=> $data['total_amount'],
                        'total_discount' => $data['total_discount']
                    ]);
                    $updatePurchaseOrderReceives = PurchaseOrderReceives::where('id', $data['purchase_order_receive_id'])->update([
                        'total_price'      => $data['sub_total'],
                        'total_gst_amount' => $data['total_gst'],
                        'total_amount'     => $data['total_amount'],
                        'pending_amount'   => $data['total_amount'],
                        'po_receive_date'  => $data['date'],
                        'customer_invoice_date' => $data['date']
                    ]);
                    $purchaseTransaction = PurchaseTransaction::where(['entity_type' => 'purchase_invoice', 'entity_id' => $data['purchase_order_receive_id']])->first();
                    $purchaseTransaction->update(['amount' => $data['total_amount']]);

                    if($updatePurchaseOrder){
                        foreach ($data['selectedProductItem'] as $invoiceDetails) {
                            if(empty($invoiceDetails['challan_detail_id'])) {
                                $customer = Customer::where('id', $data['customer_id'])->first();
                                $invoiceDetails['purchase_order_id'] = $data['purchase_order_id'];
                                $invoiceDetails['receive_qty'] = $invoiceDetails['qty'];
                                $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                                $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                                $purchaseOrderDetail = PurchaseOrderDetail::create($invoiceDetails);
                                $receivedProduct['purchase_order_receive_id'] = $data['purchase_order_receive_id'];
                                $receivedProduct['purchase_order_detail_id'] = $purchaseOrderDetail->id;
                                $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                                $receivedProduct['organization_id'] = $customer->organization_id;
                                $receivedProduct['product_id'] = $invoiceDetails['product_id'];
                                $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                                $receivedProduct['updated_by'] = $receivedProduct['created_by'] = Auth::user()->id;
                                $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);

                                $serialNo = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                                $productDetails['batch']            = $serialNo->batch;
                                $productDetails['organization_id']  = $customer->organization_id;
                                $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                                $productDetails['product_id']       = $invoiceDetails['product_id'];
                                $productDetails['mrp']              = $serialNo->mrp;
                                $productDetails['expiry_date']      = $serialNo->expiry_date;
                                $productDetails['purchase_price']   = $invoiceDetails['sell_price'];
                                $productDetails['receive_qty']      = $invoiceDetails['qty'];
                                $productDetails['sell_qty']         = 0;
                                $productDetails['created_by']       = $data['created_by'];
                                $productDetails['updated_by']       = $data['created_by'];
                                $productDetails['unique_id']        = $serialNo->unique_id;
                                $createProduct =  SerialNumbers::create($productDetails);
                            }
                        }
                    }
                }

                foreach ($data['selectedProductItem'] as $challanDetails) {
                    $challanDetails['challan_id'] = $data['challan_id'];
                    $challanDetails['price'] = $challanDetails['sell_price'];
                    if (!empty($challanDetails['challan_detail_id'])) {
                        $challanDetails['updated_by'] = Auth::user()->id;
                        $challanDetail = ChallanDetail::find($challanDetails['challan_detail_id']);
                        // to do changes
                        $getSellQty = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->first();
                        $updatedQty =  $getSellQty->sell_qty + $challanDetails['qty'] - $challanDetail->qty;
                        if($getSellQty){
                            $updateStatus  = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                        }
                        $challanDetail->update($challanDetails);

                    } else {
                        $challanDetails['invoiced_qty'] = 0;
                        $challanDetails['gst'] = isset($challanDetails['gst']) ? $challanDetails['gst'] : 0;
                        $challanDetails['created_by'] = $challanDetails['updated_by'] = Auth::user()->id;
                        $challanDetail = ChallanDetail::create($challanDetails);

                        $getSellQty = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->first();
                        $updatedQty = $getSellQty->sell_qty + $challanDetails['qty'] ;
                        if($getSellQty){
                            $updateStatus  = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                        }
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['challan_id']);
                }
                DB::commit();
                return Redirect::to('/challan')->with('success','Challan Updated Successfully');
            } else {
                // dd($data);
                $data['status'] = "Open";
                // if($data['category'] == 'Demo'){
                //     $this->updateDemoCHLNumber($data['challan_number'], $data['organization_id']);
                // } else {
                //     $this->updateCHLNumber($data['challan_number'], $data['organization_id']);
                // }

                if ($data['category'] == 'Demo') {
                    $demoCHLNumber = $this->generateDemoCHLNumber();
                    $data['challan_number'] = $demoCHLNumber[$data['organization_id']];
                    $this->updateDemoCHLNumber($data['challan_number'], $data['organization_id']);
                } else {
                    $chlNumber = $this->generateCHLNumber();
                    $data['challan_number'] = $chlNumber[$data['organization_id']];
                    $this->updateCHLNumber($data['challan_number'], $data['organization_id']);
                }
                $challan = Challan::create($data);

                if(isset($data['stock_transfer']) && $data['stock_transfer'] == 'yes'){
                    //purchase order
                    $customer = Customer::where('id', $data['customer_id'])->first();
                    $organization = Organization::where('id', $data['organization_id'])->first();
                    $company  = Company::where('name', 'like', $organization->name)->first();
                    $po_number = $this->generatePONumber();
                    $data['po_number'] = $po_number[$customer->organization_id];
                    $data['organization_id'] = $customer->organization_id;
                    $data['company_id'] = $company->id;
                    $data['status'] = "Completed";
                    $data['note'] = "stock transfer";
                    $data['type'] = "challan";
                    $purchaseOrder = PurchaseOrder::create($data);

                    //purchase order receive
                    $po_receive_number = $this->generatePOReceiveNumber();
                    $data['po_receive_number'] = $po_receive_number[$customer->organization_id];
                    $data['po_receive_date'] =  $data['customer_invoice_date'] = $data['date'];
                    $data['customer_invoice_no'] = $data['challan_number'];
                    $data['purchase_order_id'] = $purchaseOrder->id;
                    $data['total_price'] = $data['sub_total'];
                    $data['total_gst_amount'] = $data['total_gst'];
                    $data['status'] = "Unpaid";
                    $purchaseOrderReceiveId = PurchaseOrderReceives::create($data);

                    $challanUpdate = Challan::where('id',  $challan->id);
                    $challanUpdate->update(['purchase_order_id'=> $purchaseOrder->id, 'purchase_order_receive_id' => $purchaseOrderReceiveId->id]);

                    //purchase transaction
                    $data['amount'] = $data['total_amount'];
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'Bill Number: ' .$data['customer_invoice_no']. ' ' .'Date: ' .$data['customer_invoice_date'];
                    $data['entity_id'] = $purchaseOrderReceiveId->id;
                    $data['date'] = $purchaseOrderReceiveId->customer_invoice_date;
                    $data['entity_type'] = 'purchase_invoice';
                    PurchaseTransaction::create($data);

                    $this->updatePONumber($data['po_number'], $data['organization_id']);
                    $this->updatePOReceiveNumber($data['po_receive_number'], $data['organization_id']);

                    if($purchaseOrder){
                        foreach ($data['selectedProductItem'] as $invoiceDetails) {
                            $invoiceDetails['purchase_order_id'] = $purchaseOrder->id;
                            $invoiceDetails['receive_qty'] = $invoiceDetails['qty'];
                            $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                            $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                            $purchaseOrderDetail = PurchaseOrderDetail::create($invoiceDetails);

                            $receivedProduct['purchase_order_receive_id'] = $purchaseOrderReceiveId->id;
                            $receivedProduct['purchase_order_detail_id'] = $purchaseOrderDetail->id;
                            $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                            // $receivedProduct['organization_id'] = $data['organization_id'];
                            $receivedProduct['product_id'] = $invoiceDetails['product_id'];
                            $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                            $receivedProduct['organization_id'] = $customer->organization_id;
                            $receivedProduct['updated_by'] = $receivedProduct['created_by'] = Auth::user()->id;
                            $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);

                            $serialNo = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                            $productDetails['batch']            = $serialNo->batch;
                            $productDetails['organization_id']  = $customer->organization_id;
                            $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                            $productDetails['product_id']       = $invoiceDetails['product_id'];
                            $productDetails['mrp']              = $serialNo->mrp;
                            $productDetails['expiry_date']      = $serialNo->expiry_date;
                            $productDetails['purchase_price']   = $invoiceDetails['sell_price'];
                            $productDetails['receive_qty']      = $invoiceDetails['qty'];
                            $productDetails['sell_qty']         = 0;
                            $productDetails['created_by']       = $data['created_by'];
                            $productDetails['updated_by']       = $data['created_by'];
                            $productDetails['unique_id']        = $serialNo->unique_id;
                            $createProduct =  SerialNumbers::create($productDetails);
                            Product::addProductLog($createProduct, $productDetails, $invoiceDetails['product_id'], $data['organization_id'], 'received');
                        }
                    }
                }

                if($challan){
                    foreach ($data['selectedProductItem'] as $challanDetails) {
                        $challanDetails['gst'] = isset($challanDetails['gst']) ? $challanDetails['gst'] : 0;
                        $challanDetails['challan_id'] = $challan->id;
                        $challanDetails['created_by'] = $challanDetails['updated_by'] = Auth::user()->id;
                        $challanDetails['price'] = $challanDetails['sell_price'];
                        $challanDetails['invoiced_qty'] = 0;
                        $challanDetail = ChallanDetail::create($challanDetails);

                        $getSellQty = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->first();
                        $updatedQty = $getSellQty->sell_qty + $challanDetails['qty'];
                        if($getSellQty){
                            $updateStatus  = SerialNumbers::where('id',  $challanDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                        }
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $challan->id);
                }
                DB::commit();
                return Redirect::to('/challan')->with('success','Challan Created Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/challan')->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data  = Challan::where('id', $id)->with('challanDetail.viewserialnumbers.product', 'customers', 'documents', 'organization', 'challanDetail.product', 'users')->get()->toArray();
        $getSelectedNo = ChallanDetail::where('challan_id', $id)->with('viewserialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['viewserialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['viewserialnumbers']['id'];
            }
        }
        $filepath  = Config::get('constants.uploadFilePath.challanDocument');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->where('organization_id', null)->orderByRaw('customer_name')->get();
        $category = Config::get('constants.challanCategoryList');
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, lot_no, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        return Inertia::render('Challan/Edit', compact('data', 'customers', 'category', 'filepath', 'salesuser', 'serialno', 'products'));
    }

    public function view(Request $request, $id)
    {
        $data = Challan::where('id', $id)->with('challanDetail.viewserialnumbers.product', 'customers', 'documents', 'users', 'invoice', 'organization', 'challanDetail.product')->get()->toArray();
        return Inertia::render('Challan/View', compact('data'));
    }

    public function generateChallan(string $id)
    {
        $challan = Challan::find($id);
        $organizationId = $challan->organization_id;

        $data = Challan::where('id', $id)->with([
            'challanDetail' => function ($query) use ($organizationId)  {
                $query->with(['viewserialnumbers.product' => function ($query) use ($organizationId) {
                        // $query->where('organization_id', $organizationId);
                }, 'product'])->whereRaw('qty - (invoiced_qty + return_qty) > 0');
            }, 'customers', 'documents', 'organization'])->get()->toArray();
        // dd($data);
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $filepath  = Config::get('constants.uploadFilePath.challanDocument');
        $invoice_no = $this->generateInvoiceNo();
        $retail_invoice_no = $this->generateRetailInvoiceNo();
        return Inertia::render('Challan/Invoice', compact('data', 'filepath', 'salesuser', 'invoice_no', 'retail_invoice_no'));
    }

    public function saveInvoice(generateChallanRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            $data['status'] = "Unpaid";
            $data['entity_id'] = $data['challan_id'];
            $data['entity_type'] = "challan";
            if($data['invoice_type'] == 'Tax'){
                $this->updateInvoiceNo($data['invoice_no'], $data['organization_id']);
            } else {
                $this->updateRetailInvoiceNo($data['invoice_no'], $data['organization_id']);
            }
            $data['pending_amount'] = $data['total_amount'];// TO DO CHECK WHILE EDIT
            $challanIds = array_column($data['challans'], 'id');
            $challanIdsString = implode(',', $challanIds);
            $data['challan_ids'] = $challanIdsString;
            $invoice = Invoice::create($data);

            if($invoice){
                foreach ($data['selectedProductItem'] as $invoiceDetails) {
                    if($invoiceDetails['check'] == true){
                        $invoiceDetails['invoice_id'] = $invoice->id;
                        $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                        $invoiceDetail = InvoiceDetail::create($invoiceDetails);

                        //invoiced Qty
                        $challanDetail = ChallanDetail::find($invoiceDetails['challan_detail_id']);
                        $updateInvoicedQty['invoiced_qty'] = $challanDetail->invoiced_qty + $invoiceDetails['qty'];
                        $challanDetail->update($updateInvoicedQty);
                    }
                }
            }

            $files = $request->file('document');
            if($files){
                $this->uploadInvoiceDocuments($files, $invoice->id);
            }

            //update challan status
            foreach($data['challans'] as $challans){
                $challanDetail = ChallanDetail::where('challan_id', $challans['id'])->where('is_receive', NULL);
                $totalQty = $challanDetail->sum('qty');
                $totalInvoicedQty = $challanDetail->sum('invoiced_qty') + $challanDetail->sum('return_qty');
                if ($totalInvoicedQty == $totalQty) {
                    $status = 'Close';
                } elseif ($totalInvoicedQty > 0) {
                    $status = 'In-process';
                }
                $updateChallan = Challan::where('id', $challans['id'])->update([
                    'status' => $status
                ]);
            }
            //customer Transaction
            $data['amount'] = $data['total_amount'];
            $data['payment_type'] = 'dr';
            $data['entity_id'] = $invoice->id;
            $data['entity_type'] = 'invoice';
            $data['note'] = 'Invoice No :' .$data['invoice_no'];
            CustomerTransaction::create($data);

            DB::commit();
            return Redirect::to('/challan')->with('success','Invoice Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/challan')->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $challan = Challan::find($id);
                $data = Challan::where('id', $id)->with('challanDetail.viewserialnumbers')->first();
                foreach ($data->challanDetail as $detail) {
                    $serialnumbers = $detail->viewserialnumbers;
                    $serialnumbers->sell_qty = $serialnumbers->sell_qty - $detail->qty;
                    $serialnumbers->save();
                }
                $challan->delete();
                $challanDetail = ChallanDetail::where('challan_id', $id)->delete();
            DB::commit();
            return Redirect::back()->with('success','Challan Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function focChallan($id)
    {
        DB::beginTransaction();
        try {
            $data = Challan::where('id', $id)->with('challanDetail.viewserialnumbers')->first();
            $data->status = 'Foc';
            $data->save();
            DB::commit();
            return Redirect::back()->with('success','Challan Foc Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function demoClose($id)
    {
        DB::beginTransaction();
        try {
            $data = Challan::where('id', $id)->with('challanDetail.viewserialnumbers')->first();
            $data->status = 'Demo Close';
            $data->save();
            DB::commit();
            return Redirect::back()->with('success','Demo Close Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function closeChallan($id)
    {
        DB::beginTransaction();
        try {
            // (detail.qty - detail.invoiced_qty) > 0 && (detail.qty - detail.return_qty) > 0
                $data = Challan::where('id', $id)->with('closeChallanData.viewserialnumbers')->first();
                foreach ($data->closeChallanData as $detail) {

                    $remainingQty = $detail->qty - ($detail->invoiced_qty + $detail->return_qty);
                    $detailData = $detail->toArray();
                    $detailData['is_receive'] = 'yes';
                    $detailData['qty'] = $remainingQty;
                    ChallanDetail::create($detailData);

                    $detail->return_qty = $detail->return_qty + $remainingQty;
                    $detail->save();

                    $serialnumbers = $detail->viewserialnumbers;
                    $serialnumbers->sell_qty = $serialnumbers->sell_qty - $remainingQty;
                    $serialnumbers->save();
                }
                $data->status = 'Close';
                $data->save();
            DB::commit();
            return Redirect::back()->with('success','Challan Close Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function challanReturn($id, $challandetailIds)
    {
        $challandetailId = json_decode($challandetailIds, true);
        DB::beginTransaction();
        try {
                $challanDetails = ChallanDetail::whereIn('id', $challandetailId)->with('viewserialnumbers')->get();

                foreach ($challanDetails as $detail) {
                    $remainingQty = $detail->qty - ($detail->invoiced_qty + $detail->return_qty);
                    $detailData = $detail->toArray();
                    $detailData['is_receive'] = 'yes';
                    $detailData['qty'] = $remainingQty;
                    ChallanDetail::create($detailData);
                    $detail->return_qty = $detail->return_qty + $remainingQty;
                    $detail->save();
                    $serialnumbers = $detail->viewserialnumbers;
                    $serialnumbers->sell_qty = $serialnumbers->sell_qty - $remainingQty;
                    $serialnumbers->save();
                }
                //update challan status
                $challanDetail = ChallanDetail::where('challan_id', $id)->where('is_receive', NULL);
                $totalQty = $challanDetail->sum('qty');
                $totalInvoicedQty = $challanDetail->sum('return_qty') + $challanDetail->sum('invoiced_qty');
                if ($totalInvoicedQty == $totalQty) {
                    $status = 'Close';
                } elseif ($totalInvoicedQty > 0) {
                    $status = 'In-process';
                }
                $updateChallan = Challan::where('id', $id)->update([
                    'status' => $status
                ]);
            DB::commit();
            return Redirect::back()->with('success','Product Return Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function generateCombineInvoice($ids)
    {
        $challanIds = json_decode($ids, true);
        $data = ChallanDetail::whereIn('challan_id', $challanIds)->where('is_receive', NULL)->with(['viewserialnumbers.product', 'product', 'challan.customers', 'challan.documents', 'challan.organization'])->whereRaw('qty - (invoiced_qty + return_qty) > 0')->get()->toArray();
        // dd($data);
        $challanData = Challan::whereIn('id', $challanIds)->get()->toArray();
        $filepath  = Config::get('constants.uploadFilePath.challanDocument');
        $invoice_no = $this->generateInvoiceNo();
        $retail_invoice_no = $this->generateRetailInvoiceNo();
        return Inertia::render('Challan/Combine-invoice', compact('data', 'filepath', 'invoice_no', 'retail_invoice_no', 'challanData'));
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.challanDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "challan";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    private function uploadInvoiceDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.Invoice');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "invoice";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    public function downloadChallan($id, $type)
{
    $data = Challan::where('id', $id)->with('challanDetail.viewserialnumbers.product', 'customers', 'organization', 'documents', 'invoice')->get();

    foreach ($data as $challan) {
        $challan->challanDetail = $challan->challanDetail->filter(function ($detail) {
            return ($detail->qty - $detail->return_qty) > 0;
        });
    }
    $filepath = Config::get('constants.uploadFilePath.companyDocument');
    $pdf = PDF::loadView('pdf.challan', compact('data', 'filepath'))->setPaper('A4', $type);
    $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
    return $pdf->download("Challan_{$sanitizedFilename}.pdf");
    // return $pdf->stream();
}

}
