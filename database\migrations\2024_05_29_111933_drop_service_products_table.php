<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::drop('service_products');

        Schema::table('purchase_order_receive_details', function (Blueprint $table) {
            $table->dropColumn('stock');
            $table->dropColumn('purchase_price');
        });

        Schema::table('invoice', function (Blueprint $table) {
            $table->bigInteger('sales_user_id')->nullable()->after('invoice_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
