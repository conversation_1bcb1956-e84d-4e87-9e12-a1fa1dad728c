<?php

namespace App\Http\Controllers;

use App\Models\EmailTemplate;
use App\Models\EmailTags;
use Illuminate\Http\Request;
use App\Http\Requests\EmailTemplateRequest;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Config;
use Illuminate\Support\Facades\Redirect;
use App\Traits\QueryTrait;
use Illuminate\Support\Facades\DB;

class EmailTemplateController extends Controller
{

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:Email Template')->only(['index','create', 'store','edit', 'update','destroy']);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $Id = $request->input('id');

        $query = EmailTemplate::query();

        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('template_name', 'like', "%$search%")
                      ->orWhere('email_subject', 'like', "%$search%")
                      ->orWhere('content', 'like', "%$search%");
            });

            if($Id) {
                $query->where('created_by', $Id);
            }
        }

        $searchableFields = ['template_name'];
        $this->searchAndSort($query, $request, $searchableFields);

        $perPage = Config::get('constants.perPage');
        $templates = $query->orderBy('id', 'desc')->paginate($perPage);

        return Inertia::render('emailtemplate/index', [
            'templates' => $templates,
            'id' => $Id,
        ]);
     }

    public function create()
    {
        $tags  = EmailTags::select('id', 'name', 'description')->get();

        return Inertia::render('emailtemplate/create', compact('tags'));
    }

    public function store(EmailTemplateRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            if (isset($data['id'])) {
                $data['updated_by'] = auth()->id();
                $emailTemplate = EmailTemplate::findOrFail($data['id']);
                $emailTemplate->update($data);

                DB::commit();
                return redirect()->route('emailtemplates.index')->with('success', 'Email template updated successfully!');
            } else {
                $data['created_by'] = $data['updated_by'] = auth()->id();
                EmailTemplate::create($data);

                DB::commit();
                return redirect()->route('emailtemplates.index')->with('success', 'Email template created successfully!');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $templates = EmailTemplate::findOrFail($id);
        $tags  = EmailTags::select('id', 'name', 'description')->get();

        return Inertia::render('emailtemplate/Edit', [
            'templates' => $templates,
            'tags' => $tags,
        ]);
    }


    public function update(EmailTemplateRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->validate([
                'email_subject' => 'required|string|max:255',
                'template_name' => 'required|string|max:255',
                'content' => 'required|string',
            ]);

            $data['updated_by'] = Auth::id();

            $template = EmailTemplate::findOrFail($id);
            $template->update($data);

            DB::commit();

            $redirectUrl = session('emailtemplate_list_page_url', route('emailtemplates.index'));
            session()->forget('emailtemplate_list_page_url');

            return Redirect::to($redirectUrl)->with('success', 'Email Template Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $template = EmailTemplate::findOrFail($id);
            $template->delete();

            DB::commit();
            return Redirect::to('/emailtemplates')->with('success', 'Email Template Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/emailtemplates')->with('error', $e->getMessage());
        }
    }
}
