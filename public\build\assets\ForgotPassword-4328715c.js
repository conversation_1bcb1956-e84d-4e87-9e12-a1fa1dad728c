import{h as d,o as l,e as u,w as i,a as s,u as t,Z as c,c as p,t as f,f as g,b as a,g as _,n as y,d as w}from"./app-497d70e1.js";import{G as x}from"./GuestLayout-64b659a1.js";import{_ as h}from"./InputError-9708b76b.js";import{_ as b}from"./InputLabel-5f63a3d9.js";import{P as k}from"./PrimaryButton-8958b93e.js";import{_ as V}from"./TextInput-affa926c.js";import"./_plugin-vue_export-helper-c27b6911.js";const v=a("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"}," Forgot your password ?",-1),B=a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1),N={key:0,class:"mb-4 font-medium text-sm text-green-600"},P=["onSubmit"],S={class:"flex items-center justify-end mt-4"},q={__name:"ForgotPassword",props:{status:{type:String}},setup(o){const e=d({email:""}),m=()=>{e.post(route("password.email"))};return(C,r)=>(l(),u(x,null,{default:i(()=>[s(t(c),{title:"Forgot Password"}),v,B,o.status?(l(),p("div",N,f(o.status),1)):g("",!0),a("form",{onSubmit:w(m,["prevent"])},[a("div",null,[s(b,{for:"email",value:"Email"}),s(V,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(e).email,"onUpdate:modelValue":r[0]||(r[0]=n=>t(e).email=n),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),s(h,{class:"mt-2",message:t(e).errors.email},null,8,["message"])]),a("div",S,[s(k,{class:y({"opacity-25":t(e).processing}),disabled:t(e).processing},{default:i(()=>[_(" Email Password Reset Link ")]),_:1},8,["class","disabled"])])],40,P)]),_:1}))}};export{q as default};
