import{m as _,p as N,j as $,r as m,o,c as n,b as t,B as w,k as j,E as L,a as p,w as r,n as a,T as V,e as l,u as z,y as S,t as y,f as s,G as b,K as x,h as T,g as u}from"./app-2ecbacfc.js";const A={class:""},B={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:String,default:"py-1 bg-white"}},setup(d){const i=d,h=M=>{v.value&&M.key==="Escape"&&(v.value=!1)};_(()=>document.addEventListener("keydown",h)),N(()=>document.removeEventListener("keydown",h));const g=$(()=>({48:"w-48"})[i.width.toString()]),e=$(()=>i.align==="left"?"origin-top-left left-0":i.align==="right"?"origin-top-right right-0":"origin-top"),v=m(!1);return(M,k)=>(o(),n("div",A,[t("div",{onClick:k[0]||(k[0]=C=>v.value=!v.value)},[w(M.$slots,"trigger")]),j(t("div",{class:"fixed inset-0 z-40",onClick:k[1]||(k[1]=C=>v.value=!1)},null,512),[[L,v.value]]),p(V,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:r(()=>[j(t("div",{class:a(["absolute z-50 mt-2 rounded-md shadow-lg",[g.value,e.value]]),style:{display:"none"},onClick:k[2]||(k[2]=C=>v.value=!1)},[t("div",{class:a(["rounded-md ring-1 ring-black ring-opacity-5",d.contentClasses])},[w(M.$slots,"content")],2)],2),[[L,v.value]])]),_:3})]))}};const c={__name:"SideMenu",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(d){const i=d,h=$(()=>i.active?"block bg-indigo-600 text-white group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out":"block text-gray-700 hover:text-white hover:bg-indigo-600 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out");return(g,e)=>(o(),l(z(S),{href:d.href,class:a(h.value)},{default:r(()=>[w(g.$slots,"svg"),w(g.$slots,"name")]),_:3},8,["href","class"]))}},q={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},O={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},E={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-blue-100 border border-blue-400 shadow-lg ring-1 ring-black ring-opacity-5"},H={class:"p-4"},P={class:"flex items-start"},U=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-blue-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 8v0.01M12 11v5"})])],-1),D={class:"ml-3 w-0 flex-1 pt-0.5"},F={class:"text-sm font-medium text-blue-700"},I=b('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-blue-700 bg-blue-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),G={__name:"ToastNotification",props:{message:String},setup(d){const i=m(!1),h=()=>{i.value=!1,x().props.flash.message=""};return _(()=>{i.value=!0,setTimeout(()=>h(),2e3)}),(g,e)=>i.value&&d.message?(o(),n("div",q,[t("div",O,[t("div",E,[t("div",H,[t("div",P,[U,t("div",D,[t("p",F,y(d.message)+"!",1)]),I])])])])])):s("",!0)}},R={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},W={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},J={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-green-100 border border-green-400 shadow-lg ring-1 ring-black ring-opacity-5"},Q={class:"p-4"},K={class:"flex items-start"},Y=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-green-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),X={class:"ml-3 w-0 flex-1 pt-0.5"},Z={class:"text-sm font-medium text-green-700"},ee=b('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-green-700 bg-green-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),te={__name:"ToastNotificationSuccess",props:{message:String},setup(d){const i=m(!1),h=()=>{i.value=!1,x().props.flash.success=""};return _(()=>{i.value=!0,setTimeout(()=>h(),2e3)}),(g,e)=>i.value&&d.message?(o(),n("div",R,[t("div",W,[t("div",J,[t("div",Q,[t("div",K,[Y,t("div",X,[t("p",Z,y(d.message)+"!",1)]),ee])])])])])):s("",!0)}},re={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},oe={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},ne={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-red-100 border border-red-400 shadow-lg ring-1 ring-black ring-opacity-5"},se={class:"p-4"},ie={class:"flex items-start"},ae=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8 8l8 8m0-8L8 16"})])],-1),le={class:"ml-3 w-0 flex-1 pt-0.5"},ue={class:"text-sm font-medium text-red-700"},ce=b('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-red-700 bg-red-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),de={__name:"ToastNotificationError",props:{message:String},setup(d){const i=m(!1),h=()=>{i.value=!1,x().props.flash.error=""};return _(()=>{i.value=!0,setTimeout(()=>h(),2e3)}),(g,e)=>i.value&&d.message?(o(),n("div",re,[t("div",oe,[t("div",ne,[t("div",se,[t("div",ie,[ae,t("div",le,[t("p",ue,y(d.message)+"!",1)]),ce])])])])])):s("",!0)}},he={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},pe={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},ve={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-yellow-100 border border-yellow-400 shadow-lg ring-1 ring-black ring-opacity-5"},ge={class:"p-4"},fe={class:"flex items-start"},me=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-yellow-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 2L2 22h20L12 2zM12 16v-4M12 20v-2"})])],-1),we={class:"ml-3 w-0 flex-1 pt-0.5"},ke={class:"text-sm font-medium text-yellow-700"},ye=b('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-yellow-700 bg-yellow-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),_e={__name:"ToastNotificationWarning",props:{message:String},setup(d){const i=m(!1),h=()=>{i.value=!1,x().props.flash.warning=""};return _(()=>{i.value=!0,setTimeout(()=>h(),2e3)}),(g,e)=>i.value&&d.message?(o(),n("div",he,[t("div",pe,[t("div",ve,[t("div",ge,[t("div",fe,[me,t("div",we,[t("p",ke,y(d.message)+"!",1)]),ye])])])])])):s("",!0)}};const f={__name:"ActionLink",props:{href:{type:String,required:!0}},setup(d){return(i,h)=>(o(),l(z(S),{href:d.href,class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none transition duration-150 ease-in-out w-full"},{default:r(()=>[w(i.$slots,"svg"),w(i.$slots,"text")]),_:3},8,["href"]))}};const be={class:"bg-white lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r"},Me={class:"bg-white flex h-16 px-6 items-center px-10 shrink-0 w-full mt-2"},xe=["src"],$e={class:"flex grow flex-col gap-y-2 overflow-y-auto mt-2"},Ce={class:"flex flex-1 flex-col px-6"},je={role:"list",class:"flex flex-1 flex-col gap-y-7"},Le={role:"list",class:"-mx-2 space-y-1"},Be=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"},null,-1),ze=[Be],Se=t("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3 14l-3-3-3 3M12 5a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"},null,-1),Ne=[Se],Ve=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),Te=t("path",{d:"M14 2v4h4"},null,-1),Ae=t("path",{d:"M8 10h8"},null,-1),qe=t("path",{d:"M8 14h6"},null,-1),Oe=t("path",{d:"M8 18h8"},null,-1),Ee=[Ve,Te,Ae,qe,Oe],He=t("rect",{x:"5",y:"4",width:"14",height:"20",rx:"2"},null,-1),Pe=t("path",{d:"M7 4v2"},null,-1),Ue=t("path",{d:"M17 4v2"},null,-1),De=t("path",{d:"M7 8h10"},null,-1),Fe=t("path",{d:"M7 12h10"},null,-1),Ie=t("path",{d:"M7 16h6"},null,-1),Ge=t("rect",{x:"22",y:"5",width:"3",height:"18",rx:"1"},null,-1),Re=t("path",{d:"M22 23h3v2h-3z"},null,-1),We=[He,Pe,Ue,De,Fe,Ie,Ge,Re],Je=t("path",{d:"M9 3.5l2.5 2.5 5-5","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Qe=t("g",{transform:"translate(0, 2)"},[t("path",{d:"M3 6h2l3.6 9.6a1 1 0 0 0 .94.7h8.92a1 1 0 0 0 .94-.7L21 8H6"}),t("circle",{cx:"9",cy:"20",r:"2"}),t("circle",{cx:"17",cy:"20",r:"2"})],-1),Ke=[Je,Qe],Ye=t("path",{d:"M4 21V7a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v14"},null,-1),Xe=t("path",{d:"M8 10h2M8 14h2M8 18h2M14 10h2M14 14h2M14 18h2"},null,-1),Ze=t("path",{d:"M10 21v-4h4v4"},null,-1),et=t("path",{d:"M2 21h20"},null,-1),tt=[Ye,Xe,Ze,et],rt=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),ot=t("path",{d:"M14 2v4h4"},null,-1),nt=t("path",{d:"M8 12h8l-1.5 4h-5z"},null,-1),st=t("circle",{cx:"10",cy:"18",r:"1"},null,-1),it=t("circle",{cx:"14",cy:"18",r:"1"},null,-1),at=[rt,ot,nt,st,it],lt=t("rect",{x:"4",y:"3",width:"16",height:"18",rx:"2",ry:"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),ut=t("path",{d:"M6 3v2M9 3v2M12 3v2M15 3v2M18 3v2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),ct=t("rect",{x:"5",y:"7",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),dt=t("path",{d:"M5.8 8.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),ht=t("path",{d:"M10 8.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),pt=t("rect",{x:"5",y:"12",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),vt=t("path",{d:"M5.8 13.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),gt=t("path",{d:"M10 13.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),ft=t("rect",{x:"5",y:"17",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),mt=t("path",{d:"M5.8 18.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),wt=t("path",{d:"M10 18.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),kt=[lt,ut,ct,dt,ht,pt,vt,gt,ft,mt,wt],yt=t("rect",{x:"3",y:"2",width:"18",height:"20",rx:"2",ry:"2"},null,-1),_t=t("path",{d:"M3 6h14M3 10h12M3 14h10M3 18h8","stroke-linecap":"round"},null,-1),bt=t("path",{d:"M16 15l4 4m-5-3l3 3m-2-4l4 4M14 17l5 5","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Mt=[yt,_t,bt],xt=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"},null,-1),$t=[xt],Ct=t("rect",{x:"4",y:"3",width:"16",height:"18",rx:"2",ry:"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),jt=t("text",{x:"7",y:"9","font-size":"2.5","font-weight":"normal",fill:"currentColor"},"INVOICE",-1),Lt=t("path",{d:"M7 12h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Bt=t("path",{d:"M7 15h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),zt=t("path",{d:"M7 18h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),St=[Ct,jt,Lt,Bt,zt],Nt=t("rect",{x:"6",y:"6",width:"36",height:"28",rx:"3",ry:"3",fill:"none"},null,-1),Vt=t("circle",{cx:"16",cy:"14",r:"4",fill:"none"},null,-1),Tt=t("path",{d:"M12 26v-5a6 6 0 0 1 8 0v5",fill:"none"},null,-1),At=t("path",{d:"M24 14h12",fill:"none"},null,-1),qt=t("path",{d:"M24 20h12",fill:"none"},null,-1),Ot=t("path",{d:"M24 26h12",fill:"none"},null,-1),Et=[Nt,Vt,Tt,At,qt,Ot],Ht=t("path",{d:"M6 2h12a2 2 0 0 1 2 2v2H4V4a2 2 0 0 1 2-2zm0 4h12v14H6V6z"},null,-1),Pt=t("path",{d:"M8 12h8v2H8v-2zm0 4h8v2H8v-4z"},null,-1),Ut=[Ht,Pt],Dt=t("rect",{x:"6",y:"22",width:"14",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),Ft=t("rect",{x:"26",y:"22",width:"14",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),It=t("rect",{x:"16",y:"6",width:"16",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),Gt=t("path",{d:"M16 20 L12 22",stroke:"currentColor","stroke-width":"3"},null,-1),Rt=t("path",{d:"M32 20 L36 22",stroke:"currentColor","stroke-width":"3"},null,-1),Wt=[Dt,Ft,It,Gt,Rt],Jt=t("rect",{x:"3",y:"5",width:"18",height:"14",rx:"2",ry:"2",stroke:"currentColor"},null,-1),Qt=t("path",{d:"M21 10h-6a2 2 0 0 0-2 2v0a2 2 0 0 0 2 2h6",stroke:"currentColor"},null,-1),Kt=t("circle",{cx:"17",cy:"12",r:"1.5",stroke:"currentColor"},null,-1),Yt=[Jt,Qt,Kt],Xt=t("path",{d:"M5 2h14a2 2 0 0 1 2 2v16l-2-1-2 1-2-1-2 1-2-1-2 1-2-1-2 1V4a2 2 0 0 1 2-2z",stroke:"currentColor"},null,-1),Zt=t("line",{x1:"8",y1:"6",x2:"16",y2:"6",stroke:"currentColor"},null,-1),er=t("line",{x1:"8",y1:"10",x2:"16",y2:"10",stroke:"currentColor"},null,-1),tr=t("line",{x1:"8",y1:"14",x2:"12",y2:"14",stroke:"currentColor"},null,-1),rr=[Xt,Zt,er,tr],or=t("path",{d:"M3 10l9-7 9 7"},null,-1),nr=t("path",{d:"M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10"},null,-1),sr=t("path",{d:"M8 21v-6h8v6"},null,-1),ir=t("path",{d:"M12 14v7"},null,-1),ar=[or,nr,sr,ir],lr=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),ur=t("path",{d:"M14 2v4h4"},null,-1),cr=t("path",{d:"M8 14v4"},null,-1),dr=t("path",{d:"M12 10v8"},null,-1),hr=t("path",{d:"M16 12v6"},null,-1),pr=[lr,ur,cr,dr,hr],vr=t("rect",{x:"3",y:"4",width:"18",height:"16",rx:"2",ry:"2"},null,-1),gr=t("path",{d:"M8 2v4"},null,-1),fr=t("path",{d:"M16 2v4"},null,-1),mr=t("path",{d:"M3 10h18"},null,-1),wr=t("path",{d:"M7 14l2 2 3-3"},null,-1),kr=[vr,gr,fr,mr,wr],yr=t("path",{d:"M3 4h18l-6 8v5l-6 3v-8z"},null,-1),_r=[yr],br=t("circle",{cx:"8",cy:"8",r:"4"},null,-1),Mr=t("path",{d:"M3 20v-2a6 6 0 0 1 12 0v2"},null,-1),xr=t("rect",{x:"14",y:"6",width:"6",height:"10",rx:"1"},null,-1),$r=t("path",{d:"M16 8h2"},null,-1),Cr=t("path",{d:"M16 10h2"},null,-1),jr=t("path",{d:"M16 12h2"},null,-1),Lr=t("path",{d:"M14 17l2 2 4-4"},null,-1),Br=[br,Mr,xr,$r,Cr,jr,Lr],zr={class:"mt-auto px-4 mt-1"},Sr=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"},null,-1),Nr=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),Vr=[Sr,Nr],Tr={class:"lg:pl-72 border-t"},Ar={class:"sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8"},qr=b('<button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden"><span class="sr-only">Open sidebar</span><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg></button><div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>',2),Or={class:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between"},Er={class:"flex items-center"},Hr=t("div",{class:"flex w-32"},[t("a",{class:"flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",href:"#"}," Add New +")],-1),Pr=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New User",-1),Ur=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Customer",-1),Dr=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Company",-1),Fr=t("span",{class:"text-sm text-gray-700 leading-6"},"Create Challan",-1),Ir=t("span",{class:"text-sm text-gray-700 leading-6"},"Create Order",-1),Gr=t("span",{class:"text-sm text-gray-700 leading-6"},"Purchase Order",-1),Rr={class:"flex items-center gap-x-4 lg:gap-x-6"},Wr=b('<button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"><span class="sr-only">View Notifications</span><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path></svg></button><div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>',2),Jr={class:"relative"},Qr={type:"button",class:"-m-1.5 flex items-center p-1.5",id:"user-menu-button","aria-expanded":"false","aria-haspopup":"true"},Kr=t("span",{class:"sr-only"},"Open User Menu",-1),Yr=t("img",{class:"h-8 w-8 rounded-full bg-gray-50",src:"https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid",alt:""},null,-1),Xr={class:"hidden lg:flex lg:items-center"},Zr={class:"ml-4 text-sm font-semibold leading-6 text-gray-900","aria-hidden":"true"},eo=t("svg",{class:"ml-2 h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1),to=t("span",{class:"text-sm text-gray-700 leading-5"},"Your Profile",-1),ro=t("span",{class:"text-sm text-gray-700 leading-5"},"Sign Out",-1),oo={key:0},no={key:1},so={key:2},io={key:3},ao={class:"py-10 bg-slate-100"},lo={class:"px-4 sm:px-6 lg:px-8 min-h-screen"},co={__name:"AdminLayout",setup(d){const i=m("/uploads/companyprofile/defaultimg.png");T({});const h=e=>{i.value=e},g=m("/uploads/companyprofile/visionlogo.png");return _(async()=>{try{const e=await fetch("/api/logo");if(e.ok){const v=await e.json();v.logoUrl?h("/uploads/companyprofile/"+v.logoUrl):h("/uploads/companyprofile/defaultimg.png")}}catch(e){console.error("Error fetching logo:",e)}}),(e,v)=>(o(),n("div",null,[t("div",be,[t("div",Me,[t("img",{class:"h-10",src:g.value,alt:"LOGO"},null,8,xe)]),t("div",$e,[t("nav",Ce,[t("ul",je,[t("li",null,[t("ul",Le,[t("li",null,[e.$can("Dashboard")?(o(),l(c,{key:0,href:e.route("dashboard"),active:e.route().current("dashboard")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("dashboard")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},ze,2))]),name:r(()=>[u("Dashboard")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Customer")?(o(),l(c,{key:0,href:e.route("customers.index"),active:e.route().current("customers.index")||e.route().current("customers.create")||e.route().current("customers.edit")||e.route().current("customers.transaction")||e.route().current("customers.credit")||e.route().current("service-reports.show")||e.route().current("service-reports.create")||e.route().current("service-reports.edit")||e.route().current("upload-service-report")||e.route().current("service-reports.view")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("customers.index")||e.route().current("customers.create")||e.route().current("customers.edit")||e.route().current("customers.transaction")||e.route().current("customers.credit")||e.route().current("service-reports.show")||e.route().current("service-reports.create")||e.route().current("service-reports.edit")||e.route().current("upload-service-report")||e.route().current("service-reports.view")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Ne,2))]),name:r(()=>[u("Customers")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Quotation")?(o(),l(c,{key:0,href:e.route("quotation.index"),active:e.route().current("quotation.index")||e.route().current("quotation.create")||e.route().current("quotation.edit")||e.route().current("quotation.view")||e.route().current("quotation.order")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("quotation.index")||e.route().current("quotation.create")||e.route().current("quotation.edit")||e.route().current("quotation.view")||e.route().current("quotation.order")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Ee,2))]),name:r(()=>[u("Quotation")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Orders")?(o(),l(c,{key:0,href:e.route("proforma-invoice.index"),active:e.route().current("proforma-invoice.index")||e.route().current("proforma-invoice.create")||e.route().current("proforma-invoice.edit")||e.route().current("proforma-invoice.view")||e.route().current("proforma-invoice.deliver")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("proforma-invoice.index")||e.route().current("proforma-invoice.create")||e.route().current("proforma-invoice.edit")||e.route().current("proforma-invoice.view")||e.route().current("proforma-invoice.deliver")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 32 32",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},We,2))]),name:r(()=>[u("Proforma Invoice")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Orders")?(o(),l(c,{key:0,href:e.route("orders.index"),active:e.route().current("orders.index")||e.route().current("orders.create")||e.route().current("orders.edit")||e.route().current("orders.view")||e.route().current("orders.deliver")||e.route().current("orders.generate")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("orders.index")||e.route().current("orders.create")||e.route().current("orders.edit")||e.route().current("orders.view")||e.route().current("orders.deliver")||e.route().current("orders.generate")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Ke,2))]),name:r(()=>[u("Orders")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Company")?(o(),l(c,{key:0,href:e.route("companies.index"),active:e.route().current("companies.index")||e.route().current("companies.create")||e.route().current("companies.edit")||e.route().current("products.show")||e.route().current("products.create")||e.route().current("products.edit")||e.route().current("companies.transaction")||e.route().current("companies.credit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("companies.index")||e.route().current("companies.create")||e.route().current("companies.edit")||e.route().current("products.show")||e.route().current("products.create")||e.route().current("products.edit")||e.route().current("companies.transaction")||e.route().current("companies.credit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},tt,2))]),name:r(()=>[u("Company")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Companypo")?(o(),l(c,{key:0,href:e.route("companypo.index"),active:e.route().current("companypo.index")||e.route().current("companypo.create")||e.route().current("companypo.receivepo")||e.route().current("companypo.viewpo")||e.route().current("companypo.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("companypo.index")||e.route().current("companypo.create")||e.route().current("companypo.receivepo")||e.route().current("companypo.viewpo")||e.route().current("companypo.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},at,2))]),name:r(()=>[u("Company PO")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List PurchaseInvoice")?(o(),l(c,{key:0,href:e.route("purchaseinvoice.index"),active:e.route().current("purchaseinvoice.index")||e.route().current("purchaseinvoice.view")||e.route().current("purchaseinvoice.edit")||e.route().current("purchaseinvoice.convert-to-invoice")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("purchaseinvoice.index")||e.route().current("purchaseinvoice.view")||e.route().current("purchaseinvoice.edit")||e.route().current("purchaseinvoice.convert-to-invoice")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},kt,2))]),name:r(()=>[u(" Purchase ")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Challan")?(o(),l(c,{key:0,href:e.route("challan.index"),active:e.route().current("challan.index")||e.route().current("challan.create")||e.route().current("challan.edit")||e.route().current("challan.view")||e.route().current("challan.invoice")||e.route().current("challan.combine-invoice")||e.route().current("challantransfer.edit")||e.route().current("challantransfer")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0 transition-colors duration-200",{"text-white":["challan.index","challan.create","challan.edit","challan.view","challan.invoice","challan.combine-invoice","challantransfer.edit","challantransfer"].includes(e.route().current()),"text-gray-700 group-hover:text-white":!["challan.index","challan.create","challan.edit","challan.view","challan.invoice","challan.combine-invoice","challantransfer.edit","challantransfer"].includes(e.route().current())}]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"1.5"},Mt,2))]),name:r(()=>[u("Challan")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Users")?(o(),l(c,{key:0,href:e.route("users.index"),active:e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},$t,2))]),name:r(()=>[u("Users")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Invoice")?(o(),l(c,{key:0,href:e.route("invoice.index"),active:e.route().current("invoice.index")||e.route().current("invoice.view")||e.route().current("stocktransfer")||e.route().current("invoice.create")||e.route().current("invoice.edit")||e.route().current("stocktransfer.edit")||e.route().current("challaninvoice.edit")||e.route().current("creditnote.add")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("invoice.index")||e.route().current("invoice.view")||e.route().current("stocktransfer")||e.route().current("invoice.create")||e.route().current("invoice.edit")||e.route().current("stocktransfer.edit")||e.route().current("challaninvoice.edit")||e.route().current("creditnote.add")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},St,2))]),name:r(()=>[u("Invoice")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Jobcard")?(o(),l(c,{key:0,href:e.route("jobcard.index"),active:e.route().current("jobcard.index")||e.route().current("jobcard.show")||e.route().current("jobcard.create")||e.route().current("jobcard.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0 transition-colors duration-200",{"text-white":["jobcard.index","jobcard.show","jobcard.create","jobcard.edit"].includes(e.route().current()),"text-gray-700 group-hover:text-white":!["jobcard.index","jobcard.show","jobcard.create","jobcard.edit"].includes(e.route().current())}]),viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Et,2))]),name:r(()=>[u("Service Jobcard")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Maintenance")?(o(),l(c,{key:0,href:e.route("maintenance-contract.index"),active:e.route().current("maintenance-contract.index")||e.route().current("maintenance-contract.create")||e.route().current("maintenance-contract.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("maintenance-contract.index")||e.route().current("maintenance-contract.create")||e.route().current("maintenance-contract.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Ut,2))]),name:r(()=>[u("Maintenance Contract")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("Sales Stock")||e.$can("Service Stock")?(o(),l(c,{key:0,href:e.route("salesstock"),active:e.route().current("salesstock")||e.route().current("servicestock")||e.route().current("products.history")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("salesstock")||e.route().current("servicestock")||e.route().current("products.history")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"},Wt,2))]),name:r(()=>[u("Stock")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),l(c,{key:0,href:e.route("payment.index"),active:e.route().current("payment.index")||e.route().current("payment.create")||e.route().current("payment.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("payment.index")||e.route().current("payment.create")||e.route().current("payment.edit")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Yt,2))]),name:r(()=>[u("Payment")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),l(c,{key:0,href:e.route("receipt.index"),active:e.route().current("receipt.index")||e.route().current("receipt.create")||e.route().current("receipt.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("receipt.index")||e.route().current("receipt.create")||e.route().current("receipt.edit")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},rr,2))]),name:r(()=>[u("Receipt")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),l(c,{key:0,href:e.route("banktransaction.index"),active:e.route().current("banktransaction.index")||e.route().current("banktransaction.show")||e.route().current("banktransaction.create")||e.route().current("banktransaction.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("banktransaction.index")||e.route().current("banktransaction.show")||e.route().current("banktransaction.create")||e.route().current("banktransaction.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},ar,2))]),name:r(()=>[u("Bank Transactions")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Reports")?(o(),l(c,{key:0,href:e.route("reports"),active:e.route().current("reports")||e.route().current("engineer-business.report")||e.route().current("invoice.pending-amount")||e.route().current("sns-stock.report")||e.route().current("sns-sales.report")||e.route().current("sns-sales.report")||e.route().current("sns-cusomersales-report")||e.route().current("hsn.sales.summary")||e.route().current("customer-transaction.report")||e.route().current("creditnote.index")||e.route().current("creditnote.show")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("reports")||e.route().current("engineer-business.report")||e.route().current("invoice.pending-amount")||e.route().current("sns-stock.report")||e.route().current("sns-sales.report")||e.route().current("sns-customersales-report")||e.route().current("hsn.sales.summary")||e.route().current("creditnote.index")||e.route().current("customer-transaction.report")||e.route().current("creditnote.show")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},pr,2))]),name:r(()=>[u("Reports")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Weekly Planning")?(o(),l(c,{key:0,href:e.route("weeklyplan.index"),active:e.route().current("weeklyplan.index")||e.route().current("weeklyplan.create")||e.route().current("weeklyplan.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6",e.route().current("weeklyplan.index")||e.route().current("weeklyplan.create")||e.route().current("weeklyplan.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},kr,2))]),name:r(()=>[u("Daily Plan")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Funnel")?(o(),l(c,{key:0,href:e.route("funnel.index"),active:e.route().current("funnel.index")||e.route().current("funnel.create")||e.route().current("funnel.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("funnel.index")||e.route().current("funnel.create")||e.route().current("funnel.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},_r,2))]),name:r(()=>[u("Funnel")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("Activity Log")?(o(),l(c,{key:0,href:e.route("logs"),active:e.route().current("logs")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0",e.route().current("logs")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Br,2))]),name:r(()=>[u("User Activity")]),_:1},8,["href","active"])):s("",!0)])])])])])]),t("div",zr,[e.$can("List Setting")?(o(),l(c,{key:0,href:e.route("setting"),active:e.route().current("setting")||e.route().current("organization.index")||e.route().current("organization.create")||e.route().current("organization.edit")||e.route().current("manage-prefix")||e.route().current("roles.permission")||e.route().current("bankinfo.index")||e.route().current("bankinfo.create")||e.route().current("bankinfo.edit")||e.route().current("account-type.index")||e.route().current("account-type.create")||e.route().current("account-type.edit")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")||e.route().current("jobcard-checklist.index")||e.route().current("jobcard-checklist.create")||e.route().current("jobcard-checklist.edit")||e.route().current("mail-configs.index")||e.route().current("mail-configs.create")||e.route().current("mail-configs.edit")||e.route().current("emailtemplates.index")||e.route().current("emailtemplates.create")||e.route().current("emailtemplates.edit")},{svg:r(()=>[(o(),n("svg",{class:a(["h-6 w-6 shrink-0 text-gray-700",e.route().current("setting")||e.route().current("organization.index")||e.route().current("organization.create")||e.route().current("organization.edit")||e.route().current("manage-prefix")||e.route().current("roles.permission")||e.route().current("bankinfo.index")||e.route().current("bankinfo.create")||e.route().current("bankinfo.edit")||e.route().current("account-type.index")||e.route().current("account-type.create")||e.route().current("account-type.edit")||e.route().current("jobcard-checklist.index")||e.route().current("jobcard-checklist.create")||e.route().current("jobcard-checklist.edit")||e.route().current("mail-configs.index")||e.route().current("mail-configs.create")||e.route().current("mail-configs.edit")||e.route().current("emailtemplates.index")||e.route().current("emailtemplates.create")||e.route().current("emailtemplates.edit")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Vr,2))]),name:r(()=>[u("Settings")]),_:1},8,["href","active"])):s("",!0)])]),t("div",Tr,[t("div",Ar,[qr,t("div",Or,[t("div",Er,[p(B,{align:"left",width:"48"},{trigger:r(()=>[Hr]),content:r(()=>[p(f,{href:e.route("users.create")},{svg:r(()=>[]),text:r(()=>[Pr]),_:1},8,["href"]),p(f,{href:e.route("customers.create")},{svg:r(()=>[]),text:r(()=>[Ur]),_:1},8,["href"]),p(f,{href:e.route("companies.create")},{svg:r(()=>[]),text:r(()=>[Dr]),_:1},8,["href"]),p(f,{href:e.route("challan.create")},{svg:r(()=>[]),text:r(()=>[Fr]),_:1},8,["href"]),p(f,{href:e.route("orders.create")},{svg:r(()=>[]),text:r(()=>[Ir]),_:1},8,["href"]),p(f,{href:e.route("companypo.create")},{svg:r(()=>[]),text:r(()=>[Gr]),_:1},8,["href"])]),_:1})]),t("div",Rr,[Wr,t("div",Jr,[p(B,{align:"right",width:"48"},{trigger:r(()=>[t("button",Qr,[Kr,Yr,t("span",Xr,[t("span",Zr,y(e.$page.props.auth.user.name),1),eo])])]),content:r(()=>[p(f,{href:e.route("profile.edit"),as:"button"},{svg:r(()=>[]),text:r(()=>[to]),_:1},8,["href"]),p(f,{href:e.route("logout"),method:"post",as:"button"},{svg:r(()=>[]),text:r(()=>[ro]),_:1},8,["href"])]),_:1})])])])]),e.$page.props.flash.message?(o(),n("div",oo,[p(G,{message:e.$page.props.flash.message},null,8,["message"])])):s("",!0),e.$page.props.flash.success?(o(),n("div",no,[p(te,{message:e.$page.props.flash.success},null,8,["message"])])):s("",!0),u(y(e.$page.props.flash.error)+" ",1),e.$page.props.flash.error?(o(),n("div",so,[p(de,{message:e.$page.props.flash.error},null,8,["message"])])):s("",!0),e.$page.props.flash.warning?(o(),n("div",io,[p(_e,{message:e.$page.props.flash.warning},null,8,["message"])])):s("",!0),t("main",ao,[t("div",lo,[w(e.$slots,"default")])])])]))}};export{co as _,f as a,B as b};
