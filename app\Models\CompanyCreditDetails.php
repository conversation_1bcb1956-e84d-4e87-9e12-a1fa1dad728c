<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyCreditDetails extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'company_credit_details';

    protected static $logName = 'Company Credit Details';

    public function getLogDescription(string $event): string
    {

        return "Company credit details has been {$event} for <strong>{$this->amount}</strong> by";
    }

    protected static $logAttributes = [
        'company_credit_id',
        'purchase_order_receive_id',
        'amount',
        'date',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'company_credit_id',
        'purchase_order_receive_id',
        'amount',
        'date',
        'created_by',
        'updated_by'
    ];

    public function credit(){
        return $this->belongsTo(CompanyCredit::class,'company_credit_id','id');
    }

    public function invoice(){
        return $this->belongsTo(PurchaseOrderReceives::class,'purchase_order_receive_id','id');
    }
}
