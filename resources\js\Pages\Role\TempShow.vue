<script setup>
import { ref, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import FileViewer from '@/Components/FileViewer.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';

const props = defineProps(['data', 'organization', 'customer', 'permissions', 'filepath']);
const file = usePage().props.filepath.view;

// const companyID = usePage().props.organization.id;
const form = useForm({});
const modalVisible = ref(false);
const selectedUserId = ref(null);

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};


const deleteUser = () => {
    form.delete(route('service-reports.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmountNew = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const truncateCompanyName = (name) => {
    return name && name.length > 35 ? name.substring(0, 35) + '...' : name;
};

const handleSearchChange = (value) => {
    form.get(route('service-reports.show',{search:value,  id: props.customer.id}),  {
        preserveState: true,
        // replace: true,
    });
};

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
  selectedDocument.value = name;
  documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin+ file+ name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

</script>

<template>
    <Head title="Service Report"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">{{ customer.customer_name }}</h1>
            </div>
            <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
               <div class="flex justify-end w-20">
                    <CreateButton :href="route('customers.index')">
                            Back
                    </CreateButton>
                </div>
                <div class="flex justify-end">
                    <CreateButton :href="route('service-reports.create',{id:customer.id})">
                            Create Report
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2 grid grid-cols-1 gap-x-2 sm:grid-cols-12">
                            <th scope="col" class="sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900">PRODUCT NAME</th>
                            <th scope="col" class="sm:col-span-3 px-4 py-4 text-sm font-semi bold text-gray-900">COMPANY NAME</th>
                            <th scope="col" class="sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900">SERIAL NO</th>
                            <th scope="col" class="sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900">REPORT TYPE</th>
                            <th scope="col" class="sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900">DATE</th>
                            <th scope="col" class="sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900">ACTION</th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12" v-for="(userData, index) in data.data" :key="userData.id" >
                            <td class="sm:col-span-2 px-4 py-2.5 ">{{ userData.product_code ?? '' }} : {{ userData.product_name ?? '' }}</td>
                            <td class="sm:col-span-3 px-4 py-2.5">{{ truncateCompanyName(userData?.company?.name) ?? '-' }}</td>
                            <td class="sm:col-span-2 px-4 py-2.5">{{ userData.serial_no ?? '-' }}</td>
                            <td v-for="(reportData, index) in userData.report_detail" :key="index" class="sm:col-span-2 px-4 py-2.5 font-medium text-gray-900">
                                {{ reportData.type ?? '-' }}
                            </td>
                            <td v-for="(reportData, index) in userData.report_detail" :key="index" class="sm:col-span-2 px-4 py-2.5">
                                {{ formatDate(reportData.date) ?? '-' }}
                            </td>
                            <td class="sm:col-span-1 px-4 py-2.5">
                                <div class="flex items-center justify-start gap-2">
                                    <button @click="toggleDetails(index)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                    <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink :href="route('service-reports.edit',{id:userData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button type="button" @click="openDeleteModal(userData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <ActionLink :href="route('upload-service-report',{id:userData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7-7m0 0L5 14m7-7v12"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">Upload Report</span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink :href="route('service-reports.view',{id:userData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        View History
                                                    </span>
                                                </template>
                                            </ActionLink>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                            <div v-if="showDetails[index] && userData.report_detail.length > 0" class="divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4">
                                <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50">
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">REPORT DATE</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">REPORT NAME</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">REPORT TYPE </th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">SERVICE ENGINEER</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">ACTION</th>
                                </tr>
                                <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1 overflow-y-auto" style="max-height: 184px;">
                                    <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5" v-for="(reportData, index) in userData.report_detail" :key="index">
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6">{{ formatDate(reportData.date) ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6">{{ reportData.document_name ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6">{{ reportData.type ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6">{{ reportData.engineer.first_name ?? '-'  }} {{ reportData.engineer.last_name ?? '-' }}</td>
                                        <td class="sm:col-span-1 py-2 pl-4 pr-3 sm:pl-6 space-x-2 items-center">
                                            <!-- <button type="button" @click="openDeleteModal(files.id)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                            </button> -->
                                            <button type="button"  @click="openPreviewModal(reportData.document_name)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>                            </button>
                                            <button type="button"  @click="downloadDocument(reportData.document_name)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path></svg>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </div>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="5" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
                </div>
                <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
            </div>
            </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                 <FileViewer :fileUrl="file+ selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal"> Cancel </SecondaryButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: rgb(50 68 210);
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%; /* Position the tooltip above the text */
  left: 20%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

</style>
