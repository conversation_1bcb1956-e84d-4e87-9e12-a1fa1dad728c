<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\ActivityTrait;

class MailConfig extends Model
{
    use HasFactory;

    use ActivityTrait;

    protected $table = 'mail_configs'; // Ensure it matches your database table name

    protected static $logName = 'MailConfig';
    public function getLogDescription(string $event): string
    {
        return "Mail-Config has been {$event} for <strong>{$this->host}<strong> by";
    }
    protected static $logAttributes = [
        'host',
        'port',
        'username',
        'password',
        'email',
        'encryption',
        'name',
    ];
    protected $fillable = [
        'host',
        'port',
        'username',
        'password',
        'email',
        'encryption',
        'name',
    ];
}
