<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained( table: 'customers', indexName: 'ordc_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('sales_user_id')->constrained( table: 'users', indexName: 'ords_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('quotation_id')->nullable();
            $table->string('order_number');
            $table->date('date');
            $table->enum('status', ['Pending', 'In Process', 'Completed', 'Cancelled']);
            $table->double('total_amount', 16, 2);
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
