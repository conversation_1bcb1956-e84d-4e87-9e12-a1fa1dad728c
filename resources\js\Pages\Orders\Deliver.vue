<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['data', 'order_deliver_number']);

const deliveredProduct = computed(() => {
    return props.data[0].pending_order_details.map(item => ({
        delivered_qty: '',
        order_details_id: item.id,
        order_id:  props.data[0].id,
        order_deliver_number: props.order_deliver_number,
    }));
});

const form = useForm('post', '/saveorderdeliver', {
    invoice_number: '',
    deliveredProduct: [],
    order_id: props.value?.id,
});

const submit = () => {
    form.deliveredProduct = deliveredProduct.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

</script>

<template>
    <Head title="Orders"/>

    <AdminLayout>
        <form @submit.prevent="submit" class="">
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Deliver Order</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div>
                        <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                    </div>
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('orders.index')">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
                <div class="inline-flex items-start space-x-6 justify-start w-full">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Customer:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.customer_name ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.gst_no  ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.email ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.contact_no ?? '-'}}</p>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Order Number:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].order_number ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Order Date:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Category:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].category ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Sales Person:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name ?? '-'}} {{ data[0].users.last_name ?? '-'}}</p>
                        </div>
                        <div class="sm:col-span-6 mb-2">
                            <div class="flex items-center space-x-2">
                                <InputLabel for="invoice_number" value="Invoice Number:"/>
                                <TextInput
                                    id="invoice_number"
                                    type="text"
                                    @input="clearError('invoice_number')"
                                    v-model="form.invoice_number"
                                    class="flex-1"
                                />
                            </div>
                            <div v-if="form.errors.invoice_number" class="text-red-600 text-sm mt-1">
                                {{ form.errors.invoice_number }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6 bg-white p-4 shadow sm:p-8 sm:rounded-lg border divide-y divide-gray-300">
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 pb-2">
                    <div class="sm:col-span-3">
                        <p class="text-sm font-semibold text-gray-900 leading-6">Product</p>
                    </div>
                    <div class="sm:col-span-2">
                        <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                    </div>
                    <div class="sm:col-span-2">
                        <p class="text-sm font-semibold text-gray-900 leading-6">Delivered Product</p>
                    </div>
                    <div class="sm:col-span-3">
                        <p class="text-sm font-semibold text-gray-900 leading-6">Deliver QTY</p>
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 items-center" v-for="(product, index)  in data[0].pending_order_details" :key="index">
                    <div class="sm:col-span-3">
                        <p class="text-sm leading-5 text-gray-700">{{ product.product.name }}</p>
                    </div>
                    <div class="sm:col-span-2">
                        <p class="text-sm leading-5 text-gray-700">{{ product.qty }}</p>
                    </div>
                    <div class="sm:col-span-2">
                        <p class="text-sm leading-5 text-gray-700">{{ product.delivered_qty }}</p>
                    </div>
                    <div class="sm:col-span-3 mb-2">
                        <TextInput
                            id="gst"
                            type="text"
                            :numeric="true"
                            v-model="deliveredProduct[index].delivered_qty"
                            autocomplete="delivered_qty"
                            @change="clearError('deliveredProduct.' + index + '.delivered_qty')"
                            :class="{ 'error': form.errors[`deliveredProduct.${index}.delivered_qty`] }"
                        />
                        <p v-if="form.errors[`deliveredProduct.${index}.delivered_qty`]" class="text-red-500 text-xs">
                            {{ form.errors[`deliveredProduct.${index}.delivered_qty`] }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="flex mt-6 items-center justify-between">
                <div class="ml-auto flex items-center justify-end gap-x-6">
                    <PrimaryButton :disabled="form.processing">Submit</PrimaryButton>
                </div>
            </div>
        </div>
        </form>
    </AdminLayout>

</template>

<style scoped>
.error {
  border: 1px solid red;
}
</style>
