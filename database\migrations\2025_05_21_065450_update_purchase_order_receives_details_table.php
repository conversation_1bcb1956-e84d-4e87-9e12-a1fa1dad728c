<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->enum('type', ['invoice', 'challan'])->after('id')->default('invoice');
        });

        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->json('old_record')->nullable()->after('description'); // or ->text() if JSO<PERSON> not supported
        });

        Schema::table('purchase_order_receive_details', function (Blueprint $table) {
            $table->json('old_record')->nullable()->after('receive_qty'); // or ->text() if JSON not supported
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->dropColumn('type');
        });

        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->dropColumn('old_record');
        });

        Schema::table('purchase_order_receive_details', function (Blueprint $table) {
            $table->dropColumn('old_record');
        });
    }
};
