import{_ as E,b as F,a as C}from"./AdminLayout-f002e683.js";import{K as U,h as R,r as y,j as H,o as r,c as p,a as m,u as _,w as i,F as z,Z as G,b as t,y as B,n as I,t as c,f as g,i as P,e as x}from"./app-497d70e1.js";/* empty css                                                              */import{_ as K}from"./Pagination-24879c4b.js";import{_ as X}from"./SimpleDropdown-acec1cc7.js";import{_ as f}from"./InputLabel-5f63a3d9.js";import{_ as Y}from"./SearchableDropdownNew-c21d519e.js";const Z={class:"animate-top"},J={class:"flex justify-between items-center"},Q={class:"flex items-center space-x-6"},W=t("h1",{class:"text-lg font-semibold leading-7 text-gray-700"},"Sales Stock",-1),D=t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Service Stock",-1),tt={class:"justify-end"},et={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},st={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},ot=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),at={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},lt={class:"flex justify-between mb-2"},nt={class:"flex"},rt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ct=["src"],it={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},dt={class:"sm:col-span-4"},mt={class:"relative mt-2"},pt={class:"sm:col-span-4"},ht={class:"relative mt-2"},gt={class:"sm:col-span-2"},ut={key:0,class:"w-full"},_t={class:"text-base font-semibold text-gray-900"},xt={class:"mt-8 overflow-x-auto sm:rounded-lg"},yt={class:"shadow sm:rounded-lg"},ft={class:"w-full text-sm text-left rtl:text-right text-gray-500"},vt=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2 grid grid-cols-1 gap-x-2 sm:grid-cols-12"},[t("th",{scope:"col",class:"sm:col-span-5 px-4 py-4 text-sm font-semi bold text-gray-900"},"PRDUCT"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"HSN"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"GST (%)"),t("th",{scope:"col",class:"sm:col-span-3 px-4 py-4 text-sm font-semi bold text-gray-900"},"COMPANY"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"STOCK"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"ACTION")])],-1),wt={key:0},bt={class:"sm:col-span-5 px-4 py-2.5 font-medium text-gray-900"},kt={class:"sm:col-span-1 px-4 py-2.5"},St={class:"sm:col-span-1 px-4 py-2.5"},Ct={class:"sm:col-span-3 px-4 py-2.5 truncate"},zt={class:"sm:col-span-1 px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Mt={class:"sm:col-span-1 px-4 py-2.5"},Bt={class:"flex items-center justify-start gap-2"},It=["onClick"],Pt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),At=[Pt],Nt=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),jt=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1",class:"h-5 w-5","stroke-linecap":"round","stroke-linejoin":"round"},[t("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),t("line",{x1:"5",y1:"12",x2:"19",y2:"12"})],-1),Lt=t("span",{class:"text-sm text-gray-700 leading-5"},"Add Stock",-1),Ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),$t=t("span",{class:"text-sm text-gray-700 leading-5"},"Edit Stock",-1),Vt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7-7m0 0L5 14m7-7v12"})],-1),Tt=t("span",{class:"text-sm text-gray-700 leading-5"},"Product History",-1),qt={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},Et=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Batch"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Expiry Date"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"MRP (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Purchase Price (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Stock")],-1),Ft={class:"divide-y divide-gray-300 bg-white grid grid-cols-1 overflow-y-auto",style:{"max-height":"184px"}},Ut={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Rt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Ht={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Gt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Kt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Xt={key:1},Yt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Zt=[Yt],le={__name:"ServiceStock",props:["data","user","permissions","organization","stockdata","search","company","organizationId","companyId"],setup(l){const v=l,w=U().props.data.links.find(s=>s.active===!0),A=R({}),h=y(v.organizationId),u=y(v.companyId),b=y(""),k=(s,o,e)=>{b.value=s,A.get(route("servicestock",{search:s,organization_id:o,company_id:e}),{preserveState:!0})},N=(s,o)=>{h.value=s,k(b.value,h.value,u.value)},j=(s,o)=>{u.value=s,k(b.value,h.value,u.value)},L=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},M=s=>{const[o,e]=s.toFixed(2).toString().split(".");return o.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")},O=s=>{let o=s.toFixed(2).toString(),[e,n]=o.split("."),a=e.substring(e.length-3),d=e.substring(0,e.length-3);return d!==""&&(a=","+a),`${d.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${n}`},$=s=>s&&s.length>0?s.reduce((o,e)=>o+(e.receive_qty-e.sell_qty),0):"-",S=y(null),V=s=>{S.value=S.value===s?null:s},T=()=>{const s={organizationId:h.value,companyId:u.value,category:"Service"},e=`/export-stock?${new URLSearchParams(s).toString()}`;fetch(e,{method:"GET"}).then(n=>{if(!n.ok)throw new Error("Network response was not ok");return n.blob()}).then(n=>{const a=window.URL.createObjectURL(new Blob([n])),d=document.createElement("a");d.href=a,d.setAttribute("download","stock.xlsx"),document.body.appendChild(d),d.click(),document.body.removeChild(d)}).catch(n=>{console.error("Error exporting data:",n)})},q=H(()=>Math.round(v.stockdata.reduce((s,o)=>{let e=0;return o.sales_product&&o.sales_product.length>0&&(e=o.sales_product.reduce((n,a)=>n+parseFloat(a.purchase_price)*parseFloat(a.receive_qty-a.sell_qty),0)),s+e},0)));return(s,o)=>(r(),p(z,null,[m(_(G),{title:"Service Stock"}),m(E,null,{default:i(()=>[t("div",Z,[t("div",J,[t("div",Q,[m(_(B),{href:s.route("salesstock"),class:I("pb-2")},{default:i(()=>[W]),_:1},8,["href"]),m(_(B),{href:s.route("servicestock"),class:I("border-b-2 pb-2 border-gray-900")},{default:i(()=>[D]),_:1},8,["href"])]),t("div",tt,[t("div",et,[t("div",st,[ot,t("input",{id:"search-field",onInput:o[0]||(o[0]=e=>k(e.target.value,h.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])])])]),t("div",at,[t("div",lt,[t("div",nt,[rt,m(f,{for:"customer_id",value:"Filters"})]),t("button",{onClick:T},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ct)])]),t("div",it,[t("div",dt,[m(f,{for:"customer_id",value:"Organization Name"}),t("div",mt,[m(X,{options:l.organization,modelValue:h.value,"onUpdate:modelValue":o[1]||(o[1]=e=>h.value=e),onOnchange:N},null,8,["options","modelValue"])])]),t("div",pt,[m(f,{for:"customer_id",value:"Company Name"}),t("div",ht,[m(Y,{options:l.company,modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=e=>u.value=e),onOnchange:j},null,8,["options","modelValue"])])]),t("div",gt,[l.user.role_id==1?(r(),p("div",ut,[m(f,{for:"customer_id",value:"Total Amount (₹):"}),t("div",null,[t("p",_t,c(O(q.value)),1)])])):g("",!0)])])]),t("div",xt,[t("div",yt,[t("table",ft,[vt,l.data.data&&l.data.data.length>0?(r(),p("tbody",wt,[(r(!0),p(z,null,P(l.data.data,(e,n)=>(r(),p("tr",{class:"odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12",key:e.id},[t("td",bt,c(e.item_code?e.item_code+"  : ":"")+" "+c(e.name??"-"),1),t("td",kt,c(e.hsn_code??"-"),1),t("td",St,c(e.gst??"-"),1),t("td",Ct,c(e.company.name??"-"),1),t("td",zt,c($(e.sales_product)??"-"),1),t("td",Mt,[t("div",Bt,[t("button",{onClick:a=>V(n)},At,8,It),l.permissions.canProductHistory?(r(),x(F,{key:0,align:"right",width:"48"},{trigger:i(()=>[Nt]),content:i(()=>[l.permissions.canStockAdd?(r(),x(C,{key:0,href:s.route("stock.add",{id:e.id,page:_(w).url})},{svg:i(()=>[jt]),text:i(()=>[Lt]),_:2},1032,["href"])):g("",!0),l.permissions.canStockEdit?(r(),x(C,{key:1,href:s.route("stock.edit",{id:e.id,page:_(w).url})},{svg:i(()=>[Ot]),text:i(()=>[$t]),_:2},1032,["href"])):g("",!0),l.permissions.canProductHistory?(r(),x(C,{key:2,href:s.route("products.history",{id:e.id,page:_(w).url})},{svg:i(()=>[Vt]),text:i(()=>[Tt]),_:2},1032,["href"])):g("",!0)]),_:2},1024)):g("",!0)])]),S.value===n&&e.sales_product.length>0?(r(),p("div",qt,[Et,t("tbody",Ft,[(r(!0),p(z,null,P(e.sales_product,(a,d)=>(r(),p("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:d},[t("td",Ut,c(a.batch??"-"),1),t("td",Rt,c(a.expiry_date!=null?L(a.expiry_date):"-"),1),t("td",Ht,c(a.mrp?M(a.mrp):"-"),1),t("td",Gt,c(a.purchase_price?M(a.purchase_price):"-"),1),t("td",Kt,c(a.receive_qty-a.sell_qty),1)]))),128))])])):g("",!0)]))),128))])):(r(),p("tbody",Xt,Zt))])]),l.data.data&&l.data.data.length>0?(r(),x(K,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):g("",!0)])])]),_:1})],64))}};export{le as default};
