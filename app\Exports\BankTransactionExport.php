<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class BankTransactionExport implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles, ShouldAutoSize
{
    protected $transactions;
    protected $organizationName;
    protected $bankName;
    protected $fromDate;
    protected $toDate;
    protected $totalRows;
    protected $bankAmountType;
    protected $initialBalance;

    public function __construct($transactions, $organizationName, $bankName, $fromDate, $toDate, $bankAmountType, $initialBalance)
    {
        $this->transactions = $transactions;
        $this->organizationName = $organizationName;
        $this->bankName = $bankName;
        $this->fromDate = $fromDate ? Carbon::parse($fromDate)->format('d-m-Y') : 'N/A';
        $this->toDate = $toDate ? Carbon::parse($toDate)->format('d-m-Y') : 'N/A';
        $this->totalRows = count($transactions) + 5;
        $this->bankAmountType = $bankAmountType;
        $this->initialBalance = $initialBalance;
    }

    private function updateBalance($balance, $transaction, $bankAmountType)
    {
        $amount = (float)$transaction->amount;
        $isCreditBank = $bankAmountType === 'cr';
        $isCreditTransaction = $transaction->payment_type === 'cr';

        if ($isCreditBank) {
            return $isCreditTransaction ? $balance - $amount : $balance + $amount;
        } else {
            return $isCreditTransaction ? $balance + $amount : $balance - $amount;
        }
    }

    private function formatAmount($amount)
    {
        $amountStr = number_format(abs($amount), 2, '.', '');
        list($integerPart, $decimalPart) = explode('.', $amountStr);

        $lastThree = substr($integerPart, -3);
        $otherNumbers = substr($integerPart, 0, -3);

        $formattedIntegerPart = $otherNumbers !== ''
            ? preg_replace('/\B(?=(\d{2})+(?!\d))/', ',', $otherNumbers) . ',' . $lastThree
            : $lastThree;

        return $formattedIntegerPart . '.' . $decimalPart;
    }

    public function collection()
    {
        $data = [];
        $cumulativeBalance = (float)$this->initialBalance;
        $initiallBalanceprefix = $cumulativeBalance >= 0 ? 'cr' : 'dr';
        $sno = 1;

        foreach ($this->transactions as $transaction) {
            $cumulativeBalance = $this->updateBalance($cumulativeBalance, $transaction, $this->bankAmountType);
            $prefix = $cumulativeBalance >= 0 ? 'cr' : 'dr';

            $isCredit = $transaction->payment_type === 'cr';
            $receiptAmount = $isCredit ? $this->formatAmount($transaction->amount) : '';
            $paymentAmount = !$isCredit ? $this->formatAmount($transaction->amount) : '';
            $type = ($transaction->payment_type == 'cr') ? 'Rcpt' : 'Pymt';

            $narration = $transaction->accounttype
                ? "{$transaction->accounttype->name} : {$transaction->note}"
                : $transaction->note;

            $data[] = [
                'Sno'           => $sno++,
                'Date'          => \Carbon\Carbon::parse($transaction->date)->format('d-m-Y'),
                'Type'          => $type,
                'Narration'     => $narration,
                'Receipt (₹)'   => $receiptAmount,
                'Payment (₹)'   => $paymentAmount,
                'Balance (₹)'   => $this->formatAmount($cumulativeBalance) . ' ' . $prefix,
            ];
        }

        $data[] = [
            'Sno'           => 'TOTAL',
            'Date'          => '',
            'Type'          => '',
            'Narration'     => '',
            'Receipt (₹)'   => '',
            'Payment (₹)'   => '',
            'Balance (₹)'   => $this->formatAmount($cumulativeBalance) . ' ' . $initiallBalanceprefix,
        ];

        return collect($data);
    }


    public function headings(): array
    {
        return [
            [$this->organizationName],
            ["Bank: {$this->bankName}"],
            ["From: {$this->fromDate} To: {$this->toDate}"],
            ['Sno', 'Date', 'Type', 'Narration', 'Receipt (₹)', 'Payment (₹)', 'Balance (₹)'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $this->styleHeaders($sheet);

        $this->styleDataAndTotal($sheet);

        $this->setColumnWidths($sheet);

        return $sheet;
    }

    private function styleHeaders(Worksheet $sheet)
    {
        foreach (['A1:G1', 'A2:G2', 'A3:G3'] as $range) {
            $sheet->mergeCells($range);
        }

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A3')->getFont()->setBold(true);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A4:G4')->getFont()->setBold(true);
        $sheet->getStyle('A4:G4')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('D9E1F2');
        $sheet->getStyle('A4:G4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }

    private function styleDataAndTotal(Worksheet $sheet)
    {
        $sheet->getStyle("A4:G{$this->totalRows}")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle("A5:G{$this->totalRows}")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        $sheet->getStyle("A{$this->totalRows}:G{$this->totalRows}")->getFont()->setBold(true);
        $sheet->getStyle("A{$this->totalRows}:G{$this->totalRows}")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFFF00');
    }

    private function setColumnWidths(Worksheet $sheet)
    {

        $sheet->getColumnDimension('D')->setWidth(40);

        foreach ($sheet->getRowDimensions() as $dimension) {
            $dimension->setRowHeight(-1);
        }
    }
}
