<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\EmailTags;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\User;
use App\Models\Invoice;
use App\Models\CustomerTransaction;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\EmailTagRequest;
use Illuminate\Support\Facades\DB;

class EmailTagController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:Email Tags')->only(['index','create', 'store','edit', 'update','destroy']);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $data = EmailTags::orderBy('id', 'desc')->paginate(20);

        return Inertia::render('EmailTag/List', compact('data'));
    }

    public function create(Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', null)->orderByRaw('customer_name')->get();
        return Inertia::render('EmailTag/Add' , compact('organization', 'customers'));
    }

    // public function store(EmailTagRequest $request)
    public function store(Request $request)
    {
        //added for old transaction
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
            $data['status'] = "Unpaid";
            $data['entity_id'] = null;
            $data['entity_type'] = "invoice";
            $data['pending_amount'] = $data['sub_total'] = $data['total_amount'];
            $invoice = Invoice::create($data);

             if($invoice){
                $data['amount'] = $data['total_amount'];
                $data['payment_type'] = 'dr';
                $data['entity_id'] = $invoice->id;
                $data['entity_type'] = 'invoice';
                $data['note'] = 'Invoice No :' .$data['invoice_no'];
                CustomerTransaction::create($data);
             }

            DB::commit();
            return Redirect::to('/email-tag')->with('success', 'Email Tag Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }

        // DB::beginTransaction();
        // try {
        //     $data = (array) $request->DTO();
        //     $data['created_by'] = $data['updated_by'] = auth()->id();
        //     EmailTags::create($data);
        //     DB::commit();
        //     return Redirect::to('/email-tag')->with('success', 'Email Tag Added Successfully');
        // } catch (\Exception $e) {
        //     DB::rollBack();
        //     return Redirect::back()->withInput()->with('error', $e->getMessage());
        // }
    }

    public function edit(string $id)
    {
        $data = EmailTags::find($id);
        return Inertia::render('EmailTag/Edit', compact('data'));
    }

    public function update(EmailTagRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();;
            $user = EmailTags::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to('/email-tag')->with('success', 'Email Tag Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = EmailTags::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Email Tag Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
