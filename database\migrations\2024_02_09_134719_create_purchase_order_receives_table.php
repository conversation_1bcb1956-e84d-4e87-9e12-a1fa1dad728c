<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_order_receives', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_id')->constrained( table:'purchase_orders', indexName: 'por_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('purchase_order_detail_id')->constrained( table:'purchase_order_details', indexName: 'pod_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('po_receive_number');
            $table->integer('receive_qty');
            $table->date('po_receive_date');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_receives');
    }
};
