<script setup>
import { ref, onMounted, watch } from 'vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchableDropdown from '@/Components/SearchableDropdownNew.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';

const props = defineProps(['data', 'balanceStock', 'page', 'product', 'organization', 'financialYears', 'selectedFinancialYear', 'openingBalance']);


const selectedFinancialYear = ref(props.selectedFinancialYear);

const refetchBalanceStock = () => {
    form.get(route('products.history', {
        id: props.product[0].id,
        organization_id: organizationId.value,
        financial_year: selectedFinancialYear.value
    }), {
        preserveState: true,
    });
};

// const selectedFinancialYear = ref();
const handleFinancialYearChange = (id, name) => {
    selectedFinancialYear.value = id;
    refetchBalanceStock();
};

const organizationId = ref();
const setOrganization = (id, name) => {
    organizationId.value = id;
    refetchBalanceStock();
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const form = useForm({
});

const page = ref(props.page);

// const setOrganization = (id, name) => {
//     organizationId.value = id;
//     handleSearchChange(organizationId.value);
// };

// const handleSearchChange = ( organizationId ) => {
//     form.get(route('products.history',{ id: props.product[0].id ,organization_id: organizationId }),  {
//         preserveState: true,
//     });
// };

// const calculateBalance = (stock, index, balanceStock) => {
//     let cumulativeBalance = 0;
//     for (let i = balanceStock.length - 1; i >= index; i--) {
//         let currentStock = balanceStock[i];
//         console.log(currentStock);
//         if (currentStock.receive_qty) {
//             cumulativeBalance += parseFloat(currentStock.receive_qty);
//         } else if (currentStock.is_receive == 'yes') {
//             cumulativeBalance += parseFloat(currentStock.qty);
//         } else if (currentStock.invoice_id) {
//             cumulativeBalance -= parseFloat(currentStock.qty);
//         } else if (currentStock.is_receive == null) {
//             cumulativeBalance -= parseFloat(currentStock.qty);
//         }
//     }
//     return cumulativeBalance;
// };

console.log(props.openingBalance, 'balance');
const calculateBalance = (stock, index, balanceStock) => {
    let cumulativeBalance = parseFloat(props.openingBalance || 0);
    for (let i = balanceStock.length - 1; i >= index; i--) {
        let currentStock = balanceStock[i];
        if (currentStock.receive_qty) {
            cumulativeBalance += parseFloat(currentStock.receive_qty);
        } else if (currentStock.is_receive === 'yes') {
            cumulativeBalance += parseFloat(currentStock.qty);
        } else if (currentStock.invoice_id) {
            cumulativeBalance -= parseFloat(currentStock.qty);
        } else if (currentStock.is_receive == null) {
            cumulativeBalance -= parseFloat(currentStock.qty);
        }
    }
    return cumulativeBalance;
};

</script>

<template>
    <Head title="Product History"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Product History</h1>
            </div>
            <div class="w-auto">
                <div class="flex space-x-6 items-center">
                    <p class="text-sm leading-6 text-gray-900 font-semibold">{{ product[0].company.name }}</p>
                    <div class="flex justify-end w-20">
                        <CreateButton :href="page">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="inline-flex flex-col space-y-1 items-start justify-end w-full">
                <div class="inline-flex items-center justify-end w-full space-x-2">
                    <p class="text-base font-semibold text-gray-900 w-32">Product Name:</p>
                    <p class="text-base leading-6 text-gray-900 font-semibold">{{ product[0]?.item_code ?? '' }}</p>
                    <p class="text-base leading-6 text-gray-900 font-semibold">{{ product[0].name }}</p>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="financial_year" value="Financial Year" />
                    <div class="relative mt-2">
                        <SearchableDropdown
                            :options="financialYears"
                            v-model="selectedFinancialYear"
                            @onchange="handleFinancialYearChange"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">INVOICE NO</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">DATE</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">PARTICULAR</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">BATCH</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">RECEIVE</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">ISSUE</th>
                            <th scope="col" class=" px-4 py-4 text-sm font-semi bold text-gray-900">BALANCE</th>
                        </tr>
                    </thead>
                    <tbody  v-if="balanceStock && (balanceStock.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(stock, index1) in balanceStock" :key="stock.id">
                            <!-- {{ stock.id }} -->
                            <td class="whitespace-nowrap px-4 py-2.5">
                                 {{
                                    stock.purchase_order_receive_details ? stock.purchase_order_receive_details.purchase_order_receives.customer_invoice_no : '-'
                                    ?   (
                                            stock.invoice ? stock.invoice.invoice_no : '-'
                                             ? (stock.challan ? stock.challan.challan_number : '-') : '-'
                                        ) : '-'
                                }}
                            </td>
                            <!-- <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ formatDate(stock.created_at) ?? '-' }}</td> -->
                            <td class="whitespace-nowrap px-4 py-2.5">
                                {{
                                    stock.purchase_order_receive_details ? formatDate(stock.purchase_order_receive_details.purchase_order_receives.customer_invoice_date) : '-'
                                    ?   (
                                            stock.invoice ?  (stock.is_receive  == 'yes') ? formatDate(stock.creditnote.date) : formatDate(stock.invoice.date) : '1'
                                         ? (stock.challan ? (stock.is_receive  == 'yes') ? formatDate(stock.created_at) : formatDate(stock.challan.date) : formatDate(stock.created_at)) : '3'
                                        ) : '4'
                                }}
                            </td>
                            <td class="whitespace-nowrap px-4 py-2.5 font-medium text-gray-900">
                                {{
                                    stock.purchase_order_receive_details ? stock.purchase_order_receive_details.purchase_order_receives.purchase_order.company.name   : 'Stock Added'
                                    ?   (
                                            stock.invoice ? stock.invoice.customers.customer_name : 'Stock Added'
                                         ? (stock.challan ? stock.challan.customers.customer_name : 'Stock Added') : 'Stock Added'
                                        ) : 'Stock Added'
                                }}
                            </td>
                            <td class="whitespace-nowrap px-4 py-2.5">
                                 {{
                                    stock.invoice ? stock.serialnumbers.unique_id : '-'
                                    ? (stock.challan ? stock.viewserialnumbers.unique_id : stock.unique_id) : '-'
                                }}
                            </td>
                            <td class="whitespace-nowrap px-4 py-2.5">{{ stock.receive_qty ? stock.receive_qty : '-' ? (stock.is_receive == 'yes' ?  stock.qty : '-') :'-' }}</td>
                            <!-- <td class="whitespace-nowrap px-4 py-2.5">{{   (stock.invoice_id || (stock.is_receive == null)) ? stock.qty : '-' }}</td> -->
                            <td class="whitespace-nowrap px-4 py-2.5">{{   (stock.invoice && (stock.is_receive == null)) ? stock.qty : '-'
                                    ? (stock.challan && (stock.is_receive == null)) ? stock.qty : '-' : '-'}}</td>
                            <td class="whitespace-nowrap px-4 py-2.5">{{ calculateBalance(stock, index1, balanceStock) }}</td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                            <td colspan="8" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </AdminLayout>
</template>
