<?php

namespace App\Http\Controllers;

use App\Http\Requests\invoiceStoreRequest;
use App\Models\Customer;
use App\Models\User;
use App\Models\Invoice;
use App\Models\SerialNumbers;
use App\Models\Product;
use App\Models\Orders;
use App\Models\Organization;
use App\Models\OrderDetails;
use App\Models\Document;
use App\Models\OrderDeliver;
use App\Http\Requests\OrderStoreRequest;
use App\Http\Requests\OrderDeliverRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Traits\CommonTrait;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\OrdersExport;
use App\Models\Company;
use App\Models\CustomerTransaction;
use App\Models\InvoiceDetail;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseOrderReceiveDetails;
use App\Models\PurchaseOrderReceives;
use App\Models\PurchaseTransaction;
use App\Models\QuotationDetail;
use Illuminate\Support\Facades\Config;
use App\Traits\QueryTrait;
use PDF;

class OrderController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Orders')->only(['index']);
        $this->middleware('permission:Create Orders')->only(['create']);
        $this->middleware('permission:Edit Orders')->only(['edit', 'update']);
        $this->middleware('permission:Delete Orders')->only('destroy');
        $this->middleware('permission:View Orders')->only('activation');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $salesUserId = $request->input('sales_user_id');
        $categoryId = $request->input('category');
        $createdBy = $request->input('created_by');
        $statusId = $request->input('status');
        $query  = Orders::with('orderDetails.product.serialNumbers','customers', 'users', 'organization', 'quotation');
        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if($createdBy) {
            $query->where('created_by', $createdBy);
        }
        if($statusId) {
            $query->where('status', $statusId);
        }

        //for engineers only
        if(auth()->user()->can('Create Orders') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }

        if(!empty($search)){
            if(!empty($search)){
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })->orWhereHas('users', function ($subquery) use ($search) {
                    $subquery->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%$search%"]);
                })
                ->orWhere('order_number', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            }
        }

        $searchableFields = ['order_number', 'quotation.quotation_number', 'customers.customer_name', 'users.first_name', 'date', 'total_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Pending', 'In Process', 'Completed', 'Cancel'];
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate(20);
        $administrationArr =  Organization::get()->toArray();
        $administration =  (!empty($administrationArr)) ? $administrationArr[0] : $administrationArr;
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $ordersStatus = Config::get('constants.ordersStatus');
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $category = Config::get('constants.quotationList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $organization->prepend($allOrganization);
        $customers->prepend($allCustomers);
        $salesuser->prepend($allSalesuser);
        $pagetypes = collect(Config::get('constants.pageTypes'));
        $data->withQueryString()->links();

        $permissions = [
            'canCreateOrders'      => auth()->user()->can('Create Orders'),
            'canEditOrders'        => auth()->user()->can('Edit Orders'),
            'canDeleteOrders'      => auth()->user()->can('Delete Orders'),
            'canViewOrders'        => auth()->user()->can('View Orders')
        ];
        return Inertia::render('Orders/List', compact('data', 'permissions', 'administration', 'filepath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank', 'organization', 'customers', 'organizationId', 'customerId', 'salesuser', 'category', 'salesUserId', 'categoryId', 'createdBy', 'permissions', 'statusId', 'ordersStatus', 'pagetypes'));
    }

    public function create()
    {
        $order_number = $this->generateORNumber();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.productCategoryList');
        $terms = Config::get('constants.termsAndConditions');
        return Inertia::render('Orders/Add', compact('order_number', 'customers', 'salesuser', 'products', 'organization', 'category', 'terms'));
    }

    public function store(OrderStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            if(isset($data['order_id'])){
                $orders = Orders::findOrFail($data['order_id']);
                $orders->update([
                    'customer_id'       => $data['customer_id'],
                    'date'              => $data['date'],
                    'organization_id'   => $data['organization_id'],
                    'category'          => $data['category'],
                    'sales_user_id'     => $data['sales_user_id'],
                    'overall_discount'  => $data['overall_discount'],
                    'total_discount'    => $data['total_discount'],
                    'total_amount'      => $data['total_amount'],
                    'igst'              => $data['igst'],
                    'sgst'              => $data['sgst'],
                    'cgst'              => $data['cgst'],
                    'sub_total'         => $data['sub_total'],
                    'total_gst'         => $data['total_gst'],
                    'validity'          => $data['validity'],
                    'delivery'          => $data['delivery'],
                    'payment_terms'     => $data['payment_terms'],
                    'warranty'          => $data['warranty'],
                    'note'              => $data['note'],
                ]);
                foreach ($data['selectedProductItem'] as $orderDetails) {
                    $orderDetails['order_id'] = $data['order_id'];
                    $orderDetails['gst'] = isset($orderDetails['gst']) ? $orderDetails['gst'] : 0;
                    if (!empty($orderDetails['order_details_id'])) {
                        $orderDetails['updated_by'] = Auth::user()->id;
                        $quotationDetail = OrderDetails::find($orderDetails['order_details_id']);
                        $quotationDetail->update($orderDetails);
                    } else {
                        $orderDetails['created_by'] = $orderDetails['updated_by'] = Auth::user()->id;
                        $orderDetail = OrderDetails::create($orderDetails);
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['order_id']);
                }
                $orderDetail = OrderDetails::where('order_id', $data['order_id']);
                $total_qty = $orderDetail->sum('qty');
                $total_delivered_qty = $orderDetail->sum('delivered_qty');
                $status = ($total_delivered_qty == $total_qty) ? 'Completed' : (($total_delivered_qty > 0) ? 'In Process' : 'Pending');
                $updateOrder = Orders::where('id', $data['order_id'])->update([
                    'status' => $status
                ]);
                DB::commit();
                return Redirect::to('/orders')->with('success','Order Updated Successfully');
            } else {
                $data['status'] = "Pending";
                $data['overall_discount'] = $data['overall_discount'] ?? 0;

                $orderNumberByOrganization = $this->generateORNumber();
                $order_number = $orderNumberByOrganization[$data['organization_id']] ?? '';
                $data['order_number'] = $order_number;

                // $this->updateORNumber($data['order_number'], $data['organization_id']);
                $order = Orders::create($data);
                if($order){
                    foreach ($data['selectedProductItem'] as $orderDetails) {
                        $orderDetails['gst'] = isset($orderDetails['gst']) ? $orderDetails['gst'] : 0;
                        $orderDetails['order_id'] = $order->id;
                        $orderDetails['created_by'] = $orderDetails['updated_by'] = Auth::user()->id;
                        $orderDetail = OrderDetails::create($orderDetails);
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $order->id);
                }

                $this->updateORNumber($order_number, $data['organization_id']);

                DB::commit();
                return Redirect::to('/orders')->with('success','Order Created Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/orders')->with('error',$e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data     = Orders::where('id', $id)->with('orderDetails.product.serialNumbers', 'customers', 'documents', 'organization')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.orderDocument');
        $customers= Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser= User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price")->get();
        $category = Config::get('constants.productCategoryList');
        $organization  = Organization::select('id', 'name')->get();
        return Inertia::render('Orders/Edit', compact('data', 'filepath', 'customers', 'salesuser', 'products', 'category', 'organization'));
    }

    public function view(Request $request, $id)
    {
        $data = Orders::where('id', $id)->with('orderDetails.product.serialNumbers', 'customers', 'organization',  'documents', 'users')->get()->toArray();
        $orderDeliver = OrderDeliver::where('order_id', $id)->with('orderDetails.product')->get()->groupBy('order_deliver_number')->toArray();
        $filepath = Config::get('constants.uploadFilePath.orderDocument');
        return Inertia::render('Orders/View', compact('data',  'orderDeliver', 'filepath'));
    }

    public function generateOrder(string $id)
    {
        $invoice_no = $this->generateInvoiceNo();
        $retail_invoice_no = $this->generateRetailInvoiceNo();
        $order_deliver_number = $this->generateORDNumber();
        $data     = Orders::where('id', $id)->with('orderDetails.product.serialNumbers', 'customers', 'documents', 'organization', 'pendingOrderDetails.product.serialNumbers')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.orderDocument');
        $customers= Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser= User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price")->get();
        $category = Config::get('constants.productCategoryList');
        $organization  = Organization::select('id', 'name')->get();
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date, organization_id')->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        return Inertia::render('Orders/invoicegenerate', compact('data', 'order_deliver_number', 'invoice_no', 'retail_invoice_no', 'filepath','serialno', 'customers', 'salesuser', 'products', 'category', 'organization'));
    }

    public function orderToInvoice(invoiceStoreRequest $request)
    {
        DB::beginTransaction();
        $data = $request->all();
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        try {
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
            $data['status'] = "Unpaid";
            $data['entity_id'] = null;
            $data['entity_type'] = "invoice";
            if($data['invoice_type'] == 'Tax'){
                $this->updateInvoiceNo($data['invoice_no'],  $data['organization_id']);
            } else {
                $this->updateRetailInvoiceNo($data['invoice_no'], $data['organization_id']);
            }
            $data['pending_amount'] = $data['total_amount'];// TO DO CHECK WHILE EDIT
            $invoice = Invoice::create($data);

            $data['amount'] = $data['total_amount'];
            $data['payment_type'] = 'dr';
            $data['entity_id'] = $invoice->id;
            $data['entity_type'] = 'invoice';
            $lastTransaction = CustomerTransaction::where(['customer_id' => $data['customer_id'], 'organization_id' => $data['organization_id']])->latest()->first();
            $data['note'] = 'Invoice No :' .$data['invoice_no'];
            CustomerTransaction::create($data);

            if($invoice){
                foreach ($data['selectedProductItem'] as $invoiceDetails) {
                    $invoiceDetails['invoice_id'] = $invoice->id;
                    $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = $data['created_by'];
                    $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                    $invoiceDetail = InvoiceDetail::create($invoiceDetails);

                    Product::addProductLog($invoiceDetail, $invoiceDetails, $invoiceDetails['product_id'],  $invoice->organization_id, 'payment');

                    $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                    $updatedQty = $getSellQty->sell_qty + $invoiceDetails['qty'];
                    if($getSellQty){
                        $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                    }
                }
            }

            $files = $request->file('document');
            if($files){
                $this->uploadInvoiceDocuments($files, $invoice->id);
            }
            DB::commit();
            return Redirect::to("/orderdeliver/$request->order_id")->with('success', 'Invoice Created Successfully');
            // return Redirect::back()->with('success', 'Invoice Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/orders')->with('error', $e->getMessage());
        }
    }

    public function orderDeliver(Request $request, $id)
    {
        $data = Orders::where('id', $id)->with('pendingOrderDetails.product', 'customers', 'organization',  'documents', 'users')->get()->toArray();
        $order_deliver_number = $this->generateORDNumber();
        return Inertia::render('Orders/Deliver', compact('data', 'order_deliver_number'));
    }

    public function saveOrderDeliver(OrderDeliverRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $order_id = $data['deliveredProduct'][0]['order_id'];
            $invoice_number = $data['invoice_number'] ?? null; // Get invoice_number from request

            $order_deliver_number = $this->generateORDNumber();

            while (OrderDeliver::where('order_deliver_number', $order_deliver_number)->exists()) {
                $this->updateORDNumber($order_deliver_number);
                $order_deliver_number = $this->generateORDNumber();
            }

            $this->updateORDNumber($order_deliver_number);

            foreach ($data['deliveredProduct'] as $deliveredProduct) {
                if ($deliveredProduct['delivered_qty'] != '') {
                    $deliveredProduct['created_by'] = $deliveredProduct['updated_by'] = Auth::user()->id;
                    $deliveredProduct['deliver_date'] = date('Y-m-d');
                    $deliveredProduct['order_deliver_number'] = $order_deliver_number;
                    $deliveredProduct['invoice_number'] = $invoice_number; // Add invoice_number to each delivery record
                    OrderDeliver::create($deliveredProduct);

                    $purchaseOrderDetail = OrderDetails::find($deliveredProduct['order_details_id']);
                    $purchaseOrderDetail->update([
                        'delivered_qty' => $purchaseOrderDetail->delivered_qty + $deliveredProduct['delivered_qty']
                    ]);
                }
            }

            $orderDetail = OrderDetails::where('order_id', $order_id);
            $total_qty = $orderDetail->sum('qty');
            $total_delivered_qty = $orderDetail->sum('delivered_qty');
            $status = ($total_delivered_qty == $total_qty) ? 'Completed' : (($total_delivered_qty > 0) ? 'In Process' : '');

            Orders::where('id', $order_id)->update([ 'status' => $status]);

            DB::commit();
            return Redirect::to('/orders')->with('success', 'Order Delivered Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/orders')->with('error', 'Something went wrong: ' . $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $orders = Orders::find($id);
            if($orders) {
                $ordersDetail = OrderDetails::where('order_id', $id)->get();
                foreach ($ordersDetail as $detail) {
                    $detail->delete();
                }
                $orders->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Order Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error','Something want wrong');
        }
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.orderDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "orders";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    public function exportOrders(Request $request)
    {

        $data = $request->input();
        $organizationId = $data['organization_id'];
        $customerId = $data['customer_id'];
        $salesUserId = $data['sales_user_id'];
        $categoryId = $data['category_id'];
        $createdBy = $data['created_by'];
        $status = $data['status'];
        $query = Orders::with(['quotation', 'customers', 'users']);
        if($customerId != "null") {
            $query->where('customer_id', $customerId);
        }
        if($organizationId != "null") {
            $query->where('organization_id', $organizationId);
        }
        if($categoryId != "null") {
            $query->where('category', $categoryId);
        }
        if($salesUserId != "null") {
            $query->where('sales_user_id', $salesUserId);
        }
        if($createdBy != "null") {
            $query->where('created_by', $createdBy);
        }
        if($status != "null") {
            $query->where('status', $status);
        }
        $allData = $query->get()->toArray();
        return Excel::download(new OrdersExport($allData), 'Orders_Report_' . now()->format('Y-m-d') . '.xlsx');
    }

    public function downloadOrder($id, $type)
    {
        $data = Orders::with('orderDetails.product.serialNumbers', 'customers', 'documents', 'organization')->where('id', $id)->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $pdf = PDF::loadView('pdf.order', compact('data', 'filepath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank'))->setPaper('A4', $type);
        $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
        return $pdf->download("Proforma_invoice_{$sanitizedFilename}.pdf");
        // return $pdf->stream();
    }



}
