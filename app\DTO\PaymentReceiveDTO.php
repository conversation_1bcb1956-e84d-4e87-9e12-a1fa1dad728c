<?php

namespace App\DTO;

use App\Traits\ArrayToProps;

class PaymentReceiveDTO
{
    use ArrayToProps;

    public $organization_id;
    public $customer_id;
    public $payment_type;
    public $date;
    public $note;
    public $amount;
    public $tds_amount;
    public $discount_amount;
    public $round_off;
    public $is_credit;
    public $credit_data;
    public $total_unused_amount;
    public $check_number;
    public $bank_name;
    public $org_bank_id;
    public $invoice;
    public $settled_amount;
    public $advance_amount;
    public $invoice_id;
    public $invoice_data;
    public $invoice_no;
    public $invoice_nos;
    public $id;
    public $created_by;
    public $updated_by;

}
