<?php
namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class StockExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $organizationName;
    protected $companyName;

    public function __construct(array $allData, $organizationName = null, $companyName = null)
    {
        $this->allData = $allData;
        $this->organizationName = $organizationName;
        $this->companyName = $companyName;
    }

    public function title(): string
    {
        return 'Stock Report as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        return [
            ["Code", "Product Name", "Current Stock", "MRP", "Purchase Price", "Batch", "Company"]
        ];
    }

    public function collection()
    {
        return collect(array_map(function ($d1) {
            return [
                $d1['product_code'],
                $d1['product_name'],
                $d1['stock'],
                $d1['mrp'],
                $d1['purchase_price'],
                $d1['batch'],
                $d1['company_name'],
            ];
        }, $this->allData));
    }

    public function startCell(): string
    {
        return 'A3'; 
    }

    public function styles(Worksheet $sheet)
    {
        $rowIndex = 1;
    
        if ($this->organizationName) {
            $sheet->setCellValue('A' . $rowIndex, "Organization: " . $this->organizationName);
            $sheet->mergeCells('A' . $rowIndex . ':G' . $rowIndex);
            $sheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getAlignment()->setHorizontal('center'); 
            $rowIndex++;
        }
    
        if ($this->companyName) {
            $sheet->setCellValue('A' . $rowIndex, "Company: " . $this->companyName);
            $sheet->mergeCells('A' . $rowIndex . ':G' . $rowIndex);
            $sheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getAlignment()->setHorizontal('center'); 
            $rowIndex++;
        }
    
        $sheet->getStyle("A$rowIndex:G$rowIndex")->getFont()->setBold(true);
        $sheet->getStyle("A$rowIndex:G$rowIndex")->getAlignment()->setHorizontal('center');
        $sheet->getStyle("A$rowIndex:G$rowIndex")->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle("A$rowIndex:G$rowIndex")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFFFCC');
    
        $highestRow = $sheet->getHighestRow();
        $sheet->getStyle("A" . ($rowIndex + 1) . ":G$highestRow")->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle("A" . ($rowIndex + 1) . ":G$highestRow")->getAlignment()->setHorizontal('center');
    
        return [];
    }
}
