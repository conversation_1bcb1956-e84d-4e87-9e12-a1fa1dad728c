<?php 

namespace App\DTO;

use App\Traits\ArrayToProps;

class OrdersDTO
{
    use ArrayToProps;

    public $category;
    public $organization_id;
    public $customer_id;
    public $order_id;
    public $sales_user_id;
    public $quotation_id;
    public $order_number;
    public $document;
    public $igst;
    public $sgst;
    public $cgst;
    public $date;
    public $sub_total;
    public $total_gst;
    public $overall_discount;
    public $total_discount;
    public $total_amount;
    public $note;
    public $validity;
    public $delivery;
    public $payment_terms;
    public $warranty;
    public $selectedProductItem;
    public $created_by;
    public $updated_by;

}