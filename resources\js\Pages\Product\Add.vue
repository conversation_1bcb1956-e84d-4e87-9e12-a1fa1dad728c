<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import FileUpload from '@/Components/FileUpload.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps, ref , onBeforeMount  } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['company_id', 'category', 'filepath']);

const form = useForm('post', '/products', {
    company_id:props.company_id,
    name:'',
    prefix: '',
    min_qty:'1',
    item_code:'',
    description:'',
    price:'',
    rating:'',
    gst:'',
    hsn_code:'',
    image:'/uploads/companyprofile/defaultimg.png',
    category:''
});

const submit = () => {
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
});
};


const setCategory = (id, name) => {
    form.category = name;
};

const handlePhoto = (file) => {
    form.image = file;
};

</script>

<template>
    <Head title="Product Add" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Add New Product</h2>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-2">
                            <InputLabel for="name" value="Product Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                                @change="form.validate('name')"
                            />
                            <InputError  v-if="form.invalid('name')" class="" :message="form.errors.name" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="item_code" value="Product Code" />
                            <TextInput
                                id="item_code"
                                type="text"
                                v-model="form.item_code"
                                @change="form.validate('item_code')"
                            />
                            <InputError  v-if="form.invalid('item_code')" class="" :message="form.errors.item_code" />
                        </div>
                        <div class="sm:col-span-2"></div>
                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="Category" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="category"
                                    v-model="form.category"
                                    @onchange="setCategory"
                                    :class="{ 'error rounded-md': form.errors.category }"
                                    />
                                </div>
                            <InputError v-if="form.invalid('category')" class="" :message="form.errors.category" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="hsn_code" value="HSN Code" />
                            <TextInput
                                id="hsn_code"
                                type="text"
                                v-model="form.hsn_code"
                                @change="form.validate('hsn_code')"
                            />
                            <InputError  v-if="form.invalid('hsn_code')" class="" :message="form.errors.hsn_code" />
                        </div>
                        <div class="sm:col-span-2"></div>
                        <div class="sm:col-span-1">
                            <InputLabel for="price" value="Price (₹)" />
                            <TextInput
                                id="price"
                                type="text"
                                v-model="form.price"
                                @change="form.validate('price')"
                                min="1"
                            />
                            <InputError  v-if="form.invalid('price')" class="" :message="form.errors.price" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="gst" value="GST(%)" />
                            <TextInput
                                id="gst"
                                type="text"
                                v-model="form.gst"
                                @change="form.validate('gst')"
                                min="0"
                                max="100"
                            />
                            <InputError  v-if="form.invalid('gst')" class="" :message="form.errors.gst" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="min_qty" value="Minimum Qty" />
                            <TextInput
                                id="min_qty"
                                type="text"
                                v-model="form.min_qty"
                                @change="form.validate('min_qty')"
                            />
                            <InputError  v-if="form.invalid('min_qty')" class="" :message="form.errors.min_qty" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="prefix" value="Prefix" />
                            <TextInput
                                id="prefix"
                                type="text"
                                v-model="form.prefix"
                            />
                        </div>
                        <!-- <div v-if="form.category == 'Service'" class="flex sm:col-span-4 space-x-4" v-for="(product, index)  in serviceProduct" :key="index">
                            <div class="w-1/3">
                                <div class="block text-sm font-semibold leading-6 text-gray-900">{{ product.organization_name }}</div>
                            </div>
                            <div class="w-1/3">
                                <InputLabel for="stock" value="Current Stock" />
                                <TextInput
                                    id="stock"
                                    type="text"
                                    :numeric="true"
                                    v-model="product.stock"
                                />
                            </div>
                            <div class="w-1/3">
                                <InputLabel for="purchase_price" value="Purchase Price (₹)" />
                                <TextInput
                                    id="purchase_price"
                                    type="text"
                                    v-model="product.purchase_price"
                                    min="1"
                                />
                            </div>
                        </div> -->
                        <div class="sm:col-span-4">
                            <InputLabel for="image" value="Upload Image"/>
                            <FileUpload
                                label="Upload Image"
                                inputId="image"
                                inputName="image"
                                :fileUrl="form.image"
                                @file="handlePhoto"
                            />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="description" value="Description" />
                            <TextArea
                                id="description"
                                type="text"
                                v-model="form.description"
                                @change="form.validate('description')"
                                :rows="5"
                            />
                            <InputError  v-if="form.invalid('description')" class="" :message="form.errors.description" />
                        </div>
                    </div>
                </div>


                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('products.show',{id:company_id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
    </AdminLayout>
</template>
