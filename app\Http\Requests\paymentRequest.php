<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class paymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        $rules = [
            'data.payment_type' => 'required|string|max:255',
            'data.amount'       => 'required|numeric|gt:0',
            'data.date'         => 'required|date',
        ];

        if($this->input('data.payment_type') != 'cash') {
            $rules['data.org_bank_id'] = 'required|integer';
        }

        return $rules;
    }
}
