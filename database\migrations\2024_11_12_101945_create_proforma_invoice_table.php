<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proforma_invoice', function (Blueprint $table) {
            $table->id();;
            $table->enum('category', ['Sales', 'Service']);
            $table->integer('organization_id');
            $table->integer('customer_id');
            $table->integer('sales_user_id');
            $table->string('quotation_id')->nullable();
            $table->string('order_number');
            $table->string('date');
            $table->enum('status', ['Pending', 'In Process', 'Completed', 'Cancelled']);
            $table->double('igst',16,2)->nullable();
            $table->double('sgst',16,2)->nullable();
            $table->double('cgst',16,2)->nullable();
            $table->double('sub_total',16,2)->nullable();
            $table->double('total_gst',16,2)->nullable();
            $table->string('overall_discount');
            $table->string('total_discount');
            $table->double('total_amount',16,2);;
            $table->longText('note')->nullable();
            $table->string('validity');
            $table->string('delivery');
            $table->longText('payment_terms')->nullable();
            $table->string('warranty')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('proforma_invoice');
    }
};
