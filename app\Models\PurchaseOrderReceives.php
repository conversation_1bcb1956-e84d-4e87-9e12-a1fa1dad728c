<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrderReceives extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    const DOCUMENT_TYPE = 'purchase_order_receives';

    protected $table = 'purchase_order_receives';

    protected static $logName = 'Company-PO-Receive';

    public function getLogDescription(string $event): string
    {
        $companyName = $this->purchaseOrder ? $this->purchaseOrder->company->name : 'Unknown Company';
        return "<strong>{$companyName}'s</strong> Purchase Receive <strong>{$this->customer_invoice_no}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'type',
        'purchase_order_id',
        'po_receive_number',
        'customer_invoice_no',
        'customer_invoice_date',
        'po_receive_date',
        'total_price',
        'total_gst_amount',
        'total_amount',
        'status',
        'paid_amount',
        'pending_amount',
        'note',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'type',
        'purchase_order_id',
        'po_receive_number',
        'customer_invoice_no',
        'customer_invoice_date',
        'po_receive_date',
        'total_price',
        'total_gst_amount',
        'total_amount',
        'status',
        'paid_amount',
        'pending_amount',
        'note',
        'created_by',
        'updated_by'
    ];

    public function users(){
        return $this->belongsTo(User::class,'created_by','id');
    }

    public function purchaseOrderDetail()
    {
        return $this->hasMany(PurchaseOrderDetail::class, 'purchase_order_id', 'purchase_order_id');
    }

    public function purchaseOrderReceiveDetails()
    {
        return $this->hasMany(PurchaseOrderReceiveDetails::class, 'purchase_order_receive_id', 'id');
    }

    public function purchaseOrder(){
        return $this->belongsTo(PurchaseOrder::class,'purchase_order_id', 'id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Company PO Receive for : " . $model->purchaseOrder->po_number;

        foreach ($model->purchaseOrderReceiveDetails as $detail){
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'purchase_order_receives');
    }
}
