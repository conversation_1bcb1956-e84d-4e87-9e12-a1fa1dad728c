<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing foreign key constraint
        Schema::table('payment_receive', function (Blueprint $table) {
            $table->dropForeign('prin_id');
        });

        // Modify the columns
        Schema::table('payment_receive', function (Blueprint $table) {
            $table->integer('invoice_id')->nullable()->change();
            $table->integer('org_bank_id')->nullable()->change();
            $table->string('invoice_no')->nullable()->change();
        });

        // Re-add the foreign key constraint
        Schema::table('payment_receive', function (Blueprint $table) {
            $table->foreign('invoice_id')->references('id')->on('invoice');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
