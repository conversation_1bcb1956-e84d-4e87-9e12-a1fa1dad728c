import{K as st,r as g,C as It,j as P,o as _,c as p,a as d,u as i,w as f,F as A,Z as Ft,b as t,t as m,k as Vt,v as kt,d as Tt,n as y,f as x,i as ot,g as F,e as Pt,s as $t,x as Gt}from"./app-8a557454.js";import{_ as Ut,a as Dt}from"./AdminLayout-301d54ca.js";import{_ as Mt}from"./InputError-ccd7f9dc.js";import{_ as T}from"./InputLabel-07f3a6e8.js";import{P as at}from"./PrimaryButton-9d9bcdd8.js";import{_ as v}from"./TextInput-ab168ee4.js";import{_ as jt}from"./TextArea-3588e81e.js";import{_ as $}from"./SearchableDropdown-51a69527.js";import{D as lt}from"./DangerButton-41cc1b93.js";import{_ as B}from"./SecondaryButton-e65b5ab9.js";import{M as q}from"./Modal-3bbbc3d3.js";import{_ as Nt}from"./FileViewer-c68b7823.js";import{_ as At}from"./MultipleFileUpload-e5c9465d.js";import{u as Bt}from"./index-62ab7306.js";import{_ as qt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const r=w=>($t("data-v-e53b2008"),w=w(),Gt(),w),zt={class:"animate-top"},Et={class:"sm:flex sm:items-center"},Ot=r(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Challan")],-1)),Lt={class:"w-auto"},Ht={class:"flex space-x-2 items-center"},Wt={class:"text-sm font-semibold text-gray-900"},Rt={class:"flex space-x-2 items-center"},Kt=["onSubmit"],Qt={class:"shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Xt={class:"inline-flex items-start space-x-6 justify-start w-full"},Zt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Jt={class:"inline-flex items-center justify-start w-full space-x-2"},Yt=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),te={class:"text-sm leading-6 text-gray-700"},ee={class:"inline-flex items-center justify-start w-full space-x-2"},se=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),oe={class:"text-sm leading-6 text-gray-700"},ae={class:"inline-flex items-center justify-start w-full space-x-2"},le=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),ne={class:"text-sm leading-6 text-gray-700"},ie={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},ce={class:"inline-flex items-center justify-start w-full space-x-2"},de=r(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Challan Number:",-1)),re={class:"text-sm leading-6 text-gray-700"},ue={class:"inline-flex items-center justify-start w-full space-x-2"},me=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),_e={class:"text-sm leading-6 text-gray-700"},pe={class:"inline-flex items-center justify-start w-full space-x-2"},he=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1)),xe={class:"relative mt-2"},ge={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border space-y-2"},fe={class:"overflow-x-auto w-full"},ye={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},ve=r(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),we=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Serial No",-1)),be=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Ce=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Se=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Ie=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Fe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Ve=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),ke=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Te=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Pe={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},$e={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ge={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ue={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},De={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Me=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),je=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Ne=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Ae={class:"divide-y divide-gray-300 bg-white"},Be={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},qe={class:"relative mt-2"},ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Ee={class:"relative mt-2"},Oe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Le={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},He={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},We={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Re={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ze={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Je={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ye={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ts={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ss={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},os={class:"whitespace-nowrap px-3 py-3 flex space-x-2 min-w-48"},as={class:"px-3 py-3 text-sm text-gray-900"},ls=["onClick"],ns=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),is=[ns],cs={class:"mt-12 flex items-center justify-between"},ds={class:"ml-auto flex items-center justify-end gap-x-6"},rs={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},us={class:"min-w-full divide-y divide-gray-300"},ms=r(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),_s={class:"divide-y divide-gray-300 bg-white"},ps={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},hs={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},xs=["onClick"],gs=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),fs=[gs],ys=["onClick"],vs=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ws=[vs],bs=["onClick"],Cs=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Ss=[Cs],Is={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Fs={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Vs={class:"sm:col-span-3 space-y-4"},ks={class:"flex space-x-4"},Ts={class:"w-full"},Ps={class:"w-full"},$s={class:"relative mt-2"},Gs={class:"flex space-x-4"},Us={class:"w-full"},Ds={class:"w-full"},Ms={class:"sm:col-span-3"},js={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Ns={class:"inline-flex items-center justify-end w-full space-x-3"},As=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Bs={class:"text-base font-semibold text-gray-900 w-32"},qs={class:"inline-flex items-center justify-end w-full space-x-3"},zs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Es={class:"text-base font-semibold text-gray-900 w-32"},Os={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Ls=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Hs={class:"text-base font-semibold text-gray-900 w-32"},Ws={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Rs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Ks={class:"text-base font-semibold text-gray-900 w-32"},Qs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Xs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Zs={class:"text-base font-semibold text-gray-900 w-32"},Js={class:"inline-flex items-center justify-end w-full space-x-3"},Ys=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),to={class:"text-base font-semibold text-gray-900 w-32"},eo={class:"flex items-center justify-between"},so={class:"ml-auto flex items-center justify-end gap-x-6"},oo=r(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),ao={class:"p-6"},lo=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),no={class:"mt-6 flex justify-end"},io={class:"p-6"},co=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),ro={class:"mt-6 flex justify-end"},uo={class:"p-6"},mo={class:"mt-6 px-4 flex justify-end"},_o={__name:"ChallanTransaferEdit",props:["salesuser","customers","category","serialno","filepath","products"],setup(w){const V=w,z=st().props.filepath.view,c=st().props.data[0],E=g([]),nt=V.products.filter(s=>s.serial_numbers.some(e=>e.organization_id===c.organization_id));E.value=nt;const O=g([]),it=V.serialno;O.value=it;const a=Bt("post","/challan",{stock_transfer:"yes",note:c.note,date:c.date,purchase_order_id:c.purchase_order_id,purchase_order_receive_id:c.purchase_order_receive_id,category:c.category,organization_id:c.organization_id,selectedProductItem:[],customer_id:c.customer_id,sales_user_id:c.sales_user_id,challan_number:c.challan_number,challan_id:c.id,document:c.documents,cgst:c.cgst,sgst:c.sgst,igst:c.igst,total_gst:c.total_gst,sub_total:c.sub_total,total_amount:c.total_amount,total_discount:c.total_discount,dispatch:c.dispatch,transport:c.transport}),ct=()=>{a.sub_total=H.value,a.challan_number=V.challan_number,a.cgst=h.value=="CGST/SGST"?C.value/2:"0",a.sgst=h.value=="CGST/SGST"?C.value/2:"0",a.igst=h.value=="IGST"?C.value:"0",a.total_gst=C.value,a.total_amount=L.value,a.total_discount=W.value,a.selectedProductItem=u.value,a.submit({preserveScroll:!0,onSuccess:()=>a.reset()})},u=g([{serial_number_id:"",editmode:"",product_id:"",product_name:"",item_code:"",expiry_date:"",mrp:"",price:"",hsn_code:"",sell_price:"",discount:"",discount_amount:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",challan_detail_id:"",description:""}]);It(()=>{u.value=c.challan_detail.map(s=>({challan_detail_id:s.id,editmode:"editMode",product_id:s.viewserialnumbers.product.id,serial_number_id:s.serial_number_id,product_name:s.viewserialnumbers.product.name,qty:s.qty,expiry_date:s.viewserialnumbers.expiry_date,mrp:s.viewserialnumbers.mrp?parseFloat(s.viewserialnumbers.mrp).toFixed(2):"-",price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),hsn_code:s.viewserialnumbers.product.hsn_code,item_code:s.viewserialnumbers.product.item_code,description:s.description,discount:parseFloat(s.discount).toFixed(2),sell_price:parseFloat(s.price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0",total_price:parseFloat(s.total_price).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2)}))});const h=g(c.customers.gst_type),dt=(s,l)=>{a.category=s,a.errors.category=null},rt=()=>{u.value.push({challan_detail_id:"",product_name:"",product_id:"",serial_number_id:"",expiry_date:"",mrp:"",price:"",hsn_code:""})},ut=(s,l,e)=>V.serialno.filter(o=>o.product_id===s&&o.organization_id===c.organization_id),mt=(s,l,e)=>{const n=V.serialno.filter(o=>o.product_id===s&&o.organization_id===c.organization_id);O.value=n,u.value[e].product_id=s},_t=(s,l,e)=>{const n=V.serialno.find(o=>o.id===s);n&&(u.value[e].qty="",u.value[e].serial_number_id=n.id,u.value[e].product_name=n.product.name,u.value[e].expiry_date=n.expiry_date,u.value[e].mrp=n.mrp?parseFloat(n.mrp).toFixed(2):"-",u.value[e].price=parseFloat(n.purchase_price).toFixed(2),u.value[e].hsn_code=n.product.hsn_code,u.value[e].discount=0,u.value[e].sell_price=0,u.value[e].total_price=parseFloat(n.purchase_price).toFixed(2),u.value[e].gst=parseFloat(n.product.gst).toFixed(2),u.value[e].sgst=parseFloat(n.product.gst/2).toFixed(2),u.value[e].gst_amount=0,u.value[e].total_gst_amount=0,u.value[e].total_amount=0,u.value[e].description="",a.errors[`selectedProductItem.${e}.serial_number_id`]=null)},pt=(s,l)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount)||0,o=h.value=="IGST"?parseFloat(s.gst):parseFloat(s.sgst*2),I=parseFloat(s.qty),Z=e*I*(1+o/100),J=e*1*(o/100),Y=e*I*(o/100),N=Z*(n/100)||0,tt=Z-N,et=e*I;return s.total_price=isNaN(et)?"":parseFloat(et).toFixed(2),s.gst_amount=isNaN(J)?"":parseFloat(J).toFixed(2),s.total_gst_amount=isNaN(Y)?"":parseFloat(Y).toFixed(2),s.discount_amount=isNaN(N)?"":parseFloat(N).toFixed(2),isNaN(tt)?"":parseFloat(tt).toFixed(2)},b=(s,l)=>{s.total_amount=pt(s)},L=P(()=>Math.round(u.value.reduce((s,l)=>s+(l.total_amount?parseFloat(l.total_amount):0),0))),C=P(()=>u.value.reduce((s,l)=>s+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),H=P(()=>u.value.reduce((s,l)=>s+(l.total_price?parseFloat(l.total_price):0),0)),W=P(()=>u.value.reduce((s,l)=>s+(l.discount_amount?parseFloat(l.discount_amount):0),0)),G=g(!1),R=g(null),ht=g(null),U=()=>{G.value=!1},xt=()=>{a.get(route("removechallantransferproduct",{id:R.value,model:"ChallanDetail"}),{onSuccess:()=>{U(),u.value.splice(index,1)}})},gt=(s,l)=>{l!==void 0&&l!=""?(R.value=l,ht.value=s,G.value=!0):u.value.splice(s,1)},S=s=>{a.errors[s]=null},ft=s=>{a.document=s},yt=(s,l)=>{a.sales_user_id=s},D=g(!1),K=g(null),vt=s=>{K.value=s,D.value=!0},wt=()=>{a.get(route("removedocument",{id:K.value,name:"challanDocument"}),{onSuccess:()=>{M()}})},M=()=>{D.value=!1},j=g(!1),Q=g(null),bt=g("custom"),Ct=s=>{Q.value=s,j.value=!0},X=()=>{j.value=!1},St=s=>{const l=window.location.origin+z+s,e=document.createElement("a");e.href=l,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},k=s=>{const[l,e]=s.toFixed(2).toString().split(".");return l.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")};return(s,l)=>(_(),p(A,null,[d(i(Ft),{title:"Challan"}),d(Ut,null,{default:f(()=>[t("div",zt,[t("div",Et,[Ot,t("div",Lt,[t("div",Ht,[t("p",Wt,m(i(c).organization.name),1),t("div",Rt,[Vt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>i(a).date=e),onChange:l[1]||(l[1]=e=>i(a).validate("date"))},null,544),[[kt,i(a).date]])])])])]),t("form",{onSubmit:Tt(ct,["prevent"]),class:"mt-6 space-y-6"},[t("div",Qt,[t("div",Xt,[t("div",Zt,[t("div",Jt,[Yt,t("p",te,m(i(c).customers.customer_name??"-"),1)]),t("div",ee,[se,t("p",oe,m(i(c).customers.gst_no??"-"),1)]),t("div",ae,[le,t("p",ne,m(i(c).customers.email??"-"),1)])]),t("div",ie,[t("div",ce,[de,t("span",re,m(i(c).challan_number),1)]),t("div",ue,[me,t("p",_e,m(i(c).customers.contact_no??"-"),1)]),t("div",pe,[he,t("div",xe,[d($,{options:w.category,modelValue:i(a).category,"onUpdate:modelValue":l[2]||(l[2]=e=>i(a).category=e),onOnchange:dt,class:y({"error rounded-md":i(a).errors.category})},null,8,["options","modelValue","class"])])])])])]),t("div",ge,[t("div",fe,[t("table",ye,[t("thead",null,[t("tr",null,[ve,we,be,Ce,Se,Ie,Fe,Ve,ke,Te,h.value=="IGST"?(_(),p("th",Pe,"IGST (%)")):x("",!0),h.value=="IGST"?(_(),p("th",$e,"IGST (₹)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",Ge,"CGST (%)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",Ue,"SGST (%)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",De,"Total GST (₹)")):x("",!0),Me,je,Ne])]),t("tbody",Ae,[(_(!0),p(A,null,ot(u.value,(e,n)=>(_(),p("tr",{key:n},[t("td",Be,[t("div",qe,[d($,{options:E.value,modelValue:e.product_id,"onUpdate:modelValue":o=>e.product_id=o,onOnchange:(o,I)=>mt(o,I,n),onChange:l[3]||(l[3]=o=>i(a).validate("product_id")),class:y({"error rounded-md":i(a).errors[`selectedProductItem.${n}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",ze,[t("div",Ee,[d($,{options:ut(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":o=>e.serial_number_id=o,onOnchange:(o,I)=>_t(o,I,n),onChange:l[4]||(l[4]=o=>i(a).validate("serial_number_id")),class:y({"error rounded-md":i(a).errors[`selectedProductItem.${n}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Oe,m(e.hsn_code??"-"),1),t("td",Le,m(e.expiry_date??"-"),1),t("td",He,[d(v,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":o=>e.description=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".description"),class:y({error:i(a).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",We,m(e.mrp??"-"),1),t("td",Re,m(e.price),1),t("td",Ke,[d(v,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":o=>e.qty=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".qty"),class:y({error:i(a).errors[`selectedProductItem.${n}.qty`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])]),t("td",Qe,[d(v,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":o=>e.sell_price=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".sell_price"),class:y({error:i(a).errors[`selectedProductItem.${n}.sell_price`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])]),t("td",Xe,m(e.total_price),1),h.value=="IGST"?(_(),p("td",Ze,[d(v,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":o=>e.gst=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".gst"),class:y({error:i(a).errors[`selectedProductItem.${n}.gst`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])])):x("",!0),h.value=="CGST/SGST"?(_(),p("td",Je,[d(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":o=>e.sgst=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".gst"),class:y({error:i(a).errors[`selectedProductItem.${n}.gst`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])])):x("",!0),h.value=="CGST/SGST"?(_(),p("td",Ye,[d(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":o=>e.sgst=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".gst"),class:y({error:i(a).errors[`selectedProductItem.${n}.gst`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])])):x("",!0),t("td",ts,m(e.total_gst_amount),1),t("td",es,[d(v,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":o=>e.discount=o,onInput:o=>b(e,n),onChange:o=>S("selectedProductItem."+n+".discount"),class:y({error:i(a).errors[`selectedProductItem.${n}.discount`]}),disabled:e.editmode},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class","disabled"])]),t("td",ss,m(e.discount_amount),1),t("td",os,[t("div",as,m(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:o=>gt(n,e.challan_detail_id)},is,8,ls)])]))),128))])])]),t("div",cs,[t("div",ds,[d(at,{onClick:rt,type:"button"},{default:f(()=>[F("Add Product")]),_:1})])])]),i(c).documents&&i(c).documents.length>0?(_(),p("div",rs,[t("table",us,[ms,t("tbody",_s,[(_(!0),p(A,null,ot(i(c).documents,(e,n)=>(_(),p("tr",{key:i(c).id,class:""},[t("td",ps,m(e.orignal_name),1),t("td",hs,[t("button",{type:"button",onClick:o=>vt(e.id)},fs,8,xs),t("button",{type:"button",onClick:o=>Ct(e.name)},ws,8,ys),t("button",{type:"button",onClick:o=>St(e.name)},Ss,8,bs)])]))),128))])])])):x("",!0),t("div",Is,[t("div",Fs,[t("div",Vs,[t("div",ks,[t("div",Ts,[d(T,{for:"note",value:"Upload Documents"}),d(At,{inputId:"document",inputName:"document",uploadedFiles:i(a).document,onFiles:ft},null,8,["uploadedFiles"])]),t("div",Ps,[d(T,{for:"company_name",value:"Sales Person"}),t("div",$s,[d($,{options:w.salesuser,modelValue:i(a).sales_user_id,"onUpdate:modelValue":l[5]||(l[5]=e=>i(a).sales_user_id=e),onOnchange:yt,class:y({"error rounded-md":i(a).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",Gs,[t("div",Us,[d(T,{for:"company_name",value:"Transport"}),d(v,{id:"gst",type:"text",modelValue:i(a).dispatch,"onUpdate:modelValue":l[6]||(l[6]=e=>i(a).dispatch=e)},null,8,["modelValue"])]),t("div",Ds,[d(T,{for:"company_name",value:"Dispatch"}),d(v,{id:"transport",type:"text",modelValue:i(a).transport,"onUpdate:modelValue":l[7]||(l[7]=e=>i(a).transport=e)},null,8,["modelValue"])])]),t("div",null,[d(T,{for:"note",value:"Note"}),d(jt,{id:"note",type:"text",modelValue:i(a).note,"onUpdate:modelValue":l[8]||(l[8]=e=>i(a).note=e),onChange:l[9]||(l[9]=e=>i(a).validate("note"))},null,8,["modelValue"]),i(a).invalid("note")?(_(),Pt(Mt,{key:0,class:"",message:i(a).errors.note},null,8,["message"])):x("",!0)])]),t("div",Ms,[t("div",js,[t("div",Ns,[As,t("p",Bs,m(k(H.value)),1)]),t("div",qs,[zs,t("p",Es,m(k(W.value)),1)]),h.value=="IGST"?(_(),p("div",Os,[Ls,t("p",Hs,m(k(C.value)),1)])):x("",!0),h.value=="CGST/SGST"?(_(),p("div",Ws,[Rs,t("p",Ks,m(k(C.value/2)),1)])):x("",!0),h.value=="CGST/SGST"?(_(),p("div",Qs,[Xs,t("p",Zs,m(k(C.value/2)),1)])):x("",!0),t("div",Js,[Ys,t("p",to,m(k(L.value)),1)])])])])]),t("div",eo,[t("div",so,[d(Dt,{href:s.route("challan.index")},{svg:f(()=>[oo]),_:1},8,["href"]),d(at,{disabled:i(a).processing},{default:f(()=>[F("Submit")]),_:1},8,["disabled"])])])],40,Kt)]),d(q,{show:G.value,onClose:U},{default:f(()=>[t("div",ao,[lo,t("div",no,[d(B,{onClick:U},{default:f(()=>[F(" Cancel ")]),_:1}),d(lt,{class:"ml-3",onClick:xt},{default:f(()=>[F(" Delete ")]),_:1})])])]),_:1},8,["show"]),d(q,{show:D.value,onClose:M},{default:f(()=>[t("div",io,[co,t("div",ro,[d(B,{onClick:M},{default:f(()=>[F(" Cancel ")]),_:1}),d(lt,{class:"ml-3",onClick:wt},{default:f(()=>[F(" Delete ")]),_:1})])])]),_:1},8,["show"]),d(q,{show:j.value,onClose:X,maxWidth:bt.value},{default:f(()=>[t("div",uo,[d(Nt,{fileUrl:i(z)+Q.value},null,8,["fileUrl"]),t("div",mo,[d(B,{onClick:X},{default:f(()=>[F(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Po=qt(_o,[["__scopeId","data-v-e53b2008"]]);export{Po as default};
