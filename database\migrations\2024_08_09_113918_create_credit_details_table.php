<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('credit_id')->constrained( table: 'credit', indexName: 'crd_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('invoice_id');
            $table->integer('customer_transaction_id');
            $table->double('amount', 16, 2);
            $table->date('date')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_details');
    }
};
