<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('contact_no')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->string('drug_licence_no')->nullable()->change();
            // $table->dropColumn('description');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->string('contact_no')->nullable()->change();
            $table->string('gst_no')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->string('drug_licence_no')->nullable()->change();
            $table->string('website')->nullable()->change();
            // $table->dropColumn('description');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->string('item_code')->nullable()->change();
            $table->string('hsn_code')->nullable()->change();
            $table->string('part_no')->nullable()->change();
            $table->string('stock')->nullable()->change();
            // $table->dropColumn('unique_code');
            $table->dropColumn('rating');
            $table->double('price', 16, 2)->change();
            $table->integer('min_qty')->nullable()->after('stock');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
