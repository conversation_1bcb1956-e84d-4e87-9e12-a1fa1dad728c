<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_bankinfo', function (Blueprint $table) {
            $table->string('ifsc_code')->nullable()->after('account_number');
            $table->enum('amount_type', ['cr', 'dr'])->after('balance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_bankinfo', function (Blueprint $table) {
            //
        });
    }
};
