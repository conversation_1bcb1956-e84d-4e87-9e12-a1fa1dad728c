<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrder extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    const DOCUMENT_TYPE = 'purchase_orders';

    const STATUS_OPEN = 'Open';
    const STATUS_COMPLETE = 'Completed';
    const STATUS_PARTIALLY = 'Partially Received';

    protected $table = 'purchase_orders';

    protected static $logName = 'Company-PO';

    public function getLogDescription(string $event): string
    {
        $companyName = $this->company ? $this->company->name : 'Unknown Company';
        return "<strong>{$companyName}'s</strong> Purchase Order <strong>{$this->po_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'category',
        'organization_id',
        'company_id',
        'sales_user_id',
        'type',
        'cgst',
        'sgst',
        'igst',
        'total_gst',
        'overall_discount',
        'total_discount',
        'sub_total',
        'total_amount',
        'po_number',
        'date',
        'sales_order_no',
        'sales_order_date',
        'status',
        'note',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'category',
        'organization_id',
        'company_id',
        'sales_user_id',
        'type',
        'cgst',
        'sgst',
        'igst',
        'total_gst',
        'overall_discount',
        'total_discount',
        'sub_total',
        'total_amount',
        'po_number',
        'date',
        'sales_order_no',
        'sales_order_date',
        'status',
        'note',
        'created_by',
        'updated_by'
    ];

    public function company(){
        return $this->belongsTo(Company::class,'company_id','id');
    }

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function users(){
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function purchaseOrderDetail()
    {
        return $this->hasMany(PurchaseOrderDetail::class);
    }

    public function purchaseOrderDetailForReceive()
    {
        return $this->hasMany(PurchaseOrderDetail::class)->whereRaw('(qty - receive_qty) > 0');;
    }

    public function purchaseOrderReceives()
    {
        return $this->hasMany(PurchaseOrderReceives::class);
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'purchase_orders');
    }


    public function credit(){
        return $this->hasMany(CompanyCredit::class,'company_id', 'company_id')->where('unused_amount', '>', 0);
    }

    public static function boot()
    {
        parent::boot();

        static::updating(function ($model) {
            if ($model->isDirty()) {
                /*$event = $model->isDirty('status') ? 'Open' : 'updated';
                self::handleLogEntry($model, $event);*/
                self::handleLogEntry($model, 'updated');
            }
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Company PO: " . $model->po_number;
        foreach ($model->purchaseOrderDetail as $detail){
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
        //self::addCustomLogEntry($model, $event, $logName);
    }
}
