import{r as j,j as P,l as $,o as l,c,a,u as r,w as u,F as p,Z as B,b as s,d as N,e as M,f as g,i as v,g as A,T as E,t as x}from"./app-2ecbacfc.js";import{_ as F,a as O}from"./AdminLayout-42d5bb92.js";import{_ as R}from"./InputError-aa79d601.js";import{_ as b}from"./InputLabel-f62a278f.js";import{P as T}from"./PrimaryButton-0d76f021.js";import{_ as U}from"./TextInput-73b24943.js";import{_ as y}from"./Checkbox-3bb6de23.js";import{u as D}from"./index-35fd125b.js";import"./_plugin-vue_export-helper-c27b6911.js";const L={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Z=s("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Role & Permission ",-1),q=["onSubmit"],z={class:"border-b border-gray-900/10 pb-12"},G={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},H={class:"sm:col-span-12 grid grid-cols-6 gap-6"},I={class:"col-span-2"},J={class:"sm:col-span-12"},K={class:"flex justify-between items-center border px-4 py-2 bg-gray-50 rounded-lg"},Q={class:"flex items-center text-lg font-semibold leading-7 text-gray-900 space-x-2"},W=s("div",{class:"cursor-pointer"},[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})])],-1),X={class:"border border-t-0 rounded-b-lg"},Y={class:"text-sm font-semibold leading-6 text-gray-900 p-1"},ee={class:"flex justify-end p-1"},se={class:"flex mt-6 items-center justify-between"},te={class:"ml-auto flex items-center justify-end gap-x-6"},oe=s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel ",-1),ie={key:0,class:"text-sm text-gray-600"},fe={__name:"Add",props:["data"],setup(f){const m=f,e=D("post","/roles",{name:"",permissions:[]}),k=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});j({});const w=(n,t)=>{if(n)e.permissions.push(t);else{const o=e.permissions.indexOf(t);o!==-1&&e.permissions.splice(o,1)}h()},S=(n,t)=>{const o=n.target.checked;t.forEach(i=>{if(o&&!e.permissions.includes(i.id))e.permissions.push(i.id);else if(!o&&e.permissions.includes(i.id)){const d=e.permissions.indexOf(i.id);d>-1&&e.permissions.splice(d,1)}})},_=P(()=>{const n={};return Object.keys(m.data).forEach(t=>{const o=m.data[t].every(i=>e.permissions.includes(i.id));n[t]=o}),n}),h=n=>{for(const t in m.data){const o=m.data[t].every(i=>e.permissions.includes(i.id));_.value[t]=o}};return $(e.permissions,(n,t)=>{for(const o in m.data)h()},{deep:!0}),(n,t)=>(l(),c(p,null,[a(r(B),{title:"Role & Permission"}),a(F,null,{default:u(()=>[s("div",L,[Z,s("form",{onSubmit:N(k,["prevent"]),class:""},[s("div",z,[s("div",G,[s("div",H,[s("div",I,[a(b,{for:"name",value:"Role Name"}),a(U,{id:"name",type:"text",modelValue:r(e).name,"onUpdate:modelValue":t[0]||(t[0]=o=>r(e).name=o),onChange:t[1]||(t[1]=o=>r(e).validate("name"))},null,8,["modelValue"]),r(e).invalid("name")?(l(),M(R,{key:0,message:r(e).errors.name},null,8,["message"])):g("",!0)])]),s("div",J,[a(b,{for:"name",value:"Select Permission"})]),(l(!0),c(p,null,v(f.data,(o,i)=>(l(),c("div",{class:"sm:col-span-3",key:i},[s("div",K,[s("h3",Q,[a(y,{checked:_.value[i],onChange:d=>S(d,o)},null,8,["checked","onChange"]),s("span",null,x(i),1)]),W]),s("div",X,[(l(!0),c(p,null,v(o,(d,C)=>(l(),c("div",{key:C,class:"flex justify-between items-center px-4 py-1 border-b last:border-b-0"},[s("div",Y,x(d.name),1),s("div",ee,[a(y,{checked:r(e).permissions.includes(d.id),"onUpdate:checked":V=>w(V,d.id),name:"permissions"},null,8,["checked","onUpdate:checked"])])]))),128))])]))),128))])]),s("div",se,[s("div",te,[a(O,{href:n.route("roles.index")},{svg:u(()=>[oe]),_:1},8,["href"]),a(T,{disabled:r(e).processing},{default:u(()=>[A("Save")]),_:1},8,["disabled"]),a(E,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[r(e).recentlySuccessful?(l(),c("p",ie,"Saved.")):g("",!0)]),_:1})])])],40,q)])]),_:1})],64))}};export{fe as default};
