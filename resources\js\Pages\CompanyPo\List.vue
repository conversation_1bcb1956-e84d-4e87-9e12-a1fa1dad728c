<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'types', 'typeId', 'permissions', 'organization', 'paymentType', 'bankinfo', 'companies', 'organizationId', 'companyId', 'categoryId', 'category', 'salesuser', 'salesUserId', 'pagetypes']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('companypo.index',{
    organization_id: props.organizationId,
    company_id: props.companyId,
    sales_user_id: props.salesUserId,
    category: props.categoryId,
});

const filePath = usePage().props.filepath.view;
// const form = useForm({});
const companyPOData = ref([]);
const modalVisible = ref(false);
const selectedPOId = ref(null);

const columns = [
    { field: 'po_number',             label: 'PO NUMBER',        sortable: true },
    { field: 'sales_order_no',        label: 'SO NUMBER',        sortable: true },
    { field: 'type',                  label: 'TYPE',             sortable: true },
    { field: 'category',              label: 'CATEGORY',         sortable: true },
    { field: 'company.name',          label: 'COMPANY',          sortable: true },
    { field: 'date',                  label: 'DATE',             sortable: true },
    { field: 'total_amount',          label: 'AMOUNT (₹)',       sortable: true },
    { field: 'status',                label: 'STATUS',           sortable: true },
    { field: 'action',                label: 'ACTION',           sortable: false },
];

const openDeleteModal = (userId) => {
  selectedPOId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const generatePdfModal = ref(false);
const modalMaxWidth = ref('custom');

const openPreviewModal = (id) => {
  const companypo = props.data.data.find(companypo  => companypo.id === id);
  companyPOData.value = companypo
//   companyLogo.value = companypo
//   companySignature.value = companypo
  generatePdfModal.value = true;
};

const closeGeneratePdf = () => {
    generatePdfModal.value = false;
};

const deletePO = () => {
    form.delete(route('companypo.destroy',{id:selectedPOId.value}), {
        onSuccess: () => closeModal()
    });
};

const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const categoryId = ref(props.categoryId);
const salesUserId = ref(props.salesUserId);
const type = ref(props.typeId);
const searchValue = ref('');

watch([organizationId, companyId, salesUserId, categoryId ], () => {
    updateParams({
        organization_id: organizationId.value,
        company_id: companyId.value,
        sales_user_id: salesUserId.value,
        category: categoryId.value
    });
});

const handleSearchChange = (value, organizationId, companyId, categoryId, salesUserId, type) => {
    searchValue.value = value;
    form.get(route('companypo.index',{search:value,  organization_id: organizationId, company_id: companyId, category: categoryId, sales_user_id: salesUserId, type: type}),  {
        preserveState: true,
        // replace: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, companyId.value, categoryId.value, salesUserId.value, type.value);
};

const setCompany = (id, name) => {
    companyId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, categoryId.value, salesUserId.value, type.value);
};

const setCategory = (id, name) => {
    categoryId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, categoryId.value, salesUserId.value, type.value);
};

const setType = (id, name) => {
    type.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, categoryId.value, salesUserId.value, type.value);
};

const setSalesUser = (id, name) => {
    salesUserId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, categoryId.value, salesUserId.value, type.value);
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Open':
            return 'bg-blue-100';
        case 'Partially Received':
            return 'bg-yellow-100';
        case 'Completed':
            return 'bg-green-100';
        default:
            return 'bg-gray-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Open':
            return 'text-blue-600';
        case 'Partially Received':
            return 'text-yellow-600';
        case 'Completed':
            return 'text-green-600';
        default:
            return 'text-gray-600';
    }
};

const generatePDF = (filename) => {
    const sanitizedFilename = filename.replace(/\s+/g, '_').replace(/[<>:"./\\|?*]+/g, '');
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');
      html2canvas(pdfContent, { scale: 2 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 196; // PDF page width (A4)
        const pageHeight = 297; // PDF page height (A4)
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let position = 0;
        if (imgHeight <= pageHeight) {
            doc.addImage(imgData, 'PNG', 7, 10, imgWidth, imgHeight);
        } else {
            // Split image over multiple pages
            while (position < imgHeight) {
                doc.addImage(imgData, 'PNG', 7, -position +10 , imgWidth, imgHeight);
                position += pageHeight - 20; // 20px margin at the top
                if (position < imgHeight) {
                    doc.addPage();
                }
            }
        }
        doc.save(sanitizedFilename);
    });
};

const getRowHeight = (description) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = description;
    tempDiv.style.width = '1000px';
    document.body.appendChild(tempDiv);
    const height = tempDiv.offsetHeight;
    document.body.removeChild(tempDiv);
    return height;
};

const bankinfo = ref([]);
const makePaymentModal = ref(false);
const paymentReceiveWidth = ref('custom2');

const closeMakePaymentModal = () => {
    makePaymentModal.value = false;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const paymentform = {
    organization_id: '',
    company_id: '',
    org_bank_id: '',
    purchase_order_id: '',
    po_number: '',
    payment_type:'',
    amount: '',
    check_number: '',
    date: '',
    note: ''
};

const openMakePaymentModal = (id, organization_id) => {
    const invoice = props.data.data.find(invoice  => invoice.id === id);
    companyPOData.value = invoice
    makePaymentModal.value = true;
    paymentform.company_id = companyPOData.value.company_id;
    paymentform.purchase_order_id = companyPOData.value.id;
    paymentform.po_number = companyPOData.value.po_number;
    paymentform.organization_id = companyPOData.value.organization_id;
    const bank = props.bankinfo.filter(bank  => bank.organization_id === organization_id);
    bankinfo.value = bank;
};


const setPaymentType = (id, name) => {
    paymentform.payment_type = id;
    form.errors[`data.payment_type`] = null;
};

const setBankInfo = (id, name) => {
    paymentform.org_bank_id = id;
    form.errors[`data.org_bank_id`] = null;
};

const acceptPayment = () => {
    form.post(route('companypo.paymentpay',{ data : paymentform}), {
        onSuccess: () => {
            form.reset();
            makePaymentModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const page = ref('portrait');

const setPageType = (id, name) => {
    page.value = id;
};

const downloadPDF = (id, page) => {
    window.open(`/companypo/download/${id}/${page}`, '_blank');
};

</script>

<template>
    <Head title="Company PO"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Company PO</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value,  organizationId, companyId, categoryId, salesUserId, type)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                                <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateCompanypo">
                        <div class="flex justify-end">
                            <CreateButton :href="route('companypo.create')">
                                Create PO
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Organization Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Company Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="companies"
                            v-model="companyId"
                            @onchange="setCompany"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Person Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="salesuser"
                            v-model="salesUserId"
                            @onchange="setSalesUser"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Category" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="category"
                            v-model="categoryId"
                            @onchange="setCategory"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Purchase Type" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="types"
                            v-model="type"
                            @onchange="setType"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData.po_number }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                   {{ poData.sales_order_no ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5">
                                    {{ poData.type }}
                                </td>
                                <td class="px-4 py-2.5">
                                    {{ poData.category }}
                                </td>
                                <td scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ poData.company.name }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatDate(poData.date) }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatAmount(poData.total_amount)}}
                                </td>
                                <td class="flex flex-1 items-center px-4 py-2.5">
                                    <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                        <span class="text-sm font-semibold whitespace-nowrap" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                    </div>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink v-if="poData.status != 'Completed' && permissions.canEditCompanypo" :href="route('companypo.edit',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button  v-if="poData.status == 'Open' && permissions.canDeleteCompanypo" type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                                <ActionLink v-if="poData.status != 'Completed' && permissions.canReceiveCompanypo" :href="route('companypo.receivepo',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Receive PO
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink v-if="permissions.canViewCompanypo"  :href="route('companypo.viewpo',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View PO
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button"  @click="openPreviewModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Generate PDF
                                                    </span>
                                                </button>
                                                <!-- <button type="button" v-if="permissions.canPaymentCompanypo"  @click="openMakePaymentModal(poData.id, poData.organization_id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Make Payment
                                                    </span>
                                                </button> -->
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this Po?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deletePO"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="generatePdfModal" @close="closeGeneratePdf" :maxWidth="modalMaxWidth">
              <div class="p-6">
                <div id="pdf-content">
                    <div class="container1">
                        <div v-if="companyPOData.organization.id == '3'" class="header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                            <img class="w-20 h-20" :src="filePath + companyPOData.organization.logo" alt="logo">
                            <p><strong style="font-size: 20px;">Purchase Order</strong></p>
                            <h1><div style="width: 120px;">
                            </div></h1>
                        </div>
                        <div v-if="companyPOData.organization.id == '1' || companyPOData.organization.id == '2'" class="header" style="align-items: start; justify-content: center; text-align: center;">
                            <img class="w-full h-10" :src="filePath + companyPOData.organization.logo" alt="logo">
                            <div style="align-items: center; justify-content: space-between; margin-bottom: 5px;">
                                <p style="font-size: 20px;"><strong>Purchase Order</strong></p>
                            </div>
                        </div>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                                <p><strong style="font-size: 14px; margin-top: 10px">{{ companyPOData.organization.name }}</strong> </p>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <p>{{ companyPOData.organization.address_line_1}}</p>
                                <p>{{ companyPOData.organization.address_line_2}}</p>
                                <p>{{ companyPOData.organization.pincode}} , {{ companyPOData.organization.city}}</p>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <div v-if="companyPOData?.users" style="display:flex;"><p style="width: 60px;"><strong>Originator</strong></p><p>: {{ companyPOData?.users?.first_name }} {{ companyPOData?.users?.last_name }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ companyPOData.organization.contact_no }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Email</strong></p><p>: {{ companyPOData.organization.email }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ companyPOData.organization.gst_no }}</p></div>
                            </div>
                            <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 320px;">
                                <div style="display:flex;"><p style="width: 70px;"><strong>PO Number</strong></p><p>: {{ companyPOData.po_number }}</p></div>
                                <div style="display:flex;"><p style="width: 70px;"><strong>PO Date</strong></p><p>: {{ formatDate(companyPOData.date) }}</p></div>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <p><strong style="font-size: 14px; margin-top: 10px">{{ companyPOData.company.name}} </strong></p>
                                <p>{{ companyPOData.company.address}}</p>
                                <!-- <p>{{ companyPOData.company.city}}</p> -->
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ companyPOData.company.contact_no  }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Email</strong></p><p>: {{ companyPOData.company.email }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ companyPOData.company.gst_no }}</p></div>
                            </div>
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>SN</th>
                                    <th>Code</th>
                                    <th>Product Description</th>
                                    <th>HSN</th>
                                    <th>Qty</th>
                                    <th>Price (₹)</th>
                                    <th>Total Price (₹)</th>
                                    <th v-if="companyPOData.company.gst_type =='IGST' && companyPOData.company.company_type != 'Retail'">IGST (%)</th>
                                    <th v-if="companyPOData.company.gst_type =='CGST/SGST' && companyPOData.company.company_type != 'Retail'">CGST (%)</th>
                                    <th v-if="companyPOData.company.gst_type =='CGST/SGST' && companyPOData.company.company_type != 'Retail'">SGST (%)</th>
                                    <th>Total Gst</th>
                                    <th>Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(poData, index) in  companyPOData.purchase_order_detail" :key="poData.id" class="">
                                    <td>{{ index + 1 }}</td>
                                    <td>{{ poData.product.item_code }}</td>
                                    <td>{{ poData.product.name }} {{ poData.description }}</td>
                                    <td>{{ poData.product.hsn_code }}</td>
                                    <td>{{ poData.pkg_of_qty != null ? poData.pkg_of_qty : poData.qty }}</td>
                                    <td>{{ formatAmount(poData.price)}}</td>
                                    <td>{{ formatAmount(poData.total_price)}}</td>
                                    <td v-if="companyPOData.company.gst_type =='IGST' && companyPOData.company.company_type != 'Retail'">{{ formatAmount(poData.gst) }}</td>
                                    <td v-if="companyPOData.company.gst_type =='CGST/SGST' && companyPOData.company.company_type != 'Retail'">{{ formatAmount(poData.gst/2) }}</td>
                                    <td v-if="companyPOData.company.gst_type =='CGST/SGST' && companyPOData.company.company_type != 'Retail'">{{ formatAmount(poData.gst/2) }}</td>
                                    <td>{{ formatAmount(poData.total_gst_amount)}}</td>
                                    <td>{{ formatAmount(poData.total_amount)}}</td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 260px;">
                                <p>{{ companyPOData.note }}</p>
                            </div>
                            <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                                <div style="display:flex;"><p style="width: 105px;"><strong>Sub Total (₹)</strong></p><p>: {{  formatAmount(companyPOData.sub_total) }}</p></div>
                                <div style="display:flex;"><p style="width: 105px;"><strong>Total Discount (₹)</strong></p><p>: {{  formatAmount(companyPOData.total_discount) }}</p></div>
                                <div style="display:flex;"><p style="width: 105px;"><strong>Total GST (₹)</strong></p><p>: {{ formatAmount(companyPOData.total_gst) }}</p></div>
                                <div style="display:flex;"><p style="width: 105px;"><strong>Total Amount (₹)</strong></p><p>: {{ formatAmount(companyPOData.total_amount) }}</p></div>
                            </div>
                        </div>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 400;">
                            </div>
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <p><strong>FOR,</strong></p>
                            <p><strong>{{ companyPOData.organization.name }}</strong></p>
                                <img class="h-28" :src="filePath + companyPOData.organization.signature" alt="logo">
                            </div>
                        </div>
                    </div>
                </div>
                <div id="footer" style="padding: 8px 0px;">
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <div class="flex flex-col justify-end space-y-6">
                        <div class="flex items-center space-x-2">
                            <InputLabel for="customer_id" value="Page Type :" />
                            <SearchableDropdown :options="pagetypes"
                            v-model="page"
                            @onchange="setPageType"
                        />
                        </div>
                            <div class="flex justify-end">
                                <SecondaryButton @click="closeGeneratePdf"> Cancel </SecondaryButton>
                                <div class="w-36">
                                    <PrimaryButton
                                        class="ml-3 w-20"
                                        @click="downloadPDF(companyPOData.id, page)"
                                    >
                                    Generate Pdf
                                    </PrimaryButton>
                                </div>
                            </div>
                    </div>
                </div>
                <!-- <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeGeneratePdf"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="generatePDF(companyPOData.company.name)"
                    >
                        GENERATE PDF
                    </PrimaryButton> -->
                    <!-- <PrimaryButton
                        class="ml-3 w-20"
                        @click="downloadPDF(companyPOData.id)"
                    > -->
                    <!-- Generate Pdf
                    </PrimaryButton>
                    </div>
                </div> -->
            </div>
        </Modal>
        <Modal :show="makePaymentModal" @close="closeMakePaymentModal" :maxWidth="paymentReceiveWidth">
             <div class="p-6">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Make Payment</h2>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Payment Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="paymentType"
                                    v-model="paymentform.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors[`data.payment_type`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    :numeric="true"
                                      @change="clearError('data.amount')"
                                     :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                                    v-model="paymentform.amount"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="check_number" value="Cheque Number" />
                                <TextInput
                                    id="check_number"
                                    type="text"
                                    v-model="paymentform.check_number"
                                    :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="paymentform.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    @change="clearError('data.date')"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="org_bank_id" value="Bank" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="bankinfo"
                                    v-model="paymentform.org_bank_id"
                                    @onchange="setBankInfo"
                                     :class="{ 'error rounded-md': form.errors[`data.org_bank_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    :rows="3"
                                    v-model="paymentform.note"
                                />
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeMakePaymentModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="acceptPayment"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
    .error {
        border: 1px solid red;
    }

    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }

    .container1 p {
        font-size: 12px;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    #pdf-content td {
            border-bottom : 0.1px solid rgb(55 65 81)  !important;
            border-right: 0.1px solid rgb(55 65 81)  !important;
            padding: 4px 4px !important;
            text-align: left;
            font-size: 11px;
        }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding:  6px 4px  !important;
        text-align: left;
        font-size: 12px;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    #pdf-content .quotationbank p{
        font-size: 12px;
    }

    #footer  {
        font-size: 12px;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: start;
    }

</style>


