<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained( table: 'customers', indexName: 'inc_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('entity_id')->nullable();
            $table->string('entity_type')->nullable();
            $table->string('invoice_no');
            $table->date('date');
            $table->enum('status', ['Paid', 'Partially Paid', 'Unpaid']);
            $table->double('sub_total', 16, 2);
            $table->double('total_gst', 16, 2);
            $table->double('total_amount', 16, 2);
            $table->double('paid_amount', 16, 2)->nullable()->defalt(0);
            $table->double('pending_amount', 16, 2)->nullable()->defalt(0);
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice');
    }
};
