import{r as h,K as u,o as d,c as m,a as c,u as p,w as _,F as x,Z as y,b as t,d as g,g as b,t as e,i as w,e as v}from"./app-8a557454.js";import{_ as j}from"./AdminLayout-301d54ca.js";import{_ as k}from"./CreateButton-d5560e12.js";import{_ as D}from"./InputLabel-07f3a6e8.js";import{C as N}from"./CheckboxWithLabel-f345f79b.js";/* empty css                                                                          */import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top h-screen"},S={class:"sm:flex sm:items-center"},B=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard Detail")],-1),V={class:"flex items-center space-x-4"},J=t("div",null,null,-1),E={class:"flex justify-end w-20"},L={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},U={class:"inline-flex items-start space-x-6 justify-start w-full"},$={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},F={class:"inline-flex items-center justify-start w-full space-x-2"},M=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Hospital Name:",-1),q={class:"text-sm leading-6 text-gray-700"},A={class:"inline-flex items-center justify-start w-full space-x-2"},H=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1),K={class:"text-sm leading-6 text-gray-700"},P={class:"inline-flex items-center justify-start w-full space-x-2"},T=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Address:",-1),W={class:"text-sm leading-6 text-gray-700"},Z={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},z={class:"inline-flex items-center justify-start w-full space-x-2"},G=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Jobcard Number:",-1),I={class:"text-sm leading-6 text-gray-700"},O={class:"inline-flex items-center justify-start w-full space-x-2"},Q=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Date:",-1),R={class:"text-sm leading-6 text-gray-700"},X={class:"inline-flex items-center justify-start w-full space-x-2"},Y=t("p",{class:"text-sm font-semibold text-gray-900"},"Engineer Name:",-1),tt={class:"text-sm leading-6 text-gray-700"},st={class:"inline-flex items-center justify-start w-full space-x-2"},et=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Job Status:",-1),at={class:"text-sm leading-6 text-gray-700"},it={class:"mt-8 overflow-x-auto sm:rounded-lg"},ot={class:"shadow sm:rounded-lg"},lt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ct=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Equipment"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Model"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Serial No")])],-1),dt={class:"divide-y divide-gray-300 bg-white"},nt={class:"whitespace-nowrap pr-4 py-3 text-sm text-gray-900"},rt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},mt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},_t={class:"mt-6 space-y-1"},xt={class:"inline-flex items-center justify-start w-full space-x-2"},ft=t("p",{class:"text-sm font-semibold text-gray-900"},"Problem Description:",-1),ht={class:"text-sm leading-6 text-gray-700"},ut={class:"w-1/2"},pt={class:"grid sm:grid-cols-6 relative"},yt={class:"inline-flex items-center justify-start w-full space-x-2"},gt=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Note:",-1),bt={class:"text-sm leading-6 text-gray-700"},wt={class:"inline-flex items-center justify-start w-full space-x-2"},vt=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Date:",-1),jt={class:"text-sm leading-6 text-gray-700"},Jt={__name:"View",props:["data","checklist"],setup(s){const n=h([]),f=u().props.data[0];n.value=f.job_card_checks.map(a=>a.job_card_checklist_id);const r=a=>{const o=new Date(a),l={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",l)};return(a,o)=>(d(),m(x,null,[c(p(y),{title:"Jobcard"}),c(j,null,{default:_(()=>{var l;return[t("div",C,[t("form",{onSubmit:o[0]||(o[0]=g((...i)=>a.submit&&a.submit(...i),["prevent"])),class:""},[t("div",S,[B,t("div",V,[J,t("div",E,[c(k,{href:a.route("jobcard.index")},{default:_(()=>[b(" Back ")]),_:1},8,["href"])])])]),t("div",L,[t("div",U,[t("div",$,[t("div",F,[M,t("p",q,e(s.data[0].hospital_name??"-"),1)]),t("div",A,[H,t("p",K,e(s.data[0].contact_no??"-"),1)]),t("div",P,[T,t("p",W,e(s.data[0].address??"-"),1)])]),t("div",Z,[t("div",z,[G,t("p",I,e(s.data[0].job_card_number??"-"),1)]),t("div",O,[Q,t("p",R,e(r(s.data[0].date)??"-"),1)]),t("div",X,[Y,t("p",tt,e(s.data[0].users.first_name??"-")+" "+e(s.data[0].users.last_name??"-"),1)]),t("div",st,[et,t("p",at,e(s.data[0].job_status??"-"),1)])])])]),t("div",it,[t("div",ot,[t("table",lt,[ct,t("tbody",dt,[t("tr",null,[t("td",nt,e(s.data[0].product_name??"-"),1),t("td",rt,e(s.data[0].product_code??"-"),1),t("td",mt,e(s.data[0].serial_no??"-"),1)])])]),t("div",_t,[t("div",xt,[ft,t("p",ht,e(s.data[0].problem_description??"-"),1)]),t("div",ut,[c(D,{for:"engineer_id",value:"Checklist :"}),t("div",pt,[(d(!0),m(x,null,w(s.checklist,i=>(d(),v(N,{key:i.id,checked:n.value,value:i.id,label:i.type,"onUpdate:checked":a.updateChecked},null,8,["checked","value","label","onUpdate:checked"]))),128))])]),t("div",yt,[gt,t("p",bt,e(s.data[0].close_note??"-"),1)]),t("div",wt,[vt,t("p",jt,e((l=s.data[0])!=null&&l.close_date?r(s.data[0].close_date):"-"),1)])])])])],32)])]}),_:1})],64))}};export{Jt as default};
