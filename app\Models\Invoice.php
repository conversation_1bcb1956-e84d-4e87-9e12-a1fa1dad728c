<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\InvoiceDetail;
use Illuminate\Support\Facades\Log;


class Invoice extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'invoice';

    protected static $logName = 'Invoice';

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customers?->customer_name ?? 'Unknown Customer';
        return "<strong>{$customerName}'s</strong> Invoice <strong>{$this->invoice_no}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'category',
        'sales_user_id',
        'invoice_type',
        'organization_id',
        'customer_id',
        'entity_id',
        'entity_type',
        'challan_ids',
        'order_id',
        'invoice_no',
        'date',
        'status',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'paid_amount',
        'pending_amount',
        'note',
        'transport',
        'dispatch',
        'patient_name',
        'po_date',
        'customer_po_date',
        'customer_po_number',
        'due_days',
        'cr_dr_note',
        'created_by',
        'updated_by',
        'purchase_order_id',
        'purchase_order_receive_id'
    ];
    protected $fillable = [
        'category',
        'sales_user_id',
        'invoice_type',
        'organization_id',
        'customer_id',
        'entity_id',
        'entity_type',
        'challan_ids',
        'order_id',
        'invoice_no',
        'date',
        'status',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'paid_amount',
        'pending_amount',
        'note',
        'transport',
        'dispatch',
        'patient_name',
        'po_date',
        'customer_po_date',
        'customer_po_number',
        'due_days',
        'cr_dr_note',
        'created_by',
        'updated_by',
        'purchase_order_id',
        'purchase_order_receive_id'
    ];

    public function customers(){
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }
    
    public function credit(){
        return $this->hasMany(CustomerCredit::class,'customer_id','customer_id')->where('unused_amount', '>', 0);
    }

    public function users(){
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function invoiceDetail()
    {
        return $this->hasMany(InvoiceDetail::class)->where('is_receive', NULL);
    }

    public function invoiceDetails()
    {
        return $this->hasMany(InvoiceDetail::class, 'invoice_id', 'id')->where('is_receive', NULL);
    }

    public function paymentReceive()
    {
        return $this->hasMany(PaymentReceive::class);
    }

    public function challan(){
        return $this->belongsTo(Challan::class,'entity_id','id');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'invoice');
    }


}
