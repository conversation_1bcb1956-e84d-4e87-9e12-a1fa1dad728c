<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('send_emails', function (Blueprint $table) {
            $table->id();
            $table->string('to')->nullable(); // Recipient
            $table->string('subject')->nullable(); // Subject
            $table->string('from')->default('Academic Email'); // Sender
            $table->string('reply_to')->nullable(); // Reply-To
            $table->string('template')->nullable(); // Template used
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('send_emails');
    }
};
