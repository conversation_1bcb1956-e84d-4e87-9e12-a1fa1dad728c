import{_ as o}from"./AdminLayout-f002e683.js";import i from"./DeleteUserForm-5ea2a616.js";import m from"./UpdatePasswordForm-9ca8fb09.js";import r from"./UpdateProfileInformationForm-b120a0d0.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-497d70e1.js";import"./DangerButton-fc55f8d0.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-9708b76b.js";import"./InputLabel-5f63a3d9.js";import"./Modal-e8ed59aa.js";/* empty css                                                              */import"./SecondaryButton-98872fc5.js";import"./TextInput-affa926c.js";import"./PrimaryButton-8958b93e.js";import"./TextArea-a8869e21.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
