<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_contract', function (Blueprint $table) {
            $table->id();
            $table->string('hospital_name');
            $table->string('address')->nullable();
            $table->string('city');
            $table->bigInteger('contact_no')->nullable();
            $table->date('contract_start_date')->nullable();
            $table->date('contract_end_date')->nullable();
            $table->enum('maintenance_type', ['AMC', 'CMC']);
            $table->string('time_period');
            $table->string('product_name');
            $table->string('product_code');
            $table->string('qty');
            $table->string('company_name');
            $table->string('invoice_number');
            $table->string('serial_no');
            $table->date('pm_date_1')->nullable();
            $table->date('pm_date_2')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_contract');
    }
};
