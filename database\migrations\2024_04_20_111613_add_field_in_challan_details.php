<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challan', function (Blueprint $table) {
            $table->double('igst', 16, 2)->nullable()->after('status');
            $table->double('sgst', 16, 2)->nullable()->after('status');
            $table->double('cgst', 16, 2)->nullable()->after('status');
            $table->double('sub_total', 16, 2)->nullable()->after('status');
            $table->double('total_discount', 16, 2)->nullable()->after('status');
            $table->double('total_amount', 16, 2)->nullable()->after('status');
        });

        Schema::table('challan_detail', function (Blueprint $table) {
            $table->integer('qty')->nullable()->after('serial_number_id');
            $table->double('price', 16, 2)->nullable()->after('serial_number_id');
            $table->double('total_price', 16, 2)->nullable()->after('serial_number_id');
            $table->integer('gst')->nullable()->after('serial_number_id');
            $table->double('gst_amount', 16, 2)->nullable()->after('serial_number_id');
            $table->double('total_gst_amount', 16, 2)->nullable()->after('serial_number_id');
            $table->double('total_amount', 16, 2)->nullable()->after('serial_number_id');
            $table->integer('discount')->nullable()->after('serial_number_id');
            $table->double('discount_amount', 16, 2)->nullable()->after('serial_number_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('challan_details', function (Blueprint $table) {
            //
        });
    }
};
