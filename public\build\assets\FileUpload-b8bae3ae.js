import{r as c,o as s,c as l,b as n,f as r,t as f}from"./app-497d70e1.js";const m={class:"col-span-full"},p={class:"mt-2 flex items-center gap-x-3"},g={key:0,class:"h-16 w-16 rounded-full"},h=["src"],_={key:1,class:"h-16 w-16 rounded-full 11"},v=["src"],x=["id","name"],y={class:"flex flex-col gap-x-2"},k={key:0,class:"text-sm text-gray-900"},b={__name:"FileUpload",props:{inputId:String,inputName:String,fileUrl:String},emits:["file"],setup(i,{emit:d}){const t=c(""),o=c(""),u=a=>{const e=a.target.files[0];t.value=e,o.value=URL.createObjectURL(e),d("file",e)};return(a,e)=>(s(),l("div",m,[n("div",p,[t.value?(s(),l("div",g,[t.value?(s(),l("img",{key:0,class:"h-16 w-16 rounded-full",src:o.value,alt:"Selected Image"},null,8,h)):r("",!0)])):(s(),l("div",_,[n("img",{class:"h-16 w-16 rounded-full",src:i.fileUrl,alt:""},null,8,v)])),n("input",{type:"file",id:i.inputId,class:"hidden",ref:"fileInput",onChange:u,name:i.inputName},null,40,x),n("div",y,[n("label",{for:"photo",onClick:e[0]||(e[0]=w=>a.$refs.fileInput.click()),class:"rounded-md bg-white w-20 px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 cursor-pointer hover:bg-gray-50"}," Upload "),t.value?(s(),l("span",k,f(t.value.name),1)):r("",!0)])])]))}};export{b as _};
