<?php

namespace App\Http\Requests;

use App\Models\PurchaseOrderDetail;
use Illuminate\Foundation\Http\FormRequest;
use App\DTO\PurchaseOrderDetailDTO;
use Support\Contracts\HasDTO;

class CompanyPoStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'note'                              => 'nullable|string',
            'selectedProductItem.*.product_id'  => 'required|integer',
            'selectedProductItem.*.qty'         => 'required|integer|gt:0',
            'selectedProductItem.*.price'       => 'required|numeric',
            'selectedProductItem.*.total_amount'=> 'required|numeric',
            'company_id'                        => 'required|integer'
        ];

        if (empty($this->input('purchase_order_id'))) {
            $rules['organization_id'] = 'required|integer';
            $rules['category'] = 'required|string';
            $rules['type'] = 'required|string';
            $rules['sales_user_id'] = 'required|integer';
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        $purchaseOrderId = $this->input('purchase_order_id');
        if ($purchaseOrderId) {
            $validator->after(function ($validator) {
                $selectedProductItems = $this->input('selectedProductItem');

                foreach ($selectedProductItems as $key => $product) {
                    $purchaseOrderDetail = PurchaseOrderDetail::find($product['purchase_order_detail_id']);

                    if ($purchaseOrderDetail && $product['qty'] < $purchaseOrderDetail->receive_qty) {
                        $validator->errors()->add("selectedProductItem.$key.qty", __('The quantity must not be less than the receive quantity.'));
                    }
                }
            });
        }
    }

    public function DTO()
    {
        return PurchaseOrderDetailDTO::LazyFromArray($this->input());
    }

}
