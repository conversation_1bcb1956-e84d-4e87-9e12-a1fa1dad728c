<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyCredit extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'company_credit';

    protected static $logName = 'Company Credit';

    public function getLogDescription(string $event): string
    {
        $companyName = $this->company ? $this->company->name : 'Unknown Customer';

        return "<strong>{$companyName}</strong> Credit has been {$event} by";
    }

    protected static $logAttributes = [
        'payment_paid_id',
        'organization_id',
        'company_id',
        'amount',
        'unused_amount',
        'date',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'payment_paid_id',
        'organization_id',
        'company_id',
        'amount',
        'unused_amount',
        'date',
        'created_by',
        'updated_by'
    ];

    public function paymentpaid(){
        return $this->belongsTo(PaymentPaid::class,'payment_paid_id','id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function creditDetail()
    {
        return $this->hasMany(CompanyCreditDetails::class,'company_credit_id','id');
    }
}
