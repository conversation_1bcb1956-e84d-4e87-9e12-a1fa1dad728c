<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_details', function (Blueprint $table) {
            $table->bigInteger('serial_number_id')->unsigned()->nullable()->after('product_id');
            $table->foreign('serial_number_id')->references('id')->on('serial_numbers')
                  ->onDelete('cascade')->onUpdate('no action');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_details', function (Blueprint $table) {
            //
        });
    }
};
