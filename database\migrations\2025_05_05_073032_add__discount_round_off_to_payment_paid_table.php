<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_paid', function (Blueprint $table) {
            $table->double('discount_amount', 16, 2)->default(0)->after('amount');
            $table->double('round_off', 16, 2)->default(0)->after('discount_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_paid', function (Blueprint $table) {
            $table->dropColumn(['discount_amount', 'round_off']);
        });
    }
};
