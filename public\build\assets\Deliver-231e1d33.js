import{j as w,o as n,c as m,a as r,u as c,w as x,F as p,Z as j,b as e,t as s,g as v,i as S,d as P,n as D,f as q,s as C,x as V}from"./app-8a557454.js";import{_ as N}from"./AdminLayout-301d54ca.js";import{_ as $}from"./CreateButton-d5560e12.js";import{P as k}from"./PrimaryButton-9d9bcdd8.js";import{_ as B}from"./TextInput-ab168ee4.js";import{u as I}from"./index-62ab7306.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";const a=t=>(C("data-v-1d115c6b"),t=t(),V(),t),T=["onSubmit"],E={class:"animate-top"},F={class:"sm:flex sm:items-center"},U=a(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Deliver Order")],-1)),z={class:"flex items-center space-x-4"},L={class:"text-sm font-semibold text-gray-900"},Q={class:"flex justify-end w-20"},Y={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},G={class:"inline-flex items-start space-x-6 justify-start w-full"},M={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Z={class:"inline-flex items-center justify-start w-full space-x-2"},A=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),H={class:"text-sm leading-6 text-gray-700"},J={class:"inline-flex items-center justify-start w-full space-x-2"},K=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),R={class:"text-sm leading-6 text-gray-700"},W={class:"inline-flex items-center justify-start w-full space-x-2"},X=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),ee={class:"text-sm leading-6 text-gray-700"},te={class:"inline-flex items-center justify-start w-full space-x-2"},se=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),ae={class:"text-sm leading-6 text-gray-700"},oe={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},ie={class:"inline-flex items-center justify-start w-full space-x-2"},de=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Order Number:",-1)),le={class:"text-sm leading-6 text-gray-700"},re={class:"inline-flex items-center justify-start w-full space-x-2"},ce=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Order Date:",-1)),ne={class:"text-sm leading-6 text-gray-700"},me={class:"inline-flex items-center justify-start w-full space-x-2"},_e=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Category:",-1)),ue={class:"text-sm leading-6 text-gray-700"},xe={class:"inline-flex items-center justify-start w-full space-x-2"},fe=a(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Sales Person:",-1)),ge={class:"text-sm leading-6 text-gray-700"},pe={class:"mt-6 bg-white p-4 shadow sm:p-8 sm:rounded-lg border divide-y divide-gray-300"},ve=a(()=>e("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 pb-2"},[e("div",{class:"sm:col-span-3"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")]),e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")]),e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Delivered Product")]),e("div",{class:"sm:col-span-3"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Deliver QTY")])],-1)),ye={class:"sm:col-span-3"},he={class:"text-sm leading-5 text-gray-700"},be={class:"sm:col-span-2"},we={class:"text-sm leading-5 text-gray-700"},je={class:"sm:col-span-2"},Se={class:"text-sm leading-5 text-gray-700"},Pe={class:"sm:col-span-3 mb-2"},De={key:0,class:"text-red-500 text-xs"},qe={class:"flex mt-6 items-center justify-between"},Ce={class:"ml-auto flex items-center justify-end gap-x-6"},Ve={__name:"Deliver",props:["data","order_deliver_number"],setup(t){const _=t,u=w(()=>_.data[0].pending_order_details.map(i=>({delivered_qty:"",order_details_id:i.id,order_id:_.data[0].id,order_deliver_number:_.order_deliver_number}))),o=I("post","/saveorderdeliver",{deliveredProduct:[]}),y=()=>{o.deliveredProduct=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},h=i=>{o.errors[i]=null},b=i=>{const f=new Date(i),l={year:"numeric",month:"short",day:"numeric"};return f.toLocaleDateString("en-US",l)};return(i,f)=>(n(),m(p,null,[r(c(j),{title:"Orders"}),r(N,null,{default:x(()=>[e("form",{onSubmit:P(y,["prevent"]),class:""},[e("div",E,[e("div",F,[U,e("div",z,[e("div",null,[e("p",L,s(t.data[0].organization.name),1)]),e("div",Q,[r($,{href:i.route("orders.index")},{default:x(()=>[v(" Back ")]),_:1},8,["href"])])])]),e("div",Y,[e("div",G,[e("div",M,[e("div",Z,[A,e("p",H,s(t.data[0].customers.customer_name??"-"),1)]),e("div",J,[K,e("p",R,s(t.data[0].customers.gst_no??"-"),1)]),e("div",W,[X,e("p",ee,s(t.data[0].customers.email??"-"),1)]),e("div",te,[se,e("p",ae,s(t.data[0].customers.contact_no??"-"),1)])]),e("div",oe,[e("div",ie,[de,e("p",le,s(t.data[0].order_number??"-"),1)]),e("div",re,[ce,e("p",ne,s(b(t.data[0].date)??"-"),1)]),e("div",me,[_e,e("p",ue,s(t.data[0].category??"-"),1)]),e("div",xe,[fe,e("p",ge,s(t.data[0].users.first_name??"-")+" "+s(t.data[0].users.last_name??"-"),1)])])])]),e("div",pe,[ve,(n(!0),m(p,null,S(t.data[0].pending_order_details,(l,d)=>(n(),m("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 items-center",key:d},[e("div",ye,[e("p",he,s(l.product.name),1)]),e("div",be,[e("p",we,s(l.qty),1)]),e("div",je,[e("p",Se,s(l.delivered_qty),1)]),e("div",Pe,[r(B,{id:"gst",type:"text",numeric:!0,modelValue:u.value[d].delivered_qty,"onUpdate:modelValue":g=>u.value[d].delivered_qty=g,autocomplete:"delivered_qty",onChange:g=>h("deliveredProduct."+d+".delivered_qty"),class:D({error:c(o).errors[`deliveredProduct.${d}.delivered_qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),c(o).errors[`deliveredProduct.${d}.delivered_qty`]?(n(),m("p",De,s(c(o).errors[`deliveredProduct.${d}.delivered_qty`]),1)):q("",!0)])]))),128))]),e("div",qe,[e("div",Ce,[r(k,{disabled:c(o).processing},{default:x(()=>[v("Submit")]),_:1},8,["disabled"])])])])],40,T)]),_:1})],64))}},Ee=O(Ve,[["__scopeId","data-v-1d115c6b"]]);export{Ee as default};
