<?php
namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\MailConfig;
use Illuminate\Http\Request;
use App\Http\Requests\MailConfigRequest;
use Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;

class MailConfigController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:SMPT')->only(['index','create', 'store','edit', 'update','destroy']);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');

        $query = MailConfig::query();

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('smtp_host', 'like', "%$search%")
                    ->orWhere('smtp_port', 'like', "%$search%")
                    ->orWhere('smtp_username', 'like', "%$search%")
                    ->orWhere('smtp_encryption', 'like', "%$search%");
            });
        }

        $perPage = Config::get('constants.perPage');
        $data = $query->orderBy('id', 'desc')->paginate($perPage);

        return Inertia::render('SMTP/index', compact('data'));
    }

    // Method for creating a new SMTP configuration (AddSmtpController's create method)
    public function create()
    {
        return Inertia::render('SMTP/CreateSmtp');
    }

    public function show($id)
    {
        $smtp = MailConfig::findOrFail($id);
        return Inertia::render('SMTP/ShowSmtp', ['smtp' => $smtp]);
    }

    public function store(MailConfigRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $mailConfig = MailConfig::create($data);

            DB::commit();

            return redirect()->route('mail-configs.index')->with('success', 'SMTP saved successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit($id)
    {
        $smtp = MailConfig::findOrFail($id);

        return Inertia::render('SMTP/EditSmtp', ['smtp' => $smtp]);
    }

    public function update(MailConfigRequest $request, $id)
    {
        $smtp = MailConfig::findOrFail($id);

        $smtp->update($request->validated());

        return redirect()->route('mail-configs.index')->with('success', 'SMTP updated successfully!');
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $smtp = MailConfig::findOrFail($id);
            $smtp->delete();

            DB::commit();
            return Redirect::to('/mail-configs')->with('success', 'Email Template Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/mail-configs')->with('error', $e->getMessage());
        }
    }
}
