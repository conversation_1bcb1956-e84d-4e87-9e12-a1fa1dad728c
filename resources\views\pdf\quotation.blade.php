<html>
    <head>
    <meta charset="UTF-8">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
    <title>Quotation</title>
    <style>

        @page { margin: 0px;}

        body {
            / background: #666666; /
            margin: 0;
            margin: 10px;
            border: 1px solid rgb(55 65 81) !important;
            padding: 10px;
            text-align: center;
            color: #333;
            font-family: ui-sans-serif, system-ui, sans-serif;
            /* font-family: Arial, Helvetica, 'DejaVu Sans', sans-serif; */
        }
        p {
            padding: 0;
            margin: 0;
            font-size: 12px;
            line-height: 1.6;
        }
        #pdf-content table {
            page-break-inside: auto;
            page-break-after: auto;
            border-left: 1px solid rgb(55 65 81)  !important;
            width: 100%;
            border-collapse: collapse;
            padding: 20px 0px;
        }
        #pdf-content td {
            border-bottom : 1px solid rgb(55 65 81)  !important;
            border-right: 1px solid rgb(55 65 81)  !important;
            padding: 4px 2px !important;
            text-align: left;
            font-size: 11px;
        }
        #pdf-content th {
            background-color: #bfe2f291;
            border-bottom: 1px solid rgb(55 65 81)  !important;
            border-right: 1px solid rgb(55 65 81)  !important;
            border-top: 1px solid rgb(55 65 81)  !important;
            padding: 6px 2px !important;
            text-align: left;
            white-space: nowrap !important;
            font-size: 11px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 15px 20px;
            text-align: center;
        }
        .footer  p {
            font-size: 11px !important;
        }
    </style>

    </head>
    <?php
    function formatIndianRupee($amount) {
        $num = preg_replace("/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/i", "$1,", $amount);
        return $num;
    }
    ?>
    <body>
    <div id="">
        @if($data[0]->organization->id == 3)
            <table style="width: 100%; margin-bottom: 10px;">
                <tr>
                    <td style="text-align: left; width: 80px;">
                        <img style="width: 80px; height: 80px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                    <td style="text-align: center;">
                        <b class="font-size: 20px;">Quotation</b>
                    </td>
                    <td style="width: 120px;"></td>
                </tr>
            </table>
        @endif

        @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
            <table style="width: 100%; text-align: center;">
                <tr>
                    <td style="text-align: center; margin-bottom: 20px;">
                        <img style="width:100%; height:45px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                </tr>
            </table>
            <div style="align-items: center">
                <b class="font-size: 20px;">Quotation</b>
            </div>
        @endif
    </div>

    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left; width: 300px;">
                <table>
                    <tr>
                        <td>
                            <p><strong>{{$data[0]->customers->customer_name}}</strong></p>
                            <p><strong>{{$data[0]->customers->person_name}}</strong></p>
                            <p>{{ $data[0]->customers->address }}</p>
                            <p><strong>Phone : </strong>{{ $data[0]->customers->contact_no ?? '-' }}</p>
                            <p><strong>GST : </strong>{{ $data[0]->customers->gst_no ?? '-' }}</p>
                        </td>
                    </tr>
                </table>
                <td style="text-align:center; width: 220px;">
                </td>
            </td>
            <td style="text-align:start; width: 320px;  vertical-align: top;">
                <table>
                    <tr>
                        <td>
                            <p><strong>Quotation Number : </strong>{{ $data[0]->quotation_number }}</p>
                            <p><strong>Quotation Date : </strong> {{ date('d-m-Y', strtotime($data[0]->date))}}</p>
                            <p><strong>Contact Us : </strong> {{ $data[0]->organization->contact_no}}</p>
                            <p><strong>GST: </strong> {{ $data[0]->organization->gst_no}}</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <div class="" id="pdf-content">
        <table>
            <thead>
                <tr>
                    <th>SN</th>

                    @if($data[0]->organization->id != 3)
                    <th>MODEL</th>
                    @endif
                    <th>HSN</th>
                    @if($data[0]->category == 'Sales' && $data[0]->organization->id != 3)
                        <th>IMAGE</th>
                    @endif
                    <th>DESCRIPTION</th>
                    <th>MRP</th>
                    <th>PRICE</th>
                    <th>QTY</th>
                    <th>TOTAL PRICE</th>
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <th>CGST(%)</th>
                    @endif
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <th>SGST(%)</th>
                    @endif
                    @if($data[0]->customers->gst_type =='IGST')
                        <th>IGST(%)</th>
                    @endif
                    <th>GST</th>
                    <th>DIS.</th>
                    <th>TOTAL AMOUNT</th>
                </tr>
            </thead>
            <tbody>

                @foreach ($data[0]->quotationDetail as $index => $quotationData)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    @if($data[0]->organization->id != 3)
                    <td>{{ $quotationData->product->item_code }}</td>
                    @endif
                    <td>{{ $quotationData->product->hsn_code }}</td>
                    @if($data[0]->category == 'Sales' && $data[0]->organization->id != 3)
                        <td>
                            @if($quotationData && $quotationData->product->image && file_exists(public_path('uploads/product-images/' . $quotationData->product->image)))
                                <img src="data:image/jpeg;base64,{{ base64_encode(file_get_contents(public_path('uploads/product-images/' . $quotationData->product->image))) }}" alt="Product Image" width="100">
                            @else
                               -
                            @endif
                        </td>
                    @endif
                    <td>{!! nl2br(e($quotationData->description)) !!}</td>
                    <td>
                        @if (!empty($quotationData->product->serialNumbers[0]->mrp))
                           {{ formatIndianRupee($quotationData->product->serialNumbers[0]->mrp);  }}
                        @else
                            {{ '-' }}
                        @endif
                    <td>{{ formatIndianRupee($quotationData->price) }}</td>
                    <td>{{ $quotationData->qty }}</td>
                    <td>{{ formatIndianRupee($quotationData->total_price) }}</td>
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <td>{{$quotationData->gst / 2 }}</td>
                    @endif
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <td>{{ $quotationData->gst / 2 }}</td>
                    @endif
                    @if($data[0]->customers->gst_type =='IGST')
                        <td>{{ $quotationData->gst }}</td>
                    @endif
                    <td>{{ formatIndianRupee($quotationData->total_gst_amount) }}</td>
                    <td>{{ formatIndianRupee($quotationData->discount_amount) }}</td>
                    <td>{{ formatIndianRupee($quotationData->total_amount) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div style="text-align:left;">
        <p>{{ $data[0]->note }}</p>
    </div>
    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left; width: 420px;">
                <table>
                    <tr>
                        <td>
                            <p style="font-weight: bold; margin-bottom: 10px;">TERMS & CONDITIONS</p>
                            <p><strong>Validity:</strong>{{ $data[0]->validity }}</p>
                            <p><strong>Delivery:</strong>{{ $data[0]->delivery }}</p>
                            <p><strong>Payment Terms:</strong>{{ $data[0]->payment_terms }}</p>
                            <p>Make PO in Name of <b>{{ $data[0]->organization->name }}</b></p>
                            @if($data[0]->warranty != null)
                                <p><strong>Warranty:</strong>{{ $data[0]->warranty }}</p>
                            @endif
                        </td>
                    </tr>
                </table>
                <td style="text-align:center; width: 80px;">
                </td>
            </td>
            <td style="width: 320px;">
                <table>
                    <tr>
                        <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Sub Total</strong></td>
                        <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->sub_total, 2) }}</strong></td>
                    </tr>
                    <tr>
                        <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Total Discount</strong></td>
                        <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->total_discount, 2) }}</strong></td>
                    </tr>
                    @if($data[0]->customers->gst_type == 'CGST/SGST')
                        <tr>
                            <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Total CGST</strong></td>
                            <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->cgst, 2) }}</strong></td>
                        </tr>
                        <tr>
                            <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Total SGST</strong></td>
                            <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->sgst, 2) }}</strong></td>
                        </tr>
                    @endif
                    @if($data[0]->customers->gst_type == 'IGST')
                        <tr>
                            <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Total IGST</strong></td>
                            <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->igst, 2) }}</strong></td>
                        </tr>
                    @endif
                    <tr>
                        <td style="font-size: 12px; display:flex; width: 120px; justify-items: end;"><strong>Total Amount</strong></td>
                        <td style="font-size: 12px; justify-items: start;"><strong>: {{ number_format($data[0]->total_amount, 2) }}</strong></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <table style="width:100%; align-item:start">
        <tr style="width:100%;">
            <td style="text-align:left; width: 300px;">
                <table>
                    <tr>
                        <td>
                            <p style="font-weight: bold; margin-bottom: 10px;">OUR BANK DETAILS</p>
                            @if($data[0]->organization->id == 1)
                                <p><strong>Bank Name:</strong> {{ $quotationbank['bank_name'] }}</p>
                                <p><strong>Branch Name:</strong> {{ $quotationbank['branch_name'] }}</p>
                                <p><strong>Account No:</strong> {{ $quotationbank['account_no'] }}</p>
                                <p><strong>IFSC Code:</strong> {{ $quotationbank['ifsc_code'] }}</p>
                            @elseif($data[0]->organization->id == 2)
                                <p><strong>Bank Name:</strong> {{ $quotationHealthCareBankinfo['bank_name'] }}</p>
                                <p><strong>Branch Name:</strong> {{ $quotationHealthCareBankinfo['branch_name'] }}</p>
                                <p><strong>Account No:</strong> {{ $quotationHealthCareBankinfo['account_no'] }}</p>
                                <p><strong>IFSC Code:</strong> {{ $quotationHealthCareBankinfo['ifsc_code'] }}</p>
                            @elseif($data[0]->organization->id == 3)
                                <p><strong>Bank Name:</strong> {{ $quotationNoxBank['bank_name'] }}</p>
                                <p><strong>Branch Name:</strong> {{ $quotationNoxBank['branch_name'] }}</p>
                                <p><strong>Account No:</strong> {{ $quotationNoxBank['account_no'] }}</p>
                                <p><strong>IFSC Code:</strong> {{ $quotationNoxBank['ifsc_code'] }}</p>
                            @endif
                            </td>
                    </tr>
                </table>
            </td>
            <td style="text-align:center; width: 200px;">
            </td>
            <td style="width: 320px;">
                <p style="font-weight: bold;">FOR,</p>
                <p><strong>{{ $data[0]->organization->name }}</strong></p>
                <img style="width:auto; height:112px" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->signature))) }}">
            </td>
        </tr>
    </table>
    <div class="footer">
        <table style="width:100%; align-item:start">
            <tr style="width:100%;">
                <td style="text-align:start; min-width:450px;">
                    <table>
                        <tr>
                            <td>
                                <p>{{ $data[0]->organization->address_line_1 }}</p>
                                <p>{{ $data[0]->organization->address_line_2 }}</p>
                                <p>{{ $data[0]->organization->pincode }}, {{ $data[0]->organization->city }}</p>
                            </td>
                        </tr>
                    </table>
                </td>
                <td style="text-align:center;">
                    <table>
                        <tr>
                            <td>
                                @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
                                        <p>| +91 9879168842</p>
                                    @if($data[0]->category == 'Sales')
                                        <p>| +91 7949237278</p>
                                    @elseif($data[0]->category == 'Service')
                                        <p>| +91 6358838563</p>
                                    @endif
                                @elseif($data[0]->organization->id == 3)
                                        <p>| +91 9909952382</p>
                                        <p>| +91 9879168842</p>
                                @endif
                            </td>
                        </tr>
                    </table>
                </td>
                <td style="text-align:end;">
                    <table>
                        <tr>
                            <td>
                                @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
                                        <p> | <EMAIL></p>
                                    @if($data[0]->category == 'Service')
                                        <p> | <EMAIL></p>
                                    @elseif($data[0]->category == 'Sales')
                                        <p> | <EMAIL></p>
                                    @endif
                                    @if($data[0]->organization->id == 1)
                                        <p> | www.visionmedinst.com</p>
                                    @endif
                                @elseif($data[0]->organization->id == 3)
                                    <p> | <EMAIL></p>
                                    <p> | <EMAIL></p>
                                @endif
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <div class="footer-bottom">
            <p><b>We Make The Difference</b></p>
        </div>
    </div>
</body>
</html>
