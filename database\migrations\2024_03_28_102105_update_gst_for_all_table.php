<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->double('gst', 8, 2)->change()->default(0.00);
        });

        Schema::table('challan_detail', function (Blueprint $table) {
            $table->double('gst', 8, 2)->change()->default(0.00);
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->double('gst', 8, 2)->change()->default(0.00);
        });

        Schema::table('quotation_details', function (Blueprint $table) {
            $table->double('gst', 8, 2)->change()->default(0.00);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
