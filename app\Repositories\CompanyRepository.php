<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderReceives;
use App\Models\User;
use App\Models\Organization;
use Config;
use Illuminate\Support\Facades\DB;
use App\Traits\QueryTrait;

class CompanyRepository
{

    use QueryTrait;

    public function getCompanyPurchaseOrderList($request, $searchText='', $organizationId='', $companyId='', $categoryId='', $salesUserId='', $typeId='')
    {
        $query = PurchaseOrder::with('purchaseOrderDetail.product', 'company', 'organization', 'users');
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($companyId) {
            $query->where('company_id', $companyId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        if($typeId) {
            $query->where('type', $typeId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if(!empty($searchText)) {
            $query->where(function ($query) use ($searchText) {
                $query->whereHas('company', function ($subquery) use ($searchText) {
                    $subquery->where('name', 'like', "%$searchText%");
                })->orWhere('po_number', 'like', "%$searchText%")
                    ->orWhere('sales_order_no', 'like', "%$searchText%")
                    ->orWhere('date', 'like', "%$searchText%")
                    ->orWhere('status', 'like', "%$searchText%")
                    ->orWhere('total_amount', 'like', "%$searchText%");
            });
        }
        $perPage = Config::get('constants.perPage');
        $statusOrder = ['Open', 'Partially Received', 'Completed', 'Cancelled'];

        $searchableFields = ['po_number', 'sales_order_no', 'category', 'company.name', 'date', 'total_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $query->orderBy('id', 'desc');
        return $query;
    }

    public function getCompanyPurchaseOrderData($id)
    {
        return PurchaseOrder::where('id', $id)
            ->with('purchaseOrderDetail.product', 'company', 'documents', 'organization')
            ->get()
            ->toArray();
    }

    public function getCompanyPurchaseOrderReceiveData($id)
    {
        return PurchaseOrder::where('id', $id)
            ->with('purchaseOrderDetailForReceive.product', 'company', 'documents', 'organization')
            ->get()
            ->toArray();
    }

    public function getReceivedOrderData($id)
    {
        return PurchaseOrderReceives::where('purchase_order_id', $id)
            ->orderBy('id', 'desc')
            // ->with('purchaseOrderDetail.product', 'serialNumbers', 'users')
            ->with('purchaseOrderReceiveDetails.serialNumbers',  'purchaseOrderReceiveDetails.product',  'purchaseOrderReceiveDetails.purchaseOrderDetail', 'users', 'documents')
            ->get()
            // ->groupBy('po_receive_number')
            ->toArray();
    }

    public function getActiveCompanies()
    {
        return Company::with([
            'products' => function ($query) {
                $query->where('status', Product::STATUS_ACTIVE);
            }
        ])
        ->where('status', Company::STATUS_ACTIVE)
        ->select('name', 'id', 'company_type', 'gst_type')
        ->orderByRaw('name')
        ->get();
    }

    public function getSalesUserList()
    {
        return User::where(['status' => User::STATUS_ACTIVE])
            ->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'id')
            ->get();
    }

    public function getOrganization()
    {
        return Organization::select('name', 'id')->get();
    }
}
