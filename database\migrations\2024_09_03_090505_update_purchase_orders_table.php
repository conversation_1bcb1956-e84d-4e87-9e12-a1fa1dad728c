<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_paid', function (Blueprint $table) {
            $table->dropColumn('bank_name');
        });

        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->integer('sales_user_id')->nullable()->after('company_id');
        });

        Schema::table('organizations', function (Blueprint $table) {
            $table->string('pan_no')->nullable()->after('gst_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
