<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['reporttype', 'serviceperson', 'serviceReport']);

const form = useForm('post', '/save-upload-service-report', {
    service_report_id: props.serviceReport.id,
    customer_id: props.serviceReport.customers.id,
    date: '',
    document: '',
    type:'',
    service_engineer_id:''
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

const setReportType = (id, name) => {
    form.type = id;
    form.errors.type = null;
};

const setServicePerson = (id, name) => {
    form.service_engineer_id = id;
    form.errors.service_engineer_id = null;
};

const handleDocument = (file) => {
    form.document = file;
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

</script>

<template>
    <Head title="Upload Report" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Upload Report</h1>
                </div>
                <p class="text-sm font-semibold text-gray-900">{{ serviceReport.customers.customer_name }}</p>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10">
                        <div class="sm:col-span-3">
                            <InputLabel for="type" value="Report Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="reporttype"
                                    v-model="form.type"
                                    @onchange="setReportType"
                                    :class="{ 'error rounded-md': form.errors.type }"
                                />
                                <InputError  v-if="form.invalid('type')" class="" :message="form.errors.type" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="service_engineer_id" value="Service Engineer"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="serviceperson"
                                    v-model="form.service_engineer_id"
                                    @onchange="setServicePerson"
                                    :class="{ 'error rounded-md': form.errors.service_engineer_id }"
                                />
                                <InputError  v-if="form.invalid('service_engineer_id')" class="" :message="form.errors.service_engineer_id" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                                <InputLabel for="date" value="Date" />
                                <input
                                    v-model="form.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                                <InputError  v-if="form.invalid('date')" class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-4">
                            <div class="w-full">
                            <InputLabel for="note" value="Upload Report" />
                                <MultipleFileUpload
                                    inputId="document"
                                    inputName="document"
                                    @files="handleDocument"
                                />
                            </div>
                        </div>

                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('service-reports.show' ,{id:serviceReport.customers.id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

