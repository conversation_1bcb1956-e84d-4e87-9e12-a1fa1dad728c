<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import { Head , usePage, useForm} from '@inertiajs/vue3';

const props = defineProps(['data', 'receivedOrder', 'filepath', 'purchaseFilepath']);
const poData = usePage().props.data[0];
const file = usePage().props.filepath.view;
const purchaseFile = usePage().props.purchaseFilepath.view;

const form = useForm({});

const totalAmountSum = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_amount, 0);
    }
    return 0;
});

const totalQty = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.qty, 0);
    }
    return 0;
});

const totalRecQty = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.receive_qty, 0);
    }
    return 0;
});

const totalPrice = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_price, 0);
    }
    return 0;
});

const totalGst = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_gst_amount, 0);
    }
    return 0;
});

const totalReceivedProductAmount = computed(() => {
    if (props.receivedOrder) {
        return props.receivedOrder.reduce((acc, poData) => {
            const productAmount = poData.total_amount;
            return acc + productAmount;
        }, 0);
    }
    return 0;
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
    // return '\u20B9' + formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);

const purchasedocumentPreviewModal = ref(false);
const purchaseselectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
  selectedDocument.value = name;
  documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};


const purchaseopenPreviewModal = (name) => {
    purchaseselectedDocument.value = name;
    purchasedocumentPreviewModal.value = true;
};

const purchasecloseDocumentPreviewModal = () => {
    purchasedocumentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin+ file+ name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const documentDeleteModal = ref(false);
const selectedDocumentId = ref(null);

const openDeleteModal = (id) => {
  selectedDocumentId.value = id;
  documentDeleteModal.value = true;
};

const deleteDocument = () => {
    form.get(route('removeproduct',{id:selectedDocumentId.value, model:'PurchaseOrderReceiveDetails'}), {
        onSuccess: () => {
        closeDocumentModal()
        }
    });
};

const closeDocumentModal = () => {
    documentDeleteModal.value = false;
};

</script>

<template>
    <Head title="Company PO"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Purchase Order Detail</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('companypo.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Company Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.name  ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  data[0].company.gst_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.email ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.contact_no ?? '-' }}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].po_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  formatDate(data[0].date) ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Purchase Type:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].type ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Total Received Product Amount  (₹):</p>
                        <p class="text-sm leading-6 text-gray-700">{{  formatAmount(totalReceivedProductAmount) ?? '-' }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
        <div class="shadow sm:rounded-lg">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                    <tr class="border-b-2">
                        <th v-if="data[0].category == 'Sales'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Product Code</th>
                        <th v-if="data[0].category == 'Service'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Part No</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Product Description</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">HSN</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Pkg Of Qty</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">QTY</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Rec. QTY</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Price (₹)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Price (₹)</th>
                        <th v-if="data[0].company.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (%)</th>
                        <th v-if="data[0].company.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (₹)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">CGST (%)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">SGST (%)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total GST (₹)</th>
                        <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Amount (₹)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(product, index)  in data[0].purchase_order_detail" :key="index">
                        <td v-if="data[0].category == 'Sales'" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36" :class="{ 'text-red-700': (product.qty - product.receive_qty > 0) }">{{ product.product.item_code ?? '-' }}</td>
                        <td v-if="data[0].category == 'Service'" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36" :class="{ 'text-red-700': (product.qty - product.receive_qty > 0) }">{{ product.product.item_code ?? '-' }}</td>
                        <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-normal min-w-60" :class="{ 'text-red-700': (product.qty - product.receive_qty > 0) }">{{ product.product.name ?? '-'}}</td>
                        <td class="px-4 py-2.5 font-medium whitespace-nowrap min-w-32">{{ product.product.hsn_code ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-32">{{ product.pkg_of_qty ?? '-'}}</td>
                        <td class="px-4 py-2.5">{{ product.qty ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-28">{{ product.receive_qty ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-28">{{ formatAmount(product.price) ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-36">{{ formatAmount(product.total_price) ?? '-'}}</td>
                        <td v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ product.gst ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ formatAmount(product.total_gst_amount) ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-32">{{ formatAmount(product.total_gst_amount) ?? '-'}}</td>
                        <td class="px-4 py-2.5 min-w-44">{{ formatAmount(product.total_amount) ?? '-' }}</td>
                    </tr>
                    <tr class="bg-white border-b">
                        <th class="px-4 py-2.5 text-gray-900 min-w-24">Total</th>
                        <th class="px-4 py-2.5 min-w-60"></th>
                        <th class="px-4 py-2.5"></th>
                        <th class="px-4 py-2.5 min-w-28"></th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-20">{{ totalQty }}</th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-22">{{ totalRecQty }}</th>
                        <th class="px-4 py-2.5"></th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-36">{{  formatAmount(totalPrice) }}</th>
                        <th v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                        <th v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(totalGst) }}</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(totalGst) }}</th>
                        <th class="px-4 py-2.5 text-gray-900 min-w-44">{{  formatAmount(data[0].total_amount) }}</th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
        <div class="mt-6 bg-white p-1 shadow sm:rounded-lg border" v-if="poData.documents && (poData.documents.length > 0)">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">UPLOADED DOCUMENT</th>
                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white">
                        <tr v-for="(file, index) in poData.documents" :key="poData.id" class="">
                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ file.orignal_name }}
                            </td>
                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                <button type="button"  @click="openPreviewModal(file.name)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>                            </button>
                                <button type="button"  @click="downloadDocument(file.name)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path></svg>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
             </div>
        <div class="mt-6 sm:flex sm:items-center">
            <h1 class="text-2xl font-semibold leading-7 text-gray-900" v-if="(receivedOrder && receivedOrder.length != 0)">PO Receive List</h1>
        </div>
        <div class="flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden" style="min-height:500px">
                <div class="p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg" v-for="(receives, index) in receivedOrder" >
                    <div class="flex justify-between p-4">
                        <div class="justify-start">
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-40">Customer Invoice No:</p>
                                <p class="text-sm font-semibold leading-6 text-gray-700">{{receives.customer_invoice_no ?? '-'}}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-40">Customer Invoice Date:</p>
                                <p class="text-sm font-semibold leading-6 text-gray-700">{{receives.customer_invoice_date ?? '-'}}</p>
                            </div>
                        </div>
                        <div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-36">PO Received By:</p>
                                <p class="text-sm font-semibold leading-6 text-gray-700">{{receives.users.first_name }} {{ receives.users.last_name}}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-36">PO Receive Date:</p>
                                <p class="text-sm font-semibold leading-6 text-gray-700">{{ formatDate(receives.po_receive_date) }}</p>
                            </div>
                        </div>
                        <div class="justify-end">
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-36">Total Amount (₹):</p>
                                <p class="text-sm font-semibold leading-6 text-gray-700">{{ formatAmount(receives.total_amount) }}</p>
                            </div>
                        </div>
                    </div>
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50 border">
                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10">
                                 <th scope="col" class="py-3.5 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">PRODUCT CODE</th>
                                <th scope="col" class="py-3.5 sm:col-span-3 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">PRODUCT DESCRIPTION</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">HSN</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">QTY</th>
                                <th scope="col" class="py-3.5 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">RECEIVED QTY</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">ACTION</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-if="receives.purchase_order_receive_details.length === 0">
                                <td class="whitespace-nowrap sm:col-span-5 py-2 pl-4 pr-3 text-sm text-red-700 sm:pl-6 text-center" colspan="6">Received product already deleted</td>
                            </tr>
                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10" v-for="(receive, index) in receives.purchase_order_receive_details" :key="index">
                                <td class="whitespace-nowrap sm:col-span-2 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.item_code ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-3 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.name ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.hsn_code ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.purchase_order_detail.qty ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-2 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.receive_qty ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">
                                    <div class="space-x-2">
                                        <button @click="toggleDetails(index)">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                            </svg>
                                        </button>
                                        <button type="button" @click="openDeleteModal(receive.id)">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <div v-if="showDetails[index] && receive.serial_numbers.length != 0" class="divide-y divide-gray-300 sm:col-span-10 product-details border mx-6 mb-4">
                                    <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50">
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Batch</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Expiry Date</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">MRP (₹)</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Purchase Price (₹)</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Qty</th>
                                    </tr>
                                     <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1" >
                                        <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5" v-for="(productinfo, index) in receive.serial_numbers" :key="index">
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.batch ?? '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.expiry_date != null) ? formatDate(productinfo.expiry_date) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.mrp) ? formatAmount(productinfo.mrp) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.purchase_price) ? formatAmount(productinfo.purchase_price) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.receive_qty ?? '-' }}</td>
                                        </tr>
                                     </tbody>
                                </div>
                            </tr>
                        </tbody>
                    </table>
                    <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="receives.documents && (receives.documents.length > 0)">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">UPLOADED DOCUMENT</th>
                                    <th scope="col" class="px-3 py-2 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-300 bg-white">
                                <tr v-for="(files, index) in receives.documents" :key="files.id">
                                    <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ files.orignal_name }}
                                    </td>
                                    <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                        <button type="button"  @click="purchaseopenPreviewModal(files.name)">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                     </div>
                </div>
            </div>
            </div>
        </div>
        </div>
         <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                 <FileViewer :fileUrl="file+ selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal"> Cancel </SecondaryButton>
                </div>
            </div>
        </Modal>

        <Modal :show="purchasedocumentPreviewModal" @close="purchasecloseDocumentPreviewModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
                <FileViewer :fileUrl="purchaseFile+ purchaseselectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="purchasecloseDocumentPreviewModal"> Cancel </SecondaryButton>
                </div>
            </div>
        </Modal>

        <Modal :show="documentDeleteModal" @close="closeDocumentModal1">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to remove this product?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeDocumentModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteDocument"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>
