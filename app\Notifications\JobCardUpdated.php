<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\DatabaseMessage;


class JobCardUpdated extends Notification
{
    use Queueable;

    protected $jobCard;

    public function __construct($jobCard)
    {
        $this->jobCard = $jobCard;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable)
    {
        return ['database']; // or 'mail', 'broadcast', etc.
    }

    public function toDatabase($notifiable)
    {
        $engineerName = $this->jobCard->users->first_name . ' ' . $this->jobCard->users->last_name;
        return [
            'message' => $this->jobCard->job_card_number . ' has been updated by engineer '.$engineerName,
            'job_card_id' => $this->jobCard->id,
        ];
    }
    // public function via(object $notifiable): array
    // {
    //     return ['mail'];
    // }

    /**
     * Get the mail representation of the notification.
     */
    // public function toMail(object $notifiable): MailMessage
    // {
    //     return (new MailMessage)
    //                 ->line('The introduction to the notification.')
    //                 ->action('Notification Action', url('/'))
    //                 ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    // public function toArray(object $notifiable): array
    // {
    //     return [
    //         //
    //     ];
    // }
}
