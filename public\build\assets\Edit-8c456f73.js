import{K as j,h as $,r as S,j as B,l as E,o as c,c as m,a,u as r,w as p,F as u,Z as M,b as e,d as N,i as v,g as U,T as H,f as T,t as x}from"./app-2ecbacfc.js";import{_ as O,a as R}from"./AdminLayout-42d5bb92.js";import{_ as D}from"./InputError-aa79d601.js";import{_ as b}from"./InputLabel-f62a278f.js";import{P as F}from"./PrimaryButton-0d76f021.js";import{_ as A}from"./TextInput-73b24943.js";import{_ as y}from"./Checkbox-3bb6de23.js";import"./_plugin-vue_export-helper-c27b6911.js";const K={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},L=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Role & Permission",-1),Z={class:"border-b border-gray-900/10 pb-12"},q={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},z={class:"sm:col-span-12 grid grid-cols-6 gap-6"},G={class:"col-span-2"},I={class:"sm:col-span-12"},J={class:"sm:col-span-3"},Q={class:"flex justify-between items-center border px-4 py-2 bg-gray-50 rounded-lg"},W={class:"flex items-center text-lg font-semibold leading-7 text-gray-900 space-x-2"},X=e("div",{class:"cursor-pointer"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})])],-1),Y={class:"border border-t-0 rounded-b-lg"},ee={class:"text-sm font-semibold leading-6 text-gray-900 p-1"},se={class:"flex justify-end p-1"},te={class:"flex mt-6 items-center justify-between"},oe={class:"ml-auto flex items-center justify-end gap-x-6"},ie=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel ",-1),ne={key:0,class:"text-sm text-gray-600"},fe={__name:"Edit",props:["data","roleHasPermissions"],setup(f){const l=f,_=j().props.role,s=$({name:_.name,id:_.id,permissions:l.roleHasPermissions});S({});const k=i=>l.roleHasPermissions.includes(i),w=(i,t)=>{if(i)s.permissions.push(t);else{const o=s.permissions.indexOf(t);o!==-1&&s.permissions.splice(o,1)}g()},P=(i,t)=>{const o=i.target.checked;t.forEach(n=>{if(o&&!s.permissions.includes(n.id))s.permissions.push(n.id);else if(!o&&s.permissions.includes(n.id)){const d=s.permissions.indexOf(n.id);d>-1&&s.permissions.splice(d,1)}})},h=B(()=>{const i={};return Object.keys(l.data).forEach(t=>{const o=l.data[t].every(n=>s.permissions.includes(n.id));i[t]=o}),i}),g=i=>{for(const t in l.data){const o=l.data[t].every(n=>s.permissions.includes(n.id));h.value[t]=o}};return E(s.permissions,(i,t)=>{for(const o in l.data)g()},{deep:!0}),(i,t)=>(c(),m(u,null,[a(r(M),{title:"Update Role-Permission"}),a(O,null,{default:p(()=>[e("div",K,[L,e("form",{onSubmit:t[2]||(t[2]=N(o=>r(s).patch(i.route("roles.update",{role:r(s).id})),["prevent"]))},[e("div",Z,[e("div",q,[e("div",z,[e("div",G,[a(b,{for:"name",value:"Role Name"}),a(A,{id:"name",type:"text",modelValue:r(s).name,"onUpdate:modelValue":t[0]||(t[0]=o=>r(s).name=o),onChange:t[1]||(t[1]=o=>r(s).validate("name"))},null,8,["modelValue"]),a(D,{class:"",message:r(s).errors.name},null,8,["message"])])]),e("div",I,[a(b,{for:"name",value:"Select Permission"})]),(c(!0),m(u,null,v(f.data,(o,n)=>(c(),m("div",J,[e("div",Q,[e("h3",W,[a(y,{checked:h.value[n],onChange:d=>P(d,o)},null,8,["checked","onChange"]),e("span",null,x(n),1)]),X]),e("div",Y,[(c(!0),m(u,null,v(o,(d,C)=>(c(),m("div",{key:C,class:"flex justify-between items-center px-4 py-1 border-b last:border-b-0"},[e("div",ee,x(d.name),1),e("div",se,[a(y,{checked:k(d.id),"onUpdate:checked":V=>w(V,d.id),name:"permissions"},null,8,["checked","onUpdate:checked"])])]))),128))])]))),256))])]),e("div",te,[e("div",oe,[a(R,{href:i.route("roles.index")},{svg:p(()=>[ie]),_:1},8,["href"]),a(F,{disabled:r(s).processing},{default:p(()=>[U("Update")]),_:1},8,["disabled"]),a(H,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[r(s).recentlySuccessful?(c(),m("p",ne,"Saved.")):T("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{fe as default};
