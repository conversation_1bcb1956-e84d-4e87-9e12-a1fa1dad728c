<?php

namespace App\Http\Requests;
use App\Models\OrderDetails;
use Illuminate\Foundation\Http\FormRequest;

class OrderDeliverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'invoice_number' => 'required|string|max:255',
            'deliveredProduct' => 'required|array|min:1',
            'deliveredProduct.*.delivered_qty' => 'required|numeric|min:1',
            'deliveredProduct.*.order_details_id' => 'required|exists:order_details,id',
            'deliveredProduct.*.order_id' => 'required|exists:orders,id',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'invoice_number.required' => 'Invoice number is required.',
            'invoice_number.string' => 'Invoice number must be a valid string.',
            'invoice_number.max' => 'Invoice number cannot exceed 255 characters.',
            'deliveredProduct.required' => 'At least one product must be delivered.',
            'deliveredProduct.*.delivered_qty.required' => 'Delivery quantity is required.',
            'deliveredProduct.*.delivered_qty.numeric' => 'Delivery quantity must be a number.',
            'deliveredProduct.*.delivered_qty.min' => 'Delivery quantity must be at least 1.',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $deliveredProducts = $this->input('deliveredProduct');
            $filledReceiveQtys = 0;

            foreach ($deliveredProducts as $key => $product) {
                $orderDetail = OrderDetails::find($product['order_details_id']);

                if ($orderDetail && $product['delivered_qty'] > ($orderDetail->qty - $orderDetail->delivered_qty)) {
                    $validator->errors()->add("deliveredProduct.$key.delivered_qty", __('The delivered quantity must not be greater than the available quantity.'));
                }

                if (!empty($product['delivered_qty'])) {
                    $filledReceiveQtys++;
                }
            }

            if ($filledReceiveQtys == 0) {
                $validator->errors()->add("deliveredProduct.$key.delivered_qty", __('At least one quantity field must be filled.'));
            }
        });
    }
}
