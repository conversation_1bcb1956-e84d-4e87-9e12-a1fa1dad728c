<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Create 'party' table
        Schema::create('party', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('contact_no')->nullable();
            $table->string('gst_no', 15)->nullable();
            $table->enum('is_customer', ['yes', 'no'])->default('no');
            $table->enum('is_company', ['yes', 'no'])->default('no');
            $table->timestamps();
        });

        // 2. Add 'party_id' to 'customers'
        Schema::table('customers', function (Blueprint $table) {
            $table->unsignedBigInteger('party_id')->nullable()->after('id');
        });

        // 3. Add 'party_id' to 'companies'
        Schema::table('companies', function (Blueprint $table) {
            $table->unsignedBigInteger('party_id')->nullable()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('party_table_and_add_party_id_columns');
    }
};
