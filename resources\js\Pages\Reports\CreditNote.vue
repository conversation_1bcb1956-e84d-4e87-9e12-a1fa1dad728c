<script setup>
import { ref, onMounted, watch , computed} from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import Dropdown from '@/Components/Dropdown.vue';
import RadioButton from '@/Components/RadioButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';



const props = defineProps(['data', 'category', 'organization', 'customers', 'salesuser', 'permissions', 'invoicetypes']);
const form = useForm({});
const searchValue = ref('');

const handleSearchChange = (searchValue) => {
    form.get(route('credit.report', {
        search: searchValue,
    }), {
        preserveState: true,
    });
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString();
};


</script>

<template>
    <Head title="Credit Note Report"/>

    <AdminLayout>

        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Credit Note Report</h1>
                </div>
                <div class="flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                            <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field" @input="handleSearchChange($event.target.value)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                        </div>
                    </div>
                    <div class="flex ml-6">
                        <CreateButton :href="route('reports')">
                                Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                INVOICE NUMBER
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                CUSTOMER NAME
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                AMOUNT (₹)
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                ACTION
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                            <!-- <td class="px-4 py-2.5 min-w-40">
                                {{ poData.credit_note_no }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52  font-medium text-gray-900 whitespace-nowrap truncate">
                                {{ poData.customers.debit_note_number }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatDate(poData.total_amount) }}
                            </td> -->
                            <!-- <td class="px-4 py-2.5 min-w-32">
                                {{ formatAmount(poData.total_amount)}}
                            </td> -->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
    </AdminLayout>
</template>

