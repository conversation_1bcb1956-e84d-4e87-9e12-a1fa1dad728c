<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('administration_info', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->longText('address_line_1');
            $table->longText('address_line_2')->nullable();
            $table->bigInteger('pincode');
            $table->string('city');
            $table->string('state');
            $table->string('gst_no');
            $table->string('drug_licence_no');
            $table->string('email');
            $table->bigInteger('contact_no');
            $table->string('logo')->nullable();
            $table->string('signature')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('administration_info');
    }
};
