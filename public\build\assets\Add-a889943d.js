import{r as b,j as q,l as T,o as d,c,a as l,u as s,w as z,F as O,Z as X,b as o,t as _,f as p,d as ee,n as h,k as te,v as oe,i as R,g as ae,T as se,s as ne,x as le}from"./app-2ecbacfc.js";import{_ as re,a as ie}from"./AdminLayout-42d5bb92.js";import{_ as v}from"./InputLabel-f62a278f.js";import{P as de}from"./PrimaryButton-0d76f021.js";import{_ as $}from"./TextInput-73b24943.js";import{_ as ce}from"./TextArea-00d3f05d.js";import{_ as ue}from"./RadioButton-eb348e4c.js";import{_ as S}from"./SearchableDropdown-6058bf5f.js";import{u as me}from"./index-35fd125b.js";/* empty css                                                                          */import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as pe}from"./Checkbox-3bb6de23.js";const U=g=>(ne("data-v-02c314d7"),g=g(),le(),g),ve={class:"h-screen animate-top"},fe={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ye={class:"sm:flex sm:items-center"},he=U(()=>o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),ge={class:"flex items-center justify-between"},xe={key:0,class:"text-base font-semibold leading-6 text-gray-900"},be=["onSubmit"],we={class:"border-b border-gray-900/10 pb-12"},ke={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ve={class:"sm:col-span-3"},Ce={class:"relative mt-2"},Ae={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Fe={class:"sm:col-span-2"},ze={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},Se={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-1"},Pe={key:2,class:"sm:col-span-2"},Ie={key:3,class:"sm:col-span-2"},Te={class:"mt-4 flex justify-start"},Oe={class:"text-base font-semibold"},De={key:4,class:"sm:col-span-3"},Be={key:5,class:"sm:col-span-3"},je={key:6,class:"sm:col-span-3"},Ee={key:7,class:"sm:col-span-3"},Me={class:"relative mt-2"},Ye={class:"sm:col-span-6"},Le={class:"overflow-x-auto divide-y divide-gray-300 w-full"},qe=U(()=>o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Re={style:{"overflow-y":"auto","max-height":"318px"}},Ze={class:"divide-y divide-gray-300 bg-white"},Ge={class:"whitespace-nowrap px-2 text-sm text-gray-900"},He={class:"text-sm text-gray-900 leading-6 py-1.5"},Je={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Ke={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},Xe={key:0,class:"text-red-500 text-xs absolute"},et={class:"whitespace-nowrap px-2 text-sm text-gray-900"},tt={class:"sm:col-span-2"},ot={class:"mt-4 flex justify-start"},at={class:"text-base font-semibold"},st={key:0,class:"text-red-500 text-xs absolute"},nt={key:8,class:"sm:col-span-6"},lt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},rt=U(()=>o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),it={class:"divide-y divide-gray-300 bg-white"},dt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ut={class:"flex flex-col"},mt={class:"text-sm text-gray-900"},_t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},pt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},vt={class:"flex mt-6 items-center justify-between"},ft={class:"ml-auto flex items-center justify-end gap-x-6"},yt=U(()=>o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),ht={key:0,class:"text-sm text-gray-600"},gt={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(g){const w=g;b([]);const e=me("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),A=b(""),Z=()=>{e.settled_amount=P.value,e.advance_amount=B.value,e.total_unused_amount=k.value,e.is_credit=m.value,e.invoice=f.value,e.credit_data=x.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},G=(n,a)=>{A.value=a,e.payment_type=n,e.errors.payment_type=null,a==="Cash"?e.note="Cash":e.note==="Cash"&&(e.note="")},N=b([]),D=b([]),x=b([]),k=b(""),H=(n,a)=>{e.organization_id=n,e.errors.organization_id=null;const t=w.bankinfo.filter(i=>i.organization_id===n);D.value=t;const u=w.invoices.filter(i=>i.purchase_order&&i.purchase_order.organization_id===n&&i.purchase_order.company_id===e.company_id);N.value=u;const r=w.credit.filter(i=>i.organization_id===n&&i.company_id===e.company_id);x.value=r,k.value=x.value.reduce((i,y)=>i+y.unused_amount,0)},J=(n,a)=>{e.company_id=n;const t=w.invoices.filter(r=>r.purchase_order&&r.purchase_order.organization_id===e.organization_id&&r.purchase_order.company_id===n);N.value=t;const u=w.credit.filter(r=>r.company_id===n&&r.organization_id===e.organization_id);x.value=u,k.value=x.value.reduce((r,i)=>r+i.unused_amount,0),e.errors.company_id=null},K=(n,a)=>{e.org_bank_id=n,e.errors.org_bank_id=null},P=q(()=>{const n=f.value.reduce((a,t)=>a+(t.check&&t.amount?parseFloat(t.amount):0),0);return parseFloat(n.toFixed(2))}),B=q(()=>{const n=parseFloat(e.amount||0),a=parseFloat(e.round_off||0),t=P.value;return n>t?n-t-a:0}),V=n=>{let a=n.toFixed(2).toString(),[t,u]=a.split("."),r=t.substring(t.length-3),i=t.substring(0,t.length-3);return i!==""&&(r=","+r),`${i.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${u}`},j=n=>{const a=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",t)},Q=(n,a)=>{const t=m.value==="Yes"?parseFloat(k.value||0):parseFloat(e.amount||0)+parseFloat(e.round_off||0);if(a===void 0&&(a=f.value.findIndex(y=>y.check===n.target.checked)),a===-1||a>=f.value.length)return;if(!f.value[a].check){f.value[a].amount=0;return}let u=t;f.value.forEach((y,C)=>{y.check&&C!==a&&(u-=parseFloat(y.amount||0))});const r=parseFloat(f.value[a].pending_amount||0),i=Math.min(r,u);f.value[a].amount=i.toFixed(2)},f=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),m=b("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],I=()=>{f.value=N.value.map(n=>({id:n.id,date:n.customer_invoice_date,invoice_no:n.customer_invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.pending_amount||0).toFixed(2),check:!1,amount:"0.00"}))};T(N,()=>{I()}),T(m,()=>{I()}),T(()=>e.amount,()=>{m.value==="No"&&I()});const F=n=>{e.errors[n]=null,e.errors.settled_amount=null};return(n,a)=>(d(),c(O,null,[l(s(X),{title:"Payment"}),l(re,null,{default:z(()=>[o("div",ve,[o("div",fe,[o("div",ye,[he,o("div",ge,[x.value.length>0?(d(),c("div",xe," Credits Available: ₹"+_(V(k.value)),1)):p("",!0)])]),o("form",{onSubmit:ee(Z,["prevent"]),class:""},[o("div",we,[o("div",ke,[o("div",Ve,[l(v,{for:"payment_type",value:"Organization"}),o("div",Ce,[l(S,{options:g.organization,modelValue:s(e).organization_id,"onUpdate:modelValue":a[0]||(a[0]=t=>s(e).organization_id=t),onOnchange:H,class:h({"error rounded-md":s(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",Ae,[l(v,{for:"payment_type",value:"Company"}),o("div",Ne,[l(S,{options:g.companies,modelValue:s(e).company_id,"onUpdate:modelValue":a[1]||(a[1]=t=>s(e).company_id=t),onOnchange:J,class:h({"error rounded-md":s(e).errors.company_id})},null,8,["options","modelValue","class"])])]),o("div",Fe,[l(v,{for:"role_id",value:"Payment Through Credit ?"}),o("div",ze,[l(ue,{modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=t=>m.value=t),options:W},null,8,["modelValue"])])]),m.value=="No"?(d(),c("div",$e,[l(v,{for:"payment_type",value:"Payment Type"}),o("div",Se,[l(S,{options:g.paymentType,modelValue:s(e).payment_type,"onUpdate:modelValue":a[3]||(a[3]=t=>s(e).payment_type=t),onOnchange:G,class:h({"error rounded-md":s(e).errors.payment_type})},null,8,["options","modelValue","class"])])])):p("",!0),m.value=="No"?(d(),c("div",Ue,[l(v,{for:"round_off",value:"Round Off"}),l($,{type:"text",onChange:a[4]||(a[4]=t=>F("round_off")),modelValue:s(e).round_off,"onUpdate:modelValue":a[5]||(a[5]=t=>s(e).round_off=t),class:h({"error rounded-md":s(e).errors.round_off})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(d(),c("div",Pe,[l(v,{for:"amount",value:"Amount"}),l($,{id:"amount",type:"text",onChange:a[6]||(a[6]=t=>F("amount")),modelValue:s(e).amount,"onUpdate:modelValue":a[7]||(a[7]=t=>s(e).amount=t),class:h({"error rounded-md":s(e).errors.amount})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(d(),c("div",Ie,[l(v,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Te,[o("p",Oe,_(V(B.value)),1)])])):p("",!0),A.value=="Cheque"&&m.value=="No"?(d(),c("div",De,[l(v,{for:"check_number",value:"Cheque Number"}),l($,{id:"check_number",type:"text",modelValue:s(e).check_number,"onUpdate:modelValue":a[8]||(a[8]=t=>s(e).check_number=t),class:h({"error rounded-md":s(e).errors["data.check_number"]})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(d(),c("div",Be,[l(v,{for:"date",value:"Payment Date"}),te(o("input",{"onUpdate:modelValue":a[9]||(a[9]=t=>s(e).date=t),class:h(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":s(e).errors.date}]),type:"date",onChange:a[10]||(a[10]=t=>F("date"))},null,34),[[oe,s(e).date]])])):p("",!0),A.value=="Cash"&&m.value=="No"?(d(),c("div",je)):p("",!0),A.value!="Cash"&&m.value=="No"?(d(),c("div",Ee,[l(v,{for:"org_bank_id",value:"Our Bank"}),o("div",Me,[l(S,{options:D.value,modelValue:s(e).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=t=>s(e).org_bank_id=t),onOnchange:K,class:h({"error rounded-md":s(e).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):p("",!0),o("div",Ye,[o("table",Le,[qe,o("div",Re,[o("tbody",Ze,[(d(!0),c(O,null,R(f.value,(t,u)=>(d(),c("tr",{key:u},[o("td",Ge,[o("div",He,[l(pe,{name:"check",checked:t.check,"onUpdate:checked":r=>t.check=r,onChange:r=>Q(r,u)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",Je,_(t.invoice_no),1),o("td",Ke,_(t.total_amount),1),o("td",Qe,_(t.pending_amount),1),o("td",We,[l($,{id:"amount",type:"text",modelValue:t.amount,"onUpdate:modelValue":r=>t.amount=r,onChange:r=>F("invoice."+u+".amount"),class:h({error:s(e).errors[`invoice.${u}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),s(e).errors[`invoice.${u}.amount`]?(d(),c("p",Xe,_(s(e).errors[`invoice.${u}.amount`]),1)):p("",!0)]),o("td",et,_(j(t.date)),1)]))),128))])])])]),o("div",tt,[l(v,{for:"note",value:"Total Settled Amount"}),o("div",ot,[o("p",at,_(V(P.value)),1)]),s(e).errors.settled_amount?(d(),c("p",st,_(s(e).errors.settled_amount),1)):p("",!0)]),m.value=="No"?(d(),c("div",nt,[l(v,{for:"note",value:"Note"}),l(ce,{id:"note",type:"text",rows:2,modelValue:s(e).note,"onUpdate:modelValue":a[12]||(a[12]=t=>s(e).note=t)},null,8,["modelValue"])])):p("",!0)]),x.value.length>0&&m.value=="Yes"?(d(),c("table",lt,[rt,o("tbody",it,[(d(!0),c(O,null,R(x.value,(t,u)=>{var r,i,y,C,E,M,Y,L;return d(),c("tr",{key:u},[o("td",dt,_(j(t.date)),1),o("td",ct,[o("div",ut,[o("div",mt,_((i=(r=t.paymentpaid)==null?void 0:r.bank_info)!=null&&i.bank_name?(C=(y=t.paymentpaid)==null?void 0:y.bank_info)==null?void 0:C.bank_name:"Cash")+" - "+_((M=(E=t.paymentpaid)==null?void 0:E.bank_info)!=null&&M.account_number?(L=(Y=t.paymentpaid)==null?void 0:Y.bank_info)==null?void 0:L.account_number:""),1)])]),o("td",_t,_(V(t.amount)),1),o("td",pt,_(V(t.unused_amount)),1)])}),128))])])):p("",!0)]),o("div",vt,[o("div",ft,[l(ie,{href:n.route("receipt.index")},{svg:z(()=>[yt]),_:1},8,["href"]),l(de,{disabled:s(e).processing},{default:z(()=>[ae("Save")]),_:1},8,["disabled"]),l(se,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:z(()=>[s(e).recentlySuccessful?(d(),c("p",ht,"Saved.")):p("",!0)]),_:1})])])],40,be)])])]),_:1})],64))}},Ut=_e(gt,[["__scopeId","data-v-02c314d7"]]);export{Ut as default};
