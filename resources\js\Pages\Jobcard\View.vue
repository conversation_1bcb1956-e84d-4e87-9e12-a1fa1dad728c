<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import { Head , usePage} from '@inertiajs/vue3';

const props = defineProps(['data', 'checklist']);

const checkedValues = ref([]);
const userData = usePage().props.data[0];
checkedValues.value = userData.job_card_checks.map(check => check.job_card_checklist_id);

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

</script>

<template>
    <Head title="Jobcard"/>

    <AdminLayout>
    <div class="animate-top h-screen">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Jobcard Detail</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <!-- <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p> -->
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('jobcard.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Hospital Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].hospital_name ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].contact_no ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Address:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].address ?? '-'}}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Jobcard Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].job_card_number ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Engineer Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name ?? '-'}} {{ data[0].users.last_name ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Job Status:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].job_status ?? '-'}}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                            <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Equipment</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Model</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Serial No</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white">
                        <tr>
                            <td class="whitespace-nowrap pr-4 py-3 text-sm text-gray-900">{{ data[0].product_name ?? '-' }}</td>
                            <td class="whitespace-nowrap px-4 py-3 text-sm text-gray-900">{{ data[0].product_code ?? '-' }}</td>
                            <td class="whitespace-nowrap px-4 py-3 text-sm text-gray-900">{{ data[0].serial_no ?? '-'}}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="mt-6 space-y-1">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Problem Description:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].problem_description ?? '-'}}</p>
                    </div>
                    <div class="w-1/2">
                        <InputLabel for="engineer_id" value="Checklist :" />
                        <div class="grid sm:grid-cols-6 relative">
                            <CheckboxWithLabel
                            v-for="item in checklist"
                            :key="item.id"
                            :checked="checkedValues"
                            :value="item.id"
                            :label="item.type"
                            @update:checked="updateChecked"
                            />
                        </div>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Close Note:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].close_note ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Close Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0]?.close_date ? formatDate(data[0].close_date) : '-'}}</p>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    </AdminLayout>

</template>
