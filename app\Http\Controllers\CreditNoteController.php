<?php


namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\CreditNote;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\CustomerTransaction;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\SerialNumbers;
use App\Models\CustomerCredit;
use App\Models\PaymentReceive;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;

class CreditNoteController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:List Weekly Planning')->only(['index']);
        // $this->middleware('permission:Delete Weekly Planning')->only('destroy');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $query  = CreditNote::with('creditNoteDetails','customers', 'organization', 'invoice');
        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if(!empty($search)){
            if(!empty($search)){
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })->orWhereHas('invoice', function ($subquery) use ($search) {
                    $subquery->where('invoice_no', 'like', "%$search%");
                })
                ->orWhere('debit_note_number', 'like', "%$search%")
                ->orWhere('credit_note_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            }
        }

        $searchableFields = ['credit_note_no', 'debit_note_number', 'invoice.invoice_no', 'customers.customer_name', 'date', 'total_amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Pending', 'In Process', 'Completed', 'Cancel'];
        $data = $query->orderBy('id', 'desc')->paginate(20);
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $organization->prepend($allOrganization);
        $customers->prepend($allCustomers);
        $data->withQueryString()->links();

        $permissions = [
            // 'canCreateOrders'      => auth()->user()->can('Create Orders'),
            // 'canDeleteOrders'      => auth()->user()->can('Delete Orders')
        ];
        return Inertia::render('CreditNote/List', compact('data', 'permissions', 'organization', 'customers', 'organizationId', 'customerId'));
    }

    public function show(Request $request, $id)
    {
        $data = CreditNote::where('id', $id)->with('creditNoteDetails.product.serialNumbers','customers', 'organization', 'invoice')->get()->toArray();
        return Inertia::render('CreditNote/View', compact('data'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $creditNote = CreditNote::findOrFail($id);

            $relatedTransactions = CustomerTransaction::where('entity_id', $id)
                ->where('entity_type', 'credit_note')
                ->get();

            foreach ($relatedTransactions as $transaction) {
                $transaction->delete();
            }

            $invoiceId = $creditNote->invoice_id;

            // Check if there's a related customer credit
            $paymentReceive = PaymentReceive::where('note', 'LIKE', $creditNote->id . ' ' . $creditNote->debit_note_number . ' %')->first();
            if ($paymentReceive) {
                $paymentReceiveId = $paymentReceive->id;
                if ($paymentReceiveId) {
                    $customerCredit = CustomerCredit::where('payment_receive_id', $paymentReceiveId)->first();
                    if($customerCredit->amount == $customerCredit->unused_amount){
                        $customerCredit->delete();
                    } else {
                        return Redirect::to('/creditnote')->with('error', 'credit note can not delete');
                    }
                }
                $paymentReceive->delete();
            } else {
                $invoice = Invoice::find($invoiceId);
                if ($invoice) {
                    $invoice->cr_dr_note = null;
                    $invoice->pending_amount += $creditNote->total_amount;
                    $invoice->paid_amount -= $creditNote->total_amount;
                    // Update invoice status based on pending and paid amounts
                    if ($invoice->pending_amount <= 0) {
                        $invoice->status = 'Paid';
                    } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                        $invoice->status = 'Partially Paid';
                    } else {
                        $invoice->status = 'Unpaid';
                    }
                    $invoice->save();
                }
            }

            // Update serial numbers and delete invoice details
            $relatedInvoiceDetails = InvoiceDetail::where('credit_note_id', $id)->get();
            foreach ($relatedInvoiceDetails as $invoiceDetail) {
                $serialNumber = SerialNumbers::find($invoiceDetail->serial_number_id);
                if ($serialNumber) {
                    // Restore the sell_qty since we're deleting the credit note
                    $serialNumber->sell_qty += $invoiceDetail->qty;
                    $serialNumber->save();
                }
                $invoiceDetail->delete();
            }

            // Finally delete the credit note
            $creditNote->delete();

            DB::commit();
            return Redirect::to('/creditnote')->with('success', 'Credit Note Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/creditnote')->with('error', $e->getMessage());
        }
    }
}
