<?php

namespace App\Http\Requests;
use App\Models\PurchaseOrderDetail;
use App\Models\SerialNumbers;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PoReceiveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

    public function rules(): array
    {
        return [
            'created_by'  => 'required|integer',
            'customer_invoice_no'  => 'required',
            'customer_invoice_date'  => 'required',
            'receivedProduct.*.productDetails.*.batch' => 'required',
            'receivedProduct.*.productDetails.*.qty'   => 'required',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $receivedProduct  = $this->input('receivedProduct');
            // dd($receivedProduct);
            $filledReceiveQtys = 0;
            $productDetailsQty = 0;
            foreach ($receivedProduct  as $key => $product) {
                if(!empty($product['receive_qty'])){
                    $filledReceiveQtys++;
                }
                if ($product['total_qty'] < ($product['received_qty'] +  $product['receive_qty'])) {
                    $validator->errors()->add("receivedProduct.$key.receive_qty", __('qty not matched'));
                }
                if($product['receive_qty'] != null){
                    // $validator->errors()->add("receivedProduct.$key.purchase_price", __('purchase_price'));
                    // $validator->errors()->add("receivedProduct.$key.total_batch", __('total_batch'));
                }

                // dd($product);
                if(isset($product['productDetails'])){
                    $receivedQty = $product['received_qty'] + $product['receive_qty'];
                    $totalProductDetailsQty = array_sum(array_column($product['productDetails'], 'qty'));

                    if ($totalProductDetailsQty > $receivedQty) {
                        $validator->errors()->add("receivedProduct.$key.productDetails", __('Total qty in product details exceeds received quantity.'));
                    }

                    if (count($product['productDetails']) > 0 && $totalProductDetailsQty != $product['receive_qty']) {
                        $validator->errors()->add("receivedProduct.$key.productDetails", __('Total qty in product details not matched with quantity.'));
                    }
                }

            }

            if ($filledReceiveQtys == 0) {
                $validator->errors()->add("receivedProduct.$key.receive_qty", __('At least one field must be filled.'));
            }
        });
    }

}
