<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\BankTransactionDTO;
use Support\Contracts\HasDTO;

class BankTransactionRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'payment_type' => 'required',
            'amount' => 'required',
            'date' => 'required|date'
        ];
    }

    public function DTO()
    {
        return BankTransactionDTO::LazyFromArray($this->input());
    }

}
