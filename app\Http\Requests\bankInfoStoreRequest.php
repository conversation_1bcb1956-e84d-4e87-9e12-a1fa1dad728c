<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\BankInfoDTO;
use Support\Contracts\HasDTO;

class bankInfoStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'required|integer',
            'bank_name' => 'required|string',
            'account_number' => 'required|integer',
            'balance' => 'required',
            'ifsc_code' => ['required', 'string', 'regex:/^[A-Z]{4}0[A-Z0-9]{6}$/'],
            'amount_type' => 'required',
        ];
    }

    public function DTO()
    {
        return BankInfoDTO::LazyFromArray($this->input());
    }

}   
