<?php

namespace App\DTO;

use App\Traits\ArrayToProps;

class PurchaseOrderDetailDTO
{
    use ArrayToProps;

    public $note;
    public $po_date;
    public $purchase_order_id;
    public $selectedProductItem;
    public $sales_user_id;
    public $company_id;
    public $category;
    public $type;
    public $cgst;
    public $sgst;
    public $igst;
    public $total_gst;
    public $sub_total;
    public $total_amount;
    public $po_number;
    public $document;
    public $organization_id;
    public $sales_order_no;
    public $sales_order_date;
    public $overall_discount;
    public $total_discount;

}
