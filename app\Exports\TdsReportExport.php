<?php
namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TdsReportExport implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles, ShouldAutoSize
{
    protected $tdsReports;
    protected $fromDate;
    protected $toDate;
    protected $organizationName;
    protected $customerName;

    public function __construct($tdsReports, $fromDate, $toDate, $organizationName, $customerName)
    {
        $this->tdsReports = $tdsReports;
        $this->fromDate = $fromDate ? Carbon::parse($fromDate)->format('M d, Y') : 'N/A';
        $this->toDate = $toDate ? Carbon::parse($toDate)->format('M d, Y') : 'N/A';
        $this->organizationName = $organizationName;
        $this->customerName = $customerName;
    }

    public function collection()
    {
        return $this->tdsReports->map(function ($report) {
            $bankName = $report->bankInfo->bank_name ?? '';
            $accountNumber = $report->bankInfo->account_number ?? '';
            $invoiceNos = $report->invoice_data && count($report->invoice_data) > 0
            ? collect($report->invoice_data)->pluck('invoice_no')->join(', ')
            : $report->invoice_no;
            return [
                'Customer Name' => $report->customers->customer_name,
                'Invoice No'    => $invoiceNos,
                'Payment Type'  => $report->payment_type,
                'Bank'          => trim("{$bankName} : {$accountNumber}"),
                'Date'          => Carbon::parse($report->date)->format('M d, Y') ?? '-',
                'Amount'        => $report->tds_amount,
            ];
        });
    }

    public function headings(): array
    {
        return [
            [$this->organizationName],
            [$this->customerName],
            ["From: {$this->fromDate} To: {$this->toDate}"],
            ['Customer Name', 'Invoice No', 'Payment Type', 'Bank', 'Date', 'Amount'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => NumberFormat::FORMAT_DATE_XLSX15,
            'F' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:F1');
        $sheet->getStyle('A1:F1')->applyFromArray($this->getHeaderStyle(14, Alignment::HORIZONTAL_CENTER));

        $sheet->mergeCells('A2:F2');
        $sheet->getStyle('A2:F2')->applyFromArray($this->getHeaderStyle(12, Alignment::HORIZONTAL_CENTER));

        $sheet->mergeCells('A3:F3');
        $sheet->getStyle('A3:F3')->applyFromArray($this->getHeaderStyle(12, Alignment::HORIZONTAL_CENTER));

        $sheet->getStyle('A4:F4')->applyFromArray([
            'font' => ['bold' => true, 'size' => 11],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFFFCC']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        $rowCount = count($this->tdsReports) + 4;
        $sheet->getStyle("A4:F{$rowCount}")->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => '000000']],
            ],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        foreach (range(5, $rowCount) as $row) {
            $fillColor = $row % 2 == 0 ? 'F9F9F9' : 'FFFFFF';
            $sheet->getStyle("A{$row}:F{$row}")->applyFromArray([
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['argb' => $fillColor],
                ],
            ]);

            $rowValues = $sheet->getCell("A{$row}")->getValue();
            if (strtolower($rowValues) === 'total') {
                $sheet->getStyle("A{$row}:F{$row}")->getFont()->setBold(true);
                $sheet->getStyle("A{$row}:F{$row}")->getFill()->getStartColor()->setARGB('D9E1F2');
            }
        }

        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        return $sheet;
    }

    private function getHeaderStyle($fontSize, $alignment)
    {
        return [
            'font' => ['bold' => true, 'size' => $fontSize],
            'alignment' => ['horizontal' => $alignment, 'vertical' => Alignment::VERTICAL_CENTER],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
            ],
        ];
    }
}
