<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import FileUpload from '@/Components/FileUpload.vue';
import FileViewer from '@/Components/FileViewer.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { Head, usePage } from '@inertiajs/vue3';
import { defineProps, ref, watch } from 'vue';

const props = defineProps([ 'data', 'maintenance_type', 'filepath']);
const file = usePage().props.filepath.view;

const userData = usePage().props.data;

const pmDates = ref({
  pm_date_1: userData.pm_date_1 || '',
  pm_date_2: userData.pm_date_2 || '',
  pm_date_3: userData.pm_date_3 || '',
  pm_date_4: userData.pm_date_4 || '',
});

const form = useForm('post', '/maintenance-contract', {
  id: userData.id,
  hospital_name: userData.hospital_name,
  address: userData.address,
  city: userData.city,
  contact_no: userData.contact_no,
  contract_start_date: userData.contract_start_date,
  contract_end_date: userData.contract_end_date,
  maintenance_type: userData.maintenance_type,
  time_period: userData.time_period,
  product_name: userData.product_name,
  price: userData.price,
    pm_date_1: '',
    pm_date_2: '',
    pm_date_3: '',
    pm_date_4: '',
  company_name: userData.company_name,
  invoice_number: userData.invoice_number,
  name: userData.name,

});

const inputId = ref(userData.pm_date_1 ? (userData.pm_date_2 ? (userData.pm_date_3 ? (userData.pm_date_4 ? 4 : 3) : 2) : 1) : 0);

const pmCount = ref(inputId.value);
const error = ref('');

watch(inputId, (newValue) => {
  pmCount.value = parseInt(newValue, 10) || 0;
  handlePmUpdates(); // Update PM dates accordingly
});

const handlePmUpdates = () => {
  const count = pmCount.value;

  if (!form.contract_start_date) {
    pmDates.value = { pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' };
    return;
  }

  const startDate = new Date(form.contract_start_date);
  pmDates.value = { pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' };

  if (count === 2) {
    const pmDate1 = new Date(startDate);
    const pmDate2 = new Date(startDate);
    pmDate1.setMonth(pmDate1.getMonth() + 6);
    pmDate2.setMonth(pmDate2.getMonth() + 11);

    pmDates.value = {
      pm_date_1: pmDate1.toISOString().split('T')[0],
      pm_date_2: pmDate2.toISOString().split('T')[0],
    };
  } else if (count === 3) {
    const pmDate1 = new Date(startDate);
    const pmDate2 = new Date(startDate);
    const pmDate3 = new Date(startDate);
    pmDate1.setMonth(pmDate1.getMonth() + 4);
    pmDate2.setMonth(pmDate2.getMonth() + 8);
    pmDate3.setMonth(pmDate3.getMonth() + 12);

    pmDates.value = {
      pm_date_1: pmDate1.toISOString().split('T')[0],
      pm_date_2: pmDate2.toISOString().split('T')[0],
      pm_date_3: pmDate3.toISOString().split('T')[0],
    };
  } else if (count === 4) {
    const pmDate1 = new Date(startDate);
    const pmDate2 = new Date(startDate);
    const pmDate3 = new Date(startDate);
    const pmDate4 = new Date(startDate);
    pmDate1.setMonth(pmDate1.getMonth() + 3);
    pmDate2.setMonth(pmDate2.getMonth() + 6);
    pmDate3.setMonth(pmDate3.getMonth() + 9);
    pmDate4.setMonth(pmDate4.getMonth() + 12);

    pmDates.value = {
      pm_date_1: pmDate1.toISOString().split('T')[0],
      pm_date_2: pmDate2.toISOString().split('T')[0],
      pm_date_3: pmDate3.toISOString().split('T')[0],
      pm_date_4: pmDate4.toISOString().split('T')[0],
    };
  }

  error.value = count <= 0 ? 'Please enter a valid number of PMs.' : '';
};

const updateForm = (field, value) => {
  form[field] = value;
};

const setDropdownValue = (field, id, name) => {
    form[field] = id;
};

const handleDocument = (file) => {
    form.name = file;
};

const documentDeleteModal = ref(false);
// const selectedDocumentId = ref(null);



const closeDocumentModal = () => {
    documentDeleteModal.value = false;
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
  selectedDocument.value = name;
  documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const submit = () => {
    form.pm_date_1 = pmDates.value.pm_date_1;
    form.pm_date_2 = pmDates.value.pm_date_2;
    form.pm_date_3 = pmDates.value.pm_date_3;
    form.pm_date_4 = pmDates.value.pm_date_4;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });

};

</script>

<template>
    <Head title="Maintenance Contract" />
    <AdminLayout>
        <div class="animate-top h-screen">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Edit Maintenance Contract</h1>
                </div>
            </div>
            <div class="mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                            <div class="sm:col-span-4">
                                <InputLabel for="hospital_name" value="Hospital Name" />
                                <TextInput
                                    id="hospital_name"
                                    hospital_name="text"
                                    v-model="form.hospital_name"
                                    autocomplete="hospital_name"
                                    @change="form.validate('hospital_name')"
                                />
                                <InputError class="" :message="form.errors.hospital_name" />
                            </div>
                            <div class="sm:col-span-4">
                                <InputLabel for="address" value="Address" />
                                <TextInput
                                    id="address"
                                    type="text"
                                    v-model="form.address"
                                    @change="form.validate('address')"
                                />
                                <InputError class="" :message="form.errors.address" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="contact_no" value="Contact No" />
                                <TextInput
                                    id="contact_no"
                                    contact_no="text"
                                    v-model="form.contact_no"
                                    :numeric="true"
                                    autocomplete="contact_no"
                                    @change="form.validate('contact_no')"
                                />
                                <InputError class="" :message="form.errors.contact_no" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="city" value="City" />
                                <TextInput
                                    id="city"
                                    city="text"
                                    v-model="form.city"
                                    autocomplete="city"
                                    @change="form.validate('city')"
                                />
                                <InputError class="" :message="form.errors.city" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="contract_start_date" value="Contract Start" />
                                <input
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"  @change="form.validate('contract_start_date')"
                                v-model="form.contract_start_date"
                                />
                                <InputError v-if="form.invalid('contract_start_date')" class="" :message="form.errors.contract_start_date" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="contract_end_date" value="Contract End" />
                                <input
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"  @change="form.validate('contract_end_date')"
                                v-model="form.contract_end_date"
                                />
                                <InputError v-if="form.invalid('contract_end_date')" class="" :message="form.errors.contract_end_date" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="type" value="Maintenance Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="maintenance_type"
                                        v-model="form.maintenance_type"
                                        @onchange="(id, name) => setDropdownValue('maintenance_type', id, name)"
                                        @change="form.validate('maintenance_type')"
                                    />
                                </div>
                                <InputError class="" :message="form.errors.maintenance_type" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="time_period" value="Time Period" />
                                <TextInput
                                    id="time_period"
                                    time_period="text"
                                    v-model="form.time_period"
                                    autocomplete="time_period"
                                    @change="form.validate('time_period')"
                                />
                                <InputError class="" :message="form.errors.time_period" />
                            </div>
                            <div class="sm:col-span-5">
                                <InputLabel for="product_name" value="Equipment Name" />
                                <TextInput
                                    id="product_name"
                                    product_name="text"
                                    v-model="form.product_name"
                                    autocomplete="product_name"
                                    @change="form.validate('product_name')"
                                />
                                <InputError class="" :message="form.errors.product_name" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="price" value="Price" />
                                <TextInput
                                    id="price"
                                    price="text"
                                    v-model="form.price"
                                    autocomplete="price"
                                    @change="form.validate('price')"
                                />
                                <InputError class="" :message="form.errors.price" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="company_name" value="Company Name" />
                                <TextInput
                                    id="company_name"
                                    company_name="text"
                                    v-model="form.company_name"
                                    autocomplete="company_name"
                                    @change="form.validate('company_name')"
                                />
                                <InputError class="" :message="form.errors.company_name" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="invoice_number" value="Invoice Number" />
                                <TextInput
                                    id="invoice_number"
                                    invoice_number="text"
                                    v-model="form.invoice_number"
                                    autocomplete="invoice_number"
                                    @change="form.validate('invoice_number')"
                                />
                                <InputError class="" :message="form.errors.invoice_number" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="inputField" value="How Many PM ?" />
                                <TextInput
                                  id="inputField"
                                  v-model="inputId"
                                  @input="handlePmUpdates"
                                  @blur="handlePmUpdates"
                                />
                                <InputError :message="error" />
                              </div>
                              <div class="sm:col-span-4">
                                <InputLabel for="name" value="Upload Document"/>
                                <FileUpload
                                    label="Upload Document"
                                    inputId="name"
                                    inputName="name"
                                    :uploadedFiles="form.name"
                                    @file="handleDocument"
                                />
                            </div>
                             <div class="sm:col-span-10">
                            </div>
                            <div v-if="pmCount >= 1" class="sm:col-span-3">
                                <InputLabel for="pm_date_1" value="PM Date 1" />
                                <input
                                  class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  type="date"
                                  id="pm_date_1"
                                  v-model="pmDates.pm_date_1"
                                  @change="updateForm('pm_date_1', pmDates.pm_date_1)"
                                />
                              </div>

                              <div v-if="pmCount >= 2" class="sm:col-span-3">
                                <InputLabel for="pm_date_2" value="PM Date 2" />
                                <input
                                  class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  type="date"
                                  id="pm_date_2"
                                  v-model="pmDates.pm_date_2"
                                  @change="updateForm('pm_date_2', pmDates.pm_date_2)"
                                />
                              </div>

                              <div v-if="pmCount >= 3" class="sm:col-span-3">
                                <InputLabel for="pm_date_3" value="PM Date 3" />
                                <input
                                  class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  type="date"
                                  id="pm_date_3"
                                  v-model="pmDates.pm_date_3"
                                  @change="updateForm('pm_date_3', pmDates.pm_date_3)"
                                />
                              </div>

                              <div v-if="pmCount >= 4" class="sm:col-span-3">
                                <InputLabel for="pm_date_4" value="PM Date 4" />
                                <input
                                  class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  type="date"
                                  id="pm_date_4"
                                  v-model="pmDates.pm_date_4"
                                  @change="updateForm('pm_date_4', pmDates.pm_date_4)"
                                />
                              </div>
                        </div><br>
                        <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="form.name">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead class="bg-gray-50">
                                    <tr >
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">UPLOADED DOCUMENT</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-300 bg-white">
                                    <tr>
                                        <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ form.name }}
                                        </td>
                                        <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                            <button type="button"  @click="openPreviewModal(form.name)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('maintenance-contract.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                        </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <Modal :show="documentDeleteModal" @close="closeDocumentModal">
            <div class="p-6">
              <h2 class="text-lg font-medium text-gray-900">
                  Are you sure you want to delete this document?
              </h2>
              <div class="mt-6 flex justify-end">
                  <SecondaryButton @click="closeDocumentModal"> Cancel </SecondaryButton>
                  <DangerButton
                      class="ml-3"
                      @click="deleteDocument"
                  >
                      Delete
                  </DangerButton>
              </div>
          </div>
      </Modal>
      <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
            <div class="p-6">
               <FileViewer :fileUrl="file+ selectedDocument" />
              <div class="mt-6 px-4 flex justify-end">
                  <SecondaryButton @click="closeDocumentPreviewModal"> Cancel </SecondaryButton>
              </div>
          </div>
      </Modal>
    </AdminLayout>
</template>
