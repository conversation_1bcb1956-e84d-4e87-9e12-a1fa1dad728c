<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankInfo extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'organization_bankinfo';

    protected static $logName = 'Bank-Info';

    public function getLogDescription(string $event): string
    {
        return "Bank info has been {$event} for <strong>{$this->bank_name}:{$this->account_number}</strong> by";
    }

    protected static $logAttributes = [
        'organization_id',
        'bank_name',
        'account_number',
        'ifsc_code',
        'amount_type',
        'balance'
    ];

    protected $fillable = [
        'organization_id',
        'bank_name',
        'account_number',
        'ifsc_code',
        'amount_type',
        'balance'
    ];

    public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }
}
