<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ActivityTrait;

class PurchaseTransaction extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'purchase_transaction';

    protected static $logName = 'Purchase-Transaction';

    public function getLogDescription(string $event): string
    {
        return "Purchase transaction has been {$event} for <strong>{$this->note}</strong> by";
    }
    protected static $logAttributes = [
        'organization_id',
        'company_id',
        'entity_id',
        'entity_type',
        'payment_type',
        'amount',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'organization_id',
        'company_id',
        'entity_id',
        'entity_type',
        'payment_type',
        'amount',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }
}
