<?php

namespace App\Http\Requests;

use App\Models\Product;
use App\DTO\ProductDTO;
use Support\Contracts\HasDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class ProductStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'        => ['required', 'string', 'max:255'],
            'category'    => ['required'],
            'price'       => ['required', 'numeric', 'max:9999999999'],
            'gst'         => ['required', 'numeric'],
            // 'hsn_code'    => ['required', 'regex:/^\d{4,8}$/'],
            'min_qty'     => ['nullable', 'numeric', 'digits_between:1,4'],
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $company_id = $this->input('company_id');
            $name = $this->input('name');
            $item_code = $this->input('item_code');
            // $hsn_code = $this->input('hsn_code');

            $normalized_name = Str::lower(preg_replace('/\s+/', '', $name));
            $normalized_item_code = Str::lower(preg_replace('/\s+/', '', $item_code));

            if (empty($this->input('id'))) {
                $existingProduct = Product::where('company_id', $company_id)
                    ->where(function ($query) use ($normalized_name) {
                        $query->whereRaw("LOWER(REPLACE(name, ' ', '')) LIKE ?", ["$normalized_name"]);
                    })
                    ->where(function ($query) use ($normalized_item_code) {
                        $query->whereRaw("LOWER(REPLACE(item_code, ' ', '')) LIKE ?", ["$normalized_item_code"]);
                    })
                    ->exists();

                if ($existingProduct) {
                    $validator->errors()->add('name', __('Product Already Exists for this Company and Item Code'));
                }
            } else {
                $existingProduct = Product::where('company_id', $company_id)
                    ->where(function ($query) use ($normalized_name) {
                        $query->whereRaw("LOWER(REPLACE(name, ' ', '')) LIKE ?", ["$normalized_name"]);
                    })
                    ->where(function ($query) use ($normalized_item_code) {
                        $query->whereRaw("LOWER(REPLACE(item_code, ' ', '')) LIKE ?", ["$normalized_item_code"]);
                    })
                    ->where('id', '!=', $this->input('id'))
                    ->exists();

                if ($existingProduct) {
                    $validator->errors()->add('name', __('Product Already Exists for this Company and Item Code'));
                }
            }
        });
    }

    public function DTO()
    {
        return ProductDTO::LazyFromArray($this->input());
    }

}
