<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import FileUpload from '@/Components/FileUpload.vue';
import { Head , Link, usePage } from '@inertiajs/vue3';
import { defineProps, ref , onBeforeMount  } from 'vue';
import { useForm } from 'laravel-precognition-vue-inertia';


const userData = usePage().props.data[0];

const props = defineProps(['data', 'category', 'organization', 'filepath']);

const file = usePage().props.filepath.view;

const form = useForm('post', '/products', {
    id: userData.id,
    company_id: userData.company_id,
    name: userData.name,
    min_qty: userData.min_qty,
    item_code: userData.item_code,
    description: userData.description,
    price:  parseFloat(userData.price).toFixed(2),
    rating: userData.rating,
    gst:  parseFloat(userData.gst).toFixed(2),
    hsn_code: userData.hsn_code,
    prefix: userData.prefix,
    // part_no: userData.part_no,
    category: userData.category,
    image: userData ? file+ userData.image : '/uploads/product-images/defaultimg.png'
});

const setCategory = (id, name) => {
    form.category = name;
};

const handlePhoto = (file) => {
    form.image = file;
};

const removeUploadedDocument = () => {
    form.get(route('productimage.remove', { id: form.id }), {
        onSuccess: () => {
            form.image = null; 
        },
        onError: (errors) => {
            console.error("Error removing the document:", errors);
        }
    });
};

const submit = () => form.submit({
    preserveScroll: true,
    resetOnSuccess: false,
});

</script>

<template>
    <Head title="Product Edit" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Product</h2>
            <!-- <form @submit.prevent="form.patch(route('products.update'))"> -->
            <form  @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-2">
                            <InputLabel for="name" value="Product Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                            />
                            <TextInput
                                id="name"
                                type="hidden"
                                v-model="form.company_id"
                            />
                            <InputError class="" :message="form.errors.name" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="item_code" value="Product Code" />
                            <TextInput
                                id="item_code"
                                type="text"
                                v-model="form.item_code"
                            />
                            <InputError class="" :message="form.errors.item_code" />
                        </div>
                        <div class="sm:col-span-2"></div>

                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="Category" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="category"
                                    v-model="form.category"
                                    @onchange="setCategory"
                                    />
                                </div>
                            <InputError class="" :message="form.errors.category" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="hsn_code" value="HSN Code" />
                            <TextInput
                                id="hsn_code"
                                type="text"
                                v-model="form.hsn_code"
                            />
                            <InputError class="" :message="form.errors.hsn_code" />
                        </div>
                        <div class="sm:col-span-2"></div>
                        <div class="sm:col-span-1">
                            <InputLabel for="price" value="Price (₹)" />
                            <TextInput
                                id="price"
                                type="text"
                                v-model="form.price"
                                min="1"
                            />
                            <InputError class="" :message="form.errors.price" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="gst" value="GST(%)" />
                            <TextInput
                                id="gst"
                                type="text"
                                v-model="form.gst"
                                min="0"
                                max="100"
                            />
                            <InputError class="" :message="form.errors.gst" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="min_qty" value="Minimum Qty" />
                            <TextInput
                                id="min_qty"
                                type="text"
                                v-model="form.min_qty"
                            />
                            <InputError class="" :message="form.errors.min_qty" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="prefix" value="Prefix" />
                            <TextInput
                                id="prefix"
                                type="text"
                                v-model="form.prefix"
                            />
                            <InputError class="" :message="form.errors.prefix" />
                        </div>
                        <!-- <div v-if="userData.category =='Service'" class="flex sm:col-span-4 space-x-4" v-for="(product, index)  in serviceProduct" :key="index">
                            <div class="w-1/3">
                                <div class="block text-sm font-semibold leading-6 text-gray-900">{{ product.organization_name }}</div>
                            </div>
                            <div class="w-1/3">
                                <InputLabel for="stock" value="Current Stock" />
                                <TextInput
                                    id="stock"
                                    type="text"
                                    :numeric="true"
                                    v-model="product.stock"
                                />
                            </div>
                            <div class="w-1/3">
                                <InputLabel for="purchase_price" value="Purchase Price (₹)" />
                                <TextInput
                                    id="purchase_price"
                                    type="text"
                                    v-model="product.purchase_price"
                                    min="1"
                                />
                            </div>
                        </div> -->
                        <div class="sm:col-span-4">
                            <InputLabel for="photo" value="Upload Image" />
                            <FileUpload
                              label="Upload Image"
                              inputId="image"
                              inputName="image"
                              :fileUrl="form.image"
                              @file="handlePhoto"
                            />
                            <button
                                v-if="userData.image != null"  
                                type="button"
                                class="mt-2 text-sm text-red-500 hover:underline"
                                @click="removeUploadedDocument"
                            >
                                Remove File
                            </button>
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="description" value="Description" />
                            <TextArea
                                id="description"
                                type="text"
                                v-model="form.description"
                                :rows="5"
                            />
                            <InputError class="" :message="form.errors.description" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('products.show',{id:userData.company_id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>



                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
    </AdminLayout>
</template>
