<script setup>
import { ref, onMounted, watch } from 'vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import Pagination from '@/Components/Pagination.vue';
import { Head, usePage } from '@inertiajs/vue3';
import SimpleDropdown from "@/Components/SimpleDropdown.vue";
import InputLabel from "@/Components/InputLabel.vue";

const props = defineProps(['data', 'productInfo']);
const productID = usePage().props.productInfo.id;

const { search, sort, fetchData } = sortAndSearch('product.logs', {id: productID});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: 'numeric', hour12: true };
    return date.toLocaleDateString('en-US', options);
};

const nameFormat = (name) => {
    if(name !== null && name !== undefined){
        //let formattedName =  name.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
        //return formattedName.replace(/\bGst\b/g, 'GST');
        return name
            .replace(/_/g, ' ')
            .replace(/\b\w/g, char => char.toUpperCase())
            .replace(/\b(Gst|Sgst|Cgst|Igst)\b/g, match => match.toUpperCase());
    }
    return 'NA';
}

const colorMap = {
    'created': 'green', //gray
    'updated': 'blue',
    'deleted': 'red',
};

const formatEventName = (action) => {
    const color = colorMap[action] || 'blue';
    return `<div class="items-center justify-center px-3 py-1 w-fit border-none leading-normal bg-${color}-100 text-${color}-700 font-semibold rounded-md">
                ${nameFormat(action)}
            </div>`
}

const formatDescription = (userData) => {
    let actionName = userData.action;
    let descriptionText = JSON.parse(userData.description);
    let formatted = '';

    // Wrap the whole content in a container, no borders, just spacing between logs
    formatted += `<div class="space-y-4">`;

    if (actionName == 'updated' || actionName == 'received' || actionName == 'accepted') {
        for (const [key, value] of Object.entries(descriptionText)) {
            formatted += formattedHtmlForUpdateLogDescription(key, value.old, value.new);
        }
    } else {
        formatted += formattedHtmlForCreateDeleteLogDescription(actionName, descriptionText);
    }

    formatted += `</div>`;

    return formatted;
}

const escapeHtml = (text) => {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

const formattedHtmlForUpdateLogDescription = (key, oldVal, newVal) => {
    const formattedKey = nameFormat(key);
    const defaultOldVal = oldVal !== null && oldVal !== undefined ? oldVal : 'NA';
    const defaultNewVal = newVal !== null && newVal !== undefined ? newVal : 'NA';

    return `
        <div class="p-3 mb-4 rounded-lg shadow-md">
            <div class="flex gap-4">
                <div class="flex-1">
                    <div class="text-sm font-semibold text-blue-700">
                        ${formattedKey}
                    </div>
                </div>
                <div class="flex-2">
                    <div class="flex gap-2">
                        <div class="bg-red-100 text-red-700 rounded-md px-3 py-1 text-xs">
                            <pre style="white-space: pre-wrap; word-wrap: break-word;">${defaultOldVal}</pre>
                        </div>
                        <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill="currentColor" d="M10.837 3.13a.5.5 0 0 0-.674.74L16.33 9.5H2.5a.5.5 0 0 0 0 1h13.828l-6.165 5.628a.5.5 0 0 0 .674.739l6.916-6.314a.747.747 0 0 0 0-1.108z"></path>
                        </svg>
                        <div class="bg-green-100 text-green-700 rounded-md px-3 py-1 text-xs">
                            <pre style="white-space: pre-wrap; word-wrap: break-word;">${defaultNewVal}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;
}

const formattedHtmlForCreateDeleteLogDescription = (actionName, descriptionText) => {
    let key = nameFormat(descriptionText.status);
    let val = descriptionText.attributes;
    let defaultNewVal = 'NA';

    if (val !== null && val !== undefined) {
        defaultNewVal = (typeof val === 'object') ? escapeHtml(JSON.stringify(val, null, 2)) : val;
    }

    const color = colorMap[actionName] || 'blue';

    return `
        <div class="p-3 mb-4 bg-${color}-100 rounded-lg shadow-md">
            <div class="flex gap-4">
                <div class="flex-1">
                    <div class="text-sm font-semibold text-${color}-700">
                        ${key}
                    </div>
                </div>
                <div class="flex-2">
                    <div class="bg-${color}-100 text-${color}-700 rounded-md px-3 py-1 text-xs">
                        <pre style="white-space: pre-wrap; word-wrap: break-word;">${defaultNewVal}</pre>
                    </div>
                </div>
            </div>
        </div>`;
}

</script>

<template>
    <Head title="Product Logs"/>

    <AdminLayout>

        <div class="animate-top" v-if="productInfo">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">
                        All Activity Log for
                        <span class="text-blue-700">{{ productInfo.name }}</span>
                    </h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                           <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                            </svg>
                            <input id="search-field" v-model="search" @input="fetchData"
                                class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                                placeholder="Search..." type="search" name="search">
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                        <CreateButton :href="route('products.show', {id: productInfo.company_id})">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                    <div class="sm:col-span-4">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">Product Name:</p>
                                <p class="text-sm leading-6 text-gray-700">{{ productInfo.name }}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">Product Code:</p>
                                <p class="text-sm leading-6 text-gray-700">{{ productInfo.item_code }}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">Company:</p>
                                <p class="text-sm leading-6 text-gray-700">{{ productInfo.company.name }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">Price (₹)</p>
                                <p class="text-sm leading-6 text-gray-700">{{ Number(productInfo.price).toFixed(2) }}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">GST (%)</p>
                                <p class="text-sm leading-6 text-gray-700">{{ Number(productInfo.gst).toFixed(2) }}</p>
                            </div>
                            <div class="inline-flex items-center justify-start w-full space-x-2">
                                <p class="text-sm font-semibold text-gray-900 w-32">HSN Code</p>
                                <p class="text-sm leading-6 text-gray-700">{{ productInfo.hsn_code }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8" style="min-height:500px; margin-bottom:80px;">
                        <div class="p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer" @click="sort('action')">EVENT</th>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer" @click="sort('log_name')">NAME</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 cursor-pointer" @click="sort('created_at')">DATE TIME</th>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 cursor-pointer">DESCRIPTION</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-300 bg-white" v-if="data.data && (data.data.length > 0)">
                                    <tr v-for="(userData, index) in data.data" :key="userData.id" class="">
                                        <td class="whitespace-nowrap px-3 py-3 text-sm" v-html="formatEventName(userData.action)"></td>
                                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ userData.log_name }}</td>
                                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ formatDate(userData.created_at) ?? '-' }}</td>
                                        <td class="whitespace-nowrap px-3 py-3 text-sm" v-html="formatDescription(userData)"></td>
                                    </tr>
                                </tbody>
                                <tbody v-else>
                                    <tr class="bg-white">
                                        <td colspan="4" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                            No data found.
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
                    </div>
                </div>
            </div>
        </div>

        <div class="animate-top" v-else>
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">
                        Invalid product ID
                    </h1>
                </div>
            </div>
        </div>
    </AdminLayout>

</template>
