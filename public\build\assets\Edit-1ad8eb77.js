import{_ as o}from"./AdminLayout-301d54ca.js";import i from"./DeleteUserForm-e1ba00e1.js";import m from"./UpdatePasswordForm-4cdb49da.js";import r from"./UpdateProfileInformationForm-a20f09c9.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-8a557454.js";import"./DangerButton-41cc1b93.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-ccd7f9dc.js";import"./InputLabel-07f3a6e8.js";import"./Modal-3bbbc3d3.js";/* empty css                                                              */import"./SecondaryButton-e65b5ab9.js";import"./TextInput-ab168ee4.js";import"./PrimaryButton-9d9bcdd8.js";import"./TextArea-3588e81e.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
