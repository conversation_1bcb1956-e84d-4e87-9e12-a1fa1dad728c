<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerTransaction extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'customer_transaction';
    protected static $logName = 'Customer-Transaction';

    public function getLogDescription(string $event): string
    {
        return "Customer transaction has been <strong>{$event}</strong> for <strong>{$this->note}</strong> by";
    }

    protected static $logAttributes = [
        'organization_id',
        'customer_id',
        'entity_id',
        'entity_type',
        'payment_type',
        'amount',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'organization_id',
        'customer_id',
        'entity_id',
        'entity_type',
        'payment_type',
        'amount',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function paymentReceive(){
        return $this->belongsTo(PaymentReceive::class,'entity_id','id');
    }
}
