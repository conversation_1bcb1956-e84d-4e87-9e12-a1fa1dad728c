<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ActivityTrait;

class PaymentReceive extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'payment_receive';

    protected static $logName = 'Payment-Receive';

    protected $casts = [
        'invoice_data' => 'array',  // Automatically decode JSON to an array
    ];

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customers ? $this->customers->customer_name : 'Unknown Customer';
        $user = $this->updatedBy ? "{$this->updatedBy->first_name} {$this->updatedBy->last_name}" : 'Unknown User';

        if ($event === 'deleted') {
            return "Payment has been <strong style='green: red;'>Refunded</strong> by <strong>{$user}</strong> to <strong>{$customerName}</strong>.";
        }

        return "Payment has been <strong style='color: green;'>Received</strong> by <strong>{$user}</strong> for <strong>{$customerName}</strong>.";
    }


    public function getInvoiceDataAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    protected static $logAttributes = [
        'organization_id',
        'invoice_id',
        'invoice_data',
        'org_bank_id',
        'customer_id',
        'invoice_no',
        'amount',
        'payment_type',
        'tds_amount',
        'discount_amount',
        'round_off',
        'check_number',
        'bank_name',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'organization_id',
        'invoice_id',
        'invoice_data',
        'org_bank_id',
        'customer_id',
        'invoice_no',
        'amount',
        'payment_type',
        'tds_amount',
        'discount_amount',
        'round_off',
        'check_number',
        'bank_name',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];

    public function bankInfo(){
        return $this->belongsTo(BankInfo::class,'org_bank_id','id');
    }

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    // public function bankTransactions()
    // {
    //     return $this->morphMany(BankTransaction::class, 'entity');
    // }
    public function credit()
    {
        return $this->belongsTo(CustomerCredit::class,'id','payment_receive_id');
    }
}
