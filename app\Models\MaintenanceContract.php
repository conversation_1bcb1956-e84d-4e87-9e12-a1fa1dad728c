<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MaintenanceContract extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'maintenance_contract';

    protected static $logName = 'Maintenance-Contract';

    public function getLogDescription(string $event): string
    {
        return "Maintenance Contract has been {$event} for <strong>{$this->hospital_name}</strong> by";
    }

    protected static $logAttributes = [
        'hospital_name',
        'address',
        'city',
        'contact_no',
        'contract_start_date',
        'contract_end_date',
        'maintenance_type',
        'time_period',
        'product_name',
        'price',
        'pm_date_1',
        'pm_date_2',
        'pm_date_3',
        'pm_date_4',
        'name',
        'company_name',
        'invoice_number',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'hospital_name',
        'address',
        'city',
        'contact_no',
        'contract_start_date',
        'contract_end_date',
        'maintenance_type',
        'time_period',
        'product_name',
        'price',
        'pm_date_1',
        'pm_date_2',
        'pm_date_3',
        'pm_date_4',
        'name',
        'company_name',
        'invoice_number',
        'contract_name',
        'start_date',
        'end_date',
        'amount',
        'status',
        'created_by',
        'updated_by'
    ];
}
