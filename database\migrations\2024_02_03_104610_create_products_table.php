<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('item_code');
            $table->string('batch');
            $table->string('serial_no');
            $table->longText('description')->nullable();
            $table->double('price', 8, 2);
            $table->double('rating', 8, 2)->nullable();
            $table->double('gst', 8, 2);
            $table->string('hsn_code');
            $table->string('part_no');
            $table->enum('category', ['Sales', 'Service']);
            $table->foreignId('company_id')->constrained( table: 'companies', indexName: 'id')->onDelete('cascade')->onUpdate('no action');
            $table->bigInteger('stock');
            $table->integer('min_qty');
            $table->integer('max_qty');
            $table->integer('status')->default(1);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
