<script setup>
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , useForm} from '@inertiajs/vue3';

const props = defineProps(['data', 'search']);
const form = useForm({});

const modalVisible = ref(false);


const handleSearchChange = (value) => {
    form.get(route('activity-logs', {search:value}),  {
        preserveState: true,
    });
};

</script>

<template>
    <Head title="Activity Logs" />

    <AdminLayout>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Activity Logs</h1>
            </div>
            <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200">
                <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                    <input id="search-field" @input="handleSearchChange($event.target.value)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
            </div>
        </div>
        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8" style="min-height:500px">
                <div class="p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">TYPE</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">EVENT</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">DESCRIPTION</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                    </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white" v-if="data.data && (data.data.length > 0)">
                        <!-- {{ data.links }} -->
                        <tr v-for="(activityData, index) in data.data" :key="activityData.id" class="">
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900">{{ activityData.log_name }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ activityData.event }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ activityData.properties }}</td>
<!--                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500"><pre>{{ JSON.stringify(activityData.properties, null, 2) }}</pre></td>-->

                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">
                                <div class="flex justify-start gap-4">
                                    <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <button type="button" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="6" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
                </div>
                <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
            </div>
            </div>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>
