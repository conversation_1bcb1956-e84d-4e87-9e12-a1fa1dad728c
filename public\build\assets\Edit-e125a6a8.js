import{K as b,h,o as p,c as _,a,u as t,w as m,F as x,Z as V,b as o,d as k,n as d,k as T,v as w,g as $,T as B,f as C}from"./app-2ecbacfc.js";import{_ as N,a as P}from"./AdminLayout-42d5bb92.js";import{_ as c}from"./InputError-aa79d601.js";import{_ as i}from"./InputLabel-f62a278f.js";import{P as U}from"./PrimaryButton-0d76f021.js";import{_ as y}from"./TextInput-73b24943.js";import{_ as f}from"./SearchableDropdown-6058bf5f.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},S=o("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Bank",-1),A={class:"border-b border-gray-900/10 pb-12"},D={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},j={class:"sm:col-span-3"},F={class:"relative mt-2"},M={class:"sm:col-span-3"},O={class:"sm:col-span-3"},z={class:"sm:col-span-3"},I={class:"relative mt-2"},K={class:"sm:col-span-6"},Z={class:"flex mt-6 items-center justify-between"},q={class:"ml-auto flex items-center justify-end gap-x-6"},G=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),H={key:0,class:"text-sm text-gray-600"},te={__name:"Edit",props:["data","paymentType","accounttype"],setup(u){const r=b().props.data[0],e=h({id:r.id,payment_type:r.payment_type,account_type:r.account_type,org_bank_id:r.org_bank_id,date:r.date,amount:r.amount,note:r.note}),g=(l,s)=>{e.payment_type=l,e.errors.payment_type=null},v=(l,s)=>{e.account_type=l,e.errors.account_type=null};return(l,s)=>(p(),_(x,null,[a(t(V),{title:"Bank Info"}),a(N,null,{default:m(()=>[o("div",E,[S,o("form",{onSubmit:s[6]||(s[6]=k(n=>t(e).patch(l.route("banktransaction.update")),["prevent"]))},[o("div",A,[o("div",D,[o("div",j,[a(i,{for:"payment_type",value:"Payment Type"}),o("div",F,[a(f,{options:u.paymentType,modelValue:t(e).payment_type,"onUpdate:modelValue":s[0]||(s[0]=n=>t(e).payment_type=n),onOnchange:g,class:d({"error rounded-md":t(e).errors.payment_type})},null,8,["options","modelValue","class"]),a(c,{class:"",message:t(e).errors.payment_type},null,8,["message"])])]),o("div",M,[a(i,{for:"date",value:"Payment Date"}),T(o("input",{"onUpdate:modelValue":s[1]||(s[1]=n=>t(e).date=n),class:d(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":t(e).errors["data.date"]}]),type:"date"},null,2),[[w,t(e).date]]),a(c,{class:"",message:t(e).errors.date},null,8,["message"])]),o("div",O,[a(i,{for:"amount",value:"Amount"}),a(y,{id:"amount",type:"text",onChange:s[2]||(s[2]=n=>l.clearError("data.amount")),modelValue:t(e).amount,"onUpdate:modelValue":s[3]||(s[3]=n=>t(e).amount=n),class:d({"error rounded-md":t(e).errors["data.amount"]})},null,8,["modelValue","class"]),a(c,{class:"",message:t(e).errors.amount},null,8,["message"])]),o("div",z,[a(i,{for:"account_type",value:"Account Type"}),o("div",I,[a(f,{options:u.accounttype,modelValue:t(e).account_type,"onUpdate:modelValue":s[4]||(s[4]=n=>t(e).account_type=n),onOnchange:v,class:d({"error rounded-md":t(e).errors.account_type})},null,8,["options","modelValue","class"])])]),o("div",K,[a(i,{for:"note",value:"Narration"}),a(y,{id:"note",type:"text",modelValue:t(e).note,"onUpdate:modelValue":s[5]||(s[5]=n=>t(e).note=n),class:d({"error rounded-md":t(e).errors["data.note"]})},null,8,["modelValue","class"])])])]),o("div",Z,[o("div",q,[a(P,{href:l.route("banktransaction.show",{id:t(r).org_bank_id})},{svg:m(()=>[G]),_:1},8,["href"]),a(U,{disabled:t(e).processing},{default:m(()=>[$("Save")]),_:1},8,["disabled"]),a(B,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:m(()=>[t(e).recentlySuccessful?(p(),_("p",H,"Saved.")):C("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{te as default};
