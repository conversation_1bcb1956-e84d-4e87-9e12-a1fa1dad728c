import{h as lt,r as _,o as i,c as r,a as n,u as y,w as u,F as B,Z as it,b as t,t as p,g as b,i as D,e as dt,f as k,n as v,k as rt,v as ct}from"./app-2ecbacfc.js";import{_ as mt,b as pt}from"./AdminLayout-42d5bb92.js";import{_ as ut}from"./CreateButton-1fa2a774.js";import{_ as F}from"./SecondaryButton-be49842d.js";import{D as _t}from"./DangerButton-3e1103de.js";import{P as gt}from"./PrimaryButton-0d76f021.js";import{_ as W}from"./TextInput-73b24943.js";import{_ as yt}from"./TextArea-00d3f05d.js";import{_ as T}from"./SearchableDropdown-6058bf5f.js";import{_ as ht}from"./SimpleDropdown-5dc59147.js";import{M as q}from"./Modal-54f7c77a.js";import{_ as ft}from"./Pagination-56593f88.js";import{_ as h}from"./InputLabel-f62a278f.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const vt={class:"animate-top"},xt={class:"sm:flex sm:items-center"},bt=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Customer Credits")],-1),kt={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},wt={class:"flex items-center space-x-4"},Ct={class:"text-lg font-semibold leading-7 text-gray-900"},Vt={class:"flex justify-end w-20"},zt={class:"flex justify-end"},Nt={class:"relative"},Mt={class:"h-screen"},At={class:"mt-8 flow-root"},$t={key:0,class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},Bt={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px"}},Tt={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ut=t("div",{class:"p-2"},[t("p",{class:"text-lg font-semibold text-gray-900"},"Credit")],-1),Ot={class:"min-w-full divide-y divide-gray-300"},Pt=t("thead",{class:"bg-gray-50"},[t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12"},[t("th",{scope:"col",class:"sm:col-span-2 py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"DATE"),t("th",{scope:"col",class:"sm:col-span-3 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"NARRATION"),t("th",{scope:"col",class:"sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"AMOUNT (₹)"),t("th",{scope:"col",class:"sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"UNUSED AMOUNT (₹)"),t("th",{scope:"col",class:"sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"BALANCE (₹)"),t("th",{scope:"col",class:"sm:col-span-1 px-3 py-3 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),Et={key:0,class:"divide-y divide-gray-300 bg-white"},St={class:"sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-900"},jt={class:"sm:col-span-3 whitespace-nowrap px-3 py-3 text-sm text-gray-500"},It={class:"sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Rt={class:"sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Lt={class:"sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Dt={class:"sm:col-span-1 whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Ft={class:"flex items-center justify-start gap-4"},Wt=["onClick"],qt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Ht=[qt],Zt=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Gt=["onClick"],Jt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1),Kt=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Qt=[Jt,Kt],Xt=["onClick"],Yt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),te=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),ee=[Yt,te],se={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},oe=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-6 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"DATE"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"NARRATION"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"AMOUNT (₹)")],-1),ae={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},ne={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},le={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},ie={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},de={key:1},re=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ce=[re],me={key:1,class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},pe=t("table",{class:"min-w-full divide-y divide-gray-300"},[t("tbody",null,[t("tr",{class:"bg-white text-center"},[t("td",{class:"whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")])])],-1),ue=[pe],_e={class:"p-6"},ge=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),ye={class:"mt-6 flex justify-end"},he={class:"p-6"},fe=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Advance Payment Edit",-1),ve={class:"border-b border-gray-900/10 pb-12"},xe={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},be={class:"sm:col-span-3"},ke={class:"relative mt-2"},we={class:"sm:col-span-3"},Ce={class:"relative mt-2"},Ve={class:"sm:col-span-3"},ze={key:0,class:"sm:col-span-3"},Ne={class:"sm:col-span-3"},Me={key:1,class:"sm:col-span-6"},Ae={class:"relative mt-2"},$e={class:"sm:col-span-6"},Be={class:"mt-6 px-4 flex justify-end"},Te={class:"w-36"},Ge={__name:"Credit",props:["data","customer","organization","customerId","bankinfo","organizations","paymentType"],setup(l){const w=l,c=lt({}),H=a=>{c.get(route("customers.credit",{id:w.customerId,organization_id:a}),{preserveState:!0})},U=_(""),Z=(a,s)=>{U.value=a,H(U.value)},z=_(!1),G=_(null),J=a=>{G.value=a,z.value=!0},O=()=>{z.value=!1},K=()=>{},C=a=>{let s=a.toFixed(2).toString(),[e,d]=s.split("."),g=e.substring(e.length-3),x=e.substring(0,e.length-3);return x!==""&&(g=","+g),`${x.replace(/\B(?=(\d{2})+(?!\d))/g,",")+g}.${d}`},P=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},Q=a=>{let s=0;for(let d=0;d<=a;d++){const g=w.data.data[d];s+=parseFloat(g.unused_amount)}let e=s>=0?"cr":"dr";return C(Math.abs(s))+" "+e},m=_([]),E=_([]),V=_(!1),X=_("custom2"),N=_(""),o={id:"",organization_id:"",customer_id:"",org_bank_id:"",invoice_id:"",invoice_no:"",payment_type:"",amount:"",check_number:"",date:"",note:""},Y=(a,s)=>{o.organization_id=a,c.errors["data.organization_id"]=null},tt=(a,s)=>{o.payment_type=a,N.value=s,c.errors["data.payment_type"]=null},et=(a,s)=>{o.org_bank_id=a,c.errors["data.org_bank_id"]=null},st=a=>{const s=w.data.data.find(d=>d.id===a);m.value=s,o.id=a,o.organization_id=m.value.organization_id,o.customer_id=m.value.customer_id,o.org_bank_id=m.value.paymentreceive.org_bank_id,o.invoice_id=m.value.paymentreceive.invoice_id,o.payment_type=m.value.paymentreceive.payment_type,o.amount=m.value.amount,o.check_number=m.value.paymentreceive.check_number,o.date=m.value.date,o.note=m.value.paymentreceive.note;const e=w.bankinfo.filter(d=>d.organization_id===m.value.organization_id);E.value=e,V.value=!0},S=()=>{V.value=!1},ot=()=>{c.post(route("customers.advancepayment",{data:o}),{onSuccess:()=>{c.reset(),V.value=!1},onError:a=>{}})},M=_([]),at=a=>{M.value[a]=!M.value[a]};return(a,s)=>(i(),r(B,null,[n(y(it),{title:"Customers"}),n(mt,null,{default:u(()=>[t("div",vt,[t("div",xt,[bt,t("div",kt,[t("div",wt,[t("div",null,[t("h1",Ct,p(l.customer.customer_name)+" - "+p(l.customer.city),1)]),t("div",Vt,[n(ut,{href:a.route("customers.index")},{default:u(()=>[b(" Back ")]),_:1},8,["href"])]),t("div",zt,[t("div",Nt,[n(ht,{options:l.organization,onOnchange:Z},null,8,["options"])])])])])]),t("div",Mt,[t("div",At,[l.data.data&&l.data.data.length>0?(i(),r("div",$t,[t("div",Bt,[t("div",Tt,[Ut,t("table",Ot,[Pt,l.data.data&&l.data.data.length>0?(i(),r("tbody",Et,[(i(!0),r(B,null,D(l.data.data,(e,d)=>{var g,x,A,$,j,I,R,L;return i(),r("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12",key:e.id},[t("td",St,p(P(e.date)??"-"),1),t("td",jt,p((x=(g=e.paymentreceive)==null?void 0:g.bank_info)!=null&&x.bank_name?($=(A=e.paymentreceive)==null?void 0:A.bank_info)==null?void 0:$.bank_name:"Cash")+":"+p((I=(j=e.paymentreceive)==null?void 0:j.bank_info)!=null&&I.account_number?(L=(R=e.paymentreceive)==null?void 0:R.bank_info)==null?void 0:L.account_number:""),1),t("td",It,p(C(e.amount)),1),t("td",Rt,p(C(e.unused_amount)),1),t("td",Lt,p(Q(d)),1),t("td",Dt,[t("div",Ft,[t("button",{onClick:f=>at(d)},Ht,8,Wt),n(pt,{align:"right",width:"48"},{trigger:u(()=>[Zt]),content:u(()=>[e.credit_detail.length==0?(i(),r("button",{key:0,type:"button",onClick:f=>st(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Qt,8,Gt)):k("",!0),t("button",{type:"button",onClick:f=>J(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ee,8,Xt)]),_:2},1024)])]),M.value[d]&&e.credit_detail.length>0?(i(),r("div",se,[oe,t("tbody",ae,[(i(!0),r(B,null,D(e.credit_detail,(f,nt)=>(i(),r("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-6",key:nt},[t("td",ne,p(P(f.date)??"-"),1),t("td",le,p(f.invoice.invoice_no??"-"),1),t("td",ie,p(C(f.amount)??"-"),1)]))),128))])])):k("",!0)])}),128))])):(i(),r("tbody",de,ce))])]),l.data.data&&l.data.data.length>0?(i(),dt(ft,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):k("",!0)])])):(i(),r("div",me,ue))])])]),n(q,{show:z.value,onClose:O},{default:u(()=>[t("div",_e,[ge,t("div",ye,[n(F,{onClick:O},{default:u(()=>[b(" Cancel ")]),_:1}),n(_t,{class:"ml-3",onClick:K},{default:u(()=>[b(" Delete ")]),_:1})])])]),_:1},8,["show"]),n(q,{show:V.value,onClose:S,maxWidth:X.value},{default:u(()=>[t("div",he,[fe,t("div",ve,[t("div",xe,[t("div",be,[n(h,{for:"role_id",value:"Organization"}),t("div",ke,[n(T,{options:l.organizations,modelValue:o.organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>o.organization_id=e),onOnchange:Y,class:v({"error rounded-md":y(c).errors["data.organization_id"]})},null,8,["options","modelValue","class"])])]),t("div",we,[n(h,{for:"role_id",value:"Payment Type"}),t("div",Ce,[n(T,{options:l.paymentType,modelValue:o.payment_type,"onUpdate:modelValue":s[1]||(s[1]=e=>o.payment_type=e),onOnchange:tt,class:v({"error rounded-md":y(c).errors["data.payment_type"]})},null,8,["options","modelValue","class"])])]),t("div",Ve,[n(h,{for:"amount",value:"Amount"}),n(W,{id:"amount",type:"text",onChange:s[2]||(s[2]=e=>a.clearError("data.amount")),modelValue:o.amount,"onUpdate:modelValue":s[3]||(s[3]=e=>o.amount=e),class:v({"error rounded-md":y(c).errors["data.amount"]})},null,8,["modelValue","class"])]),N.value=="Cheque"?(i(),r("div",ze,[n(h,{for:"check_number",value:"Cheque Number"}),n(W,{id:"check_number",type:"text",modelValue:o.check_number,"onUpdate:modelValue":s[4]||(s[4]=e=>o.check_number=e),class:v({"error rounded-md":y(c).errors["data.check_number"]})},null,8,["modelValue","class"])])):k("",!0),t("div",Ne,[n(h,{for:"date",value:"Payment Date"}),rt(t("input",{"onUpdate:modelValue":s[5]||(s[5]=e=>o.date=e),class:v(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":y(c).errors["data.date"]}]),type:"date",onChange:s[6]||(s[6]=e=>a.clearError("data.date"))},null,34),[[ct,o.date]])]),N.value!="Cash"?(i(),r("div",Me,[n(h,{for:"org_bank_id",value:"Bank"}),t("div",Ae,[n(T,{options:E.value,modelValue:o.org_bank_id,"onUpdate:modelValue":s[7]||(s[7]=e=>o.org_bank_id=e),onOnchange:et,class:v({"error rounded-md":y(c).errors["data.org_bank_id"]})},null,8,["options","modelValue","class"])])])):k("",!0),t("div",$e,[n(h,{for:"note",value:"Note"}),n(yt,{id:"note",type:"text",rows:2,modelValue:o.note,"onUpdate:modelValue":s[8]||(s[8]=e=>o.note=e)},null,8,["modelValue"])])])]),t("div",Be,[n(F,{onClick:S},{default:u(()=>[b(" Cancel ")]),_:1}),t("div",Te,[n(gt,{class:"ml-3 w-20",onClick:ot},{default:u(()=>[b(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}};export{Ge as default};
