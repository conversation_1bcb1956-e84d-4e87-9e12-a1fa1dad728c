<script setup>
import { ref, onMounted, watch, computed  } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';

const props = defineProps(['data', 'customers', 'organization', 'organizationId',  'customerId', 'creditdata']);

const form = useForm({});
const companyName = ref('customer tranasction');
// const from_date = ref('');
// const to_date = ref('');
const customerName = ref('ALL CUSTOMERS');
const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const searchValue = ref('');
const from_date = ref(props.fromDate || '');
const to_date = ref(props.toDate || '');


watch([organizationId, customerId, from_date, to_date], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
        from_date: from_date.value,
        to_date: to_date.value
    });
});


const handleSearchChange = (value, organizationId, customerId, from_date, to_date) => {
    searchValue.value = value;
    form.get(route('customer-transaction.report', { search: value, organization_id: organizationId, customer_id: customerId, from_date: from_date, to_date: to_date }), {
        preserveState: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

const setCustomer = (id, name) => {
    customerId.value = id;
    customerName.value = name;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, from_date.value, to_date.value);
};



const customerReport = computed(() => {
    // Filter the report data based on selected filters
    let filteredData = props.data.filter((transaction) => {
        const transactionDate = new Date(transaction.date);
        const fromDate = new Date(from_date.value);
        const toDate = new Date(to_date.value);

        if (from_date.value && to_date.value) {
            return transactionDate >= fromDate && transactionDate <= toDate;
        }

        const isAfterFromDate = !from_date.value || transactionDate >= fromDate;
        const isBeforeToDate = !to_date.value || transactionDate <= toDate;
        return isAfterFromDate && isBeforeToDate;
    });

    if (organizationId.value) {
        filteredData = filteredData.filter(transaction => transaction.organization_id === organizationId.value);
    }
    if (customerId.value) {
        filteredData = filteredData.filter(transaction => transaction.customer_id === customerId.value);
    }
    return filteredData;
});

const formatAmountNew = (amount) => {
    if (amount == null) return '-';
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};


const handleDateChange = () => {
};

const exportXls = () => {
    const xlsName = companyName.value.replace(/\s+/g, '_');
    const params = {
        customer_id: props.customerId || '',
        organization_id: organizationId.value || '',
        from_date: from_date.value || '',
        to_date: to_date.value || ''
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export/customer-report?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${xlsName}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};

// const formatAmountNew = (amount) => {
//     let amountStr = amount.toFixed(2).toString();
//     let [integerPart, decimalPart] = amountStr.split('.');
//      let lastThree = integerPart.substring(integerPart.length - 3);
//     let otherNumbers = integerPart.substring(0, integerPart.length - 3);
//     if (otherNumbers !== '') {
//         lastThree = ',' + lastThree;
//     }
//     let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
//     let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
//     return formattedAmount;
// };

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const handleStartDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

const handleToDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

// const totalUnusedAmount = ref('');
// totalUnusedAmount.value = props.creditdata.reduce((sum, item) => sum + item.unused_amount, 0);

</script>

<template>
    <Head title="Customers Pending Amount"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Customers Pending Amount</h1>
                </div>
                <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex items-center space-x-4">
                        <div class="flex ml-6">
                        <CreateButton :href="route('reports')">
                                Back
                        </CreateButton>
                    </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="inline-flex items-center space-x-4 justify-end w-full ">
                    <button @click="exportXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center">
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>

                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Customer Name" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="customers"
                          v-model="customerId"
                        @onchange="setCustomer"
                        />
                    </div>
                </div>

                <div class="sm:col-span-3">
                    <InputLabel for="date" value="From Date" />
                    <input
                        v-model="from_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                            @change="handleStartDate"
                        :class="{ 'error rounded-md': form.errors.from_date }"
                    />
                 </div>

                 <div class="sm:col-span-3">
                    <InputLabel for="date" value="To Date" />
                    <input
                        v-model="to_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleToDate"
                        :class="{ 'error rounded-md': form.errors.to_date }"
                    />
                 </div>
            </div>
        </div>

        <div class="mt-8 overflow-x-auto sm:rounded-lg" >
            <div class="shadow sm:rounded-lg">
                <div class="p-2 flex justify-end text-base font-semibold leading-6 text-gray-900">
                </div>
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                CUSTOMER NAME
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                BILL NO
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                DEBIT (₹)
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                CREDIT (₹)
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                DATE
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                BALANCE (₹)
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(transaction, index) in customerReport" :key="transaction.id">
                            <td class="px-4 py-2.5 font-medium text-gray-900 min-w-32">
                                {{ transaction.customer ? transaction.customer.customer_name : '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ transaction.invoice_no ? transaction.invoice_no : '-' }}
                            </td>


                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatAmountNew(transaction.total_amount) }}
                            </td>

                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatAmountNew(transaction.paid_amount) }}
                            </td>

                            <td class="px-4 py-2.5 min-w-44">
                                {{ formatDate(transaction.date) ?? '-' }}
                            </td>

                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatAmountNew(transaction.pending_amount) }}
                            </td>
                        </tr>
                        <tr v-if="customerReport.length === 0" class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </AdminLayout>

</template>
