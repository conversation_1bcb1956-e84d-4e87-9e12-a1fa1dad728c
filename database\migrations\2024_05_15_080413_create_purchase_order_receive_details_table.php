<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_order_receive_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_receive_id')->nullable()->constrained( table:'purchase_order_receives', indexName: 'pord_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('purchase_order_detail_id')->nullable();
            $table->integer('organization_id');
            $table->foreignId('product_id')->constrained( table:'products', indexName: 'pordp_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('receive_qty');
            $table->integer('stock');
            $table->double('purchase_price', 16, 2);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_receive_details');
    }
};
