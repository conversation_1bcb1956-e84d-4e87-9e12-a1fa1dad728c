<?php

namespace App\Http\Controllers;

use App\Models\ProductActivityLog;
use App\Models\ProductLog;
use App\Traits\QueryTrait;
use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Product;
use App\Models\Company;
use App\Models\Organization;
use App\Models\SerialNumbers;
use App\Models\PurchaseOrderReceiveDetails;
use App\Models\InvoiceDetail;
use App\Http\Requests\ProductStoreRequest;
use App\Http\Requests\stockAddRequest;
use App\Models\ChallanDetail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;
use Config;
use Carbon\Carbon;

class ProductController extends Controller
{
    use QueryTrait;
    use CommonTrait;

    public function __construct()
    {
        $this->middleware('permission:List Products')->only(['show']);
        $this->middleware('permission:Create Products')->only(['create', 'store']);
        $this->middleware('permission:Edit Products')->only(['edit', 'update']);
        $this->middleware('permission:Delete Products')->only('destroy');
        $this->middleware('permission:Product Activation')->only('activation');
        $this->middleware('permission:Product History')->only('history');
    }

    public function show(Request $request, $id)
    {
        $query = Product::where('company_id', $id);
        $searchableFields = ['name', 'item_code', 'hsn_code', 'gst', 'price', 'category'];
        $this->searchAndSort($query, $request, $searchableFields);
        $data = $this->getResult($query);
        $companyInfo = Company::find($id);

        $data->withQueryString()->links();
        $permissions = [
            'canCreateProducts'      => auth()->user()->can('Create Products'),
            'canEditProducts'        => auth()->user()->can('Edit Products'),
            'canDeleteProducts'      => auth()->user()->can('Delete Products'),
        ];

        return Inertia::render('Product/List', compact('data', 'companyInfo', 'permissions'));
    }

    public function index(Request $request)
    {
        //
    }

    public function create(Request $request)
    {
        $company_id = $request->id;
        $category = Config::get('constants.productCategoryList');
        $filepath = Config::get('constants.uploadFilePath.productImages');
        return Inertia::render('Product/Add', compact('company_id', 'category', 'filepath'));
    }

    public function store(ProductStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $files = $request->file();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            unset($data['image']);
            if(isset($request->id)){
                $productInfo = Product::find($request->id);
                $productInfo->update($data);
                if($files){
                    $this->uploadPhoto($files, $productInfo);
                }
                DB::commit();
                return Redirect::to("/products/$request->company_id")->with('success', 'Product Updated Successfully');
            } else {
                $productInfo = Product::create($data);
                if($files){
                    $this->uploadPhoto($files, $productInfo);
                }
                DB::commit();
                return Redirect::to("/products/$request->company_id")->with('success', 'Product Added Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Product::where('id', $id)->get();
        $category = Config::get('constants.productCategoryList');
        $filepath = Config::get('constants.uploadFilePath.productImages');
        return Inertia::render('Product/Edit', compact('data', 'category', 'filepath'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $product = Product::find($id);
            if ($product->serialNumbers()->count() > 0 || $product->purchaseDetails()->count() > 0 || $product->invoiceDetails()->count() > 0 || $product->orderDetails()->count() > 0 || $product->quotationDetails()->count() > 0 ) {
                return Redirect::back()->with('error', 'Product cannot be deleted');
            }
            $product->delete();
            // Product::addProductLog($product, $product->toArray(), $id, 'deleted');
            DB::commit();
            return Redirect::back()->with('success','Product Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function activation(Request $request)
    {
        DB::beginTransaction();
        try {
            $product = Product::find($request->id);
            $product->update(['status' => $request->status]);
            DB::commit();
            return Redirect::back()->with('success','Product Status Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function stockAdd(Request $request)
    {
        $product = Product::find($request->id);
        $page = $request->page;
        $organization = Organization::select('id', 'name')->get();
        return Inertia::render('Product/StockAdd', compact('product', 'organization', 'page'));
    }

    public function stockedit(Request $request)
    {
        $product = Product::find($request->id);
        $page = $request->page;
        $data = Product::where('id', $request->id)->with('serialNumbers')->get()->toArray();
        $organization = Organization::select('id', 'name')->get();
        return Inertia::render('Product/StockEditNew', compact('product', 'organization','data', 'page'));
    }

    public function saveStock(stockAddRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            if(isset($data['product_id'])){
                foreach($data['stockItems'] as $stock){
                    $stock['updated_by'] = auth()->id();
                    if($stock['batch'] != ''){ //to do check if null
                        $stock['unique_id'] = $stock['batch'];
                    } else {
                        $stock['unique_id'] = "(₹) " .$stock['purchase_price'];
                    }
                    $serialNumbers = SerialNumbers::findOrFail($stock['serial_number_id']);
                    SerialNumbers::find($stock['serial_number_id'])->update([
                        'organization_id'   => $stock['organization_id'],
                        'batch'             => $stock['batch'],
                        'expiry_date'       => $stock['expiry_date'],
                        'mrp'               => $stock['mrp'],
                        'purchase_price'    => $stock['purchase_price'],
                        'receive_qty'       => $stock['receive_qty'],
                        'unique_id'         => $stock['unique_id'],
                        'updated_by'        => $stock['updated_by']
                    ]);
                    Product::addProductLog($serialNumbers, $stock, $stock['product_id'], $stock['organization_id'], 'edit-stock');
                }
                DB::commit();
                return Redirect::to($request->page)->withInput()->with('success', 'Stock updated Successfully');
                // return Redirect::to("/products/$request->company_id")->with('success', 'Stock updated Successfully');
            } else {
                foreach($data['stockItems'] as $stock){
                    $stock['created_by'] = $stock['updated_by'] = auth()->id();
                    if($stock['batch'] != ''){ //to do check if null
                        $stock['unique_id'] = $stock['batch'];
                    } else {
                        $stock['unique_id'] = "(₹) " .$stock['purchase_price'];
                    }
                    $stockAdd = SerialNumbers::create($stock);
                    Product::addProductLog($stockAdd, $stock, $stock['product_id'], $stock['organization_id'], 'add-stock');
                }
                DB::commit();
                return Redirect::to($request->page)->withInput()->with('success', 'Stock Added Successfully');
                // return Redirect::to("/products/$request->company_id")->with('success', 'Stock Added Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function combineDateTime($date, $time){
        $datePart = Carbon::parse($date)->toDateString(); // Extract date
        $timePart =  $time ? Carbon::parse($time)->toTimeString() : '00:00:00';
        return Carbon::createFromFormat('Y-m-d H:i:s', "$datePart $timePart"); // Combine
    }

    // public function history(Request $request, $id)
    // {
    //     $page = $request->page;
    //     $product = Product::with('company')->where('id', $id)->get();
    //     $organizationId = $request->input('organization_id');
    //     $financialYear = $request->input('financial_year');
    //     $financialYears = $this->getFinancialYears();

    //     // dd($financialYears);

    //     $serialNumbers = SerialNumbers::with('purchaseOrderReceiveDetails.purchaseOrderReceives.purchaseOrder.company')->where('product_id', $id);

    //     if($organizationId) {
    //         $serialNumbers->where('organization_id', $organizationId);
    //     }

    //     $serialNumbers = $serialNumbers->get();

    //     $invoiceDetails = InvoiceDetail::whereHas('invoice' , function ($query1) use ($organizationId) {
    //         if ($organizationId) {
    //             $query1->where('organization_id', $organizationId);
    //         }
    //         $query1->where('entity_type', 'invoice');
    //     })->with(['invoice.customers', 'serialnumbers', 'creditnote'])->where('product_id', $id)->get();

    //     $challanDetails = ChallanDetail::whereHas('challan' , function ($query1) use ($organizationId) {
    //         if ($organizationId) {
    //             $query1->where('organization_id', $organizationId);
    //         }
    //     })->with(['challan.customers', 'viewserialnumbers'])->where('product_id', $id)->get();

    //     $mergedRecords = $serialNumbers->merge($invoiceDetails)->merge($challanDetails);

    //     $sortedRecords = $mergedRecords->sortByDesc(function ($record) {
    //         // Check if the record is from invoiceDetails or challanDetails
    //         if ($record instanceof InvoiceDetail) {
    //             if($record->is_receive == 'yes'){
    //                 return $this->combineDateTime($record->creditnote->date, $record->creditnote->updated_at);  // assuming invoice has a 'date' field
    //             } else {
    //                 return $this->combineDateTime($record->invoice->date, $record->invoice->updated_at);  // assuming invoice has a 'date' field
    //             }
    //         } elseif ($record instanceof ChallanDetail) {
    //             // Use challan date for sorting
    //             // return $this->combineDateTime($record->challan->date, $record->challan->updated_at);  // assuming challan has a 'date' field
    //             if($record->is_receive == 'yes'){
    //                 return $record->created_at;  // assuming invoice has a 'date' field
    //             } else {
    //                 return $this->combineDateTime($record->challan->date, $record->challan->updated_at);  // assuming invoice has a 'date' field
    //             }
    //         } else {
    //             if($record->purchaseOrderReceiveDetails){
    //                 $purchaseOrderReceiveDetail = $record->purchaseOrderReceiveDetails;
    //                 if($purchaseOrderReceiveDetail->purchaseOrderReceives->customer_invoice_date){
    //                     return $this->combineDateTime($purchaseOrderReceiveDetail->purchaseOrderReceives->customer_invoice_date, null);
    //                 } else {
    //                     return $record->created_at;
    //                 }
    //             } else {
    //                 return $record->created_at;
    //             }
    //         }
    //     })->values()->all();

    //     $balanceStock = [];

    //     foreach ($sortedRecords as $record) {
    //         $balanceStock[] = $record->toArray();
    //     }

    //     $organization  = Organization::select('id', 'name')->get();
    //     $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
    //     $organization->prepend($allOption);
    //     // dd($organization);
    //     return Inertia::render('Product/History', compact('balanceStock', 'page', 'product', 'organization', 'financialYears'));
    // }

    public function history(Request $request, $id)
    {
        $page = $request->page;
        $product = Product::with('company')->where('id', $id)->get();
        $organizationId = $request->input('organization_id');
        $financialYear = $request->input('financial_year');
        // dd($financialYear);
        $financialYears = $this->getFinancialYears();

        // Parse financial year range
        $startDate = null;
        $endDate = null;
        if (!$financialYear && count($financialYears)) {
            $financialYear = $financialYears[0]['id']; // assuming first is current year, else write logic to detect current range
        }
        if($financialYear) {
            [$startDateStr, $endDateStr] = explode(' to ', $financialYear);
            $startDate = Carbon::createFromFormat('d-m-Y', $startDateStr)->startOfDay();
            $endDate = Carbon::createFromFormat('d-m-Y', $endDateStr)->endOfDay();
        }

        // === 1. Get Serial Numbers (Receive)
        $serialNumbersQuery = SerialNumbers::with('purchaseOrderReceiveDetails.purchaseOrderReceives.purchaseOrder.company')
            ->where('product_id', $id);
                if ($organizationId) {
                    $serialNumbersQuery->where('organization_id', $organizationId);
                }
        $serialNumbers = $serialNumbersQuery->get();

        // === 2. Invoices (Issue)
        $invoiceDetails = InvoiceDetail::whereHas('invoice', function ($q) use ($organizationId) {
            if ($organizationId) $q->where('organization_id', $organizationId);
            $q->where('entity_type', 'invoice');
        })->with(['invoice.customers', 'serialnumbers', 'creditnote'])
        ->where('product_id', $id)
        ->get();

        // === 3. Challans (Issue)
        $challanDetails = ChallanDetail::whereHas('challan', function ($q) use ($organizationId) {
            if ($organizationId) $q->where('organization_id', $organizationId);
        })->with(['challan.customers', 'viewserialnumbers'])
        ->where('product_id', $id)
        ->get();

        // === 4. Filter for financial year if selected
        // dd($startDate);
        if ($startDate && $endDate) {
            $serialNumbers = $serialNumbers->filter(function ($record) use ($startDate, $endDate) {
                // return $record->created_at >= $startDate && $record->created_at <= $endDate;
                // Safely access customer_invoice_date
                $customerInvoiceDate = optional($record->purchaseOrderReceiveDetails)
                ->purchaseOrderReceives
                ->customer_invoice_date ?? null;
                $dateToCompare = $customerInvoiceDate ? Carbon::parse($customerInvoiceDate) : $record->created_at;
                return $dateToCompare >= $startDate && $dateToCompare <= $endDate;
            });

            $invoiceDetails = $invoiceDetails->filter(function ($record) use ($startDate, $endDate) {
                $rawDate = $record->is_receive == 'yes'
                    ? $record->creditnote->date ?? $record->created_at
                    : $record->invoice->date ?? $record->created_at;
                // return $date >= $startDate && $date <= $endDate;
                $date = Carbon::parse($rawDate);
                return $date->between($startDate, $endDate);
            });

            $challanDetails = $challanDetails->filter(function ($record) use ($startDate, $endDate) {
                $rawDate = $record->is_receive == 'yes' ? $record->created_at : ($record->challan->date ?? $record->created_at);
                // return $date >= $startDate && $date <= $endDate;
                $date = Carbon::parse($rawDate);
                return $date->between($startDate, $endDate);
            });
        }

        // === 5. Calculate opening balance
        $openingBalance = 0;

        // Fetch previous serial numbers, invoices and challans before start date
        // dd($startDate);
        if ($startDate) {
            // $prevSerialNumbers = SerialNumbers::where('product_id', $id)
            //     ->when($organizationId, fn($q) => $q->where('organization_id', $organizationId))
            //     ->where('created_at', '<', $startDate)
            //     ->get();

            $prevSerialNumbers = SerialNumbers::with('purchaseOrderReceiveDetails.purchaseOrderReceives')
            ->where('product_id', $id)
            ->when($organizationId, fn($q) => $q->where('organization_id', $organizationId))
            ->get()
            ->filter(function ($record) use ($startDate) {
            $customerInvoiceDate = optional($record->purchaseOrderReceiveDetails)
                ->purchaseOrderReceives
                ->customer_invoice_date ?? null;

            $dateToCompare = $customerInvoiceDate ? Carbon::parse($customerInvoiceDate) : $record->created_at;

            return $dateToCompare < $startDate;
            })
            ->values();

            $prevInvoices = InvoiceDetail::with('creditnote')->where('product_id', $id)
                ->whereHas('invoice', function ($q) use ($organizationId, $startDate) {
                    if ($organizationId) $q->where('organization_id', $organizationId);
                    $q->where('entity_type', 'invoice');
                    $q->where('date', '<', $startDate);
                })->with('invoice')->get();

            $prevChallans = ChallanDetail::where('product_id', $id)
                ->whereHas('challan', function ($q) use ($organizationId, $startDate) {
                    if ($organizationId) $q->where('organization_id', $organizationId);
                    $q->where('date', '<', $startDate);
                })->with('challan')->get();


            foreach ($prevSerialNumbers as $s) {
                $openingBalance += $s->receive_qty ?? 0;
            }

            foreach ($prevInvoices as $i) {
                if ($i->is_receive == 'yes') {
                    $date = $i->creditnote ? Carbon::parse($i->creditnote->date) : $i->invoice->date;
                    if ($date < $startDate) {
                        $openingBalance += $i->qty;
                    }
                } else {
                    $openingBalance -= $i->qty;
                }
            }

            foreach ($prevChallans as $c) {
                if ($c->is_receive == 'yes') {
                    $date = Carbon::parse($c->created_at);
                    if ($date <= $startDate) {
                        $openingBalance += $c->qty;
                    }
                } else {
                    $openingBalance -= $c->qty;
                }
            }
        }

        // $mergedRecords = $serialNumbers->merge($invoiceDetails)->merge($challanDetails);
        $mergedRecords = $serialNumbers->concat($invoiceDetails)->concat($challanDetails);
        // dd($mergedRecords);

        $sortedRecords = $mergedRecords->sortByDesc(function ($record) {
            if ($record instanceof InvoiceDetail) {
                if($record->is_receive == 'yes'){
                    return $this->combineDateTime($record->creditnote->date, $record->creditnote->updated_at);
                } else {
                    return $this->combineDateTime($record->invoice->date, $record->invoice->updated_at);
                }
            } elseif ($record instanceof ChallanDetail) {
                if($record->is_receive == 'yes'){
                    return $record->created_at;
                } else {
                    return $this->combineDateTime($record->challan->date, $record->challan->updated_at);
                }
            } else {
                if($record->purchaseOrderReceiveDetails){
                    $purchaseOrderReceiveDetail = $record->purchaseOrderReceiveDetails;
                    if($purchaseOrderReceiveDetail->purchaseOrderReceives->customer_invoice_date){
                        return $this->combineDateTime($purchaseOrderReceiveDetail->purchaseOrderReceives->customer_invoice_date, null);
                    } else {
                        return $record->created_at;
                    }
                } else {
                    return $record->created_at;
                }
            }
        })->values()->all();

        $organization  = Organization::select('id', 'name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);

        // Send to Vue
        return Inertia::render('Product/History', [
            'balanceStock' => collect($sortedRecords)->values(),
            'openingBalance' => $openingBalance,
            'page' => $page,
            'product' => $product,
            'organization' => $organization,
            'financialYears' => $financialYears,
            'selectedFinancialYear' => $financialYear,
        ]);
    }

    public function logs(Request $request, $productId)
    {
        $productInfo = Product::with('Company')->find($productId);

        if ($productInfo) {
            $query = ProductActivityLog::where('product_id', $productId);
            $searchableFields = ['log_name', 'action', 'description', 'created_at'];
            $this->searchAndSort($query, $request, $searchableFields);
            $data = $this->getResult($query);
            $data->withQueryString()->links();
        }else{
            $data = [];
        }

        return Inertia::render('Product/Logs', compact('data', 'productInfo'));
    }

    private function uploadPhoto($files, $productInfo)
    {
        $filePath = Config::get('constants.uploadFilePath.productImages');

        if ($files) {
            foreach ($files as $key => $file) {
                $originalName = $file->getClientOriginalName();
                $fileName = str_replace(' ', '-', $originalName);
                $path = $filePath['default'];
                if(!is_dir($path)) {
                    mkdir($path, 755, true);
                }
                if($productInfo->image) {
                    $existingImagePath = $path . '/' . $productInfo->image;
                    if (file_exists($existingImagePath)) {
                        unlink($existingImagePath);
                    }
                }
                $upload_success = $file->move($path, $fileName);
                if($upload_success){
                    $productInfo->update([$key => $fileName]);
                }
            }
        }
    }

    public function removeProductImage($id)
    {
        $productInfo = Product::findOrFail($id);

        if ($productInfo->image) {
            $filePath = Config::get('constants.uploadFilePath.productImages');
            $imagePath = $filePath['default'] . '/' . $productInfo->image;

            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            $productInfo->update(['image' => null]);
        }

    }

}
