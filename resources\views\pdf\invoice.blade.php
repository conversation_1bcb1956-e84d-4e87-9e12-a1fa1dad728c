<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <title>Invoice</title>

</head>
<body>
    <div id="">
        @if($data[0]->organization->id == 3)
        <table style="width: 100%; margin-bottom: 10px;">
            <tr>
                <td style="text-align: left; width: 80px; vertical-align: middle;"> <!-- Ensures vertical alignment -->
                    <img style="width: 80px; height: 80px; margin-bottom: 0px;" 
                         src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}"
                         alt="logo">
                </td>
                <td style="text-align: center; vertical-align: middle;"> <!-- Aligns text and image -->
                    <b style="font-size: 20px; margin-left: -10px;">{{ $data[0]->customers->customer_type }} Invoice</b>
                </td>
                <td style="width: 50px;"></td>
            </tr>
        </table>
        
        @endif

        @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
            <table style="width: 100%; text-align: center; margin-bottom: 10px;">
                <tr>
                    <table style="width: 100%; margin-bottom: 0px;"> 
                        <tr>
                            <td style="text-align: center;">
                                <img style="width: 100%; height: 45px; margin-bottom: 0px; display: block;"
                                     src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}"
                                     alt="logo">
                            </td>
                        </tr>
                    </table>
                    <div style="text-align: center; margin-top: -5px;">
                        <b style="font-size: 20px;">{{ $data[0]->customers->customer_type }} Invoice</b>
                    </div>
        @endif
    </div>
    <table style="width:100%; border-collapse: collapse; margin-top: 20px;">
        <tr>
            <td style="text-align:left; width: 300px; border: 1px solid black; padding: 10px;">
                <p><strong>{{ $data[0]->organization->name }}</strong></p>
                <p>{{ $data[0]->organization->address_line_1 }}</p>
                <p>{{ $data[0]->organization->address_line_2 }}</p>
                <p>{{ $data[0]->organization->city }}</p>
                <p><strong>Phone : </strong>{{ $data[0]->organization->contact_no }}</p>
                <p><strong>Email : </strong>{{ $data[0]->organization->email }}</p>
                <p><strong>GST : </strong>{{ $data[0]->organization->gst_no }}</p>
            </td>
            <td style="text-align:left; width: 320px; border: 1px solid black; vertical-align: top; padding: 10px;">
                <table>
                    <tr>
                        <td>
                            <p><span class="label"><strong>Invoice Number: </strong></span>{{ $data[0]->invoice_no }}</p>
                            <p><span class="label"><strong>Invoice Date: </strong></span>{{ date('d-m-Y', strtotime($data[0]->date)) }}</p>
                            <p><strong>{{ $data[0]->customers->customer_name }}</strong></p>
                            <p>{{ $data[0]->customers->address }}</p>
                            <p><span class="label"><strong>Phone: </strong></span>{{ $data[0]->customers->contact_no ?? '-' }}</p>
                            <p><span class="label"><strong>Email: </strong></span>{{ $data[0]->customers->email ?? '-' }}</p>
                            <p><span class="label"><strong>GST: </strong></span>{{ $data[0]->customers->gst_no ?? '-' }}</p>
                            @if($data[0]->transport != null)
                                <p><span class="label"><strong>Transport: </strong></span>{{ $data[0]->transport ?? '-' }}</p>
                            @endif
                            @if($data[0]->dispatch != null)
                                <p><span class="label"><strong>Dispatch: </strong></span>{{ $data[0]->dispatch ?? '-' }}</p>
                            @endif
                        </td>
                  </tr>
                </table>
            </td>
        </tr>
    </table>
    <div class="" id="pdf-content">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
            <thead>
                <tr>
                    <th>SN</th>
                    @if($data[0]->category =='Service')
                        <th>PART NO</th>
                    @endif
                    @if($data[0]->category =='Sales')
                        <th>CODE</th>
                    @endif
                    <th>PRODUCT NAME</th>
                    <th>HSN</th>
                    <th>QTY</th>
                    <th>BATCH</th>
                    <th>EXP</th>
                    <th>MRP</th>
                    <th>RATE</th>
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <th>CGST(%)</th>
                        <th>SGST(%)</th>
                    @endif
                    @if($data[0]->customers->gst_type =='IGST')
                        <th>IGST(%)</th>
                    @endif
                    <th>DIS.</th>
                    <th>AMOUNT</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data[0]->invoiceDetail as $index => $poData)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    @if($data[0]->category =='Service' || $data[0]->category =='Sales')
                        <td>{{ $poData->serialnumbers->product->item_code }}</td>
                    @endif
                    <td>
                        {{ $poData->serialnumbers->product->name }}
                        <span style="display: flex">{{ $poData->description }}</span>
                    </td>
                    <td>{{ $poData->serialnumbers->product->hsn_code }}</td>
                    <td>{{ $poData->qty }}</td>
                    <td>{{ $poData->serialnumbers->batch ?? '-' }}</td>
                    <td>{{ $poData->serialnumbers->expiry_date ? date('d-m-Y', strtotime($poData->serialnumbers->expiry_date)) : '-' }}</td>
                    <td>{{ $poData->serialnumbers->mrp ? number_format($poData->serialnumbers->mrp, 2) : '-' }}</td>
                    <td>{{ number_format($poData->price, 2) }}</td>
                    @if($data[0]->customers->gst_type == 'IGST')
                        <td>{{ number_format($poData->gst, 2) ?? '-' }}</td>
                    @elseif($data[0]->customers->gst_type == 'CGST/SGST')
                        <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                        <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                    @endif
                    <td>{{ number_format($poData->discount_amount, 2) ?? '-' }}</td>
                    <td>{{ number_format($poData->total_amount, 2) ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        <table style="width: 100%; border: 1px solid black; margin-top: 10px;">
            <tr>
                <td style="padding: 10px 10px 10px 20px; width: 50%; vertical-align: top; border-right: 1px solid black;">
                    <p style="font-weight: bold; margin-top: 10px; margin-left: 5px;">OUR BANK DETAILS</p> <!-- Space from the border -->
                    @if($data[0]->organization->id == 1)
                        <p style="margin-left: 5px;"><strong>Bank Name:</strong> {{ $invoiceMCbank['bank_name'] }}</p>
                        <p style="margin-left: 5px;"><strong>Branch Name:</strong> {{ $invoiceMCbank['branch_name'] }}</p>
                        <p style="margin-left: 5px;"><strong>Account No:</strong> {{ $invoiceMCbank['account_no'] }}</p>
                        <p style="margin-left: 5px;"><strong>IFSC Code:</strong> {{ $invoiceMCbank['ifsc_code'] }}</p>
                    @endif
                </td>                
                <td style="padding: 10px 10px 10px 20px; width: 50%; vertical-align: top;">
                    <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                        <strong>Sub Total:</strong>
                        <span style="text-align: right; min-width: 120px;">
                            {{ number_format($data[0]->sub_total, 2) }}
                        </span>
                    </p>
                    <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                        <strong>Total Discount:</strong>
                        <span style="text-align: right; min-width: 120px;">
                            {{ number_format($data[0]->total_discount, 2) }}
                        </span>
                    </p>
                
                    @if($data[0]->customers->gst_type == 'CGST/SGST')
                        <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                            <strong>Total CGST:</strong>
                            <span style="text-align: right; min-width: 120px;">
                                {{ number_format($data[0]->cgst, 2) }}
                            </span>
                        </p>
                        <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                            <strong>Total SGST:</strong>
                            <span style="text-align: right; min-width: 120px;">
                                {{ number_format($data[0]->sgst, 2) }}
                            </span>
                        </p>
                    @endif
                
                    @if($data[0]->customers->gst_type == 'IGST')
                        <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                            <strong>Total IGST:</strong>
                            <span style="text-align: right; min-width: 120px;">
                                {{ number_format($data[0]->igst, 2) }}
                            </span>
                        </p>
                    @endif
                
                    <p style="display: flex; justify-content: space-between; margin-left: 5px;">
                        <strong>Total Amount:</strong>
                        <span style="text-align: right; min-width: 120px;">
                            {{ number_format($data[0]->total_amount, 2) }}
                        </span>
                    </p>
                </td>
                
                
                
            </tr>
            <tr>
                <td style="padding: 10px 10px 10px 20px; width: 50%; vertical-align: top; border-right: 1px solid black;">
                    <p style="font-weight: bold; margin-top: 10px; margin-left: 5px;">TERMS & CONDITIONS</p> 
                    <p style="margin-left: 5px;">{{ $terms['term1'] }}</p>
                    <p style="margin-left: 5px;">{{ $terms['term2'] }}</p>
                    <p style="margin-left: 5px;">{{ $terms['term3'] }}</p>
                </td>
                
                    <td style="padding: 10px; width: 50%; vertical-align: top; text-align: center;">
                    <p style="font-weight: bold;">FOR,</p>
                    <p><strong>{{ $data[0]->organization->name }}</strong></p>
                    <img style="width:auto; height:112px; display: block; margin: 0 auto;"
                         src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->signature))) }}">
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
  <style>

    @page { margin: 0px;}

    body {
        / background: #666666; /
        margin: 0;
        margin: 10px;
        border: 1px solid rgb(55 65 81) !important;
        padding: 10px;
        text-align: center;
        color: #333;
        font-family: ui-sans-serif, system-ui, sans-serif;
        /* font-family: Arial, Helvetica, 'DejaVu Sans', sans-serif; */
        }
    p {
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.6;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        padding: 20px 0px;
    }
    #pdf-content td {
        border-bottom : 1px solid rgb(55 65 81)  !important;
        border-right: 1px solid rgb(55 65 81)  !important;
        padding: 4px 2px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 1px solid rgb(55 65 81)  !important;
        border-right: 1px solid rgb(55 65 81)  !important;
        border-top: 1px solid rgb(55 65 81)  !important;
        padding: 6px 2px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap !important;
    }
</style>
