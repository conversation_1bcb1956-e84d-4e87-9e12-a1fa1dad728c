<?php

namespace App\Http\Requests;

use App\Models\QuotationDetail;
use Illuminate\Foundation\Http\FormRequest;

class ServiceReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'serial_no.*'          => 'required',
            'service_engineer_id'  => 'required|integer',
            'product_name'         => 'required',
            'company_id'           => 'required|integer',
            'document'             => 'required',
            'type'                 => 'required',
            'date'                 => 'required'
            // 'warranty'                          => 'required',
        ];
    }

}
