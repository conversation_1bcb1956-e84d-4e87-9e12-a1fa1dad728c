<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('gst_no', 15)->change()->nullable();
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->string('gst_no', 15)->change()->nullable();
        });

        Schema::table('administration_info', function (Blueprint $table) {
            $table->string('gst_no', 15)->change()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
