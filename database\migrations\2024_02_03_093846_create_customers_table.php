<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('hospital_name');
            $table->string('person_name');
            $table->longText('address');
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->bigInteger('contact_no');
            $table->string('email');
            $table->string('drug_licence_no');
            $table->string('gst_no');
            $table->enum('type', ['Dr.', 'Hospital', 'Clinic']);
            $table->integer('status')->default(1);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
