<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import MultipleCheckbox from '@/Components/MultipleCheckbox.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps, ref } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['customers', 'engineer', 'warranty_status', 'jobcard_number', 'checklist']);

const checkedValues = ref([]);

const form = useForm('post', '/jobcard', {
    customer_id: '',
    job_card_number: props.jobcard_number,
    engineer_id:'',
    hospital_name:'',
    address:'',
    city:'',
    contact_no:'',
    product_name:'',
    product_code:'',
    serial_no:'',
    accessories:'',
    warranty_status:'',
    jobchecks:[],
    date:new Date().toISOString().slice(0, 10)
});

const submit = () => {
    form.jobchecks = checkedValues.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setEngineer = (id, name) => {
    form.engineer_id = id;
    form.errors.engineer_id = null;
};

const updateChecked = (newCheckedValues) => {
  checkedValues.value = newCheckedValues;
};

const setCustomer = (id, name) => {
    form.customer_id = id;
    if(id == 'new_customer'){
        form.hospital_name = '';
        form.address = '';
        form.city = '';
        form.contact_no = '';
    } else {
        const selectedCustomer = props.customers.find(customer => customer.id === id);
        if(selectedCustomer){
            form.hospital_name = selectedCustomer.customer_name;
            form.address = selectedCustomer.address;
            form.city = selectedCustomer.city;
            form.contact_no = selectedCustomer.contact_no;
        }
    }
};

</script>

<template>
    <Head title="Jobcard" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Jobcard</h1>
            </div>
            <div class="w-auto">
                <div class="flex space-x-2">
                    <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Jobcard Number:</span>
                    <span class="text-sm font-semibold text-gray-900 leading-6">{{ jobcard_number }}</span>
                </div>
                <div class="flex space-x-2 items-center">
                    <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Date :</span>
                    <input
                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    type="date"  v-model="form.date"   @change="form.validate('date')"
                />
                </div>
            </div>
        </div>
        <div class="mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-5">
                            <InputLabel for="customer_id" value="Customer Name"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="customers"
                                    v-model="form.customer_id"
                                    @onchange="setCustomer"
                                    :class="{ 'error rounded-md': form.errors.customer_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-7">
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="hospital_name" value="Hospital Name" />
                            <TextInput
                                id="hospital_name"
                                hospital_name="text"
                                v-model="form.hospital_name"
                                autocomplete="hospital_name"
                                @change="form.validate('hospital_name')"
                            />
                            <InputError  v-if="form.invalid('hospital_name')" class="" :message="form.errors.hospital_name" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="contact_no" value="Contact No" />
                            <TextInput
                                id="contact_no"
                                contact_no="text"
                                v-model="form.contact_no"
                                :numeric="true"
                                autocomplete="contact_no"
                                @change="form.validate('contact_no')"
                            />
                            <InputError  v-if="form.invalid('contact_no')" class="" :message="form.errors.contact_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="city" value="City" />
                            <TextInput
                                id="city"
                                city="text"
                                v-model="form.city"
                                autocomplete="city"
                                @change="form.validate('city')"
                            />
                            <InputError  v-if="form.invalid('city')" class="" :message="form.errors.city" />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="address" value="Address" />
                            <TextArea
                                id="address"
                                type="text"
                                v-model="form.address"
                                @change="form.validate('address')"
                            />
                            <InputError  v-if="form.invalid('address')" class="" :message="form.errors.address" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product_name" value="Equipment" />
                            <TextInput
                                id="product_name"
                                product_name="text"
                                v-model="form.product_name"
                                autocomplete="product_name"
                                @change="form.validate('product_name')"
                            />
                            <InputError  v-if="form.invalid('product_name')" class="" :message="form.errors.product_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product_code" value="Model" />
                            <TextInput
                                id="product_code"
                                product_code="text"
                                v-model="form.product_code"
                                autocomplete="product_code"
                                @change="form.validate('product_code')"
                            />
                            <InputError  v-if="form.invalid('product_code')" class="" :message="form.errors.product_code" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="serial_no" value="Serial No" />
                            <TextInput
                                id="serial_no"
                                serial_no="text"
                                v-model="form.serial_no"
                                autocomplete="serial_no"
                                @change="form.validate('serial_no')"
                            />
                            <InputError  v-if="form.invalid('serial_no')" class="" :message="form.errors.serial_no" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="accessories" value="Accessories" />
                            <TextInput
                                id="accessories"
                                accessories="text"
                                v-model="form.accessories"
                                autocomplete="accessories"
                                @change="form.validate('accessories')"
                            />
                            <InputError  v-if="form.invalid('accessories')" class="" :message="form.errors.accessories" />
                        </div>
                        <div  class="sm:col-span-6">
                            <InputLabel for="problem_description" value="Description" />
                            <TextArea
                                id="problem_description"
                                type="text"
                                :rows="3"
                                v-model="form.problem_description"
                                @change="form.validate('problem_description')"
                            />
                            <InputError class="" :message="form.errors.problem_description" />
                        </div>
                        <div class="sm:col-span-6">
                            <InputLabel for="parts_required" value="Parts Required" />
                            <TextInput
                                id="parts_required"
                                type="text"
                                v-model="form.parts_required"
                                autocomplete="parts_required"
                                @change="form.validate('parts_required')"
                            />
                            <InputError class="" :message="form.errors.parts_required" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="engineer_id" value="Engineer Name" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="engineer"
                                v-model="form.engineer_id"
                                @onchange="setEngineer"
                                :class="{ 'error rounded-md': form.errors.engineer_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="warranty_status" value="Warranty Status" />
                            <div class="mt-2 space-y-2">
                                <label class="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="warranty"
                                        value="warranty"
                                        v-model="form.warranty_status"
                                        @change="form.validate('warranty_status')"
                                        class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                    />
                                    <span>Warranty</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="out_of_warranty"
                                        value="out_of_warranty"
                                        v-model="form.warranty_status"
                                        @change="form.validate('warranty_status')"
                                        class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                    />
                                    <span>Out of Warranty</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="amc"
                                        value="amc"
                                        v-model="form.warranty_status"
                                        @change="form.validate('warranty_status')"
                                        class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                    />
                                    <span>AMC</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="cmc"
                                        value="cmc"
                                        v-model="form.warranty_status"
                                        @change="form.validate('warranty_status')"
                                        class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                    />
                                    <span>CMC</span>
                                </label>
                            </div>
                            <InputError v-if="form.invalid('warranty_status')" :message="form.errors.warranty_status" />
                        </div>
                        <div class="sm:col-span-6">
                            <InputLabel for="engineer_id" value="Checklist" />
                            <div class="grid sm:grid-cols-6 relative mt-2">
                                <CheckboxWithLabel
                                v-for="item in checklist"
                                :key="item.id"
                                :checked="checkedValues"
                                :value="item.id"
                                :label="item.type"
                                @update:checked="updateChecked"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('jobcard.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

