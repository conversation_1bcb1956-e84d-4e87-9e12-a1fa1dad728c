<script setup>
import { ref, onMounted, watch , computed} from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps(['data', 'user', 'permissions', 'stockdata', 'organization', 'search', 'company', 'organizationId', 'companyId']);
const activeLink = usePage().props.data.links.find(link => link.active === true);

const form = useForm({
});


const companyName = ref('ALL COMPANY');
const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const searchValue = ref('');

const handleSearchChange = (value) => {
    searchValue.value = value;
    fetchData();
};

const fetchData = () => {
    form.get(route('salesstock', {
        search: searchValue.value,
        organization_id: organizationId.value,
        company_id: companyId.value,
    }), {
        preserveState: true,
    });
};
const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, companyId.value);
};

const setCompany = (id, name) => {
    companyId.value = id;
    companyName.value = name;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value);
};

const openDetailIndex = ref(null);

const toggleDetails = (index) => {
    openDetailIndex.value = openDetailIndex.value === index ? null : index;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};

const formatAmountNew = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const calculateTotalStock = (salesProduct) => {
    if (salesProduct && salesProduct.length > 0) {
        return salesProduct.reduce((total, product) => total + (product.receive_qty - product.sell_qty), 0);
    } else {
        return '-';
    }
};

const exportXls = () => {
    let organizationName = '';

    switch (organizationId.value) {
        case 1:
            organizationName = 'MC';
            break;
        case 2:
            organizationName = 'HC';
            break;
        case 3:
            organizationName = 'NOX';
            break;
        default:
            organizationName = 'All_Organizations';
            break;
    }

    const cleanedCompanyName = companyName.value.replace(/\s+/g, '_');
    const fileName = `SNS_Sales_Report_${organizationName}_${cleanedCompanyName}.xlsx`;

    const params = {
        organizationId: organizationId.value,
        companyId: companyId.value,
        category: 'Sales',
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-stock?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const truncateCompanyName = (name) => {
    return name && name.length > 27 ? name.substring(0, 27) + '...' : name;
};

const totalAmount = computed(() => {
    return Math.round(props.stockdata.reduce((total, product) => {
        let subtotal = 0;
        if (product.sales_product && product.sales_product.length > 0) {
            subtotal = product.sales_product.reduce((sum, salesProduct) => {
                // Only calculate when purchase_price is not null
                if (salesProduct.purchase_price !== null) {
                    const purchasePrice = parseFloat(salesProduct.purchase_price);
                    const receiveQty = parseFloat(salesProduct.receive_qty);
                    const sellQty = parseFloat(salesProduct.sell_qty);

                    if (!isNaN(purchasePrice) && !isNaN(receiveQty) && !isNaN(sellQty)) {
                        return sum + (purchasePrice * (receiveQty - sellQty));
                    }
                }
                return sum; // Skip calculation if purchase_price is null
            }, 0);
        }
        return total + subtotal;
    }, 0));
});

</script>

<template>
    <Head title="Sales Stock"/>
    <AdminLayout>
        <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-6">
                <!-- <h1 class="text-2xl font-semibold leading-7 text-gray-900">Sales Stock</h1> -->
                <Link :href="route('salesstock')" :class="'border-b-2 pb-2 border-gray-900'">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Sales Stock</h1>
                </Link>
                <Link :href="route('servicestock')" :class="'pb-2'">
                    <h1 class="text-lg font-semibold leading-7 text-gray-700">Service Stock</h1>
                </Link>
            </div>

            <div class="items-start">
            </div>
            <div class="justify-end">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, companyId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <button @click="exportXls">
                    <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                </button>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />

                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Company Name" />
                    <div class="relative mt-2">
                        <SearchableDropdownNew :options="company"
                          v-model="companyId"
                        @onchange="setCompany"
                        />
                    </div>
                </div>
                <div v-if="user.role_id == 1" class="sm:col-span-2">
                    <InputLabel for="customer_id" value="Total Amount (₹):" />
                    <div>
                        <p class="text-base font-semibold text-gray-900">{{ formatAmountNew(totalAmount) }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2 grid grid-cols-1 gap-x-2 sm:grid-cols-12">
                            <!-- <th scope="col" class="py-3.5 sm:col-span-2 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">PRODUCT CODE</th> -->
                            <th scope="col" class="sm:col-span-5 px-4 py-4 text-sm font-semi bold text-gray-900">PRODUCT</th>
                            <th scope="col" class="sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900">HSN</th>
                            <th scope="col" class="sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900">GST (%)</th>
                            <th scope="col" class="sm:col-span-3 px-4 py-4 text-sm font-semi bold text-gray-900">COMPANY</th>
                            <th scope="col" class="sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900">STOCK</th>
                            <th scope="col" class="sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900">ACTION</th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12" v-for="(userData, index) in data.data" :key="userData.id">
                            <td class="sm:col-span-5 px-4 py-2.5 font-medium text-gray-900">{{ userData.item_code ? userData.item_code + '  : ' :  ''}}  {{ userData.name ?? '' }}</td>
                            <td class="sm:col-span-1 px-4 py-2.5">{{ userData.hsn_code ?? '-' }}</td>
                            <td class="sm:col-span-1 px-4 py-2.5">{{ userData.gst ?? '-' }}</td>
                            <td class="sm:col-span-3 px-4 py-2.5 truncate">{{ userData.company.name ?? '-' }}</td>
                            <td class="sm:col-span-1 px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">{{ calculateTotalStock(userData.sales_product) ?? '-' ?? '-' }}</td>
                            <td class="sm:col-span-1 px-4 py-2.5">
                                <div class="flex items-center justify-start gap-2">
                                    <button @click="toggleDetails(index)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                    <Dropdown align="right" width="48" v-if="permissions.canProductHistory">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink v-if="permissions.canStockAdd" :href="route('stock.add',{id:userData.id, page:activeLink.url})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="h-5 w-5" stroke-linecap="round" stroke-linejoin="round">
                                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">Add Stock</span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink v-if="permissions.canStockEdit" :href="route('stock.edit',{id:userData.id, page:activeLink.url})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"/>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">Edit Stock</span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink v-if="permissions.canProductHistory" :href="route('products.history',{id:userData.id, page:activeLink.url})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7-7m0 0L5 14m7-7v12"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">Product History</span>
                                                </template>
                                            </ActionLink>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                            <div v-if="openDetailIndex === index && userData.sales_product.length > 0" class="divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4">
                                <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50">
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Batch</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Expiry Date</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">MRP (₹)</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Purchase Price (₹)</th>
                                    <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Stock</th>
                                </tr>
                                <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1 overflow-y-auto" style="max-height: 184px;">
                                    <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5" v-for="(productinfo, index) in userData.sales_product" :key="index">
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.batch ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.expiry_date != null) ? formatDate(productinfo.expiry_date) : '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.mrp) ? formatAmount(productinfo.mrp) : '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.purchase_price) ? formatAmount(productinfo.purchase_price) : '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.receive_qty -productinfo.sell_qty ?? '-' }}</td>
                                    </tr>
                                </tbody>
                            </div>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="8" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        </div>
    </AdminLayout>

</template>
