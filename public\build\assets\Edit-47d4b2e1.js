import{r as d,h as S,o as i,c as r,a as t,u as l,w as n,F as w,Z as N,b as e,g as m,d as M,T as $,f as h,i as B,t as k}from"./app-8a557454.js";import{_ as F,a as q}from"./AdminLayout-301d54ca.js";import{_ as y}from"./InputLabel-07f3a6e8.js";import{_ as T}from"./TextInput-ab168ee4.js";import{P as j}from"./PrimaryButton-9d9bcdd8.js";import{Q as U}from"./vue-quill.snow-3f4b2c87.js";import{M as D}from"./Modal-3bbbc3d3.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const L={class:"animate-top"},O={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},P={class:"text-2xl font-semibold leading-7 text-gray-900 flex justify-between items-center"},Q={class:"ml-auto flex items-center justify-end gap-x-6"},I=["onSubmit"],Z={class:"border-b border-gray-900/10 pb-12"},z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},A={class:"col-span-3"},G={class:"col-span-3"},H={class:"sm:col-span-6"},J={class:"flex mt-6 items-center justify-between"},K={class:"ml-auto flex items-center justify-end gap-x-6"},R=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),W={key:0,class:"text-sm text-gray-600"},X={class:"p-6 relative"},Y=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),ee=[Y],te=e("h2",{class:"text-lg font-medium text-gray-900"},"Email Tags",-1),se={class:"mt-4 overflow-x-auto sm:rounded-lg"},oe={class:"w-full text-sm text-left text-gray-500"},ae=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50"},[e("tr",null,[e("th",{class:"px-4 py-2 text-gray-900"},"Tag Name"),e("th",{class:"px-4 py-2 text-gray-900"},"Description")])],-1),le={key:0},ne=e("td",{colspan:"2",class:"px-4 py-4 text-center text-gray-500"}," No Email Tags Found. ",-1),ie=[ne],re={class:"divide-y divide-gray-300 bg-white"},ce={class:"px-4 py-2"},de={class:"flex items-center space-x-2"},me=["onClick"],ue={key:0,class:"text-green-600 text-xs"},pe={class:"px-4 py-2"},_e={class:"flex items-center space-x-2"},je={__name:"Edit",props:{templates:{type:Object,required:!0},id:{type:[String,Number],required:!1},tags:{type:Object,required:!0}},setup(p){const u=p,b=d(null),C=c=>{b.value=c,g.value=!0},v=()=>{g.value=!1,b.value=null},_=d(null),f=d(""),E=(c,a,s)=>{navigator.clipboard.writeText(c).then(()=>{_.value=a,f.value=s,setTimeout(()=>{_.value=null,f.value=""},2e3)})},g=d(!1);d(null);const o=S({email_subject:u.templates.email_subject||"",template_name:u.templates.template_name||"",content:u.templates.content||""}),V=()=>{o.put(`/emailtemplates/${u.templates.id}`,{onSuccess:()=>{o.reset()}})};return(c,a)=>(i(),r(w,null,[t(l(N),{title:"Email Template"}),t(F,null,{default:n(()=>[e("div",L,[e("div",O,[e("h2",P,[m(" Edit Email Template "),e("div",Q,[t(j,{onClick:a[0]||(a[0]=s=>C(c.tag))},{default:n(()=>[m(" Email Tags ")]),_:1})])]),e("form",{onSubmit:M(V,["prevent"]),class:""},[e("div",Z,[e("div",z,[e("div",A,[t(y,{for:"email_subject",class:"block text-sm font-medium text-gray-700"},{default:n(()=>[m("Email Subject")]),_:1}),t(T,{id:"email_subject",type:"text",modelValue:l(o).email_subject,"onUpdate:modelValue":a[1]||(a[1]=s=>l(o).email_subject=s),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,8,["modelValue"])]),e("div",G,[t(y,{for:"template_name",class:"block text-sm font-medium text-gray-700"},{default:n(()=>[m("Template Name")]),_:1}),t(T,{id:"template_name",type:"text",modelValue:l(o).template_name,"onUpdate:modelValue":a[2]||(a[2]=s=>l(o).template_name=s),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,8,["modelValue"])]),e("div",H,[t(y,{value:"Email Content",class:"block text-sm font-medium text-gray-700"}),t(l(U),{content:l(o).content,"onUpdate:content":a[3]||(a[3]=s=>l(o).content=s),contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"])])])]),e("div",J,[e("div",K,[t(q,{href:c.route("emailtemplates.index")},{svg:n(()=>[R]),_:1},8,["href"]),t(j,{disabled:l(o).processing},{default:n(()=>[m("Save")]),_:1},8,["disabled"]),t($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:n(()=>[l(o).recentlySuccessful?(i(),r("p",W,"Saved.")):h("",!0)]),_:1})])])],40,I)])]),t(D,{show:g.value,onClose:v},{default:n(()=>[e("div",X,[(i(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500",onClick:v},ee)),te,e("div",se,[e("table",oe,[ae,p.tags.length===0?(i(),r("tr",le,ie)):h("",!0),e("tbody",re,[(i(!0),r(w,null,B(p.tags,(s,x)=>(i(),r("tr",{key:x},[e("td",ce,[e("div",de,[e("span",null,k(s.name),1),e("span",{onClick:fe=>E(s.name,x,"name"),class:"cursor-pointer"}," 📋 ",8,me)]),_.value===x&&f.value==="name"?(i(),r("span",ue," Copied! ")):h("",!0)]),e("td",pe,[e("div",_e,[e("span",null,k(s.description),1)])])]))),128))])])])])]),_:1},8,["show"])]),_:1})],64))}};export{je as default};
