<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import MultipleCheckbox from '@/Components/MultipleCheckbox.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import FileUpload from '@/Components/FileUpload.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps, ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios'; // Import axios

// const props = defineProps([ 'filepath']);

const form = useForm('post', '/maintenance-contract', {
    hospital_name: '',
    address: '',
    city: '',
    contact_no: '',
    contract_start_date: '',
    contract_end_date: '',
    maintenance_type: '',
    time_period: '',
    product_name: '',
    price: '',
    company_name: '',
    invoice_number: '',
    pm_date_1: '',
    pm_date_2: '',
    pm_date_3: '',
    pm_date_4: '',
    name: '/uploads/maintenancecontract/defaultimg.png',

});

defineProps({
    maintenance_type: Array,
    filepath: Array,
});

const setDropdownValue = (field, id, name) => {
    form[field] = id;
};

const inputId = ref('');
const pmDates = ref({ pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' });
const pmCount = ref(0);
const error = ref('');

const handlePmUpdates = () => {
    const count = parseInt(inputId.value, 10);
    pmCount.value = isNaN(count) ? 0 : count;

    if (!form.contract_start_date) {
        pmDates.value = { pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' };
        return;
    }

    const startDate = new Date(form.contract_start_date);
    pmDates.value = { pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' };

    if (pmCount.value === 2) {
        const pmDate1 = new Date(startDate);
        const pmDate2 = new Date(startDate);
        pmDate1.setMonth(pmDate1.getMonth() + 6);
        pmDate2.setMonth(pmDate2.getMonth() + 11);

        pmDates.value = {
            pm_date_1: pmDate1.toISOString().split('T')[0],
            pm_date_2: pmDate2.toISOString().split('T')[0],
        };
    } else if (pmCount.value === 3) {
        const pmDate1 = new Date(startDate);
        const pmDate2 = new Date(startDate);
        const pmDate3 = new Date(startDate);
        pmDate1.setMonth(pmDate1.getMonth() + 4);
        pmDate2.setMonth(pmDate2.getMonth() + 8);
        pmDate3.setMonth(pmDate3.getMonth() + 12);

        pmDates.value = {
            pm_date_1: pmDate1.toISOString().split('T')[0],
            pm_date_2: pmDate2.toISOString().split('T')[0],
            pm_date_3: pmDate3.toISOString().split('T')[0],
        };
    } else if (pmCount.value === 4) {
        const pmDate1 = new Date(startDate);
        const pmDate2 = new Date(startDate);
        const pmDate3 = new Date(startDate);
        const pmDate4 = new Date(startDate);
        pmDate1.setMonth(pmDate1.getMonth() + 3);
        pmDate2.setMonth(pmDate2.getMonth() + 6);
        pmDate3.setMonth(pmDate3.getMonth() + 9);
        pmDate4.setMonth(pmDate4.getMonth() + 12);

        pmDates.value = {
            pm_date_1: pmDate1.toISOString().split('T')[0],
            pm_date_2: pmDate2.toISOString().split('T')[0],
            pm_date_3: pmDate3.toISOString().split('T')[0],
            pm_date_4: pmDate4.toISOString().split('T')[0],
        };
    }

    if (pmCount.value <= 0) {
        error.value = 'Please enter a valid number of PMs.';
    } else {
        error.value = '';
    }
};

const handleDocument = (file) => {
    form.name = file;
};

const submit = async () => {
    // Save PM dates to form data
    form.pm_date_1 = pmDates.value.pm_date_1;
    form.pm_date_2 = pmDates.value.pm_date_2;
    form.pm_date_3 = pmDates.value.pm_date_3;
    form.pm_date_4 = pmDates.value.pm_date_4;

    try {
        await form.submit({
            preserveScroll: true,
            onSuccess: () => {
                form.reset();
                pmDates.value = { pm_date_1: '', pm_date_2: '', pm_date_3: '', pm_date_4: '' };
            },
        });
    } catch (error) {
        console.error('Submission error:', error);
        // Handle error if necessary
    }
};

</script>


<template>
    <Head title="Maintenance Contract" />
    <AdminLayout>
        <div class="animate-top h-screen">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Maintenance</h1>
            </div>
        </div>
        <div class="mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-4">
                            <InputLabel for="hospital_name" value="Hospital Name" />
                            <TextInput
                                id="hospital_name"
                                hospital_name="text"
                                v-model="form.hospital_name"
                                autocomplete="hospital_name"
                                @change="form.validate('hospital_name')"
                            />
                            <InputError  v-if="form.invalid('hospital_name')" class="" :message="form.errors.hospital_name" />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="address" value="Address" />
                            <TextInput
                                id="address"
                                type="text"
                                v-model="form.address"
                                @change="form.validate('address')"
                            />
                            <InputError  v-if="form.invalid('address')" class="" :message="form.errors.address" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="contact_no" value="Contact No" />
                            <TextInput
                                id="contact_no"
                                contact_no="text"
                                v-model="form.contact_no"
                                :numeric="true"
                                autocomplete="contact_no"
                                @change="form.validate('contact_no')"
                            />
                            <InputError  v-if="form.invalid('contact_no')" class="" :message="form.errors.contact_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="city" value="City" />
                            <TextInput
                                id="city"
                                city="text"
                                v-model="form.city"
                                autocomplete="city"
                                @change="form.validate('city')"
                            />
                            <InputError  v-if="form.invalid('city')" class="" :message="form.errors.city" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="contract_start_date" value="Contract Start" />
                            <input
                              class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              type="date"
                              v-model="form.contract_start_date"
                              @change="updatePmDates"
                            />
                            <InputError v-if="form.invalid('contract_start_date')" :message="form.errors.contract_start_date" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="contract_end_date" value="Contract End" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"  v-model="form.contract_end_date"   @change="form.validate('contract_end_date')"
                            />
                            <InputError v-if="form.invalid('contract_end_date')" class="" :message="form.errors.contract_end_date" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="type" value="Maintenance Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="maintenance_type"
                                                    v-model="form.maintenance_type"
                                                    @onchange="(id, name) => setDropdownValue('maintenance_type', id, name)"
                                                    :class="{ 'error rounded-md': form.errors.maintenance_type }"
                                />
                            </div>
                            <InputError v-if="form.invalid('maintenance_type')" class="" :message="form.errors.maintenance_type"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="time_period" value="Time Period" />
                            <TextInput
                                id="time_period"
                                time_period="text"
                                v-model="form.time_period"
                                autocomplete="time_period"
                                @change="form.validate('time_period')"
                            />
                            <InputError  v-if="form.invalid('time_period')" class="" :message="form.errors.time_period" />
                        </div>
                        <div class="sm:col-span-5">
                            <InputLabel for="product_name" value="Equipment Name" />
                            <TextInput
                                id="product_name"
                                product_name="text"
                                v-model="form.product_name"
                                autocomplete="product_name"
                                @change="form.validate('product_name')"
                            />
                            <InputError  v-if="form.invalid('product_name')" class="" :message="form.errors.product_name" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="price" value="Price" />
                            <TextInput
                                id="price"
                                price="text"
                                v-model="form.price"
                                autocomplete="price"
                                @change="form.validate('price')"
                            />
                            <InputError  v-if="form.invalid('price')" class="" :message="form.errors.price" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="company_name" value="Company Name" />
                            <TextInput
                                id="company_name"
                                company_name="text"
                                v-model="form.company_name"
                                autocomplete="company_name"
                                @change="form.validate('company_name')"
                            />
                            <InputError  v-if="form.invalid('company_name')" class="" :message="form.errors.company_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="invoice_number" value="Invoice Number" />
                            <TextInput
                                id="invoice_number"
                                invoice_number="text"
                                v-model="form.invoice_number"
                                autocomplete="invoice_number"
                                @change="form.validate('invoice_number')"
                            />
                            <InputError  v-if="form.invalid('invoice_number')" class="" :message="form.errors.invoice_number" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="inputField" value="How Many PM ?" />
                            <TextInput
                              id="inputField"
                              v-model="inputId"
                              @input="handlePmUpdates"
                              @blur="handlePmUpdates"
                            />
                            <InputError :message="error" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="name" value="Upload Document"/>
                            <FileUpload
                                label="Upload Document"
                                inputId="name"
                                inputName="name"
                                :fileUrl="form.name"
                                @file="handleDocument"
                            />
                        </div>
                        <div class="sm:col-span-10">
                        </div>
                        <div v-if="pmCount >= 1" class="sm:col-span-3">
                            <InputLabel for="pm_date_1" value="PM Date 1" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                v-model="pmDates.pm_date_1"
                                readonly
                            />
                        </div>

                        <div v-if="pmCount >= 2" class="sm:col-span-3">
                            <InputLabel for="pm_date_2" value="PM Date 2" />
                                <input
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    v-model="pmDates.pm_date_2"
                                    readonly
                                />
                        </div>

                        <div v-if="pmCount >= 3" class="sm:col-span-3">
                        <InputLabel for="pm_date_3" value="PM Date 3" />
                        <input
                            class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"
                            v-model="pmDates.pm_date_3"
                            readonly
                        />
                        </div>

                        <div v-if="pmCount >= 4" class="sm:col-span-3">
                            <InputLabel for="pm_date_4" value="PM Date 4" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                v-model="pmDates.pm_date_4"
                                readonly
                            />
                        </div>
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('maintenance-contract.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>
                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

