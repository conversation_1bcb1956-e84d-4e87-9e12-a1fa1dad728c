<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <title>Service Jobcard</title>
  <style>

    @page { margin: 0px;}

    body {
        / background: #666666; /
        margin: 0;
        margin: 10px;
        border: 1px solid rgb(55 65 81) !important;
        padding: 10px;
        text-align: center;
        color: #333;
        font-family: ui-sans-serif, system-ui, sans-serif;
        /* font-family: Arial, Helvetica, 'DejaVu Sans', sans-serif; */
    }
    p {
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.6;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        padding: 20px 0px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 4px 4px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 6px 4px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap !important;
    }

    .checkbox {
        text-align: left;
    }

    .checkbox-container {
        display: block;
    }

    .checkbox label {
        display: block;
        margin-bottom: 5px;
        font-size: 12px;
    }

    .checkbox-table {
    width: 100%;
    margin-top: 10px;
}

.checkbox-column {
    width: 50%;
    vertical-align: top;
    padding-right: 10px;
}

.checkbox-container label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
}
</style>

</head>
<body>
    <div id="">
        <div style="align-items: center">
            <b class="font-size: 20px;">Maintenance Workshop
            </b>
        </div>

        <table style="width:100%;">
            <tr style="width:100%;">
                <td style="text-align:left; width: 300px;">
                    <table>
                        <tr>
                            <td>
                                <p><strong>Hospital Name : </strong> {{ $data[0]->hospital_name }}</p>
                                <p><strong>Address : </strong> {{ $data[0]->address }}</p>
                                <p><strong>Contact Number : </strong>{{ $data[0]->contact_no }}</p>
                            </td>
                        </tr>
                    </table>
                </td>
                <td style="text-align:center; width: 120px;">
                </td>
                <td style="text-align:right; width: 320px; vertical-align: top;">
                    <table>
                        <tr>
                            <td>
                                <p><span class="label"><strong>Job Number : </strong></span>{{ $data[0]->job_card_number }}</p>
                                <p><span class="label"><strong>Job Date : </strong></span> {{ $data[0]->date }}</p>
                                <p><span class="label"><strong>Engineer : </strong></span> {{ $data[0]->users->first_name }} {{ $data[0]->users->last_name }}</p>
                           </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
  <div class="" id="pdf-content">
    <table>
        <thead>
            <tr>
                <th>Equipment Name</th>
                <th>Model</th>
                <th>Serial No</th>
            </tr>
        </thead>
        <tbody>
             @foreach($data as $jobCardData)
                <tr>
                    <td>{{ $jobCardData->product_name ?? '-' }}</td>
                    <td>{{ $jobCardData->product_code ?? '-' }}</td>
                    <td>{{ $jobCardData->serial_no ?? '-' }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
      </div>

    <div class="Description ">
        <strong>Problem Description:</strong> {{ $data[0]->problem_description }}
    </div>
    <div class="Accessories ">
        <strong>Accessories:</strong> {{ $data[0]->accessories }}
    </div>
    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left;">
                <table>
                    <tr>
                        <td>
                            <p><strong>Checklist</strong></p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <div class="checkbox">
        <table class="checkbox-table">
            <tr>
                @php
                    $checklistArray = $checklist->toArray();
                    $half = ceil(count($checklistArray) / 2);
                    $columns = array_chunk($checklistArray, $half);
                @endphp
                
                @foreach($columns as $column)
                <td class="checkbox-column">
                    <div class="checkbox-container">
                        @foreach($column as $item)
                        <label>
                            <input type="checkbox" name="checklist[]" value="{{ $item['id'] }}"
                                @if(in_array($item['id'], $checkedValues)) checked @endif>
                            {{ $item['type'] }}
                        </label>
                        @endforeach
                    </div>
                </td>
                @endforeach
            </tr>
        </table>
    </div>


    <div class="Note ">
        <strong>Close Note:</strong> {{ $data[0]->close_note }}
    </div>

    <div class="Date ">
        <strong>Close Date:</strong> {{ ($data[0]->close_date != null) ? date('d-m-Y', strtotime($data[0]->close_date)) : '-' }}
    </div>

    </body>
</html>

<style>
    .Description, .Note, .Date, .Accessories {
        font-size: 12px;
        line-height: 1.6;
        padding: 10px 0;

    }

    .Description {
        float: left;
        width: 70%;
        text-align: left;
        margin-bottom: 20px;

    }

    .Accessories {
    clear: both;
    float: left;
    width: 70%;
    text-align: left;
    margin-top: 20px;
}

    .Note {
        clear: both;
        float: left;
        width: 70%;
        text-align: left;
        margin-top: 20px;
    }

    .Date {
        float: right;
        width: 30%;
        text-align: right;
        margin-top: 10px;
    }


</style>
