<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->string('sales_order_no')->nullable()->after('date');
            $table->date('sales_order_date')->nullable()->after('sales_order_no');
            $table->double('igst', 16, 2)->nullable()->after('status');
            $table->double('sgst', 16, 2)->nullable()->after('igst');
            $table->double('cgst', 16, 2)->nullable()->after('sgst');
            $table->double('total_gst', 16, 2)->nullable()->after('cgst');
            $table->double('sub_total', 16, 2)->nullable()->after('total_gst');
        });

        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->string('customer_invoice_no')->nullable()->after('po_receive_date');
            $table->date('customer_invoice_date')->nullable()->after('customer_invoice_no');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->enum('company_type', ['Retail', 'Tax'])->after('name')->default('Tax');
            $table->enum('gst_type', ['IGST', 'CGST/SGST'])->after('company_type')->default('CGST/SGST');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->dropColumn('sales_order_no');
            $table->dropColumn('sales_order_date');
            $table->dropColumn('igst');
            $table->dropColumn('sgst');
            $table->dropColumn('cgst');
            $table->dropColumn('sub_total');
        });

        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->dropColumn('customer_invoice_no');
            $table->dropColumn('customer_inovice_date');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('company_type');
            $table->dropColumn('gst_type');
        });
    }
};
