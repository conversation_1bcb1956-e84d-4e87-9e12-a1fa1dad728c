<?php

namespace App\Http\Controllers;
use App\Models\Organization;
use Inertia\Inertia;
use App\Http\Requests\OrganizationRequest;
use Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrganizationController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Organization')->only(['index']);
        $this->middleware('permission:Organization')->only(['index','create','edit','store','destroy']);
        $this->middleware('permission:Create Organization')->only(['create', 'store']);
        $this->middleware('permission:Edit Organization')->only(['edit', 'store']);
        $this->middleware('permission:Delete Organization')->only('destroy');
    }

    public function index(Request $request)
    {
        $data = Organization::paginate(10);
        $permissions = [
            'canCreateOrganization'      => auth()->user()->can('Create Organization'),
            'canEditOrganization'        => auth()->user()->can('Edit Organization'),
            'canDeleteOrganization'      => auth()->user()->can('Delete Organization')
        ];
        return Inertia::render('Organization/List', compact('data', 'permissions'));
    }

    public function create()
    {
        return Inertia::render('Organization/Add');
    }

    public function store(OrganizationRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $files = $request->file();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            unset($data['logo']);
            unset($data['signature']);

            if(isset($request->id)){
                $administrationInfo = Organization::find($request->id);
                $administrationInfo->update($data);
            } else {
                $administrationInfo = Organization::create($data);
            }
            if($files){
                $this->uploadInvoiceDocuments($files, $administrationInfo);
            }
            DB::commit();
            return Redirect::to('/organization')->with('success','Organization Update Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/organization')->with('error',$e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data     = Organization::find($id);
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        return Inertia::render('Organization/Edit', compact('data', 'filepath'));
    }

    private function uploadInvoiceDocuments($files, $administrationInfo)
    {
        $filePath = Config::get('constants.uploadFilePath.companyDocument');
        if($files){
            foreach ($files as $key => $file){
                $originalName = $file->getClientOriginalName();
                $fileName = str_replace(' ', '-', $originalName);
                $path = $filePath['default'];
                if(!is_dir($path)) {
                    mkdir($path, 0777, true);
                }
                $upload_success = $file->move($path, $fileName);
                if($upload_success){
                    $administrationInfo->update([$key => $fileName]);
                }
            }
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = Organization::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('message','Organization Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

}
