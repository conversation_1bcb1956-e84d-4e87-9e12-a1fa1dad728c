<script setup>
import { ref, onMounted, watch, computed, onBeforeMount } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import TextInput from '@/Components/TextInput.vue';
import DateInput from '@/Components/DateInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import Modal from '@/Components/Modal.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['data', 'po_receive_number','salesuser']);

const receivedProduct = ref([
    {
        organization_id: '',
        company_id: '',
        product_id: '',
        purchase_order_detail_id:'',
        purchase_order_id: '',
        qty: '',
        po_receive_number: '',
        total_qty: '',
        received_qty: '',
        mrp: '',
        purchase_price: '',
        total_amount: '',
        total_price: '',
        total_gst_amount: ''
    }
])

const po_receive_number = ref();
po_receive_number.value = props.po_receive_number[props.data[0].purchase_order.organization.id];

const calculateDynamicAmount = (product, index) => {
    const price = parseFloat(product?.serial_numbers[0].purchase_price) || 0;
    const gst = parseFloat(product?.purchase_order_detail.gst) || 0;
    const qty = parseFloat(product?.receive_qty) || 0;
    console.log('price', price);
    console.log('gst', gst);
    console.log('qty', qty);
    console.log('Product:', product);
    const total_price = price * qty;
    const total_gst_amount = total_price * (gst / 100);
    const total_amount = total_price + total_gst_amount;
    return {'total_amount': total_amount.toFixed(2), 'total_price': total_price.toFixed(2),'total_gst_amount': total_gst_amount.toFixed(2)};
};

onBeforeMount(() => {
    receivedProduct.value = props.data[0].purchase_order_receive_details.map((item, index) => {
        const calculate_dynamic_amount = calculateDynamicAmount(item, index); // Calculate amounts for initialization
        const firstSerial = item.serial_numbers.length > 0 ? item.serial_numbers[0] : {};
        return {
            organization_id: props.data[0].organization_id,
            company_id: props.data[0].company_id,
            product_id: item.product_id,
            purchase_order_detail_id: item.purchase_order_detail_id,
            purchase_order_id: props.data[0].purchase_order_id,
            po_receive_number: props.po_receive_number[props.data[0].organization_id],
            total_qty: item.qty,
            received_qty: item.receive_qty,
            receive_qty: item.receive_qty,
            total_batch: item.serial_numbers.length,
            mrp: firstSerial.mrp ?? '',
            purchase_price: item.serial_numbers[0].purchase_price,
            total_amount: calculate_dynamic_amount.total_amount,
            total_price: calculate_dynamic_amount.total_price,
            total_gst_amount: calculate_dynamic_amount.total_gst_amount
        };
    });

    productDetails.value = props.data[0].purchase_order_receive_details.map(item => {
        return item.serial_numbers.map(serial => ({
            batch: serial.batch || '',
            expiry_date: serial.expiry_date || '',
            qty: serial.receive_qty || ''
        }));
    });
});

const form = useForm('post', '/purchaseinvoice', {
    purchase_order_receive_id: props.data[0].id,
    created_by: props.data[0].purchase_order.purchase_order_receives[0].created_by,
    total_price: '',
    total_gst_amount: '',
    total_amount: '',
    receivedProduct: [],
    customer_invoice_no: props.data[0].purchase_order.purchase_order_receives[0].customer_invoice_no,
    customer_invoice_date: props.data[0].purchase_order.purchase_order_receives[0].customer_invoice_date,
    category: props.data[0].purchase_order.category
});

const submit = () => {
    form.total_amount = totalAmount.value;
    form.total_gst_amount = totalGstAmount.value;
    form.total_price = totalPrice.value;
    form.receivedProduct = receivedProduct.value.map((item, index) => ({
        ...item,
        productDetails: productDetails.value[index] || []
    }));
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const productDetails = ref([]);

const addFields = (index) => {
    // if(props.data[0].category == 'Sales'){
        form.errors['receivedProduct.' + index + '.receive_qty'] = null;
            const qty = receivedProduct.value[index].total_batch;
            const total_qty = receivedProduct.value[index].total_qty;
            const received_qty = receivedProduct.value[index].received_qty;
            const otherFields = [];
            let actualQty;
            if (qty && !isNaN(qty)){
                // if( qty > (total_qty - received_qty)){
                //     actualQty = total_qty - received_qty;
                // } else {
                    actualQty = qty;
                // }
                for (let i = 0; i < actualQty; i++) {
                    otherFields.push({
                        batch: '',
                        expiry_date: '',
                        qty: ''
                    });
                }
            }
            receivedProduct.value[index].total_batch = actualQty;
            productDetails.value[index] = otherFields;
    // }
};

const currentDate = computed(() => {
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return today.toLocaleDateString('en-US', options);
});

const setReceivedBy = (id, name) => {
    form.created_by = id;
    form.errors.created_by = null;
};

const calculateAmount = (product, index) => {
    const price = parseFloat(receivedProduct.value[index].purchase_price)  ;
    const gst = parseFloat(product.gst) || 0;
    const qty = parseFloat(receivedProduct.value[index].receive_qty);
    const amount = price * qty * (1 + gst / 100)
    const total_price = price * qty;
    const total_gst_amount = price * qty * (gst / 100);
    receivedProduct.value[index].total_price      = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    receivedProduct.value[index].total_gst_amount = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    return isNaN(amount) ? '' :  parseFloat(amount).toFixed(2);
};

const updateAmount = (product, index) => {
    receivedProduct.value[index].total_amount= calculateAmount(product, index);
};

const totalAmount = computed(() => {
    return receivedProduct.value.reduce((total, product) => {
        return total + (product.total_amount ? parseFloat(product.total_amount) : 0);
    }, 0);
});

const totalGstAmount = computed(() => {
    return receivedProduct.value.reduce((total, product) => {
        return total +  (product.total_gst_amount ? parseFloat(product.total_gst_amount) : 0);
    }, 0);
});

const totalPrice = computed(() => {
    return receivedProduct.value.reduce((total, product) => {
        return total +  (product.total_price ? parseFloat(product.total_price) : 0);
    }, 0);
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};


const productDeleteModal = ref(false);
const selectedProductId = ref(null);

const closeProductModal = () => {
    productDeleteModal.value = false;
};

const deleteProduct = () => {
    form.get(route('removeproduct',{id:selectedProductId.value,  model:'InvoiceDetail'}), {
        onSuccess: () => {
        closeProductModal()
        // selectedProductItem.value.splice(index, 1);
        }
    });
};

const removeProduct = (index, id) => {
    if(id !== undefined && id != ''){
        selectedProductId.value = id;
        productDeleteModal.value = true;
    } else {
        // selectedProductItem.value.splice(index, 1);
    }
};

</script>

<template>
    <Head title="Company PO"/>

    <AdminLayout>
        <div class="animate-top">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Receive Product</h1>
            </div>
            <p class="text-sm font-semibold text-gray-900">{{ data[0].purchase_order.organization.name }}</p>
        </div>
        <div class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Company Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.company.name ?? '-'  }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.company.gst_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.company.email ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.company.contact_no ?? '-' }}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Receive No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].po_receive_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.po_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  formatDate(data[0].purchase_order.date) ?? '-'}}</p>
                    </div>
                 </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_invoice_no" value="Company Invoice No" />
                    {{ customer_invoice_no }}
                    <TextInput
                        id="customer_invoice_no"
                        type="text"
                        v-model="form.customer_invoice_no"
                        @change="clearError('customer_invoice_no')"
                        :class="{ 'error rounded-md': form.errors.customer_invoice_no }"
                    />
                    <InputError  v-if="form.invalid('customer_invoice_no')" class="" :message="form.errors.customer_invoice_no" />
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_invoice_date" value="Company Invoice Date" />
                    <input
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    type="date"  v-model="form.customer_invoice_date"
                    @change="clearError('customer_invoice_date')"
                      :class="{ 'error rounded-md': form.errors.customer_invoice_date }"
                    />
                    <InputError v-if="form.invalid('customer_invoice_date')" class="" :message="form.errors.customer_invoice_date" />
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="company_name" value="Received By:" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="salesuser"
                            v-model="form.created_by"
                            @onchange="setReceivedBy"
                            :class="{ 'error rounded-md': form.errors.created_by }"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto">
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2" style="width: 140%;">
                <div class="sm:col-span-2">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Product</p>
                </div>
                <div class="sm:col-span-1">
                    <p v-if="data[0].category == 'Service'" class="text-sm font-semibold text-gray-900 leading-6">Part No</p>
                    <p v-else class="text-sm font-semibold text-gray-900 leading-6">Product Code</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">GST %</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Received QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">MRP (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Purchase Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Batch</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Amount</p>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center" style="width: 140%;" v-for="(product, index)  in data[0].purchase_order.purchase_order_detail" :key="index">
                <div class="sm:col-span-2">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.name ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.item_code ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ formatAmount(product.price) ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ formatAmount(product.gst) ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.qty  ?? '-'}}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.receive_qty ?? '-'}}</p>
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].mrp"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.mrp`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].purchase_price"
                        @input="updateAmount(product, index)"
                        @change="clearError('receivedProduct.' + index + '.purchase_price')"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.purchase_price`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].receive_qty"
                        :numeric="true"
                        @change="clearError(`receivedProduct.${index}.receive_qty`)"
                        @input="updateAmount(product, index)"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.receive_qty`] }"
                        min="1"
                    />
                    <p v-if="form.errors[`receivedProduct.${index}.receive_qty`]" class="text-red-500 text-xs absolute">
                        {{ form.errors[`receivedProduct.${index}.receive_qty`] }}
                    </p>
                </div>

                <div class="flex sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        :numeric="true"
                        v-model="receivedProduct[index].total_batch"
                        @change="addFields(index)"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.total_batch`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1">
                    <div class="flex space-x-2">
                        <p class="text-sm leading-5 text-gray-700">{{ receivedProduct[index].total_amount }}</p>
                        <button type="button" @click="removeProduct(index, product.id)">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
                <div v-if="productDetails[index]" class="sm:col-span-9 mb-2">
                    <div v-for="(field, index2) in productDetails[index]" :key="index2" class="grid grid-cols-1 gap-x-6  sm:grid-cols-12 items-center">
                        <div class="sm:col-span-3">
                            <TextInput
                                type="text"
                                v-model="field.batch"
                                placeholder="Batch"
                                @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.batch')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.batch`] }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <DateInput
                                v-model="field.expiry_date"
                                  @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.expiry_date')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.expiry_date`] }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <TextInput
                                type="text"
                                v-model="field.qty"
                                placeholder="Qty"
                                  @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.qty')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.qty`] }"
                            />
                        </div>
                    </div>
                    <p v-if="form.errors[`receivedProduct.${index}.productDetails`]" class="text-red-500 text-xs absolute">
                        {{ form.errors[`receivedProduct.${index}.productDetails`] }}
                    </p>
                </div>
            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-2">
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total GST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalAmount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex mt-6 items-center justify-between">
            <div class="ml-auto flex items-center justify-end gap-x-6">
                <PrimaryButton class="" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Submit
                </PrimaryButton>
            </div>
        </div>
        </form>
        </div>
        <Modal :show="productDeleteModal" @close="closeProductModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to Remove this Purchase?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeProductModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteProduct"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
.error {
  border: 1px solid red;
}
</style>
