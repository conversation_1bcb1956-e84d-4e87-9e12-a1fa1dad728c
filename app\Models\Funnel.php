<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Funnel extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'funnel';

    protected static $logName = 'Funnel';

    public function getLogDescription(string $event): string
    {
        return "Funnel has been {$event} for <strong>{$this->customer_name}</strong> by";
    }

    protected static $logAttributes = [
        'customer_name',
        'dr_name',
        'place',
        'mobile_number',
        'company',
        'product',
        'close_date',
        'order_value',
        'order_month',
        'inquiry_type',
        'status',
    ];

    protected $fillable = [
        'customer_name',
        'dr_name',
        'place',
        'mobile_number',
        'company',
        'product',
        'close_date',
        'order_value',
        'order_month',
        'inquiry_type',
        'status',
        'created_by',
        'updated_by'
    ];

    public function users(){
        return $this->belongsTo(User::class,'created_by','id');
    }
}
