<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_receive', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained( table: 'invoice', indexName: 'prin_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('customer_id');
            $table->integer('org_bank_id');
            $table->string('invoice_no');
            $table->enum('payment_type', ['cash', 'check', 'NEFT']);
            $table->double('amount', 16, 2);
            $table->string('check_number')->nullable();
            $table->date('date')->nullable();
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_receive');
    }
};
