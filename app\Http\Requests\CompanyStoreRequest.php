<?php

namespace App\Http\Requests;

use App\DTO\CompanyDTO;
use Support\Contracts\HasDTO;
use Illuminate\Foundation\Http\FormRequest;

class CompanyStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'              => ['required','string','max:255'],
            // 'website'           => ['required','string','max:255'],
            'address'           => ['required','string','max:500'],
            'city'              => ['required','string','max:255'],
            // 'contact_no'        => ['required','numeric', 'digits:10'],
            // 'email'             => ['required','string','email','max:255'],
            // 'drug_licence_no'   => ['required','string','max:255'],
            // 'gst_no'          => ['required', 'string', 'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$/'], // GST validation
            'gst_type'          => ['required','string'],
        ];
    }
    
    public function DTO()
    {
        return CompanyDTO::LazyFromArray($this->input());
    }

}

