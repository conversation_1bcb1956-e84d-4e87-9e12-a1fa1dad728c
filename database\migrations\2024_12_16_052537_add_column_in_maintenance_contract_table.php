<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('maintenance_contract', callback: function (Blueprint $table) {
            $table->enum('status',['Open','Close'])->after('contract_end_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('maintenance_contract', function (Blueprint $table) {
            $table->enum('status',['Open','Close'])->after('contract_end_date');
        });
    }
};
