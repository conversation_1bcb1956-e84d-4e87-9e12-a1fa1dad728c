<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <title>Challan</title>
  <style>

    @page { margin: 0px;}

    body {
        / background: #666666; /
        margin: 0;
        margin: 10px;
        border: 1px solid rgb(55 65 81) !important;
        padding: 10px;
        text-align: center;
        color: #333;
        font-family: ui-sans-serif, system-ui, sans-serif;
        /* font-family: Arial, Helvetica, 'DejaVu Sans', sans-serif; */
    }
    p {
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.6;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 1px solid rgb(55 65 81) !important;
        width: 100%;
        border-collapse: collapse;
        padding: 20px 0px;
    }
    #pdf-content td {
        border-bottom : 1px solid rgb(55 65 81) !important;
        border-right: 1px solid rgb(55 65 81) !important;
        padding: 4px 2px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 1px solid rgb(55 65 81) !important;
        border-right: 1px solid rgb(55 65 81) !important;
        border-top: 1px solid rgb(55 65 81) !important;
        padding: 6px 2px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap !important;
    }
</style>

</head>
<body>
    <div id="">
        @if($data[0]->organization->id == 3)
            <table style="width: 100%; margin-bottom: 10px;">
                <tr>
                    <td style="text-align: left; width: 80px;">
                        <img style="width: 80px; height: 80px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                    <td style="text-align: center;">
                        <b class="font-size: 20px;"> Challan </b>
                    </td>
                    <td style="width: 120px;"></td>
                </tr>
            </table>
        @endif

        @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
            <table style="width: 100%; text-align: center;">
                <tr>
                    <td style="text-align: center; margin-bottom: 20px;">
                        <img style="width:100%; height:40px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                </tr>
            </table>
            <div style="align-items: center">
                <b class="font-size: 20px;">Challan
                </b>
            </div>
        @endif
    </div>


    <table style="width:100%;">
        <tr>
            <td style="text-align:left; width: 300px; vertical-align: top;">
                <table>
                    <tr>
                        <td>
                            <p><strong>{{ $data[0]->organization->name }}</strong></p>
                            <p>{{ $data[0]->organization->address_line_1 }}</p>
                            <p>{{ $data[0]->organization->address_line_2 }}</p>
                            <p>{{ $data[0]->organization->city }}</p>
                            <p><strong>Phone : </strong> {{ $data[0]->organization->contact_no ?? '-' }}</p>
                            <p><strong>Email : </strong> {{ $data[0]->organization->email ?? '-' }}</p>
                            <p><strong>GST : </strong>{{ $data[0]->organization->gst_no ?? '-' }}</p>
                        </td>
                    </tr>
                </table>
                <td style="text-align:center; width: 120px;">
                </td>
            </td>
            <td style="text-align:right; width: 320px; vertical-align: top;">
                <table>
                    <tr>
                        <td>
                            <p><span class="label"><strong>Challan Number:</strong></span>{{ $data[0]->challan_number }}</p>
                            <p><span class="label"><strong>Challan Date:</strong></span> {{ $data[0]->date }}</p>
                            <p><strong>{{ $data[0]->customers->customer_name }}</strong></p>
                            <p>{{ $data[0]->customers->address }}</p>
                            <p><span class="label"><strong>Phone : </strong></span>{{ $data[0]->customers->contact_no ?? '-' }}</p>
                            <p><span class="label"><strong>Email : </strong></span>{{ $data[0]->customers->email ?? '-'}}</p>
                            <p><span class="label"><strong>GST : </strong></span>{{ $data[0]->customers->gst_no ?? '-'}}</p>
                            <p><span class="label"><strong>Transport : </strong></span>{{ $data[0]->transport ?? '-' }}</p>
                            <p><span class="label"><strong>Dispatch : </strong></span>{{ $data[0]->dispatch ?? '-' }}</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <div id="pdf-content">
        <table>
            <thead>
                <tr>
                    <th>SN</th>
                    <th>Code</th>
                    <th>Product Name</th>
                    <th>HSN</th>
                    <th>Qty</th>
                    <th>Batch</th>
                    <th>Exp</th>
                    <th>MRP</th>
                    <th>Rate</th>
                    <th>Discount(%)</th>
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <th>CGST(%)</th>
                    @endif
                    @if($data[0]->customers->gst_type =='CGST/SGST')
                        <th>SGST(%)</th>
                    @endif
                    @if($data[0]->customers->gst_type =='IGST')
                        <th>IGST(%)</th>
                    @endif
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                @if (isset($data[0]->challanDetail) && is_iterable($data[0]->challanDetail))
                    @foreach ($data[0]->challanDetail as $index => $poData)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $poData->viewserialnumbers->product->item_code }}</td>
                            <td>{{ $poData->viewserialnumbers->product->name }}
                                <span style="display: flex">{{ $poData->description }}</span>
                            </td>
                            <td>{{ $poData->viewserialnumbers->product->hsn_code }}</td>
                            <td>{{ $poData->qty }}</td>
                            <td>{{ $poData->viewserialnumbers->batch ?? '-' }}</td>
                            <td>{{ $poData->viewserialnumbers->expiry_date ?? '-' }}</td>
                            <td>{{ $poData->viewserialnumbers->mrp ? number_format($poData->viewserialnumbers->mrp, 2) : '-' }}</td>
                            <td>{{ number_format($poData->price, 2) ?? '-' }}</td>
                            <td>{{ number_format($poData->discount, 2) ?? '-' }}</td>
                            @if ($data[0]->customers->gst_type == 'IGST')
                                <td>{{ number_format($poData->gst, 2) ?? '-' }}</td>
                            @elseif ($data[0]->customers->gst_type == 'CGST/SGST')
                                <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                                <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                            @endif
                            <td>{{ number_format($poData->total_price, 2) ?? '-' }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="14">No data available</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
    <table style="width:100%; margin-top: 20px; border-collapse: collapse;">
        <tr>
            <td style="width: 50%; vertical-align: top; text-align: left;">
                @if(!empty($data[0]->note))
                    <p><strong>Note:</strong> {{ $data[0]->note }}</p>
                @endif
            </td>
    
            <td style="width: 50%; vertical-align: top;">
                <div style="border: 1px solid #000; padding: 10px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 2px;"><strong>Sub Total:</strong></td>
                            <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->sub_total, 2) }}</td>
                        </tr>
                        <tr>
                            <td style="padding: 2px;"><strong>Total Discount:</strong></td>
                            <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->total_discount, 2) }}</td>
                        </tr>
                        @if($data[0]->customers->gst_type =='CGST/SGST')
                            <tr>
                                <td style="padding: 2px;"><strong>Total CGST:</strong></td>
                                <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->cgst, 2) }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 2px;"><strong>Total SGST:</strong></td>
                                <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->sgst, 2) }}</td>
                            </tr>
                        @endif
                        @if($data[0]->customers->gst_type =='IGST')
                            <tr>
                                <td style="padding: 2px;"><strong>Total IGST:</strong></td>
                                <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->igst, 2) }}</td>
                            </tr>
                        @endif
                        <tr>
                            <td style="padding: 2px;"><strong>Total Amount:</strong></td>
                            <td style="padding: 2px; text-align: right;">{{ number_format($data[0]->total_amount, 2) }}</td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
    
    <table style="width:100%; margin-top: 40px;">
        <tr>
            <td style="width: 60%;"></td>
            <td style="width: 40%; text-align: right;">
                <div style="border: 1px solid #000; padding: 10px;">
                    <p style="font-weight: bold; margin-bottom: 5px;">FOR,</p>
                    <p style="font-weight: bold; margin-bottom: 10px;">{{ $data[0]->organization->name }}</p>
                    <img style="width: auto; height: 112px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->signature))) }}">
                </div>
            </td>
        </tr>
    </table>
    

</div>
</body>
</html>
