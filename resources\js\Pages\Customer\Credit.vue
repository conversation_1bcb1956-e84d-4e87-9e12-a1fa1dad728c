<script setup>
import { ref, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';

const props = defineProps(['data', 'customer', 'organization', 'customerId', 'bankinfo', 'organizations', 'paymentType']);

const form = useForm({});

const handleSearchChange = (organizationId) => {
    form.get(route('customers.credit',{id: props.customerId, organization_id: organizationId}),  {
        preserveState: true,
        // replace: true,
    });
};

const organizationId = ref('');

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(organizationId.value);
};

const modalVisible = ref(false);
const selectedUserId = ref(null);

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    // form.delete(route('banktransaction.destroy',{id:selectedUserId.value}), {
    //     onSuccess: () => closeModal()
    // });
};

const formatAmountNew = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const calculateCreditBalance = (index) => {
    let balance = 0; // Initialize balance with initial bank balance
    for (let i = 0; i <= index; i++) {
    const transaction = props.data.data[i];
        balance += parseFloat(transaction.unused_amount);
    }
    let prefix = balance >= 0 ? 'cr' : 'dr';
    return formatAmountNew(Math.abs(balance)) + ' ' + prefix;
};

const paymentData = ref([]);
const bankinfo = ref([]);
const paymentReceiveModal = ref(false);
const paymentReceiveWidth = ref('custom2');
const payment_type = ref('');

const paymentform = {
    id: '',
    organization_id: '',
    customer_id: '',
    org_bank_id: '',
    invoice_id: '',
    invoice_no: '',
    payment_type:'',
    amount: '',
    check_number: '',
    date: '',
    note: ''
};

const setOrganizations = (id, name) => {
    paymentform.organization_id = id;
    form.errors[`data.organization_id`] = null;
};

const setPaymentType = (id, name) => {
    paymentform.payment_type = id;
    payment_type.value = name;
    form.errors[`data.payment_type`] = null;
};

const setBankInfo = (id, name) => {
    paymentform.org_bank_id = id;
    form.errors[`data.org_bank_id`] = null;
};

const openPaymentReceiveModal = (id) => {
    const payment = props.data.data.find(invoice  => invoice.id === id);
    paymentData.value = payment
    paymentform.id = id;
    paymentform.organization_id = paymentData.value.organization_id;
    paymentform.customer_id = paymentData.value.customer_id;
    paymentform.org_bank_id = paymentData.value.paymentreceive.org_bank_id;
    paymentform.invoice_id = paymentData.value.paymentreceive.invoice_id;
    paymentform.payment_type = paymentData.value.paymentreceive.payment_type;
    paymentform.amount = paymentData.value.amount;
    paymentform.check_number = paymentData.value.paymentreceive.check_number;
    paymentform.date = paymentData.value.date;
    paymentform.note = paymentData.value.paymentreceive.note;
    const bank = props.bankinfo.filter(bank  => bank.organization_id === paymentData.value.organization_id);
    bankinfo.value = bank;
    paymentReceiveModal.value = true;
};

const paymentReceiveCloseModal = () => {
    paymentReceiveModal.value = false;
};

const acceptPayment = () => {
    form.post(route('customers.advancepayment',{ data : paymentform}), {
        onSuccess: () => {
            form.reset();
            paymentReceiveModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

</script>

<template>
    <Head title="Customers"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Customer Credits</h1>
                </div>
                <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <!-- <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                        </div>
                    </div> -->
                    <div class="flex items-center space-x-4">
                        <div>
                            <h1 class="text-lg font-semibold leading-7 text-gray-900">{{ customer.customer_name  }} - {{ customer.city  }}</h1>
                        </div>
                        <div class="flex justify-end w-20">
                            <CreateButton :href="route('customers.index')">
                                Back
                            </CreateButton>
                        </div>
                        <div class="flex justify-end">
                            <div class="relative">
                            <SimpleDropdown :options="organization"
                                @onchange="setOrganization"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="h-screen">
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8" v-if="data.data && (data.data.length > 0)">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8" style="min-height:500px">
                        <div class="p-1 bg-white  shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                            <div class="p-2">
                                <p class="text-lg font-semibold text-gray-900">Credit</p>
                            </div>
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-12">
                                    <th scope="col" class="sm:col-span-2 py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">DATE</th>
                                    <th scope="col" class="sm:col-span-3 px-3 py-3.5 text-left text-sm font-semibold text-gray-900">NARRATION</th>
                                    <th scope="col" class="sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900">AMOUNT (₹)</th>
                                    <th scope="col" class="sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900">UNUSED AMOUNT (₹)</th>
                                    <th scope="col" class="sm:col-span-2 px-3 py-3.5 text-left text-sm font-semibold text-gray-900">BALANCE (₹)</th>
                                    <th scope="col" class="sm:col-span-1 px-3 py-3 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-300 bg-white" v-if="data.data && (data.data.length > 0)">
                                <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-12" v-for="(customerData, index) in data.data" :key="customerData.id">
                                    <td class="sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-900">{{ formatDate(customerData.date) ?? '-' }}</td>
                                    <td class="sm:col-span-3 whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ customerData.paymentreceive?.bank_info?.bank_name ? customerData.paymentreceive?.bank_info?.bank_name : 'Cash' }}:{{ customerData.paymentreceive?.bank_info?.account_number ? customerData.paymentreceive?.bank_info?.account_number : '' }}</td>
                                    <td class="sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ formatAmountNew(customerData.amount) }}</td>
                                    <td class="sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ formatAmountNew(customerData.unused_amount) }}</td>
                                    <td class="sm:col-span-2 whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ calculateCreditBalance(index) }}</td>
                                    <td class="sm:col-span-1 whitespace-nowrap px-3 py-3 text-sm text-gray-500">
                                        <div class="flex items-center justify-start gap-4">
                                            <button @click="toggleDetails(index)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                </svg>
                                            </button>
                                            <Dropdown align="right" width="48">
                                                <template #trigger>
                                                    <button type="button" title="Open details" class="p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100">
                                                        <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                            <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                        </svg>
                                                    </button>
                                                </template>
                                                <template #content>
                                                    <button type="button" v-if="customerData.credit_detail.length == 0"  @click="openPaymentReceiveModal(customerData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                        </svg>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </button>
                                                    <button type="button" @click="openDeleteModal(customerData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                            />
                                                        </svg>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Delete
                                                        </span>
                                                    </button>
                                                </template>
                                            </Dropdown>
                                        </div>
                                    </td>
                                    <div v-if="showDetails[index] && customerData.credit_detail.length > 0" class="divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4">
                                        <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-6 bg-gray-50">
                                            <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">DATE</th>
                                            <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">NARRATION</th>
                                            <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">AMOUNT (₹)</th>
                                        </tr>
                                        <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1" >
                                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-6" v-for="(creditInfo, index) in customerData.credit_detail" :key="index">
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">{{ formatDate(creditInfo.date) ?? '-' }}</td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">{{ creditInfo.invoice.invoice_no ?? '-' }}</td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">{{ formatAmountNew(creditInfo.amount) ?? '-' }}</td>
                                            </tr>
                                        </tbody>
                                    </div>
                                </tr>
                            </tbody>
                            <tbody v-else>
                                <tr class="bg-white">
                                <td colspan="8" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                    No data found.
                                </td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
                    </div>
                </div>
                <div v-else class="p-1 bg-white  shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <tbody>
                        <tr class="bg-white text-center">
                                <td class=" whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                    No data found.
                                </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            </div>
        </div>

        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="paymentReceiveModal" @close="paymentReceiveCloseModal" :maxWidth="paymentReceiveWidth">
             <div class="p-6">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Advance Payment Edit</h2>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Organization" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="organizations"
                                    v-model="paymentform.organization_id"
                                    @onchange="setOrganizations"
                                    :class="{ 'error rounded-md': form.errors[`data.organization_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Payment Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="paymentType"
                                    v-model="paymentform.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors[`data.payment_type`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    @change="clearError('data.amount')"
                                    v-model="paymentform.amount"
                                    :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="check_number" value="Cheque Number" />
                                <TextInput
                                    id="check_number"
                                    type="text"
                                    v-model="paymentform.check_number"
                                    :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="paymentform.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                     @change="clearError('data.date')"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                            </div>
                            <div v-if="payment_type != 'Cash'" class="sm:col-span-6">
                                <InputLabel for="org_bank_id" value="Bank" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="bankinfo"
                                    v-model="paymentform.org_bank_id"
                                    @onchange="setBankInfo"
                                    :class="{ 'error rounded-md': form.errors[`data.org_bank_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    :rows="2"
                                    v-model="paymentform.note"
                                />
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="paymentReceiveCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="acceptPayment"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>

</template>
