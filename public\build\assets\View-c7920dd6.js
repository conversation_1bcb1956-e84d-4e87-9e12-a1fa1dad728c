import{o as d,c as m,b as t,t as s,f as P,K as M,j as i,r as y,a as n,u as $,w as g,F as w,Z as C,d as B,g as j,i as k}from"./app-8a557454.js";import{_ as R}from"./AdminLayout-301d54ca.js";import{_ as T}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as A}from"./SecondaryButton-e65b5ab9.js";import{_ as I}from"./CreateButton-d5560e12.js";import{M as q}from"./Modal-3bbbc3d3.js";import{_ as F}from"./FileViewer-c68b7823.js";/* empty css                                                              */const O={props:{data:{type:Array,required:!0},colspan:{type:Number,default:4},message:{type:String,default:"No Records Found..."}},computed:{noRecords(){return this.data.length===0}}},V={key:0},U=["colspan"];function G(l,c,p,x,b,h){return h.noRecords?(d(),m("tr",V,[t("td",{colspan:p.colspan,class:"py-4 text-center text-sm text-red-500 font-semibold"},s(p.message),9,U)])):P("",!0)}const L=T(O,[["render",G]]),W={class:"animate-top h-screen"},E={class:"sm:flex sm:items-center"},Q=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Purchase Invoice")],-1),z={class:"flex items-center space-x-4"},H={class:"text-sm font-semibold text-gray-900"},K={class:"flex justify-end w-20"},Z={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},J={class:"inline-flex items-start space-x-6 justify-start w-full"},X={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Y={class:"inline-flex items-center justify-start w-full space-x-2"},tt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1),st={class:"text-sm leading-6 text-gray-700"},et={class:"inline-flex items-center justify-start w-full space-x-2"},at=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1),ot={class:"text-sm leading-6 text-gray-700"},lt={class:"inline-flex items-center justify-start w-full space-x-2"},ct=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Invoice No:",-1),rt={class:"text-sm leading-6 text-gray-700"},it={class:"inline-flex items-center justify-start w-full space-x-2"},nt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Invoice Date:",-1),dt={class:"text-sm leading-6 text-gray-700"},mt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},pt={class:"inline-flex items-center justify-start w-full space-x-2"},_t=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1),ut={class:"text-sm leading-6 text-gray-700"},xt={class:"inline-flex items-center justify-start w-full space-x-2"},ht=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1),ft={class:"text-sm leading-6 text-gray-700"},yt={class:"inline-flex items-center justify-start w-full space-x-2"},gt=t("p",{class:"text-sm font-semibold text-gray-900"},"PO Received By",-1),wt={class:"text-sm leading-6 text-gray-700"},bt={class:"flow-root"},vt={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},jt={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden"},kt={class:"p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Pt={class:"min-w-full divide-y divide-gray-300"},Nt=t("thead",{class:"bg-gray-50 border"},[t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10"},[t("th",{scope:"col",class:"py-3.5 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Product Code"),t("th",{scope:"col",class:"py-3.5 sm:col-span-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Product Description"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"HSN"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"GST"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Action")])],-1),St={class:"divide-y divide-gray-300 bg-white"},Dt={class:"whitespace-nowrap sm:col-span-2 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Mt={class:"whitespace-nowrap sm:col-span-3 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},$t={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Ct={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Bt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Rt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Tt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},At=["onClick"],It=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),qt=[It],Ft={key:0,class:"divide-y divide-gray-300 sm:col-span-10 product-details border mx-6 mb-4"},Ot=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Batch"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Expiry Date"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"MRP (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Purchase Price (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Qty")],-1),Vt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},Ut={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Gt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Lt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Wt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Et={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Qt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},zt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ht=t("div",{class:"sm:col-span-3 space-y-2"},null,-1),Kt={class:"sm:col-span-3"},Zt={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Jt={class:"inline-flex items-center justify-end w-full space-x-3"},Xt=t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1),Yt={class:"text-base font-semibold text-gray-900 w-32"},ts={class:"inline-flex items-center justify-end w-full space-x-3"},ss=t("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1),es={class:"text-base font-semibold text-gray-900 w-32"},as={class:"inline-flex items-center justify-end w-full space-x-3"},os=t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1),ls={class:"text-base font-semibold text-gray-900 w-32"},cs={class:"p-6"},rs={class:"mt-6 px-4 flex justify-end"},ys={__name:"View",props:["data"],setup(l){const c=l;M().props.data[0],i(()=>c.data[0].purchase_order_detail?c.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_amount,0):0),i(()=>c.data[0].purchase_order_detail?c.data[0].purchase_order_detail.reduce((e,o)=>e+o.qty,0):0),i(()=>c.data[0].purchase_order_detail?c.data[0].purchase_order_detail.reduce((e,o)=>e+o.receive_qty,0):0),i(()=>c.data[0].purchase_order_detail?c.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_price,0):0),i(()=>c.data[0].purchase_order_detail?c.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_gst_amount,0):0),i(()=>c.receivedOrder?c.receivedOrder.reduce((e,o)=>{const a=o.total_amount;return e+a},0):0);const p=e=>{const o=new Date(e),a={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",a)},x=y([]),b=e=>{x.value[e]=!x.value[e]},h=y(!1),N=y(null),S=y("custom"),v=()=>{h.value=!1},_=e=>{let o=e.toFixed(2).toString(),[a,u]=o.split("."),r=a.substring(a.length-3),f=a.substring(0,a.length-3);return f!==""&&(r=","+r),`${f.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${u}`},D=i(()=>new URLSearchParams(window.location.search).get("source")==="dashboard"?route("dashboard"):route("purchaseinvoice.index"));return(e,o)=>(d(),m(w,null,[n($(C),{title:"Purchase Invoice"}),n(R,null,{default:g(()=>[t("div",W,[t("form",{onSubmit:o[0]||(o[0]=B((...a)=>e.submit&&e.submit(...a),["prevent"])),class:""},[t("div",E,[Q,t("div",z,[t("div",null,[t("p",H,s(l.data[0].purchase_order.organization.name),1)]),t("div",K,[n(I,{href:D.value},{default:g(()=>[j(" Back ")]),_:1},8,["href"])])])]),t("div",Z,[t("div",J,[t("div",X,[t("div",Y,[tt,t("p",st,s(l.data[0].purchase_order.company.name??"-"),1)]),t("div",et,[at,t("p",ot,s(l.data[0].purchase_order.company.gst_no??"-"),1)]),t("div",lt,[ct,t("p",rt,s(l.data[0].customer_invoice_no??"-"),1)]),t("div",it,[nt,t("p",dt,s(p(l.data[0].customer_invoice_date)??"-"),1)])]),t("div",mt,[t("div",pt,[_t,t("p",ut,s(l.data[0].purchase_order.po_number??"-"),1)]),t("div",xt,[ht,t("p",ft,s(p(l.data[0].purchase_order.date)??"-"),1)]),t("div",yt,[gt,t("p",wt,s(l.data[0].users.first_name)+" "+s(l.data[0].users.last_name),1)])])])]),t("div",bt,[t("div",vt,[t("div",jt,[t("div",kt,[t("table",Pt,[Nt,t("tbody",St,[n(L,{data:l.data[0].purchase_order_receive_details,colspan:7},null,8,["data"]),(d(!0),m(w,null,k(l.data[0].purchase_order_receive_details,(a,u)=>(d(),m("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10",key:u},[t("td",Dt,s(a.product.item_code??"-"),1),t("td",Mt,s(a.product.name??"-"),1),t("td",$t,s(a.product.hsn_code??"-"),1),t("td",Ct,s(a.receive_qty??"-"),1),t("td",Bt,s(a.purchase_order_detail.gst??"-"),1),t("td",Rt,s(a.serial_numbers[0].purchase_price??"-"),1),t("td",Tt,[t("button",{onClick:r=>b(u)},qt,8,At)]),x.value[u]&&a.serial_numbers.length!=0?(d(),m("div",Ft,[Ot,t("tbody",Vt,[(d(!0),m(w,null,k(a.serial_numbers,(r,f)=>(d(),m("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:f},[t("td",Ut,s(r.batch??"-"),1),t("td",Gt,s(r.expiry_date!=null?p(r.expiry_date):"-"),1),t("td",Lt,s(r.mrp?_(r.mrp):"-"),1),t("td",Wt,s(r.purchase_price?_(r.purchase_price):"-"),1),t("td",Et,s(r.receive_qty??"-"),1)]))),128))])])):P("",!0)]))),128))])])])])])]),t("div",Qt,[t("div",zt,[Ht,t("div",Kt,[t("div",Zt,[t("div",Jt,[Xt,t("p",Yt,s(_(l.data[0].total_price)),1)]),t("div",ts,[ss,t("p",es,s(_(l.data[0].total_gst_amount)),1)]),t("div",as,[os,t("p",ls,s(_(l.data[0].total_amount)),1)])])])])])],32)]),n(q,{show:h.value,onClose:v,maxWidth:S.value},{default:g(()=>[t("div",cs,[n(F,{fileUrl:e.file+N.value},null,8,["fileUrl"]),t("div",rs,[n(A,{onClick:v},{default:g(()=>[j(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}};export{ys as default};
