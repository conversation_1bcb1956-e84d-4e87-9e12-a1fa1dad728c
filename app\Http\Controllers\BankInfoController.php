<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\BankInfo;
use App\Models\Organization;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\bankInfoStoreRequest;
use Illuminate\Support\Facades\DB;

class BankInfoController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Bank Info')->only(['index']);
        $this->middleware('permission:Banks')->only(['index','create','store','edit','update', 'destroy']);
        $this->middleware('permission:Create Bank Info')->only(['create', 'store']);
        $this->middleware('permission:Edit Bank Info')->only(['edit', 'update']);
        $this->middleware('permission:Delete Bank Info')->only('destroy');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $data = BankInfo::with('organization')->orderBy('id', 'desc')->paginate(10);

        $permissions = [
            'canCreateBankInfo'      => auth()->user()->can('Create Bank Info'),
            'canEditBankInfo'        => auth()->user()->can('Edit Bank Info'),
            'canDeleteBankInfo'      => auth()->user()->can('Delete Bank Info')
        ];

        return Inertia::render('BankInfo/List', compact('data', 'permissions'));
    }

    public function create(Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $amounttype = [['id' => 'cr', 'name' => 'Cr'], ['id' => 'dr', 'name' => 'Dr']];
        return Inertia::render('BankInfo/Add', compact('organization', 'amounttype'));
    }

    public function store(bankInfoStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $existingAccount = BankInfo::where('account_number', $request->account_number)
                                        ->where('bank_name', $request->bank_name)
                                        ->first();

            if ($existingAccount) {
                return Redirect::back()->withInput()->with('error', 'This account number already exists in the bank.');
            }
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            BankInfo::create($data);

            DB::commit();
            return Redirect::to('/bankinfo')->with('success', 'Bank Info Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = BankInfo::find($id);
        $organization  = Organization::select('id', 'name')->get();
        $amounttype = [['id' => 'cr', 'name' => 'Cr'], ['id' => 'dr', 'name' => 'Dr']];
        return Inertia::render('BankInfo/Edit', compact('data', 'organization', 'amounttype'));
    }

    public function update(BankInfoStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $existingAccount = BankInfo::where('account_number', $request->account_number)
                ->where('id', '!=', $request->id)
                ->first();

            if ($existingAccount) {
                return Redirect::back()->withInput()->with('error', 'This account number already exists in the bank.');
            }

            $data = $request->all();
            $data['updated_by'] = auth()->id();

            $bankInfo = BankInfo::findOrFail($request->id);
            $bankInfo->update($data);

            DB::commit();
            return Redirect::to('/bankinfo')->with('success', 'Bank Info Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = BankInfo::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Bank Info Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
