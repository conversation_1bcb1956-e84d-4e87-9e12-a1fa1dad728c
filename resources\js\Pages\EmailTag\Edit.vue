<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head , Link, useForm, usePage } from '@inertiajs/vue3';

const props = defineProps(['data']);

const userData = usePage().props.data;

const form = useForm({
    id: userData.id,
    name:userData.name,
    description:userData.description,
});

</script>

<template>
    <Head title="Email Tags" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Tag</h2>
            <form @submit.prevent="form.patch(route('email-tag.update'))">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-6">
                            <InputLabel for="name" value="Tag Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                                autocomplete="name"
                                @change="form.validate('name')"
                            />
                            <InputError class="" :message="form.errors.name" />
                        </div>
                        <div class="sm:col-span-6">
                            <InputLabel for="description" value="Description" />
                            <TextArea
                                id="description"
                                type="text"
                                :rows="2"
                                v-model="form.description"
                                @change="form.validate('description')"
                            />
                            <InputError class="" :message="form.errors.description" />
                        </div>
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('email-tag.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>

                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
