<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuotationDetail extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'quotation_details';

    protected static $logName = 'Quotation-Detail';

    public function getLogDescription(string $event): string
    {
        $quotationNumber = $this->quotation->quotation_number;

        $productNames = $this->product()->pluck('name')->implode(', ');

        return "Quotation detail for <strong>{$productNames}</strong> : {$quotationNumber} has been {$event} by";
    }

    protected $fillable = [
        'quotation_id',
        'product_id',
        'description',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'is_ordered',
        'created_by',
        'updated_by'
    ];

    protected static $logAttributes = [
        'quotation_id',
        'product_id',
        'description',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'is_ordered',
        'created_by',
        'updated_by'
    ];

    public function quotation()
    {
        return $this->belongsTo(Quotation::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            //return false;
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $mergedAttributes = [];
        $quotationNumber = $model->quotation->quotation_number;
        if($event == 'created' || $event == 'deleted'){
            $mergedAttributes = [
                'quotation'         => $model->quotation->getAttributes(),
                'quotationDetail'   => $model->getAttributes()
            ];
            $logName = "Quotation Number: $quotationNumber";
        }else{
            $logName = "Quotation details $event for $quotationNumber";
        }

        self::addCustomLogEntry($model, $event, $logName, $mergedAttributes);
    }

}
