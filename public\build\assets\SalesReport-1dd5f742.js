import{K as q,r as d,l as K,j as X,o as m,c as p,a as c,u as v,w as C,F as k,Z as Y,b as t,g as N,t as _,k as M,v as A,n as E,i as I,e as O,f as T}from"./app-8a557454.js";import{_ as H}from"./AdminLayout-301d54ca.js";import{_ as Z}from"./CreateButton-d5560e12.js";/* empty css                                                              */import{_ as J}from"./Pagination-da60561b.js";import{_ as Q}from"./SimpleDropdown-0b1c9d92.js";import"./html2canvas.esm-96b9a1c9.js";import{_ as x}from"./InputLabel-07f3a6e8.js";import{_ as W}from"./ArrowIcon-10b49f20.js";import{s as tt}from"./sortAndSearch-c3af03c0.js";const et={class:"animate-top"},ot={class:"flex justify-between items-center"},at=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Sales Report")],-1),st={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},rt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},lt={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},nt=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),it={class:"flex ml-6"},dt={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ct={class:"flex justify-between mb-2"},ut={class:"flex"},mt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),_t={class:"inline-flex items-center space-x-4 justify-end w-full"},gt={class:"text-base font-semibold text-gray-900"},ht=["src"],ft={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},pt={class:"sm:col-span-4"},vt={class:"relative mt-2"},xt={class:"sm:col-span-4"},bt={class:"sm:col-span-4"},yt={class:"mt-8 overflow-x-auto sm:rounded-lg"},wt={class:"shadow sm:rounded-lg"},kt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},St={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},zt={class:"border-b-2"},Ct=["onClick"],Nt={key:0},Mt={scope:"row",class:"px-4 py-2.5 min-w-36 whitespace-nowrap"},At={class:"px-4 py-2.5 min-w-28"},Et={class:"px-4 py-2.5 min-w-28"},It={class:"px-4 py-2.5 min-w-32 font-medium text-gray-900"},Ot={class:"px-4 py-2.5 min-w-32"},Tt={class:"px-4 py-2.5 min-w-32"},$t={key:1},Rt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ut=[Rt],Ht={__name:"SalesReport",props:["data","total_data","organization","organization_id","organizationId"],setup(u){const r=u,{form:y,search:Vt,sort:$,fetchData:Bt,sortKey:R,sortDirection:U,updateParams:V}=tt("sales.report",{organization_id:r.organizationId,from_date:r.from_date,to_date:r.to_date});q().props.data.links.find(a=>a.active===!0);const l=d(""),n=d("");d("Salse Report");const s=d(r.organizationId);d(r.customerId),d(r.invoice_type),d(r.salesUserId),d(r.categoryId);const w=d("");K([s,l,n],()=>{V({organization_id:s.value,from_date:l.value,to_date:n.value})});const B=[{field:"invoice_no",label:"INVOICE NUMBER",sortable:!0},{field:"invoice_type",label:"TYPE",sortable:!0},{field:"category",label:"CATEGORY",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0}],b=(a,o,e,g)=>{y.get(route("sales.report",{search:a,organization_id:o,from_date:e,to_date:g}),{preserveState:!0})},P=(a,o)=>{s.value=a,b(w.value,s.value,l.value,n.value)},j=()=>{b(w.value,s.value,l.value,n.value)},D=()=>{b(w.value,s.value,l.value,n.value)},F=()=>{let a="";switch(s.value){case 1:a="MC";break;case 2:a="HC";break;case 3:a="NOX";break;default:a="All_Organizations";break}const o=`Sales_Report_${a}`,e={organization_id:s.value||"",from_date:l.value||"",to_date:n.value||""},h=`/export-sales-report?${new URLSearchParams(e).toString()}`;fetch(h,{method:"GET"}).then(i=>{if(!i.ok)throw new Error("Network response was not ok");return i.blob()}).then(i=>{const z=window.URL.createObjectURL(new Blob([i])),f=document.createElement("a");f.href=z,f.setAttribute("download",`${o}.xlsx`),document.body.appendChild(f),f.click(),document.body.removeChild(f)}).catch(i=>{console.error("Error exporting data:",i)})},L=a=>{const o=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},S=a=>{let o=a.toFixed(2).toString(),[e,g]=o.split("."),h=e.substring(e.length-3),i=e.substring(0,e.length-3);return i!==""&&(h=","+h),`${i.replace(/\B(?=(\d{2})+(?!\d))/g,",")+h}.${g}`},G=X(()=>Math.round(Math.round(r.total_data.reduce((a,o)=>a+(o.total_amount?parseFloat(o.total_amount):0),0))));return(a,o)=>(m(),p(k,null,[c(v(Y),{title:"Sales Report"}),c(H,null,{default:C(()=>[t("div",et,[t("div",ot,[at,t("div",st,[t("div",rt,[t("div",lt,[nt,t("input",{id:"search-field",onInput:o[0]||(o[0]=e=>b(e.target.value,s.value,l.value,n.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",it,[c(Z,{href:a.route("reports")},{default:C(()=>[N(" Back ")]),_:1},8,["href"])])])]),t("div",dt,[t("div",ct,[t("div",ut,[mt,c(x,{for:"customer_id",value:"Filters"})]),t("div",_t,[c(x,{for:"date",value:"Total Amount (₹):"}),t("p",gt,_(S(G.value)),1),t("button",{onClick:F},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ht)])])]),t("div",ft,[t("div",pt,[c(x,{for:"customer_id",value:"Organization Name"}),t("div",vt,[c(Q,{options:u.organization,modelValue:s.value,"onUpdate:modelValue":o[1]||(o[1]=e=>s.value=e),onOnchange:P},null,8,["options","modelValue"])])]),t("div",xt,[c(x,{for:"date",value:"From Date"}),M(t("input",{"onUpdate:modelValue":o[2]||(o[2]=e=>l.value=e),class:E(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":v(y).errors.from_date}]),type:"date",onChange:j},null,34),[[A,l.value]])]),t("div",bt,[c(x,{for:"date",value:"To Date"}),M(t("input",{"onUpdate:modelValue":o[3]||(o[3]=e=>n.value=e),class:E(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":v(y).errors.to_date}]),type:"date",onChange:D},null,34),[[A,n.value]])])])]),t("div",yt,[t("div",wt,[t("table",kt,[t("thead",St,[t("tr",zt,[(m(),p(k,null,I(B,(e,g)=>t("th",{key:g,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:h=>v($)(e.field,e.sortable)},[N(_(e.label)+" ",1),e.sortable?(m(),O(W,{key:0,isSorted:v(R)===e.field,direction:v(U)},null,8,["isSorted","direction"])):T("",!0)],8,Ct)),64))])]),u.data.data&&u.data.data.length>0?(m(),p("tbody",Nt,[(m(!0),p(k,null,I(u.data.data,(e,g)=>(m(),p("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",Mt,_(e.invoice_no),1),t("td",At,_(e.invoice_type),1),t("td",Et,_(e.category),1),t("td",It,_(e.customers.customer_name),1),t("td",Ot,_(L(e.date)),1),t("td",Tt,_(S(e.total_amount)),1)]))),128))])):(m(),p("tbody",$t,Ut))])])]),u.data.data&&u.data.data.length>0?(m(),O(J,{key:0,class:"mt-6",links:u.data.links},null,8,["links"])):T("",!0)])]),_:1})],64))}};export{Ht as default};
