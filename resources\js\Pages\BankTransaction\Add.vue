<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['paymentType', 'accounttype', 'bank']);

const form = useForm('post', '/banktransaction', {
    org_bank_id: props.bank.id,
    payment_type: '',
    account_type:'',
    date:'',
    note:'',
    amount:''
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

const setPaymentType = (id, name) => {
    form.payment_type = id;
    form.errors.payment_type = null;
};

const setAccountType = (id, name) => {
    form.account_type = id;
    form.errors.account_type = null;
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

</script>

<template>
    <Head title="Transactions" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Transaction</h1>
                </div>
                <p class="text-sm font-semibold text-gray-900">{{ bank.bank_name }}</p>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">
                        <div class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Payment Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="paymentType"
                                    v-model="form.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors.payment_type }"
                                />
                                <InputError  v-if="form.invalid('payment_type')" class="" :message="form.errors.payment_type" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="form.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                                <InputError  v-if="form.invalid('date')" class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="amount" value="Amount" />
                            <TextInput
                                id="amount"
                                type="text"
                                @change="clearError('data.amount')"
                                v-model="form.amount"
                                :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                            />
                            <InputError  v-if="form.invalid('amount')" class="" :message="form.errors.amount" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="account_type" value="Account Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="accounttype"
                                    v-model="form.account_type"
                                    @onchange="setAccountType"
                                    :class="{ 'error rounded-md': form.errors.account_type }"
                                />
                                <InputError  v-if="form.invalid('account_type')" class="" :message="form.errors.account_type" />
                            </div>
                        </div>
                        <div class="sm:col-span-6">
                            <InputLabel for="note" value="Narration" />
                            <TextInput
                                id="note"
                                type="text"
                                v-model="form.note"
                                :class="{ 'error rounded-md': form.errors[`data.note`] }"
                            />
                            <InputError  v-if="form.invalid('note')" class="" :message="form.errors.note" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('banktransaction.show',{id:bank.id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

