<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head , Link, useForm, usePage } from '@inertiajs/vue3';

const userData = usePage().props.data;

defineProps({
    data: Object,
    types: Array,
    customer_type: Array,
    gst_type: Array,
    is_organization: Array,
    organization: Array
});

const form = useForm({
    id: userData.id,
    customer_name: userData.customer_name,
    customer_type: userData.customer_type,
    gst_type:  userData.gst_type,
    person_name: userData.person_name,
    address: userData.address,
    city: userData.city,
    contact_no: userData.contact_no,
    email: userData.email,
    drug_licence_no: userData.drug_licence_no,
    gst_no: userData.gst_no,
    type: userData.type,
    is_organization: userData.is_organization,
    organization_id: userData.organization_id,
});

const setDropdownValue = (field, id, name) => {
    switch (field) {
        case 'customer_type':
            form.errors.customer_type = null;
            break;
        case 'gst_type':
            form.errors.gst_type = null;
            break;
        case 'type':
            form.errors.type = null;
            break;
        default:
            console.warn(`Unhandled field: ${field}`);
    }
    form[field] = id;
};

</script>

<template>
    <Head title="Customers" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Customer</h2>
            <form @submit.prevent="form.patch(route('customers.update'))">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-3">
                            <InputLabel for="customer_name" value="Customer Name" />
                            <TextInput
                                id="customer_name"
                                type="text"
                                v-model="form.customer_name"
                                @change="form.validate('customer_name')"
                            />
                            <InputError class="" :message="form.errors.customer_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="person_name" value="Contact Person Name" />
                            <TextInput
                                id="person_name"
                                type="text"
                                v-model="form.person_name"
                            />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="Customer Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="customer_type"
                                    v-model="form.customer_type"
                                    @onchange="(id, name) => setDropdownValue('customer_type', id, name)"
                                    @change="form.validate('customer_type')"
                                />
                            </div>
                            <InputError class="" :message="form.errors.customer_type" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="GST Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="gst_type"
                                    v-model="form.gst_type"
                                    @onchange="(id, name) => setDropdownValue('gst_type', id, name)"
                                    @change="form.validate('gst_type')"
                                />
                            </div>
                            <InputError class="" :message="form.errors.gst_type" />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="Occupation Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="types"
                                    v-model="form.type"
                                    @onchange="(id, name) => setDropdownValue('type', id, name)"
                                />
                            </div>
                            <InputError class="" :message="form.errors.type" />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="email" value="Email" />
                            <TextInput
                                id="email"
                                type="email"
                                v-model="form.email"
                            />
                            <InputError class="" :message="form.errors.email" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="contact_no" value="Contact No" />
                            <TextInput
                                id="contact_no"
                                type="text"
                                :numeric="true"
                                maxLength="10"
                                v-model="form.contact_no"
                                @change="form.validate('contact_no')"
                            />
                            <InputError class="" :message="form.errors.contact_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="drug_licence_no" value="Drug Licence No" />
                            <TextInput
                                id="drug_licence_no"
                                type="text"
                                v-model="form.drug_licence_no"
                            />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="gst_no" value="GST No" />
                            <TextInput
                                id="gst_no"
                                type="text"
                                maxLength="15"
                                v-model="form.gst_no"
                                :class="{ 'error rounded-md': form.errors.gst_no }"
                            />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="city" value="City" />
                            <TextInput
                                id="city"
                                type="text"
                                v-model="form.city"
                                 :class="{ 'error rounded-md': form.errors.city }"

                            />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="Is Organization ?"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="is_organization"
                                    v-model="form.is_organization"
                                    @onchange="(id, name) => setDropdownValue('is_organization', id, name)"
                                    :class="{ 'error rounded-md': form.errors.is_organization }"
                                />
                            </div>
                        </div>
                        <div v-if="userData.is_organization == 'yes' || form.is_organization == 'yes'" class="sm:col-span-2">
                            <InputLabel for="type" value="Organization Name"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="organization"
                                    v-model="form.organization_id"
                                    @onchange="(id, name) => setDropdownValue('organization_id', id, name)"
                                    :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel for="address" value="Address" />
                            <TextArea
                                id="address"
                                type="text"
                                :rows="4"
                                v-model="form.address"
                                @change="form.validate('address')"
                            />
                            <InputError class="" :message="form.errors.address" />
                        </div>
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('customers.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Update</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>


<style scoped>
.error {
  border: 1px solid red;
}
</style>
