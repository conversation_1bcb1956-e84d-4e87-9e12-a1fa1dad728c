<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ActivityTrait;

class PaymentPaid extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'payment_paid';

    protected $casts = [
        'invoice_data' => 'array',  // Automatically decode JSON to an array
    ];

    protected static $logName = 'PaymentPaid';
    public function getLogDescription(string $event): string
    {
        $companyName = $this->company ? $this->company->name : 'Unknown Company';
        $user = $this->updatedBy ? "{$this->updatedBy->first_name} {$this->updatedBy->last_name}" : 'Unknown User';

        if ($event === 'deleted') {
            return "Payment has been <strong style='color: red;'>Cancelled</strong> by <strong>{$user}</strong> for <strong>{$companyName}</strong>.";
        }

        return "Payment has been {$event} for <strong>{$companyName}</strong>.";
    }

    public function getInvoiceDataAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    protected static $logAttributes  = [
        'organization_id',
        'purchase_order_receive_id',
        'invoice_data',
        'org_bank_id',
        'company_id',
        'invoice_no',
        'amount',
        'discount_amount',
        'round_off',
        'payment_type',
        'check_number',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'organization_id',
        'purchase_order_receive_id',
        'invoice_data',
        'org_bank_id',
        'company_id',
        'invoice_no',
        'amount',
        'discount_amount',
        'round_off',
        'payment_type',
        'check_number',
        'date',
        'note',
        'created_by',
        'updated_by'
    ];

    public function company(){
        return $this->belongsTo(Company::class,'company_id','id');
    }

    public function bankInfo(){
        return $this->belongsTo(BankInfo::class,'org_bank_id','id');
    }

    // public function bankTransactions()
    // {
    //     return $this->morphMany(BankTransaction::class, 'entity');
    // }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    public function credit()
    {
        return $this->belongsTo(CompanyCredit::class,'id','payment_paid_id');
    }

}
