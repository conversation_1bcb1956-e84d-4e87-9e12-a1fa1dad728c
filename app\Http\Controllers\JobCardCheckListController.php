<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\JobChecklist;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\JobcardChecklistRequest;
use Illuminate\Support\Facades\DB;
use App\Traits\QueryTrait;

class JobCardCheckListController extends Controller
{
    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:Job Card Checklist')->only(['index','create', 'store','edit', 'update','destroy']);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');

        $query = JobChecklist::query();

        $searchableFields = ['type'];
        $this->searchAndSort($query, $request, $searchableFields);
        
        $data = $query->orderBy('id', 'desc')->paginate(10);

        $permissions = [
        ];
        return Inertia::render('JobCardCheckList/List', compact('data', 'permissions'));
    }

    public function create(Request $request)
    {
        return Inertia::render('JobCardCheckList/Add');
    }

    public function store(JobcardChecklistRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            JobChecklist::create($data);
            DB::commit();
            return Redirect::to('/jobcard-checklist')->with('success', 'Checklist Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = JobChecklist::find($id);
        return Inertia::render('JobCardCheckList/Edit', compact('data'));
    }

    public function update(JobcardChecklistRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();;
            $user = JobChecklist::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to('/jobcard-checklist')->with('success', 'Checklist Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = JobChecklist::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Checklist Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
