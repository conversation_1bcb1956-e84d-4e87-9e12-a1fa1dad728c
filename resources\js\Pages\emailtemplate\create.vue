
<script setup>
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SvgLink from '@/Components/ActionLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { QuillEditor } from "@vueup/vue-quill";
import Modal from '@/Components/Modal.vue';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

defineProps(['tags'])

const form = useForm({
  email_subject: '',
  template_name: '',
  content: '',
});

const modalVisible = ref(false);
const selectedTag = ref(null);

const openTagsModal = (tag) => {
  selectedTag.value = tag;
  modalVisible.value = true;
};

const closeTagsModal = () => {
  modalVisible.value = false;
  selectedTag.value = null;
};

const copiedIndex = ref(null);
const copiedField = ref('');

const copyToClipboard = (text, index, field) => {
  navigator.clipboard.writeText(text).then(() => {
    copiedIndex.value = index;
    copiedField.value = field;

    setTimeout(() => {
      copiedIndex.value = null;
      copiedField.value = '';
    }, 2000);
  });
};

const submit = () => {
  console.log("Submitting:", form);
  form.post(route('emailtemplates.store'), {
    preserveScroll: true,
    onSuccess: () => {
      console.log("Form submitted successfully");
      form.reset();
    },
    onError: (errors) => console.log("Errors:", errors)
  });
};

</script>
<template>
  <Head title="Email Template" />
    <AdminLayout>
        <div class="animate-top">
            <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900 flex justify-between items-center">
                    Create Email Template
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <PrimaryButton
                            @click="openTagsModal(tag)">
                            Email Tags
                        </PrimaryButton>
                    </div>
                </h2>
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="email_subject"  value="Email Subject" class="block text-sm font-medium text-gray-700" />
                                <TextInput
                                    id="email_subject"
                                    type="text"
                                    v-model="form.email_subject"
                                    autocomplete="email_subject"
                                    @change="form.validate('email_subject')"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="template_name" value="Template Name" class="block text-sm font-medium text-gray-700" />
                                <TextInput
                                    id="template_name"
                                    type="text"
                                    v-model="form.template_name"
                                    autocomplete="template_name"
                                    @change="form.validate('template_name')"
                                />
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel value="Email Content" class="block text-sm font-medium text-gray-700" />
                                <quill-editor v-model:content="form.content" contentType="html" theme="snow" toolbar="essential"></quill-editor>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('emailtemplates.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                                </template>
                            </SvgLink>
                            <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                            <Transition
                                enter-active-class="transition ease-in-out"
                                enter-from-class="opacity-0"
                                leave-active-class="transition ease-in-out"
                                leave-to-class="opacity-0"
                            >
                                <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                            </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <Modal :show="modalVisible" @close="closeTagsModal">
            <div class="p-6 relative">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    class="h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500"
                    @click="closeTagsModal"
                    >
                    <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                    />
                </svg>
                <h2 class="text-lg font-medium text-gray-900">Email Tags</h2>
                <div class="mt-4 overflow-x-auto sm:rounded-lg">
                    <table class="w-full text-sm text-left text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                            <th class="px-4 py-2 text-gray-900">Tag Name</th>
                            <th class="px-4 py-2 text-gray-900">Description</th>
                            </tr>
                        </thead>
                        <tr v-if="tags.length === 0">
                            <td colspan="2" class="px-4 py-4 text-center text-gray-500">
                                No Email Tags Found.
                            </td>
                        </tr>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-for="(tag, index) in tags" :key="index">
                                <td class="px-4 py-2">
                                    <div class="flex items-center space-x-2">
                                        <span>{{ tag.name }}</span>
                                        <span @click="copyToClipboard(tag.name, index, 'name')" class="cursor-pointer">
                                            📋
                                        </span>
                                    </div>
                                    <span v-if="copiedIndex === index && copiedField === 'name'" class="text-green-600 text-xs">
                                        Copied!
                                    </span>
                                </td>
                                <td class="px-4 py-2">
                                    <div class="flex items-center space-x-2">
                                        <span>{{ tag.description }}</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
