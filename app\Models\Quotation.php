<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Quotation extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'quotation';

    protected static $logName = 'Quotation';

    public function getLogDescription(string $event): string
    {
        $customerName = optional($this->customers)->customer_name ?? 'Unknown Customer';

        return "<strong>{$customerName}'s</strong> Quotation <strong>{$this->quotation_number}</strong> has been {$event} by";
    }

    protected $fillable = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_number',
        'date',
        'status',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];

    protected static $logAttributes = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_number',
        'date',
        'status',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];

    public function customers()
    {
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function users()
    {
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function quotationDetail()
    {
        return $this->hasMany(QuotationDetail::class);
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'quotation');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public static function boot()
    {
        parent::boot();

        /*static::updating(function ($model) {
            // Disable Spatie logging for updates (handled manually)
            static::$logAttributes = [];
        });*/

        static::updated(function ($model) {
            if ($model->isDirty()) {
                $event = $model->isDirty('status') ? strtolower($model->status) : 'updated';
                self::handleLogEntry($model, $event);
                //self::handleLogEntry($model, 'updated');
            }
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        if($event == 'Accepted'){
            $logName = "Quotation " . $model->quotation_number . " is Convert to Order";
        }else{
            $logName = "Quotation $event for " . $model->quotation_number;
        }

        foreach ($model->quotationDetail as $detail){
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
        //self::addCustomLogEntry($model, $event, $logName);
    }

    protected static function handleLogEntryV2($model, $event)
    {

        $quotationDetails = $model->quotationDetail->map(function ($detail) {
            return $detail; //$detail->getDirty();
        });
        $mergedAttributes = [
            'quotation'       => $model->getAttributes(),
            'quotationDetails' => $quotationDetails
        ];

        $logName = ($event == 'accepted')
            ? "Quotation {$model->quotation_number} is Convert to Order"
            : "Quotation $event for {$model->quotation_number}";

        activity(static::$logName)
            ->causedBy(auth()->user())
            ->performedOn($model)
            ->withProperties($mergedAttributes)
            ->event($event)
            ->log($logName);

        foreach ($model->quotationDetail as $detail){
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
        //self::addCustomLogEntry($model, $event, $logName, $mergedAttributes);
    }

}
