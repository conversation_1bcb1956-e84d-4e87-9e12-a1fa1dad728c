import{K as y,h as v,o as p,c,a as t,u as s,w as r,F as f,Z as V,b as l,d as b,g as x,T as w,f as $}from"./app-8a557454.js";import{_ as C,a as U}from"./AdminLayout-301d54ca.js";import{_ as d}from"./InputError-ccd7f9dc.js";import{_ as i}from"./InputLabel-07f3a6e8.js";import{P as N}from"./PrimaryButton-9d9bcdd8.js";import{_ as m}from"./TextInput-ab168ee4.js";import{_ as T}from"./TextArea-3588e81e.js";import{_ as S}from"./SearchableDropdown-51a69527.js";import"./_plugin-vue_export-helper-c27b6911.js";const h={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},E=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Company Details",-1),B={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},k={class:"sm:col-span-3"},A={class:"sm:col-span-3"},D={class:"sm:col-span-2"},G={class:"relative mt-2"},L={class:"sm:col-span-2"},F={class:"sm:col-span-2"},O={class:"sm:col-span-2"},P={class:"sm:col-span-2"},K=l("div",{class:"sm:col-span-2"},null,-1),M={class:"sm:col-span-2"},W={class:"sm:col-span-2"},Z={class:"sm:col-span-4"},q={class:"flex mt-6 items-center justify-between"},z={class:"ml-auto flex items-center justify-end gap-x-6"},H=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),I={key:0,class:"text-sm text-gray-600"},oe={__name:"Edit",props:{data:{type:Object},company_type:{type:Array},gst_type:{type:Array}},setup(_){const n=y().props.data,e=v({id:n.id,name:n.name,website:n.website,address:n.address,city:n.city,state:n.state,contact_no:n.contact_no,email:n.email,drug_licence_no:n.drug_licence_no,gst_no:n.gst_no,type:n.type,company_type:n.company_type,gst_type:n.gst_type}),g=(u,a)=>{e.errors.gst_type=null,e.gst_type=a};return(u,a)=>(p(),c(f,null,[t(s(V),{title:"Company Edit"}),t(C,null,{default:r(()=>[l("div",h,[E,l("form",{onSubmit:a[15]||(a[15]=b(o=>s(e).patch(u.route("companies.update")),["prevent"]))},[l("div",B,[l("div",j,[l("div",k,[t(i,{for:"name",value:"Name"}),t(m,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":a[0]||(a[0]=o=>s(e).name=o),onChange:a[1]||(a[1]=o=>s(e).validate("name"))},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.name},null,8,["message"])]),l("div",A,[t(i,{for:"website",value:"Website"}),t(m,{id:"website",type:"text",modelValue:s(e).website,"onUpdate:modelValue":a[2]||(a[2]=o=>s(e).website=o)},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.website},null,8,["message"])]),l("div",D,[t(i,{for:"type",value:"GST Type"}),l("div",G,[t(S,{options:_.gst_type,modelValue:s(e).gst_type,"onUpdate:modelValue":a[3]||(a[3]=o=>s(e).gst_type=o),onOnchange:g,onChange:a[4]||(a[4]=o=>s(e).validate("gst_type"))},null,8,["options","modelValue"])]),t(d,{class:"",message:s(e).errors.gst_type},null,8,["message"])]),l("div",L,[t(i,{for:"gst_no",value:"GST No"}),t(m,{id:"gst_no",type:"text",modelValue:s(e).gst_no,"onUpdate:modelValue":a[5]||(a[5]=o=>s(e).gst_no=o),maxLength:"15"},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.gst_no},null,8,["message"])]),l("div",F,[t(i,{for:"drug_licence_no",value:"Drug Licence No"}),t(m,{id:"drug_licence_no",type:"text",modelValue:s(e).drug_licence_no,"onUpdate:modelValue":a[6]||(a[6]=o=>s(e).drug_licence_no=o)},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.drug_licence_no},null,8,["message"])]),l("div",O,[t(i,{for:"city",value:"City"}),t(m,{id:"city",type:"text",modelValue:s(e).city,"onUpdate:modelValue":a[7]||(a[7]=o=>s(e).city=o),onChange:a[8]||(a[8]=o=>s(e).validate("city"))},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.city},null,8,["message"])]),l("div",P,[t(i,{for:"state",value:"State"}),t(m,{id:"state",type:"text",modelValue:s(e).state,"onUpdate:modelValue":a[9]||(a[9]=o=>s(e).state=o),onChange:a[10]||(a[10]=o=>s(e).validate("state"))},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.state},null,8,["message"])]),K,l("div",M,[t(i,{for:"email",value:"Email"}),t(m,{id:"email",type:"email",modelValue:s(e).email,"onUpdate:modelValue":a[11]||(a[11]=o=>s(e).email=o)},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.email},null,8,["message"])]),l("div",W,[t(i,{for:"contact_no",value:"Contact No"}),t(m,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:s(e).contact_no,"onUpdate:modelValue":a[12]||(a[12]=o=>s(e).contact_no=o)},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.contact_no},null,8,["message"])]),l("div",Z,[t(i,{for:"address",value:"Address"}),t(T,{id:"address",type:"text",rows:4,modelValue:s(e).address,"onUpdate:modelValue":a[13]||(a[13]=o=>s(e).address=o),onChange:a[14]||(a[14]=o=>s(e).validate("address"))},null,8,["modelValue"]),t(d,{class:"",message:s(e).errors.address},null,8,["message"])])])]),l("div",q,[l("div",z,[t(U,{href:u.route("companies.index")},{svg:r(()=>[H]),_:1},8,["href"]),t(N,{disabled:s(e).processing},{default:r(()=>[x("Update")]),_:1},8,["disabled"]),t(w,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:r(()=>[s(e).recentlySuccessful?(p(),c("p",I,"Saved.")):$("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{oe as default};
