import{_ as N,b as A,a as M}from"./AdminLayout-f002e683.js";import{_ as T}from"./CreateButton-400e96c7.js";import{_ as $}from"./SecondaryButton-98872fc5.js";import{D as z}from"./DangerButton-fc55f8d0.js";import{M as S}from"./Modal-e8ed59aa.js";import{_ as j}from"./Pagination-24879c4b.js";import{r as u,o as a,c as n,a as o,u as i,w as s,F as _,Z as I,b as t,g as d,i as p,e as b,f as x,t as r}from"./app-497d70e1.js";import{_ as O}from"./ArrowIcon-92b60770.js";import{s as V}from"./sortAndSearch-07be74d0.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const E={class:"animate-top"},U={class:"flex justify-between items-center"},F=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Bank Transaction Accounts")],-1),L={class:"flex justify-end"},K=t("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[t("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),t("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),R={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Z={class:"flex justify-end"},D={class:"mt-8 overflow-x-auto sm:rounded-lg"},G={class:"shadow sm:rounded-lg"},H={class:"w-full text-sm text-left rtl:text-right text-gray-500"},q={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},J={class:"border-b-2"},P=["onClick"],Q={key:0},W={scope:"row",class:"px-4 py-2.5"},X={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Y={class:"px-4 py-2.5 min-w-52"},tt={class:"px-4 py-2.5 min-w-32"},et={class:"px-4 py-2.5 min-w-32"},st={class:"font-bold text-gray-900"},at={class:"items-center px-4 py-2.5"},ot={class:"flex items-center justify-start gap-4"},lt=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),nt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 6l4 4M4 6l4-4M20 18H4m16 0l-4-4m4 4l-4 4"})],-1),rt=t("span",{class:"text-sm text-gray-700 leading-5"}," View Transaction ",-1),it={key:1},dt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ct=[dt],_t={class:"p-6"},ft=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),mt={class:"mt-6 flex justify-end"},Tt={__name:"List",props:["data"],setup(l){const{form:g,search:ht,sort:y,fetchData:ut,sortKey:w,sortDirection:v}=V("banktransaction.index"),f=u(!1),k=u(null),C=[{field:"bank_name",label:"BANK NAME",sortable:!0},{field:"account_number",label:"ACCOUNT NUMBER",sortable:!0},{field:"ifsc_code",label:"IFSC CODE",sortable:!0},{field:"organization.name",label:"ORGANIZATION",sortable:!0},{field:"balance",label:"BALANCE (₹)",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],c=()=>{f.value=!1},B=()=>{g.delete(route("bankinfo.destroy",{id:k.value}),{onSuccess:()=>c()})};return(m,pt)=>(a(),n(_,null,[o(i(I),{title:"Bank Transactions"}),o(N,null,{default:s(()=>[t("div",E,[t("div",U,[F,t("div",L,[K,t("div",R,[t("div",Z,[o(T,{href:m.route("internalbanktransfer")},{default:s(()=>[d(" Internal Transfer ")]),_:1},8,["href"])])])])]),t("div",D,[t("div",G,[t("table",H,[t("thead",q,[t("tr",J,[(a(),n(_,null,p(C,(e,h)=>t("th",{key:h,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:bt=>i(y)(e.field,e.sortable)},[d(r(e.label)+" ",1),e.sortable?(a(),b(O,{key:0,isSorted:i(w)===e.field,direction:i(v)},null,8,["isSorted","direction"])):x("",!0)],8,P)),64))])]),l.data.data&&l.data.data.length>0?(a(),n("tbody",Q,[(a(!0),n(_,null,p(l.data.data,(e,h)=>(a(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",W,r(e.bank_name??"-"),1),t("td",X,r(e.account_number??"-"),1),t("td",Y,r(e.ifsc_code??"-"),1),t("td",tt,r(e.organization.name??"-"),1),t("td",et,[t("span",st,r(e.formatted_balance??"-"),1)]),t("td",at,[t("div",ot,[o(A,{align:"right",width:"48"},{trigger:s(()=>[lt]),content:s(()=>[o(M,{href:m.route("banktransaction.show",{id:e.id})},{svg:s(()=>[nt]),text:s(()=>[rt]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])):(a(),n("tbody",it,ct))])])]),l.data.data&&l.data.data.length>0?(a(),b(j,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):x("",!0)]),o(S,{show:f.value,onClose:c},{default:s(()=>[t("div",_t,[ft,t("div",mt,[o($,{onClick:c},{default:s(()=>[d(" Cancel ")]),_:1}),o(z,{class:"ml-3",onClick:B},{default:s(()=>[d(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Tt as default};
