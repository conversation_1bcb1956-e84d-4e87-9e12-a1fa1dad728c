<?php 

namespace App\DTO;

use App\Traits\ArrayToProps;

class ChallanDTO
{
    use ArrayToProps;

    public $category;
    public $organization_id;
    public $customer_id;
    public $sales_user_id;
    public $challan_number;
    public $date;
    public $note;
    public $sub_total;
    public $total_discount;
    public $igst;
    public $cgst;
    public $sgst;
    public $document;
    public $total_gst;
    public $total_amount;
    public $transport;
    public $dispatch;
    public $challan_id;
    public $stock_transfer;
    public $serial_number_id;
    public $selectedProductItem;
    public $created_by;
    public $updated_by;

}