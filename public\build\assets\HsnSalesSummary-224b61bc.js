import{K as M,h as $,r,o as B,c as L,a as n,u as h,w as x,F as T,Z as U,b as e,g as j,k as w,v as y,n as b}from"./app-8a557454.js";import{_ as D}from"./AdminLayout-301d54ca.js";import{_ as E}from"./CreateButton-d5560e12.js";/* empty css                                                              */import{_ as H}from"./SimpleDropdown-0b1c9d92.js";import{_ as m}from"./InputLabel-07f3a6e8.js";const I={class:"animate-top h-screen"},A={class:"flex justify-between items-center"},F=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"HSN Sales Summary")],-1),R={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},q={class:"flex ml-6"},G={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},P={class:"flex justify-between mb-2"},X={class:"flex"},K=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Y={class:"inline-flex items-center space-x-4 justify-end w-full"},Z=["src"],J={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},Q={class:"sm:col-span-4"},W={class:"relative mt-2"},ee={class:"sm:col-span-4"},te={class:"sm:col-span-4"},ie={__name:"HsnSalesSummary",props:["data","organization","invoices","organizationId"],setup(f){const v=f;M().props.data.links.find(t=>t.active===!0);const u=$({}),o=r(v.organizationId),k=r(v.companyId);r("ALL COMPANY");const l=r(""),i=r(""),g=r(""),_=(t,s,a,p)=>{u.get(route("hsn.sales.summary",{search:t,organization_id:s,from_date:a,to_date:p}),{preserveState:!0})},S=(t,s)=>{o.value=t,_(g.value,o.value,k.value,l.value,i.value)};r([]);const z=()=>{let t="";switch(o.value){case 1:t="MC";break;case 2:t="HC";break;case 3:t="NOX";break;default:t="All_Organizations";break}const s=`HSN_Sales_Summary_${t}`,a={organization_id:o.value||"",from_date:l.value||"",to_date:i.value||""},O=`/export-hsn-sales-summary?${new URLSearchParams(a).toString()}`;fetch(O,{method:"GET"}).then(d=>{if(!d.ok)throw new Error("Network response was not ok");return d.blob()}).then(d=>{const V=window.URL.createObjectURL(new Blob([d])),c=document.createElement("a");c.href=V,c.setAttribute("download",`${s}.xlsx`),document.body.appendChild(c),c.click(),document.body.removeChild(c)}).catch(d=>{console.error("Error exporting data:",d)})},C=()=>{_(g.value,o.value,l.value,i.value)},N=()=>{_(g.value,o.value,l.value,i.value)};return(t,s)=>(B(),L(T,null,[n(h(U),{title:"Hsn Sales Report"}),n(D,null,{default:x(()=>[e("div",I,[e("div",A,[F,e("div",R,[e("div",q,[n(E,{href:t.route("reports")},{default:x(()=>[j(" Back ")]),_:1},8,["href"])])])]),e("div",G,[e("div",P,[e("div",X,[K,n(m,{for:"customer_id",value:"Filters"})]),e("div",Y,[e("button",{onClick:z},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,Z)])])]),e("div",J,[e("div",Q,[n(m,{for:"customer_id",value:"Organization Name"}),e("div",W,[n(H,{options:f.organization,modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=a=>o.value=a),onOnchange:S},null,8,["options","modelValue"])])]),e("div",ee,[n(m,{for:"date",value:"From Date"}),w(e("input",{"onUpdate:modelValue":s[1]||(s[1]=a=>l.value=a),class:b(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(u).errors.from_date}]),type:"date",onChange:C},null,34),[[y,l.value]])]),e("div",te,[n(m,{for:"date",value:"To Date"}),w(e("input",{"onUpdate:modelValue":s[2]||(s[2]=a=>i.value=a),class:b(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(u).errors.to_date}]),type:"date",onChange:N},null,34),[[y,i.value]])])])])])]),_:1})],64))}};export{ie as default};
