<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /*Schema::table('quotation', function (Blueprint $table) {
            $table->text('paymentterms')->change();
            $table->renameColumn('paymentterms', 'payment_terms');
        });*/
        DB::statement('ALTER TABLE quotation CHANGE paymentterms payment_terms TEXT');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        /*Schema::table('quotation', function (Blueprint $table) {
            $table->string('paymentterms', 255)->change();
            $table->renameColumn('payment_terms', 'paymentterms');
        });*/
        DB::statement('ALTER TABLE quotation CHANGE payment_terms paymentterms VARCHAR(255)');
    }
};
