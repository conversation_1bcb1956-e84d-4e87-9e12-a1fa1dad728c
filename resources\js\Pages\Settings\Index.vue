<script setup>
import { ref, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CustomButton from '@/Components/CustomButton.vue';
import { Head, useForm } from '@inertiajs/vue3';

const form = useForm({});
const searchQuery = ref('');
const props = defineProps(['permissions']);

const items = ref([
    { name: 'Organization', route: 'organization.index' },
    { name: 'Roles & Permissions', route: 'roles.index' },
    { name: 'Manage Prefix', route: 'manage-prefix' },
    { name: 'Banks', route: 'bankinfo.index' },
    { name: 'Account Type', route: 'account-type.index' },
    { name: 'Jobcard CheckList', route: 'jobcard-checklist.index' },
    { name: 'SMTP', route: 'mail-configs.index' },
    { name: 'Email Template', route: 'emailtemplates.index' },
    { name: 'Email Tags', route: 'email-tag.index' },
    { name: 'Number Setting', route: 'number-setting' }

]);

const filteredItems = computed(() => {
    return items.value.filter(item =>
        item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

</script>

<template>
    <Head title="Settings" />

    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Settings</h1>
                </div>
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg
                            class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <input
                            id="search-field"
                            v-model="searchQuery"
                            class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                            placeholder="Search..."
                            type="search"
                            name="search"
                        />
                    </div>
                </div>
            </div>
            <div class="border-gray-900 mt-10" style="height: 500px;">
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                    <div class="sm:col-span-2" v-if="permissions.canOrganizationAdd">
                        <CustomButton :href="route('organization.index')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 3h16a1 1 0 0 1 1 1v17h-6v-5h-6v5H3V4a1 1 0 0 1 1-1zm2 2v14h4v-5h4v5h4V5H6zm2 3h2v2H8V8zm6 0h2v2h-2V8zM8 12h2v2H8v-2zm6 0h2v2h-2v-2z"/>
                            </svg>
                            <span class="font-semibold text-lg ml-4">Organization</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2">
                        <CustomButton :href="route('roles.index')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2l7 4v6c0 5-3 9-7 10-4-1-7-5-7-10V6l7-4z"/>
                                <circle cx="12" cy="10" r="3" fill="white"/>
                                <path d="M9 16c1-2 5-2 6 0" stroke="white" stroke-width="2" fill="none"/>
                            </svg>
                            <span class="font-semibold text-lg ml-4">Roles & Permissions</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2" v-if="permissions.canPrefixAdd">
                        <CustomButton :href="route('manage-prefix')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6H6zm12 18H6V4h7v5h5v11z"/>
                                <path d="M8 10h8v2H8v-2zm0 4h6v2H8v-2z"/>
                                <rect x="14" y="15" width="5" height="4" rx="1" stroke="currentColor" stroke-width="2" fill="none"/>
                                <text x="15.5" y="18.5" font-size="3" fill="currentColor" text-anchor="middle">A</text>
                            </svg>
                            <span class="font-semibold text-lg ml-4">Manage Prefix</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2" v-if="permissions.canBanksAdd">
                        <CustomButton :href="route('bankinfo.index')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 10l9-7 9 7" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10" stroke="currentColor" stroke-width="2" fill="none"/>
                                <rect x="10" y="14" width="4" height="6" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                            <span class="font-semibold text-lg ml-4">Banks</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2" v-if="permissions.canAccountTypeAdd">
                        <CustomButton :href="route('account-type.index')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                                <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M15 16c0-1.5-3-1.5-3-1.5s-3 0-3 1.5" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                            <span class="font-semibold text-lg ml-4">Account Type</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2" v-if="permissions.canJobCardChecklistAdd">
                            <CustomButton :href="route('jobcard-checklist.index')">
                                <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <polyline points="9 2 11 4 15 0" fill="none" stroke="currentColor" stroke-width="2"/>
                                    <rect x="6" y="7" width="12" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>
                                    <circle cx="9" cy="6.5" r="1" fill="currentColor"/>
                                    <circle cx="12" cy="6.5" r="1" fill="currentColor"/>
                                    <circle cx="15" cy="6.5" r="1" fill="currentColor"/>
                                    <line x1="8" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="2"/>
                                    <line x1="8" y1="15" x2="16" y2="15" stroke="currentColor" stroke-width="2"/>
                                    <line x1="8" y1="19" x2="16" y2="19" stroke="currentColor" stroke-width="1"/>
                                </svg>
                                <span class="font-semibold text-lg ml-4">Jobcard Checklist</span>
                            </CustomButton>
                        </div>
                    <div class="sm:col-span-2" v-if="permissions.canSMPTAdd">
                        <CustomButton :href="route('mail-configs.index')">
                            <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2 4h20v16H2z" fill="none" stroke="currentColor" stroke-width="2"/>
                                <path d="M2 4l10 9L22 4" fill="none" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <span class="font-semibold text-lg ml-4">SMTP</span>
                        </CustomButton>
                    </div>
                    <div class="sm:col-span-2" v-if="permissions.canEmailTemplateAdd">
                    <CustomButton :href="route('emailtemplates.index')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M14 2v6h6" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M4 14l8 6 8-6" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M4 14v6h16v-6" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Email Template</span>
                    </CustomButton>
                </div>
                    <div class="sm:col-span-2" v-if="permissions.canEmailTagsAdd">
                            <CustomButton :href="route('email-tag.index')">
                                <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2 10V4a2 2 0 0 1 2-2h6l10 10a2 2 0 0 1 0 3l-6 6a2 2 0 0 1-3 0L2 12V10z" stroke="currentColor" stroke-width="2" fill="none"/>
                                <circle cx="7" cy="7" r="2" fill="currentColor"/>
                                <path d="M15 12l 5M18 9l4" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                                <span class="font-semibold text-lg ml-4">Email Tags</span>
                            </CustomButton>
                        </div>
                        <div class="sm:col-span-2">
                            <CustomButton :href="route('number-setting')">
                                <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4 4h16v16H4z" stroke="currentColor" stroke-width="2" fill="none"/>
                                    <text x="12" y="16" font-size="10" fill="currentColor" text-anchor="middle" font-weight="bold">123</text>
                                </svg>
                                <span class="font-semibold text-lg ml-4">Number Setting</span>
                            </CustomButton>
                        </div>

                </div>
            </div>
        </div>
    </AdminLayout>
</template>
