<script setup>
import { onMounted, ref, watch, computed  } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableMultiSelect from '@/Components/SearchableMultiselect.vue';
import SvgLink from '@/Components/ActionLink.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';
import axios from 'axios';

const props = defineProps(['customers', 'invoice_no', 'retail_invoice_no', 'serialno', 'products', 'organization', 'category', 'salesuser', 'invoice_details']);
const invoice_no = ref();
const retail_invoice_no = ref();
const selectedProductItem = ref([
    {
        serial_number_id: '',
        product_id: '',
        product_name: '',
        item_code: '',
        hsn_code: '',
        price: '',
        sell_price: '',
        discount: '',
        discount_amount: '',
        discount_before_tax_product: '',
        gst: '',
        sgst: '',
        total_price: '',
        gst_amount: '',
        total_gst_amount: '',
        total_amount: '',
        qty: '',
        description: '',
        expiry_date: '',
        mrp: ''
    }
])

const previousInvoiceInfo = ref(
    {
        product_name: '',
        hsn_code: '',
        qty: '',
        price: '',
        gst: '',
        total_amount: '',
    }
)

//new code
const gst_type = ref(null);
const customer_type = ref(null);

const setCustomer = (id, name) => {
    form.customer_id = id;
    form.errors.customer_id = null;
    const selectedCustomer = props.customers.find(customer => customer.id === id);
    if(selectedCustomer){
        gst_type.value = selectedCustomer.gst_type;
        customer_type.value = selectedCustomer.customer_type
    }
};

const filteredSerialOptions = (index) => {
    return props.serialno.filter(option => !selectedProductItem.value.some(item => item.serial_number_id === option.id));
};

const serial = ref([]);

const setSerialIds = async (id, name, index) => {
    form.errors[`selectedProductItem.${index}.product_id`] = null;

    const product = props.serialno.filter(
        product => product.product_id === id && product.organization_id === form.organization_id
    );
    serial.value = product;
    selectedProductItem.value[index].product_id = id;

    try {
        const response = await axios.post('/api/invoices/previous', {
            customer_id: form.customer_id,
            organization_id: form.organization_id,
            product_id: id,
        });

        if (response.data.success) {
            previousInvoiceInfo.value = response.data.data;
        } else {
            previousInvoiceInfo.value = {
                product_name: '',
                hsn_code: '',
                qty: '',
                price: '',
                gst: '',
                total_amount: '',
            };
        }
    } catch (error) {
        console.error('Error fetching previous invoice:', error);
        previousInvoiceInfo.value = {
            product_name: '',
            hsn_code: '',
            qty: '',
            price: '',
            gst: '',
            total_amount: '',
        };
    }
};

// const setSerialIds = (id, name, index) => {
//     form.errors[`selectedProductItem.${index}.product_id`] = null;
//     const product = props.serialno.filter(product  => product.product_id === id && product.organization_id === form.organization_id);
//     serial.value = product;
//     selectedProductItem.value[index].product_id = id;
//     const previousInvoice = props.invoice_details.filter(invoiceDetail => {
//         if (invoiceDetail.invoice && invoiceDetail.invoice.customer_id === form.customer_id) {
//             return invoiceDetail.product_id === id;
//         }
//         return false;
//     });

//     if(previousInvoice.length > 0){
//         previousInvoiceInfo.value = {
//             product_name: name,
//             hsn_code: previousInvoice[0].product.hsn_code,
//             qty: previousInvoice[0].qty,
//             price: previousInvoice[0].price,
//             gst: previousInvoice[0].gst,
//             total_amount: previousInvoice[0].total_amount
//         };
//     } else {
//         previousInvoiceInfo.value = {
//             product_name: '',
//             hsn_code: '',
//             qty: '',
//             price: '',
//             gst: '',
//             total_amount: ''
//         };
//     }
// };

const setProductInfo = (id, name, index) => {
    const selectedProduct = props.serialno.find(product => product.id === id);
    if(selectedProduct) {
        selectedProductItem.value[index].qty = '';
        selectedProductItem.value[index].product_id = selectedProduct.product.id;
        selectedProductItem.value[index].serial_number_id = selectedProduct.id;
        selectedProductItem.value[index].item_code = selectedProduct.product.item_code;
        selectedProductItem.value[index].expiry_date = selectedProduct.expiry_date;
        selectedProductItem.value[index].mrp = (selectedProduct.mrp) ? parseFloat(selectedProduct.mrp).toFixed(2) : '-';
        selectedProductItem.value[index].product_name = selectedProduct.product.name;
        selectedProductItem.value[index].hsn_code = selectedProduct.product.hsn_code;
        selectedProductItem.value[index].discount = '0.00';
        selectedProductItem.value[index].price = parseFloat(selectedProduct.purchase_price).toFixed(2);
        selectedProductItem.value[index].total_price = parseFloat(selectedProduct.purchase_price).toFixed(2);
        selectedProductItem.value[index].gst = parseFloat(selectedProduct.product.gst).toFixed(2);
        selectedProductItem.value[index].sgst = parseFloat(selectedProduct.product.gst/2).toFixed(2);
        selectedProductItem.value[index].gst_amount = '';
        selectedProductItem.value[index].total_gst_amount = '';
        selectedProductItem.value[index].total_amount = '';
        selectedProductItem.value[index].description = '';
        form.errors[`selectedProductItem.${index}.serial_number_id`] = null;
    }
};

const calculateAmount = (product, index) => {
    const sell_price = parseFloat(product.sell_price);
    const discount_before_tax_product = parseFloat(product.discount_before_tax_product) || 0;
    const discount = parseFloat(product.discount) || 0;
    const gst = (gst_type.value =='IGST') ? (product.gst) : parseFloat(product.sgst*2);
    const qty = parseFloat(product.qty);
    let amount = 0;
    let totalAmount = 0;
    if(discount > 0 || discount_before_tax_product > 0){
      amount = sell_price * qty;
    } else {
      amount = sell_price * qty * (1 + gst / 100);
    }
    const discountAmount = (amount * (discount / 100)) || 0;
    const gst_amount = sell_price * 1 * (gst / 100);
    const total_gst_amount = ((sell_price * qty) - discountAmount - discount_before_tax_product) * (gst / 100);
    if(discount > 0 || discount_before_tax_product > 0){
      totalAmount = amount - discountAmount - discount_before_tax_product + total_gst_amount;
    } else {
      totalAmount = amount - discountAmount;
    }
    const total_price = sell_price * qty;
    product.total_price      = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    product.gst_amount       = isNaN(gst_amount) ? '' : parseFloat(gst_amount).toFixed(2);
    product.total_gst_amount       = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    product.discount_amount = isNaN(discountAmount) ? '' : parseFloat(discountAmount).toFixed(2);
    product.gst = gst;
    return isNaN(totalAmount) ? '' :  parseFloat(totalAmount).toFixed(2);
};

const updateAmount = (product, index) => {
    applyDiscountBeforeTax();
    product.total_amount = calculateAmount(product, index);
};

const totalAmount = computed(() => {
    const totalAmount = Math.round(selectedProductItem.value.reduce((total, product) => {
        return total + (product.total_amount ? parseFloat(product.total_amount) : 0);
    }, 0));
    const overallDiscountAmount = form.overall_discount ? parseFloat(form.overall_discount) : 0;
    return totalAmount - overallDiscountAmount;
});

const totalGstAmount = computed(() => {
    return selectedProductItem.value.reduce((total, product) => {
        return total +  (product.total_gst_amount ? parseFloat(product.total_gst_amount) : 0);
    }, 0);
});

const totalPrice = computed(() => {
    return selectedProductItem.value.reduce((total, product) => {
        return total +  (product.total_price ? parseFloat(product.total_price) : 0);
    }, 0);
});

const totalDiscountAmount = computed(() => {
    const totalDiscountFromProducts = selectedProductItem.value.reduce((total, product) => {
        return total + (product.discount_amount ? parseFloat(product.discount_amount) : 0);
    }, 0);
    const overallDiscountAmount = form.overall_discount ? parseFloat(form.overall_discount) : 0;
    const discountBeforeAmount = form.discount_before_tax ? parseFloat(form.discount_before_tax) : 0;
    return totalDiscountFromProducts + overallDiscountAmount + discountBeforeAmount;
});

const form = useForm('post', '/invoice', {
    note: '',
    date:  new Date().toISOString().slice(0, 10),
    sales_user_id: '',
    invoice_type: '',
    selectedProductItem: [],
    customer_id: '',
    category: '',
    invoice_no: '',
    document: '',
    cgst: '',
    sgst: '',
    igst: '',
    total_gst: '',
    sub_total: '',
    total_amount: '',
    total_discount: '',
    organization_id: '',
    dispatch: '',
    transport: '',
    patient_name: '',
    customer_po_date: '',
    customer_po_number: '',
    eway_bill: '',
    due_days: '',
    cr_dr_note: '',
    overall_discount: '',
    discount_before_tax: ''
});

const blankItem = () => {
    selectedProductItem.value = [{
        serial_number_id: '',
        product_id: '',
        product_name: '',
        item_code: '',
        hsn_code: '',
        price: '',
        sell_price: '',
        discount: '',
        discount_amount: '',
        discount_before_tax_product: '',
        gst: '',
        sgst: '',
        total_price: '',
        gst_amount: '',
        total_gst_amount: '',
        total_amount: '',
        qty: '',
        description: '',
        expiry_date: '',
        mrp: ''
    }];
}

const finalproducts = ref([]);

const setCategory = (id, name) => {
    blankItem();
    const finalProducts = props.products.filter(product => product.category === id);
        const filteredProducts = props.products.filter(product => {
        const foundInSerial = product.sales_products.some(serial => serial.organization_id === form.organization_id);
        return foundInSerial;
    });
    finalproducts.value = filteredProducts;
    form.category = id;
    form.errors.category = null;
};

const setSalesUser = (id, name) => {
    form.sales_user_id = id;
    form.errors.sales_user_id = null;
};

const setOrganization = (id, name) => {
    blankItem();
    const finalProducts = props.products.filter(product => product.category === form.category);
        const filteredProducts = props.products.filter(product => {
        const foundInSerial = product.sales_products.some(serial => serial.organization_id === id);
        return foundInSerial;
    });
    finalproducts.value = filteredProducts;
    form.organization_id = id;
    form.errors.organization_id = null;
    invoice_no.value = props.invoice_no[id];
    retail_invoice_no.value = props.retail_invoice_no[id];
};

const submit = () => {
    for (let i = 0; i < selectedProductItem.value.length; i++) {
        const product = selectedProductItem.value[i];
        if (product.qty > product.available_stock) {
            alert("Total qty exceeds available stock.");
            return;
        }
    }

    form.sub_total = totalPrice.value;
    form.cgst = (gst_type.value == 'CGST/SGST') ? totalGstAmount.value / 2 : '0';
    form.sgst = (gst_type.value == 'CGST/SGST') ? totalGstAmount.value / 2 : '0';
    form.igst = (gst_type.value == 'IGST') ? totalGstAmount.value : '0';
    form.total_gst = totalGstAmount.value;
    form.total_amount = totalAmount.value;
    form.total_discount = totalDiscountAmount.value;
    form.invoice_no = (customer_type.value == 'Tax') ? invoice_no.value : retail_invoice_no.value;
    form.invoice_type = customer_type.value;
    form.selectedProductItem = selectedProductItem.value;

    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const addProduct = () => {
    selectedProductItem.value.push({
        serial_number_id: '',
        product_name: '',
        hsn_code: ''
    });
};

const removeProduct = (index) => {
    selectedProductItem.value.splice(index, 1);
};

// const clearError = (fieldName) => {
//     form.errors[fieldName] = null;
// };

const poDate = computed(() => {
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return today.toLocaleDateString('en-US', options);
});

const handleDocument = (file) => {
    form.document = file;
};

const formatAmount = (amount) => {
    // let amountStr = amount.toFixed(2).toString();
    if (amount == null || isNaN(amount)) {
        return "0.00";  // Default value
    }
    let amountStr = Number(amount).toFixed(2); // Ensure it's a number before calling toFixed
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const selectedOptionRemove = (index) => {
    // return index.filter(option => !selectedProductItem.value.some(item => item.serial_number_id === option.id));
    return index;
};

const getProductSpecificOptions = (id, name, index) => {
    const productNew = props.serialno.filter(product  => product.product_id === id && product.organization_id === form.organization_id);
    return productNew;
};

const selectedOptionRemoveProduct = (index) => {
    return index;
};

const calculateProportionalDiscount = (totalDiscount, items) => {
    const totalQty = items.length;
    const itemValue = items.reduce((total, item) => total + (item.total_price ? parseFloat(item.total_price) : 0), 0);
    const totalPrice = selectedProductItem.value.reduce((sum, product) => sum + (product.total_price ? parseFloat(product.total_price) : 0), 0);
    const discount_amount = ((totalDiscount * itemValue) / totalPrice) / totalQty;
    items.forEach(item => {
        item.discount_before_tax_product = discount_amount;
    });
};

//discount flow
const applyDiscountBeforeTax = () => {
    const totalDiscount = parseFloat(form.discount_before_tax) || 0;
    const itemsFor5PercentGst = selectedProductItem.value.filter(item => item.gst == 5 && item.total_price > 0);
    const itemsFor12PercentGst = selectedProductItem.value.filter(item => item.gst == 12 && item.total_price > 0);
    const itemsFor18PercentGst = selectedProductItem.value.filter(item => item.gst == 18 && item.total_price > 0);
    const itemsFor28PercentGst = selectedProductItem.value.filter(item => item.gst == 28 && item.total_price > 0);
    calculateProportionalDiscount(totalDiscount, itemsFor5PercentGst);
    calculateProportionalDiscount(totalDiscount, itemsFor12PercentGst);
    calculateProportionalDiscount(totalDiscount, itemsFor18PercentGst);
    calculateProportionalDiscount(totalDiscount, itemsFor28PercentGst);
};

watch(
    () => form.discount_before_tax, // Watch the specific property
    (newDiscount) => {
        applyDiscountBeforeTax();
        selectedProductItem.value.forEach((product) => {
            updateAmount(product);
        });
    }
);

const getSerialNumberDetails = (serialNumberId) => {
    return props.serialno.find((serial) => serial.id === serialNumberId) || null;
};

const clearError = (fieldName) => {
    if (form.errors[fieldName]) {
        delete form.errors[fieldName];
    }
};

</script>

<template>
    <Head title="Invoice" />
    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Invoice</h1>
            </div>
            <div>
                <div class="w-auto">
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Invoice Number:</span>
                        <span v-if="customer_type == 'Retail'" class="text-sm font-semibold text-gray-900 leading-6">{{ retail_invoice_no }}</span>
                        <span v-if="customer_type == 'Tax'" class="text-sm font-semibold text-gray-900 leading-6">{{ invoice_no }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Date :</span>
                        <input
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"  v-model="form.date"   @change="form.validate('date')"
                    />
                    </div>
                </div>
            </div>
        </div>
        <form @submit.prevent="submit" class="">
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-4">
                    <InputLabel for="company_name" value="Organization" />
                     <div class="relative mt-2">
                        <SearchableDropdown :options="organization"
                        v-model="form.organization_id"
                        @onchange="setOrganization"
                        :class="{ 'error rounded-md': form.errors.organization_id }"
                        />
                     </div>
                </div>
                 <div class="sm:col-span-4">
                <InputLabel for="customer_id" value="Customer Name" />
                     <div class="relative mt-2">
                        <SearchableDropdown :options="customers"
                        v-model="form.customer_id"
                        @onchange="setCustomer"
                        :class="{ 'error rounded-md': form.errors.customer_id }"
                        />
                     </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="company_name" value="Category" />
                     <div class="relative mt-2">
                        <SearchableDropdown :options="category"
                        v-model="form.category"
                        @onchange="setCategory"
                        :class="{ 'error rounded-md': form.errors.category }"
                        />
                     </div>
                </div>

            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full">
            <div class="overflow-x-auto w-full">
                <table class="overflow-x-auto divide-y divide-gray-300" style="margin-bottom: 160px;">
                    <thead>
                        <tr>
                            <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Product Name</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Batch</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">HSN</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">EXP</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Description</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">MRP (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Price (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Qty</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Sell Price (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total Price (₹)</th>
                            <th v-if="gst_type =='IGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">IGST (%)</th>
                            <th v-if="gst_type =='IGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">IGST (₹)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">CGST (%)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">SGST (%)</th>
                            <th v-if="gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total GST (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Discount (%)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Discount (₹)</th>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Total Amount (₹)</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white">
                        <tr v-for="(product, index)  in selectedProductItem" :key="index">
                            <td class="whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96">
                                <div class="relative mt-2">
                                    <SearchableDropdown
                                    :options="selectedOptionRemoveProduct(finalproducts)"
                                    v-model="product.product_id"
                                    @onchange="(id, name) => setSerialIds(id, name, index)"
                                    @change="form.validate('product_id')"
                                    :class="{ 'error rounded-md': form.errors[`selectedProductItem.${index}.product_id`] }"
                                    />
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60">
                                <div class="relative mt-2">
                                    <SearchableDropdown
                                        :options="getProductSpecificOptions(product.product_id)"
                                        v-model="product.serial_number_id"
                                        @onchange="(id, name) => setProductInfo(id, name, index)"
                                        @change="form.validate('selectedProductItem.${index}.serial_number_id')"
                                        :class="{ 'error rounded-md': form.errors[`selectedProductItem.${index}.serial_number_id`] }"
                                    />
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">{{  product.hsn_code}}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">{{ product.expiry_date ?? '-' }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40">
                                <TextInput
                                    id="description"
                                    type="text"
                                    v-model="product.description"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.description')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.description`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">{{ product.mrp ?? '-' }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">{{ product.price }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                <TextInput
                                    id="qty"
                                    type="text"
                                     @input="updateAmount(product, index)"
                                    v-model="selectedProductItem[index].qty"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.qty`] }"
                                />
                                <InputError :message="form.errors[`selectedProductItem.${index}.qty`]" />
                            </td>
                              <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32">
                                <TextInput
                                    id="sell_price"
                                    type="text"
                                    v-model="product.sell_price"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.sell_price')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.sell_price`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32">{{  product.total_price }}</td>
                            <td  v-if="gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                <TextInput
                                    id="gst"
                                    type="text"
                                    v-model="product.gst"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.gst')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                />
                            </td>
                            <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                <TextInput
                                    id="gst"
                                    type="text"
                                    v-model="product.sgst"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.gst')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                />
                            </td>
                            <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24">
                                <TextInput
                                    id="gst"
                                    type="text"
                                    v-model="product.sgst"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.gst')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.gst`] }"
                                />
                            </td>
                            <td  v-if="gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">{{ product.total_gst_amount }}</td>
                            <td  v-if="gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">{{ product.total_gst_amount }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28">
                                <TextInput
                                    id="discount"
                                    type="text"
                                    v-model="product.discount"
                                    @input="updateAmount(product, index)"
                                    @change="clearError('selectedProductItem.' + index + '.discount')"
                                    :class="{ 'error': form.errors[`selectedProductItem.${index}.discount`] }"
                                />
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"> {{ product.discount_amount }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48">
                                <div class="px-3 py-3 text-sm text-gray-900">
                                    {{ product.total_amount }}
                                </div>
                                <button v-if="index!=0" type="button" class="mt-1" @click="removeProduct(index)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                        />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="flex items-center justify-between">
                <div v-if="previousInvoiceInfo.product_name" class="">
                    <span class="text-lg font-semibold text-gray-900 leading-6">PREVIOUS INVOICE</span>
                </div>
                <div class="ml-auto flex items-center justify-end gap-x-6">
                    <PrimaryButton @click="addProduct" type="button">Add Product</PrimaryButton>
                </div>
            </div>
            <div v-if="previousInvoiceInfo.product_name" class="flex justify-between">
                <div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">PRODUCT:</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{ previousInvoiceInfo.product_name }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">HSN Code:</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{ previousInvoiceInfo.hsn_code }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">QTY:</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{ previousInvoiceInfo.qty }}</span>
                    </div>
                </div>
                <div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">PRICE (₹):</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{formatAmount(previousInvoiceInfo.price) }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">GST (%):</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{ formatAmount(previousInvoiceInfo.gst) }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-700 leading-6">TOTAL AMOUNT (₹):</span>
                        <span class="text-sm font-semibold text-gray-700 leading-6">{{ formatAmount(previousInvoiceInfo.total_amount) }}</span>
                    </div>
                </div>
                <div>
                </div>
            </div>
        </div>

        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-4">
                    <div class="flex space-x-4">
                        <div class="w-full">
                            <InputLabel for="note" value="Upload Documents" />
                            <MultipleFileUpload
                                inputId="document"
                                inputName="document"
                                @files="handleDocument"
                            />
                        </div>
                        <div class="w-full">
                            <InputLabel for="sales_user_id" value="Sales Person" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="salesuser"
                                v-model="form.sales_user_id"
                                @onchange="setSalesUser"
                                :class="{ 'error rounded-md': form.errors.sales_user_id }"
                            />
                        </div>
                    </div>
                </div>
                    <div class="flex space-x-4">
                        <div class="w-full">
                             <InputLabel for="company_name" value="Transport" />
                            <TextInput
                                id="gst"
                                type="text"
                                v-model="form.dispatch"
                            />
                        </div>
                        <div class="w-full">
                             <InputLabel for="company_name" value="Dispatch" />
                            <TextInput
                                id="transport"
                                type="text"
                                v-model="form.transport"
                            />
                        </div>
                        <div class="w-full">
                            <InputLabel for="eway_bill" value="Eway Bill" />
                            <TextInput
                                id="eway_bill"
                                type="text"
                                v-model="form.eway_bill"
                            />
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <div class="w-full">
                             <InputLabel for="company_name" value="PO Number" />
                            <TextInput
                                id="gst"
                                type="text"
                                v-model="form.customer_po_number"
                            />
                        </div>
                        <div class="w-full">
                             <InputLabel for="company_name" value="PO Date" />
                            <TextInput
                                id="customer_po_date"
                                type="date"
                                v-model="form.customer_po_date"
                            />
                        </div>
                        <div class="w-full">
                             <InputLabel for="due_days" value="Due Days" />
                            <TextInput
                                id="due_days"
                                type="text"
                                v-model="form.due_days"
                            />
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <div class="w-full">
                             <InputLabel for="patient_name" value="Patient Name" />
                            <TextInput
                                id="patient_name"
                                type="text"
                                v-model="form.patient_name"
                            />
                        </div>
                        <div class="w-full">
                             <InputLabel for="cr_dr_note" value="CR DR Note" />
                            <TextInput
                                id="cr_dr_note"
                                type="text"
                                v-model="form.cr_dr_note"
                            />
                        </div>
                    </div>
                    <div>
                        <InputLabel for="note" value="Note" />
                        <TextArea
                            id="note"
                            type="text"
                            v-model="form.note"
                            @change="form.validate('note')"
                        />
                        <InputError  v-if="form.invalid('note')" class="" :message="form.errors.note" />
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700 mt-2">Discount On Sub Total(₹):</p>
                            <div class="w-40">
                                <TextInput
                                id="discount_before_tax"
                                type="text"
                                v-model="form.discount_before_tax"
                            />
                            </div>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700 mt-2">Overall Discount (₹):</p>
                            <div class="w-40">
                                <TextInput
                                id="overall_discount"
                                type="text"
                                v-model="form.overall_discount"
                            />
                            </div>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total Discount Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalDiscountAmount) }}</p>
                        </div>
                        <div v-if="gst_type =='IGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total IGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                        </div>
                        <div v-if="gst_type =='CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total CGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount/2) }}</p>
                        </div>
                        <div v-if="gst_type =='CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total SGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount/2) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalAmount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex mt-6 items-center justify-between">
            <div class="ml-auto flex items-center justify-end gap-x-6">
                <SvgLink :href="route('invoice.index')">
                    <template #svg>
                        <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                            Cancel
                        </button>
                    </template>
                </SvgLink>
                <PrimaryButton :disabled="form.processing">Submit</PrimaryButton>
            </div>
        </div>
        </form>
        </div>
    </AdminLayout>
</template>


<style scoped>
.error {
  border: 1px solid red;
}
</style>


