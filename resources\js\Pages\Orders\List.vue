<script setup>
import { ref, onMounted, watch, computed  } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions', 'administration', 'quotationbank', 'quotationHealthCareBankinfo',  'quotationNoxBank', 'organization', 'customers', 'salesuser', 'category', 'organizationId', 'customerId', 'salesUserId', 'categoryId', 'createdBy', 'statusId', 'ordersStatus', 'pagetypes']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('orders.index', {
    organization_id: props.organizationId,
    customer_id: props.customerId,
    sales_user_id: props.salesUserId,
    category: props.categoryId,
    created_by: props.createdBy,
    status: props.status
});

const filePath = usePage().props.filepath.view;
// const form = useForm({});
const quotationData = ref([]);
const deleteModalVisible = ref(false);
const quotationApproveModal = ref(false);
const selectedUserId = ref(null);
const selectedQuotationId = ref(null);

const columns = [
    { field: 'order_number',                   label: 'ORDER NUMBER',       sortable: true },
    { field: 'quotation.quotation_number',     label: 'QUOTATION NUMBER',   sortable: true },
    { field: 'customers.customer_name',        label: 'CUSTOMER NAME',      sortable: true },
    { field: 'users.first_name',               label: 'SALES PERSON',       sortable: true },
    { field: 'date',                           label: 'DATE',               sortable: true },
    { field: 'total_amount',                   label: 'AMOUNT (₹)',         sortable: true },
    { field: 'status',                         label: 'STATUS',             sortable: true },
    { field: 'action',                         label: 'ACTION',             sortable: false },
];

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  deleteModalVisible.value = true;
};

const converToOrderModal = (userId) => {
  selectedQuotationId.value = userId;
  quotationApproveModal.value = true;
};

const closeModal = () => {
    deleteModalVisible.value = false;
};

const closeConverToOrderModal = () => {
    quotationApproveModal.value = false;
};

const deletePO = () => {
    form.delete(route('orders.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const convertToOrder = () => {
    form.get(route('orders.convertorder',{id: selectedQuotationId.value}), {
        onSuccess: () => closeModal()
    });
};

const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const salesUserId = ref(props.salesUserId);
const categoryId = ref(props.categoryId);
const createdBy = ref(props.createdBy);
const statusId = ref(props.statusId);
const searchValue = ref('');

watch([organizationId, customerId, salesUserId, categoryId, createdBy, statusId ], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
        sales_user_id: salesUserId.value,
        category: categoryId.value,
        created_by: createdBy.value,
        status: statusId.value
    });
});

const handleSearchChange = (value, organizationId, customerId, salesUserId, categoryId, createdBy, statusId) => {
    searchValue.value = value;
    form.get(route('orders.index',{search:value,  organization_id: organizationId ,  customer_id: customerId, sales_user_id: salesUserId ,  category: categoryId, created_by: createdBy, status: statusId}),  {
        preserveState: true,
        // replace: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, salesUserId.value, categoryId.value, createdBy.value , statusId.value);
};

const setCustomers = (id, name) => {
    customerId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value, createdBy.value , statusId.value);
};

const setType = (id, name) => {
    categoryId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value, createdBy.value , statusId.value);
};

const setSalesUser = (id, name) => {
    salesUserId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value, createdBy.value, statusId.value);
};

const setCreatedBy = (id, name) => {
    createdBy.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value, createdBy.value, statusId.value);
};

const setStatus = (id, name) => {
    statusId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, salesUserId.value, categoryId.value, createdBy.value, statusId.value);
};

const exportOrdersXls = () => {
    const xlsName = 'Orders_Report_' + new Date().toISOString().split('T')[0];
    const params = {
        organization_id: organizationId.value,customer_id: customerId.value, sales_user_id: salesUserId.value, category_id: categoryId.value, created_by: createdBy.value, status: statusId.value
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-orders?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', xlsName + '.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        // Handle error
        console.error('Error exporting data:', error);
    });
};

const generatePdfModal = ref(false);
const modalMaxWidth = ref('custom');

const openPreviewModal = (id) => {
  const quotation = props.data.data.find(companypo  => companypo.id === id);
  quotationData.value = quotation
  generatePdfModal.value = true;
};

const closeGeneratePdf = () => {
    generatePdfModal.value = false;
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Pending':
            return 'bg-blue-100';
        case 'Accepted':
            return 'bg-yellow-100';
        case 'Completed':
            return 'bg-green-100';
        default:
            return 'bg-red-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Pending':
            return 'text-blue-600';
        case 'Accepted':
            return 'text-yellow-600';
        case 'Completed':
            return 'text-green-600';
        default:
            return 'text-red-600';
    }
};

const generatePDF = (filename) => {
    const sanitizedFilename = filename.replace(/\s+/g, '_').replace(/[<>:"./\\|?*]+/g, '');
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');
      html2canvas(pdfContent, { scale: 2 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 196; // PDF page width (A4)
        const pageHeight = 297; // PDF page height (A4)
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let position = 0;
        if (imgHeight <= pageHeight) {
            doc.addImage(imgData, 'PNG', 7, 10, imgWidth, imgHeight);
        } else {
            while (position < imgHeight) {
                doc.addImage(imgData, 'PNG', 7, -position +10 , imgWidth, imgHeight);
                position += pageHeight - 20; // 20px margin at the top
                if (position < imgHeight) {
                    doc.addPage();
                }
            }
        }
        // Save the PDF
        doc.save(sanitizedFilename); // Save PDF with a filename
    });
};

const page = ref('portrait');

const setPageType = (id, name) => {
    page.value = id;
};

const downloadPDF = (id, page) => {
    window.open(`/order/download/${id}/${page}`, '_blank');
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const truncateName = (name) => {
    return name && name.length > 40 ? name.substring(0, 40) + '...' : name;
};

const getRowHeight = (description) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = description;
    tempDiv.style.width = '1000px';
    document.body.appendChild(tempDiv);
    const height = tempDiv.offsetHeight;
    document.body.removeChild(tempDiv);
    return height;
};

const convertNewlinesToBr = (text) => {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
};

</script>

<template>
    <Head title="Orders"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Orders</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, customerId, salesUserId, categoryId, createdBy, statusId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                                <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateOrders">
                        <div class="flex justify-end">
                            <CreateButton :href="route('orders.create')">
                                Add Order
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                    <button @click="exportOrdersXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Organization Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Customer Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="customers"
                            v-model="customerId"
                            @onchange="setCustomers"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4" v-if="permissions.canCreateOrders">
                        <InputLabel for="customer_id" value="Sales Person" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="salesuser"
                            v-model="salesUserId"
                            @onchange="setSalesUser"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Category" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="category"
                            v-model="categoryId"
                            @onchange="setType"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Created By" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="salesuser"
                            v-model="createdBy"
                            @onchange="setCreatedBy"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Status" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="ordersStatus"
                            v-model="statusId"
                            @onchange="setStatus"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ poData.order_number }}
                                </td>
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData?.quotation?.quotation_number ?? '-' }}
                                </td>
                                <th scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ truncateName(poData.customers.customer_name) ?? '-' }}
                                </th>
                                <td class="px-4 py-2.5 min-w-52">
                                    {{ poData.users.first_name }} {{ poData.users.last_name }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatDate(poData.date) }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatAmount(poData.total_amount)}}
                                </td>
                                <td class="flex flex-1 items-center px-4 py-2.5">
                                    <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                        <span class="text-sm font-semibold" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                    </div>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown :align="'right'" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink v-if="(poData.status == 'Pending' || poData.status == 'In Process') &&  permissions.canEditOrders" :href="route('orders.edit',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button  v-if="poData.status == 'Pending' &&  permissions.canDeleteOrders" type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                                <ActionLink v-if="poData.status != 'Completed' && permissions.canCreateOrders" :href="route('orders.deliver',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Deliver Order
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink :href="route('orders.view',{id:poData.id})" v-if="permissions.canViewOrders">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View Order
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink v-if="poData.status != 'Completed' && permissions.canCreateOrders" :href="route('orders.generate', { id: poData.id })">
                                                    <template #svg>
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-width="1.5" stroke-linejoin="round" d="M3 7.5v9a1.5 1.5 0 001.5 1.5h15a1.5 1.5 0 001.5-1.5v-9H3z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 6V3.75A1.5 1.5 0 017.5 2h9A1.5 1.5 0 0118 3.75V6m-15 0h18"></path>
                                                            </svg>
                                                            </template>
                                                            <template #text>
                                                            <span class="text-sm text-gray-700 leading-5">Generate Invoice</span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button"  @click="openPreviewModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                            Generate PDF
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="deleteModalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deletePO"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="quotationApproveModal" @close="closeConverToOrderModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you want to convert this quotation in to order?
                </h2>
                <div class="mt-6 flex justify-end space-x-3">
                    <SecondaryButton @click="closeConverToOrderModal"> Cancel </SecondaryButton>
                    <div class="w-32">
                        <PrimaryButton @click="convertToOrder" type="button">Approve</PrimaryButton>
                    </div>
                </div>
            </div>
        </Modal>
        <Modal :show="generatePdfModal" @close="closeGeneratePdf" :maxWidth="modalMaxWidth">
              <div class="p-6">
                <div id="pdf-content">
                    <div class="container1">
                        <div v-if="quotationData.organization.id == '3'" class="header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                            <img class="w-20 h-20" :src="filePath + quotationData.organization.logo" alt="logo">
                            <p><strong style="font-size: 20px;">Order</strong></p>
                            <h1><div style="width: 120px;">
                            </div></h1>
                        </div>
                        <div v-if="quotationData.organization.id == '1' || quotationData.organization.id == '2'" class="header" style="align-items: start; justify-content: center; text-align: center;">
                            <img class="w-full h-10" :src="filePath + quotationData.organization.logo" alt="logo">
                            <div style="align-items: center; justify-content: space-between; margin-bottom: 10px;">
                                <p style="font-size: 20px;"><strong>Order</strong></p>
                            </div>
                        </div>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                                <p><strong style="font-size: 14px; margin-top: 10px">{{ quotationData.organization.name }}</strong> </p>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <p>{{ quotationData.organization.address_line_1}}</p>
                                <p>{{ quotationData.organization.address_line_2}}</p>
                                <p>{{ quotationData.organization.pincode}} , {{ quotationData.organization.city}}</p>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ quotationData.organization.contact_no }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Email</strong></p><p>: {{ quotationData.organization.email }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ quotationData.organization.gst_no }}</p></div>
                            </div>
                            <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 320px;">
                                <div style="display:flex;"><p style="width: 120px;"><strong>Invoice Number</strong></p><p>: {{ quotationData.order_number  }}</p></div>
                                <div style="display:flex;"><p style="width: 120px;"><strong>Invoice Date</strong></p><p>: {{ formatDate(quotationData.date) }}</p></div>
                                <p style="margin-bottom: 10px;"><strong></strong></p>
                                    <p><strong style="font-size: 14px; margin-top: 10px">{{ quotationData.customers.customer_name}} </strong></p>
                                <p>{{ quotationData.customers.address}}</p>
                                <p style="margin-bottom: 4px;"><strong></strong></p>
                                <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ quotationData.customers.contact_no ?? '-' }}</p></div>
                                <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ quotationData.customers.gst_no ?? '-'}}</p></div>
                            </div>
                        </div>
                        <div style="overflow-x:auto;">
                            <table>
                                <thead>
                                    <tr>
                                        <th>SN</th>
                                        <th>MODEL</th>
                                         <th>HSN</th>
                                        <th>DESCRIPTION</th>
                                        <th>MRP</th>
                                        <th>PRICE (₹)</th>
                                        <th>QTY</th>
                                        <th>TOTAL PRICE (₹)</th>
                                        <th v-if="quotationData.customers.gst_type =='IGST'">IGST (%)</th>
                                        <th v-if="quotationData.customers.gst_type =='CGST/SGST'">CGST (%)</th>
                                        <th v-if="quotationData.customers.gst_type =='CGST/SGST'">SGST (%)</th>
                                        <th>GST (₹)</th>
                                        <th>DIS.(₹)</th>
                                        <th>TOTAL AMOUNT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(poData, index) in quotationData.order_details" :key="poData.id">
                                        <td>{{  index + 1 }}</td>
                                        <td>{{ poData.product.item_code }}</td>
                                        <td>{{ poData.product.hsn_code ?? '-' }}</td>
                                        <td v-html="convertNewlinesToBr(poData.description)"></td>
                                        <td>{{ (poData.product.serial_numbers[0]?.mrp) ? formatAmount(poData.product.serial_numbers[0].mrp) : '-' }}</td>
                                        <td>{{ formatAmount(poData.price)}}</td>
                                        <td>{{ poData.qty }}</td>
                                        <td>{{ formatAmount(poData.total_price)}}</td>
                                        <td v-if="quotationData.customers.gst_type =='IGST'">{{ formatAmount(poData.gst) ?? '-' }}</td>
                                        <td v-if="quotationData.customers.gst_type =='CGST/SGST'">{{ formatAmount(poData.gst/2) ?? '-' }}</td>
                                        <td v-if="quotationData.customers.gst_type =='CGST/SGST'">{{ formatAmount(poData.gst/2) ?? '-' }}</td>
                                        <td>{{ formatAmount(poData.total_gst_amount)}}</td>
                                        <td>{{ formatAmount(poData.discount_amount) }}</td>
                                        <td>{{ formatAmount(poData.total_amount)}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="" style="margin-bottom: 10px; justify-items: start; width: 400;">
                            <p><strong></strong></p>
                            <p>{{ quotationData.note }}</p>
                        </div>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 400;">
                                <p><strong>OUR BANK DETAILS</strong></p>
                                <div v-if="quotationData.organization.id == '1'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ quotationbank.bank_name }}</p></div>
                                <div v-if="quotationData.organization.id == '1'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ quotationbank.branch_name }}</p></div>
                                <div v-if="quotationData.organization.id == '1'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ quotationbank.account_no }}</p></div>
                                <div v-if="quotationData.organization.id == '1'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ quotationbank.ifsc_code }}</p></div>
                                <div v-if="quotationData.organization.id == '2'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ quotationHealthCareBankinfo.bank_name }}</p></div>
                                <div v-if="quotationData.organization.id == '2'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ quotationHealthCareBankinfo.branch_name }}</p></div>
                                <div v-if="quotationData.organization.id == '2'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ quotationHealthCareBankinfo.account_no }}</p></div>
                                <div v-if="quotationData.organization.id == '2'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ quotationHealthCareBankinfo.ifsc_code }}</p></div>
                                <div v-if="quotationData.organization.id == '3'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ quotationNoxBank.bank_name }}</p></div>
                                <div v-if="quotationData.organization.id == '3'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ quotationNoxBank.branch_name }}</p></div>
                                <div v-if="quotationData.organization.id == '3'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ quotationNoxBank.account_no }}</p></div>
                                <div v-if="quotationData.organization.id == '3'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ quotationNoxBank.ifsc_code }}</p></div>
                                <div style="display:flex;"><p><strong>GSTIN</strong></p><p>: {{ quotationData.organization.gst_no ?? ''}}</p></div>
                                <div style="display:flex;"><p><strong>PAN</strong></p><p>: {{ quotationData.organization.pan_no ?? '' }}</p></div>
                                <div style="display:flex;"><p><strong>Payment Terms</strong></p><p>: {{ quotationData.payment_terms }}</p></div>
                            </div>
                            <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                                <div style="display:flex;"><p style="width: 105px;"><strong>Sub Total (₹)</strong></p><p>: {{  formatAmount(quotationData.sub_total) }}</p></div>
                                <div style="display:flex;"><p style="width: 105px;"><strong>Total Discount (₹)</strong></p><p>: {{  formatAmount(quotationData.total_discount) }}</p></div>
                                <div v-if="quotationData.customers.gst_type =='IGST'" style="display:flex;"><p style="width: 105px;"><strong>Total IGST (₹):</strong></p><p>: {{  formatAmount(quotationData.igst) }}</p></div>
                                <div v-if="quotationData.customers.gst_type =='CGST/SGST'" style="display:flex;"><p style="width: 105px;"><strong>Total CGST (₹):</strong></p><p>: {{  formatAmount(quotationData.cgst) }}</p></div>
                                <div v-if="quotationData.customers.gst_type =='CGST/SGST'" style="display:flex;"><p style="width: 105px;"><strong>Total SGST (₹):</strong></p><p>: {{  formatAmount(quotationData.sgst) }}</p></div>
                                <div style="display:flex;"><p style="width: 105px;"><strong>Total Amount (₹)</strong></p><p>: {{ formatAmount(quotationData.total_amount) }}</p></div>
                            </div>
                        </div>
                        <div style="display:flex; justify-content: space-between;">
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 400px;">
                            </div>
                            <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <p><strong>FOR,</strong></p>
                            <p><strong>{{ quotationData.organization.name }}</strong></p>
                                <img class="h-28" :src="filePath + quotationData.organization.signature" alt="logo">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <div class="flex flex-col justify-end space-y-6">
                        <div class="flex items-center space-x-2">
                            <InputLabel for="customer_id" value="Page Type :" />
                            <SearchableDropdown :options="pagetypes"
                            v-model="page"
                            @onchange="setPageType"
                        />
                        </div>
                            <div class="flex justify-end">
                                <SecondaryButton @click="closeGeneratePdf"> Cancel </SecondaryButton>
                                <div class="w-36">
                                    <PrimaryButton
                                        class="ml-3 w-20"
                                        @click="downloadPDF(quotationData.id, page)"
                                    >
                                    Generate Pdf
                                    </PrimaryButton>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 4px 4px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 6px 4px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap;
    }
    #pdf-content .quotationbank p{
        font-size: 12px;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
</style>


