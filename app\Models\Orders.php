<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Orders extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'orders';

    protected static $logName = 'Order';

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customer ? $this->customer->customer_name : 'Unknown Customer';
        return "<strong>{$customerName}'s</strong> Order <strong>{$this->order_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_id',
        'order_number',
        'date',
        'status',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'category',
        'organization_id',
        'customer_id',
        'sales_user_id',
        'quotation_id',
        'order_number',
        'date',
        'status',
        'igst',
        'sgst',
        'cgst',
        'sub_total',
        'total_gst',
        'overall_discount',
        'total_discount',
        'total_amount',
        'note',
        'validity',
        'delivery',
        'payment_terms',
        'warranty',
        'created_by',
        'updated_by'
    ];


    public function organization()
    {
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function quotation()
    {
        return $this->belongsTo(Quotation::class,'quotation_id','id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id'); // Adjust 'customer_id' if your foreign key is named differently
    }

    public function customers(){
        return $this->belongsTo(Customer::class,'customer_id','id');
    }

    public function users(){
        return $this->belongsTo(User::class,'sales_user_id','id');
    }

    public function orderDetails()
    {
        return $this->hasMany(OrderDetails::class ,'order_id','id');
    }

    public function pendingOrderDetails()
    {
        return $this->hasMany(OrderDetails::class ,'order_id','id')->whereRaw('(qty - delivered_qty) > 0');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')->where('entity_type', 'orders');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
                //$event = $model->isDirty('status') ? strtolower($model->status) : 'updated';
                //self::handleLogEntry($model, $event);
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });

    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Order data $event for " . $model->order_number;

        foreach ($model->orderDetails as $detail){
            $modelClone = clone $model;
            $modelClone->product_id = $detail->product_id;
            self::addCustomLogEntry($modelClone, $event, $logName);
        }
        //self::addCustomLogEntry($model, $event, $logName);
    }
}
