<?php


namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\WeeklyPlan;
use App\Models\User;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\WeeklyPlanRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WeeklyPlanController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Weekly Planning')->only(['index']);
        $this->middleware('permission:Create Weekly Planning')->only(['create', 'store']);
        $this->middleware('permission:Edit Weekly Planning')->only(['edit', 'update']);
        $this->middleware('permission:Delete Weekly Planning')->only('destroy');
        $this->middleware('permission:Close Weekly Planning')->only('changeStatus');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $userId = $request->input('user_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');
        $query = WeeklyPlan::with('users');
        $userInfo = Auth()->user();

        if($userId) {
            $query->where('created_by', $userId);
        }
        if($from_date) {
            $query->whereDate('date', '>=', $from_date);
        }
        if($to_date) {
            $query->whereDate('date', '<=', $to_date);
        }

        if(auth()->user()->can('Filter Weekly Planning') != true){
            $query->where('created_by', $userInfo->id);
            $query->where(function ($query) {
                // Condition to include all records where status is "Open"
                $query->where('status', 'Open')
                      // Or include records where status is "Close" and the date is today
                      ->orWhere(function ($query) {
                          $query->where('status', 'Close')
                                ->whereDate('date', '=', date('Y-m-d'));
                      });
            });
        }

        $user = User::where(['status' => '1'])->whereIn('role_id', [6,7,8])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $searchableFields = [ 'users.first_name', 'date', 'customer_name', 'dr_name', 'place', 'company', 'product', 'brief_discussion', 'status', 'close_date'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Open', 'Close'];
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $query->orderBy('date', 'desc');
        $data = $this->getResult($query);
        $data->withQueryString()->links();
        $allUsers = ['id' => null, 'name' => 'ALL USERS'];
        $user->prepend($allUsers);

        $permissions = [
            'canCreateWeeklyPlanning' => auth()->user()->can('Create Weekly Planning'),
            'canEditWeeklyPlanning'   => auth()->user()->can('Edit Weekly Planning'),
            'canDeleteWeeklyPlanning' => auth()->user()->can('Delete Weekly Planning'),
            'canCloseWeeklyPlanning'  => auth()->user()->can('Close Weekly Planning'),
            'canFilterWeeklyPlanning' => auth()->user()->can('Filter Weekly Planning')
        ];

        return Inertia::render('WeeklyPlan/List', compact('data', 'user', 'userId', 'userInfo', 'permissions'));
    }


    public function create()
    {
        return Inertia::render('WeeklyPlan/Add');
    }

    public function store(WeeklyPlanRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::id();
            WeeklyPlan::create($data);
            DB::commit();
            return Redirect::to('/weeklyplan')->with('success', 'Plan Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = WeeklyPlan::findOrFail($id);
        session(key: ['weekly_plan_list_page_url' => url()->previous()]);
        return Inertia::render('WeeklyPlan/Edit', compact('data'));
    }

    public function update(WeeklyPlanRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = Auth::id();
            $plan = WeeklyPlan::findOrFail($request->id);
            $plan->update($data);
            DB::commit();
            $redirectUrl = session('weekly_plan_list_page_url', route('weeklyplan.index'));
            session()->forget('weekly_plan_list_page_url');
            return Redirect::to($redirectUrl)->with('success', 'Plan Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $company = WeeklyPlan::findOrFail($id);
            $company->delete();
            DB::commit();
            return Redirect::to('/weeklyplan')->with('success', 'Plan Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/weeklyplan')->with('error', $e->getMessage());
        }
    }

    public function changeStatus(Request $request)
    {
        DB::beginTransaction();
        try {
            $company = WeeklyPlan::findOrFail($request->id);
            $company->close_date = date('Y-m-d');
            $company->status = 'Close';
            $company->save();
            DB::commit();
            return Redirect::to('/weeklyplan')->with('success', 'Plan Status Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/weeklyplan')->with('error', $e->getMessage());
        }
    }
}
