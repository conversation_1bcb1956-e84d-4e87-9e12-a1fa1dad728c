<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_delivers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained( table:'orders', indexName: 'od_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('order_details_id')->constrained( table:'order_details', indexName: 'odd_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('order_deliver_number');
            $table->integer('delivered_qty');
            $table->date('deliver_date');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_delivers');
    }
};
