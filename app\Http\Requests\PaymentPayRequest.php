<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\PaymentPaidDTO;
use Support\Contracts\HasDTO;

class PaymentPayRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        if ($this->input('is_credit') === 'Yes') {
            return [
                'organization_id' => 'required',
                'company_id'     => 'required',
            ];
        }

        $rules = [
            'organization_id' => 'required',
            'company_id'     => 'required',
            'payment_type'    => 'required',
            'amount'          => 'required|numeric|gt:0',
            'date'            => 'required|date'
        ];

        if($this->input('payment_type') != 'cash') {
            $rules['org_bank_id'] = 'required|integer';
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $isCredit = $this->input('is_credit') === 'Yes';
            $amount         = $this->input('amount') + $this->input('discount_amount') + $this->input('round_off');
            $settled_amount = $this->input('settled_amount');
            $filteredInvoiceData = array_filter($this->input('invoice'), function ($invoice) {
                return $invoice['check'] === true;
            });

            foreach ($filteredInvoiceData as $key => $invoice) {
                if($invoice && ($invoice['pending_amount'] < ($invoice['amount']))) {
                    $validator->errors()->add("invoice.$key.amount", __('Amount exceeds pending amount'));
                }
            }
            // if($settled_amount > $amount){
            //     $validator->errors()->add("settled_amount", __('Settle amount exceeds total amount'));
            // }
            // if($settled_amount < $amount){
            //     $validator->errors()->add("settled_amount", __('Amount exceeds settle amount'));
            // }
            if (!$isCredit) {
                if ($settled_amount > $amount) {
                    $validator->errors()->add("settled_amount", __('Settled amount exceeds total payable amount'));
                }
                if ($settled_amount <= 0) {
                    $validator->errors()->add("settled_amount", __('Settled amount must be greater than 0'));
                }
            } else {
                if (count($filteredInvoiceData) === 0) {
                    $validator->errors()->add('settled_amount', __('At least one invoice must be selected for credit application.'));
                }
                if ($settled_amount <= 0) {
                    $validator->errors()->add("settled_amount", __('Settled amount must be greater than 0'));
                }
                $totalAvailableCredit = collect($this->input('credit_data'))->sum('unused_amount');
                if ($settled_amount > $totalAvailableCredit) {
                    $validator->errors()->add("settled_amount", __('Settled amount exceeds available credit.'));
                }
            }
        });
    }

    public function DTO()
    {
        return PaymentPaidDTO::LazyFromArray($this->input());
    }

}
