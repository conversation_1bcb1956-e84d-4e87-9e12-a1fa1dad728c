<?php 

namespace App\DTO;

use App\Traits\ArrayToProps;

class MaintenanceContractDTO
{
    use ArrayToProps;

    public $hospital_name;
    public $address;
    public $city;
    public $contact_no;
    public $contract_start_date;
    public $contract_end_date;
    public $maintenance_type;
    public $time_period;
    public $product_name;
    public $price;
    public $company_name;
    public $invoice_number;
    public $pm_date_1;
    public $pm_date_2;
    public $pm_date_3;
    public $pm_date_4;
    public $name;
    public $id;
    public $created_by;
    public $updated_by;

}