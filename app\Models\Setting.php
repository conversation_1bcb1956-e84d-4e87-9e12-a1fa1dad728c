<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    // use ActivityTrait;

    // protected $table = 'settings';

    // protected static $logName = 'Setting';

    // protected static $logAttributes = [
    //     'type',
    //     'value',
    //     'organization_id',
    //     'number',
    //     'updated_by'
    // ];

    protected $fillable = [
        'type',
        'value',
        'number',
        'organization_id',
        'created_by',
        'updated_by'
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id'); 
    }

}
