<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import { Head , usePage} from '@inertiajs/vue3';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps(['data',  'filepath']);
const file = usePage().props.filepath.view;
const poData = usePage().props.data[0];
const totalQty = computed(() => {
    if (props.data[0].proforma_invoice_details) {
        return props.data[0].proforma_invoice_details.reduce((acc, poData) => acc + poData.qty, 0);
    }
    return 0;
});


const totalPrice = computed(() => {
    if (props.data[0].proforma_invoice_details) {
        return props.data[0].proforma_invoice_details.reduce((acc, poData) => acc + poData.total_price, 0).toFixed(2);
    }
    return 0;
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');


const openPreviewModal = (name) => {
    selectedDocument.value = name;
    documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin + file + name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

</script>

<template>
    <Head title="Proforma Invoice"/>

    <AdminLayout>
    <form @submit.prevent="submit" class="">
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Proforma Invoice Detail</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div>
                        <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                    </div>
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('proforma-invoice.index')">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
                <div class="inline-flex items-start space-x-6 justify-start w-full">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Customer:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.customer_name ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.gst_no  ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.email ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.contact_no ?? '-'}}</p>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">PI Number:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].order_number ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">PI Date:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Category:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].category ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-32">Sales Person:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name ?? '-'}} {{ data[0].users.last_name ?? '-'}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Sr No</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Model</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Description</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">MRP (₹)</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Price (₹)</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">QTY</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Price (₹)</th>
                            <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (%)</th>
                            <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (₹)</th>
                            <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">CGST (%)</th>
                            <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">SGST (%)</th>
                            <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total GST (₹)</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Amount (₹)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(product, index)  in data[0].proforma_invoice_details" :key="index">
                            <td class="px-4 py-2.5 min-w-20">{{ index + 1 }}</td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-32">{{ product.product.item_code ?? '-' }}</td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-normal min-w-60"> {{ product.description }}</td>
                            <td class="px-4 py-2.5 min-w-28">{{ (product.product?.serial_numbers[0]?.mrp) ? formatAmount(product.product.serial_numbers[0].mrp): '-' }}</td>
                            <td class="px-4 py-2.5 min-w-28">{{ formatAmount(product.price)}}</td>
                            <td class="px-4 py-2.5">{{ product.qty }}</td>
                            <td class="px-4 py-2.5 min-w-36">{{ formatAmount(product.total_price)}}</td>
                            <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ product.gst }}</td>
                            <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ formatAmount(product.total_gst_amount) }}</td>
                            <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                            <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                            <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-32">{{ formatAmount(product.total_gst_amount) }}</td>
                            <td class="px-4 py-2.5 min-w-44">{{ formatAmount(product.total_amount) }}</td>
                        </tr>
                        <tr class="bg-white border-b">
                            <th class="px-4 py-2.5 min-w-20"></th>
                            <th class="px-4 py-2.5 min-w-32"></th>
                            <th class="px-4 py-2.5 min-w-60"></th>
                            <th class="px-4 py-2.5"></th>
                            <th class="px-4 py-2.5 min-w-28"></th>
                            <th class="px-4 py-2.5 text-gray-900 min-w-22">{{totalQty}}</th>
                            <th class="px-4 py-2.5 text-gray-900 min-w-36">{{ formatAmount(data[0].sub_total) }}</th>
                            <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                            <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(data[0].total_gst) }}</th>
                            <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                            <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                            <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(data[0].total_gst) }}</th>
                            <th class="px-4 py-2.5 text-gray-900 min-w-44">{{  formatAmount(data[0].total_amount) }}</th>
                        </tr>
                    </tbody>
                    </table>
                </div>
            </div>
            <div class="mt-3 bg-white p-1 shadow sm:rounded-lg border" v-if="poData.documents && (poData.documents.length > 0)">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">UPLOADED
                                DOCUMENT
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white">
                        <tr v-for="(files, index) in poData.documents" :key="poData.id" class="">
                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">
                                {{ files.orignal_name }}
                            </td>
                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                <button type="button" @click="openPreviewModal(files.name)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                    </svg>
                                </button>
                                <button type="button" @click="downloadDocument(files.name)">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
    <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
        <div class="p-6">
            <FileViewer :fileUrl="file+ selectedDocument"/>
            <div class="mt-6 px-4 flex justify-end">
                <SecondaryButton @click="closeDocumentPreviewModal"> Cancel</SecondaryButton>
            </div>
        </div>
    </Modal>
    </AdminLayout>
</template>
