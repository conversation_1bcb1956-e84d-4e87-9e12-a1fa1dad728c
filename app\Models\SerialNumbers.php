<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SerialNumbers extends Model
{
    use ActivityTrait;

    use SoftDeletes;

    use HasFactory;

    const STATUS_STOCK = 'Stock';

    protected $table = 'serial_numbers';

    protected static $logName = 'Stock';

    public function getLogDescription(string $event): string
{
    $productNames = $this->product()->pluck('name')->implode(', ');

    return "Stock has been {$event} for <strong>{$productNames}</strong> Batch : {$this->batch} by";
}

    protected static $logAttributes = [
        'organization_id',
        'product_id',
        'purchase_order_receive_detail_id',
        'unique_id',
        'batch',
        // 'serial_no',
        // 'lot_no',
        'expiry_date',
        'purchase_price',
        'mrp',
        'receive_qty',
        'sell_qty',
    ];

    protected $fillable = [
        'organization_id',
        'product_id',
        'purchase_order_receive_detail_id',
        'unique_id',
        'batch',
        // 'serial_no',
        // 'lot_no',
        'expiry_date',
        'purchase_price',
        'mrp',
        'receive_qty',
        'sell_qty',
        'created_by',
        'updated_by'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function purchaseOrderReceiveDetails()
    {
        return $this->belongsTo(PurchaseOrderReceiveDetails::class,  'purchase_order_receive_detail_id', 'id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Stock has been $event for this product";
        self::addCustomLogEntry($model, $event, $logName);
    }
}
