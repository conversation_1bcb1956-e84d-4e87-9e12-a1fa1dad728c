<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->string('cr_dr_note')->nullable()->after('transport');
            $table->bigInteger('due_days')->nullable()->after('transport');
            $table->string('eway_bill')->nullable()->after('transport');
            $table->string('customer_po_number')->nullable()->after('transport');
            $table->date('customer_po_date')->nullable()->after('transport');
            $table->string('patient_name')->nullable()->after('transport');
        });

        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->integer('organization_id')->nullable()->after('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            //
        });
    }
};
