<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->defalt(0)->after('total_gst');
        });

        Schema::table('quotation', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->defalt(0)->after('igst');
            $table->double('total_discount', 16, 2)->nullable()->defalt(0)->after('overall_discount');

        });

        Schema::table('quotation_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->defalt(0)->after('total_gst_amount');
            $table->double('discount_amount', 16, 2)->nullable()->defalt(0)->after('discount');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->defalt(0)->after('total_gst');
            $table->double('total_discount', 16, 2)->nullable()->defalt(0)->after('overall_discount');
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->defalt(0)->after('total_gst_amount');
            $table->double('discount_amount', 16, 2)->nullable()->defalt(0)->after('discount');
        });

        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->double('overall_discount', 16, 2)->nullable()->defalt(0)->after('total_gst');
            $table->double('total_discount', 16, 2)->nullable()->defalt(0)->after('overall_discount');
        });

        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->nullable()->defalt(0)->after('total_gst_amount');
            $table->double('discount_amount', 16, 2)->nullable()->defalt(0)->after('discount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
