<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChallanDetail extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'challan_detail';

    protected static $logName = 'Challan-Detail';

    public function getLogDescription(string $event): string
    {
        $challanNumber = $this->challan->challan_number;

        $productNames = $this->product()->pluck('name')->implode(', ');

        if (empty($productNames)) {
            $productNames = 'Unknown Product';
        }

        return "Challan detail has been {$event} for <strong>{$productNames}</strong> : {$challanNumber} by";
    }

    protected static $logAttributes = [
        'challan_id',
        'is_receive',
        'product_id',
        'serial_number_id',
        'serial_id',
        'created_by',
        'updated_by',
        'qty',
        'price',
        'total_price',
        'gst',
        'gst_amount',
        'total_gst_amount',
        'total_amount',
        'description',
        'invoiced_qty',
        'return_qty'
    ];
    protected $fillable = [
        'challan_id',
        'is_receive',
        'product_id',
        'serial_number_id',
        'serial_id',
        'created_by',
        'updated_by',
        'qty',
        'price',
        'total_price',
        'gst',
        'gst_amount',
        'total_gst_amount',
        'total_amount',
        'description',
        'invoiced_qty',
        'return_qty'
    ];

    public function challan()
    {
        return $this->belongsTo(Challan::class);
    }

    public function serialnumbers(){
        return $this->belongsTo(SerialNumbers::class, 'serial_number_id', 'id')->whereRaw('(receive_qty - sell_qty) > 0');
    }

    public function viewserialnumbers(){
        return $this->belongsTo(SerialNumbers::class, 'serial_number_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                $event = $model->isDirty('is_receive') ? 'received' : 'updated';
                self::handleLogEntry($model, $event);
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Challan: " . $model->challan->challan_number;
        $mergedAttributes = [];

        if ($event == 'created' || $event == 'deleted') {
            $mergedAttributes = [
                'challan'         => $model->challan->getAttributes(),
                'challanDetail'   => $model->getAttributes()
            ];
        } elseif ($event == 'updated') {
            // You can include only changed attributes if needed.
        }

        self::addCustomLogEntry($model, $event, $logName, $mergedAttributes);
    }
}
