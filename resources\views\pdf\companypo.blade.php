<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <title>Purchase Order</title>
  <style>

    @page { margin: 0px;}

    body {
        / background: #666666; /
        margin: 0;
        margin: 10px;
        border: 1px solid rgb(55 65 81) !important;
        padding: 10px;
        text-align: center;
        color: #333;
        font-family: ui-sans-serif, system-ui, sans-serif;
        /* font-family: Arial, Helvetica, 'DejaVu Sans', sans-serif; */
    }
    p {
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.6;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        padding: 20px 0px;
    }
    #pdf-content td {
        border-bottom : 1px solid rgb(55 65 81)  !important;
        border-right: 1px solid rgb(55 65 81)  !important;
        padding: 4px 2px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 1px solid rgb(55 65 81)  !important;
        border-right: 1px solid rgb(55 65 81)  !important;
        border-top: 1px solid rgb(55 65 81)  !important;
        padding: 6px 2px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap !important;
    }
</style>

</head>
<body>
    <div id="">
        @if($data[0]->organization->id == 3)
            <table style="width: 100%; margin-bottom: 10px;">
                <tr>
                    <td style="text-align: left; width: 80px;">
                        <img style="width: 80px; height: 80px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                    <td style="text-align: center;">
                        <b class="font-size: 20px;">Purchase Order</b>
                    </td>
                    <td style="width: 120px;"></td>
                </tr>
            </table>
        @endif

        @if($data[0]->organization->id == 1 || $data[0]->organization->id == 2)
            <table style="width: 100%; text-align: center;">
                <tr>
                    <td style="text-align: center;">
                        <img style="width:100%; height:40px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->logo))) }}" alt="logo">
                    </td>
                </tr>
            </table>
            <div style="align-items: center">
                <b class="font-size: 20px;"> Purchase Order
                </b>
            </div>
        @endif
    </div>
    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left; width: 300px;">
                <table>
                    <tr>
                        <td>
                            <p><strong>{{ $data[0]->organization->name }}</strong></p>
                            <p>{{ $data[0]->organization->address_line_1 }}</p>
                            <p>{{ $data[0]->organization->address_line_2 }}</p>
                            <p>{{ $data[0]->organization->city }}</p>
                            @if (isset($data[0]->users))
                            <p><strong>Originator : </strong>{{ $data[0]->users->first_name }}  {{ $data[0]->users->last_name }}</p>
                            @endif
                            <p><strong>Phone : </strong>{{ $data[0]->organization->contact_no }}</p>
                            <p><strong>Email : </strong>{{ $data[0]->organization->email }}</p>
                            <p><strong>GST : </strong>{{ $data[0]->organization->gst_no }}</p>
                        </td>
                    </tr>
                </table>
                <td style="text-align:center; width: 120px;">
                </td>
            </td>
            <td style="text-align:right; width: 320px; vertical-align: top;">
                <table>
                    <tr>
                        <td>
                            <p><span class="label"><strong>Po Number : </strong></span>{{ $data[0]->po_number }}</p>
                            <p><span class="label"><strong>Po Date : </strong></span>{{ date('d-m-Y', strtotime($data[0]->date))}}</p>
                            <p><strong>{{ $data[0]->company->name }}</strong></p>
                            <p>{{ $data[0]->company->address }}</p>
                            <p><span class="label"><strong>Phone : </strong></span>{{ $data[0]->company->contact_no ?? '-'}}</p>
                            <p><span class="label"><strong>Email : </strong></span>{{ $data[0]->company->email ?? '-' }}</p>
                            <p><span class="label"><strong>GST : </strong></span> {{ $data[0]->company->gst_no ?? '-' }}</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <div class="" id="pdf-content">
        <table>
          <thead>
              <tr>
                  <th>SN</th>
                  <th>Code</th>
                  <th>Product Description</th>
                  <th>HSN</th>
                  <th>Qty</th>
                  <th>Price</th>
                  <th>Total Price</th>
                @if($data[0]->company->gst_type =='CGST/SGST')
                    <th>CGST(%)</th>
                @endif
                @if($data[0]->company->gst_type =='CGST/SGST')
                    <th>SGST(%)</th>
                @endif
                @if($data[0]->company->gst_type =='IGST')
                    <th>IGST(%)</th>
                @endif
                <th>Total Gst</th>
                <th>Total Amount</th>
              </tr>
          </thead>
          <tbody>
            @foreach ($data[0]->purchaseOrderDetail as $index => $poData)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $poData->product->item_code }}</td>
                <td>{{ $poData->product->name }} {{ $poData->description }}</td>
                <td>{{ $poData->product->hsn_code ?? '-' }}</td>
                <td>{{ $poData->pkg_of_qty != null ? $poData->pkg_of_qty : $poData->qty }}</td>
                <td>{{ number_format($poData->price, 2) }}</td>
                <td>{{ number_format($poData->total_price, 2) }}</td>
                @if($data[0]->company->gst_type == 'CGST/SGST')
                    <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                    <td>{{ number_format($poData->gst / 2, 2) ?? '-' }}</td>
                @elseif($data[0]->company->gst_type == 'IGST')
                    <td>{{ number_format($poData->gst, 2) ?? '-' }}</td>
                @endif
                <td>{{ number_format($poData->total_gst_amount, 2) }}</td>
                <td>{{ number_format($poData->total_amount, 2) }}</td>
            </tr>
            @endforeach
              </tbody>
        </table>
    </div>
    <div style="text-align:left;">
        <p>{{ $data[0]->note }}</p>
    </div>
    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left; width: 420px;">
                <td style="text-align:center; width: 80px;"></td>
            </td>
            <td style="text-align:right; width: 320px;">
                <table>
                    <tr>
                        <p><span class="label"><strong >Sub Total : </strong></span>{{ number_format($data[0]->sub_total, 2) }}</p>
                        <p><span class="label"><strong>Total Discount : </strong></span>{{ number_format($data[0]->total_discount, 2) }}</p>
                        @if($data[0]->company->gst_type =='CGST/SGST')
                            <p><span class="label"><strong>Total CGST : </strong></span>{{ number_format($data[0]->cgst, 2)}}</p>
                        @endif
                        @if($data[0]->company->gst_type =='CGST/SGST')
                            <p><span class="label"><strong>Total SGST : </strong></span>{{ number_format($data[0]->sgst, 2)}}</p>
                        @endif
                        @if($data[0]->company->gst_type =='IGST')
                            <p><span class="label"><strong>Total IGST : </strong></span>{{ number_format($data[0]->igst, 2)}}</p>
                        @endif
                        <p><span class="label"><strong>Total Amount : </strong></span>{{ number_format($data[0]->total_amount, 2) }}</p>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <table style="width:100%;">
        <tr style="width:100%;">
            <td style="text-align:left; width: 300px;"> <p><strong></strong></p></td>
            <td style="text-align:center; width: 120px;"></td>
            <td style="width: 320px;">
                <p style="font-weight: bold;">FOR,</p>
                <p><strong>{{ $data[0]->organization->name }}</strong></p>
                <img style="width:auto; height:112px" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($filepath['view'] . '/' . $data[0]->organization->signature))) }}">
            </td>
        </tr>
    </table>
</body>
</html>

<style>


</style>
