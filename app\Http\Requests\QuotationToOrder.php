<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuotationToOrder extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            //
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $selectedProductItem  = $this->input('selectedProductItem');
            $acceptedQtys = 0;
            foreach ($selectedProductItem  as $key => $product) {
                if($product['check']){
                    $acceptedQtys++;
                }
            }
            if ($acceptedQtys == 0) {
                $validator->errors()->add("selectedProductItem.$key.check", __('At least one qty must be Checked.'));
            }
        });
    }
}
