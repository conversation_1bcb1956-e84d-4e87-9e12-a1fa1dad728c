<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('funnel', function (Blueprint $table) {
            $table->string('close_date')->nullable()->after('inquiry_type');         
            $table->string('mobile_number')->nullable()->after('place');
        });
        DB::statement("ALTER TABLE `funnel` MODIFY `inquiry_type` ENUM('Hot', 'Cold', 'Warm', 'Close')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
