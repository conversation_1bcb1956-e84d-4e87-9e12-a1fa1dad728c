<?php

namespace App\Http\Requests;

use App\Models\ChallanDetail;
use App\Models\SerialNumbers;
use App\Models\Customer;
use Illuminate\Foundation\Http\FormRequest;
use App\DTO\ChallanDTO;
use Support\Contracts\HasDTO;

class ChallanStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'note'          => 'nullable|string',
            'customer_id'   => 'required|integer',
            'selectedProductItem.*.product_id'  => 'required|integer',
            'selectedProductItem.*.qty'  => 'required|integer',
            'sales_user_id' => 'required|integer',
        ];

        if (empty($this->input('challan_id'))) {
            $rules['organization_id'] = 'required|integer';
            $rules['category'] = 'required|string';
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        if (empty($this->input('challan_id'))) {
            $validator->after(function ($validator) {
                if ($this->input('stock_transfer')) {
                    $customer = Customer::where('id', $this->input('customer_id'))->first();
                    if ($customer && ($this->input('organization_id') == $customer->organization_id)) {
                        $validator->errors()->add("customer_id", __('Can not transfer to same organization'));
                    }
                }

                $selectedProductItems = $this->input('selectedProductItem');
                $serialNumberQuantities = [];

                foreach ($selectedProductItems as $key => $product) {
                    $serialNumberId = $product['serial_number_id'];
                    $qty = $product['qty'];
                    if (!isset($serialNumberQuantities[$serialNumberId])) {
                        $serialNumberQuantities[$serialNumberId] = 0;
                    }
                    $serialNumberQuantities[$serialNumberId] += $qty;
                    $productDetails = SerialNumbers::find($product['serial_number_id']);
                    if ($productDetails) {
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                        if ($productDetails && ($productDetails->mrp != '') && $product['sell_price'] > ($productDetails->mrp)) {
                            $validator->errors()->add("selectedProductItem.$key.sell_price", __('Price exceeds mrp'));
                        }
                    }
                }
            });
        } else {
            $validator->after(function ($validator) {
                $selectedProductItems = $this->input('selectedProductItem');
                $serialNumberQuantities = [];

                foreach ($selectedProductItems as $key => $product) {
                    $serialNumberId = $product['serial_number_id'];
                    $qty = $product['qty'];

                    if (!isset($serialNumberQuantities[$serialNumberId])) {
                        $serialNumberQuantities[$serialNumberId] = 0;
                    }
                    $serialNumberQuantities[$serialNumberId] += $qty;

                    if (empty($product['challan_detail_id'])) {
                        $productDetails = SerialNumbers::find($product['serial_number_id']);
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                    } else {
                        $productDetails = SerialNumbers::find($product['serial_number_id']);
                        $challanDetails = ChallanDetail::find($product['challan_detail_id']);
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty + $challanDetails->qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty && $product['qty'] != $challanDetails->qty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                    }

                    if ($productDetails && ($productDetails->mrp != '') && $product['sell_price'] > ($productDetails->mrp)) {
                        $validator->errors()->add("selectedProductItem.$key.sell_price", __('Price exceeds mrp'));
                    }
                }
            });
        }
    }

    public function DTO()
    {
        return ChallanDTO::LazyFromArray($this->input());
    }

}
