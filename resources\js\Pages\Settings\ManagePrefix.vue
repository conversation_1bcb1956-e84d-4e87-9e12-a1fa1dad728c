<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SvgLink from '@/Components/ActionLink.vue';
import { Head , usePage } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';
import { onMounted } from 'vue';

const prefixData  = usePage().props.data;

const props = defineProps(['data']);

const findValueByType = (type) => {
    const setting = prefixData.find(item => item.type === type);
    return setting ? setting.value : null;
};

const form = useForm('post', '/saveprefix', {
    po_number:  prefixData ? findValueByType('po_number') : '',
    po_receive_number: prefixData ? findValueByType('po_receive_number') : '',
    quotation_number: prefixData ? findValueByType('quotation_number') : '',
    order_number: prefixData ? findValueByType('order_number') : '',
    order_deliver_number: prefixData ? findValueByType('order_deliver_number') : '',
    challan_number: prefixData ? findValueByType('challan_number') : '',
    invoice_number: prefixData ? findValueByType('invoice_number') : '',
    retail_invoice_number: prefixData ? findValueByType('retail_invoice_number') : '',
    demo_challan_number: prefixData ? findValueByType('demo_challan_number') : '',
    job_card_number: prefixData ? findValueByType('job_card_number') : '',
    pi_number: prefixData ? findValueByType('pi_number') : '',
    credit_note_no: prefixData ? findValueByType('credit_note_no') : ''
});

const submit = () => form.submit({
    preserveScroll: true,
    resetOnSuccess: false
});

</script>

<template>
    <Head title="Manage Prefix" />
    <AdminLayout>
        <div class="h-screen animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Manage Prefix</h1>
                </div>
                <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('setting')">
                                Back
                        </CreateButton>

                    </div>
                </div>
            </div>
            <div class="mt-8 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-4">
                        <div class="mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <InputLabel for="po_number" value="PO Prefix" />
                                <TextInput
                                    id="po_number"
                                    type="text"
                                    v-model="form.po_number"
                                    autocomplete="po_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="po_receive_number" value="PO Receive Prefix"/>
                                <TextInput
                                    id="po_receive_number"
                                    type="text"
                                    v-model="form.po_receive_number"
                                    autocomplete="po_receive_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="quotation_number" value="Quotation Prefix"/>
                                <TextInput
                                    id="quotation_number"
                                    type="text"
                                    v-model="form.quotation_number"
                                    autocomplete="quotation_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="order_number" value="Order Prefix" />
                                <TextInput
                                    id="order_number"
                                    type="text"
                                    v-model="form.order_number"
                                    autocomplete="order_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="order_deliver_number" value="Order Deliver Prefix"/>
                                <TextInput
                                    id="order_deliver_number"
                                    type="text"
                                    v-model="form.order_deliver_number"
                                    autocomplete="order_deliver_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="challan_number" value="Challan Prefix"/>
                                <TextInput
                                    id="challan_number"
                                    type="text"
                                    v-model="form.challan_number"
                                    autocomplete="challan_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="invoice_number" value="Tax Invoice Prefix"/>
                                <TextInput
                                    id="invoice_number"
                                    type="text"
                                    v-model="form.invoice_number"
                                    autocomplete="invoice_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="retail_invoice_number" value="Retail Invoice Prefix"/>
                                <TextInput
                                    id="retail_invoice_number"
                                    type="text"
                                    v-model="form.retail_invoice_number"
                                    autocomplete="retail_invoice_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="demo_challan_number" value="Demo Challan Prefix"/>
                                <TextInput
                                    id="demo_challan_number"
                                    type="text"
                                    v-model="form.demo_challan_number"
                                    autocomplete="demo_challan_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="job_card_number" value="Job Card Number"/>
                                <TextInput
                                    id="job_card_number"
                                    type="text"
                                    v-model="form.job_card_number"
                                    autocomplete="job_card_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="pi_number" value="PI Prefix" />
                                <TextInput
                                    id="pi_number"
                                    type="text"
                                    v-model="form.pi_number"
                                    autocomplete="pi_number"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="credit_note_no" value="Credit Note Prefix" />
                                <TextInput
                                    id="credit_note_no"
                                    type="text"
                                    v-model="form.credit_note_no"
                                    autocomplete="credit_note_no"
                                    required
                                />
                                <InputError class="" :message="form.errors.value" />
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">

                        <div class="ml-auto flex items-center justify-end gap-x-6">
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                        </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
