<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditNote extends Model
{
    use HasFactory;

    use ActivityTrait;

    // use SoftDeletes;

    protected $table = 'credit_notes';

    protected static $logName = 'CreditNote';

    public function getLogDescription(string $event): string
    {
        $customerName = $this->customers?->customer_name ?? 'Unknown Customer';

        return "CreditNote <strong>{$this->credit_note_no}</strong> has been {$event} for <strong>{$customerName}</strong> by";
    }

    protected static $logAttributes = [
        'invoice_type',
        'organization_id',
        'customer_id',
        'invoice_id',
        'credit_note_no',
        'debit_note_number',
        'date',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'reason',
        'created_by',
        'updated_by',
    ];
    protected $fillable = [
        'invoice_type',
        'organization_id',
        'customer_id',
        'invoice_id',
        'credit_note_no',
        'debit_note_number',
        'date',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'reason',
        'created_by',
        'updated_by',
    ];

    public function creditNoteDetails()
    {
        return $this->hasMany(InvoiceDetail::class, 'credit_note_id', 'id')->where('is_receive', 'yes');
    }

    public function customers(){
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function organization(){
        return $this->belongsTo(Organization::class,'organization_id','id');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id','id');
    }
}
