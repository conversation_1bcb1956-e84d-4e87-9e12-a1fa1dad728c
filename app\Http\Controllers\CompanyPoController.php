<?php

namespace App\Http\Controllers;

use App\Services\CompanyService;
use Illuminate\Http\Request;
use App\Http\Requests\CompanyPoStoreRequest;
use App\Http\Requests\paymentRequest;
use App\Http\Requests\PoReceiveRequest;
use App\Traits\CommonTrait;
use Config;

class CompanyPoController extends Controller
{
    use CommonTrait;

    protected $companyService;

    public function __construct(CompanyService $companyService){
        $this->companyService = $companyService;
        $this->middleware('permission:List Companypo')->only(['index']);
        $this->middleware('permission:Create Companypo')->only(['create', 'store']);
        $this->middleware('permission:Edit Companypo')->only(['edit', 'store']);
        $this->middleware('permission:Delete Companypo')->only('destroy');
        $this->middleware('permission:View Companypo')->only('viewPO');
        $this->middleware('permission:Receive Companypo')->only('receivePO', 'saveReceivePO');
        $this->middleware('permission:Payment Companypo')->only('paymentPay');
    }

    public function index(Request $request)
    {
        return $this->companyService->getCompanyPurchaseOrderList($request);
    }

    public function create()
    {
        return $this->companyService->createCompanyPurchaseOrder();
    }

    public function store(CompanyPoStoreRequest $request)
    {
        return $this->companyService->saveCompanyCompanyPurchaseData($request);
    }

    public function receivePO(Request $request, $id)
    {
        return $this->companyService->receiveCompanyPurchaseOrder($id);
    }

    public function saveReceivePO(PoReceiveRequest $request)
    {
        return $this->companyService->receiveCompanyPurchaseOrderData($request);
    }

    public function paymentPay(paymentRequest $request)
    {
        return $this->companyService->purchaseOrderpaymentPayData($request);
    }

    public function viewPO(Request $request, $id)
    {
        return $this->companyService->viewCompanyPurchaseOrder($id);
    }

    public function edit(string $id)
    {
        return $this->companyService->editCompanyPurchaseOrder($id);
    }

    public function destroy($id)
    {
        return $this->companyService->deleteCompanyPurchaseOrder($id);
    }

    public function downloadPo($id, $type)
    {
        return $this->companyService->downloadPurchaseOrder($id, $type);
    }

}
