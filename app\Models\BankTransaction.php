<?php

namespace App\Models;
use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankTransaction extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'bank_transaction';

    protected static $logName = 'Bank-Transaction';

    public function getLogDescription(string $event): string
    {
        $bankName = $this->bankInfo ? $this->bankInfo->bank_name : 'Unknown Bank';
        $accountNumber = $this->bankInfo ? $this->bankInfo->account_number : 'Unknown Account';

        return "Bank transaction has been {$event} for <strong>{$bankName}:{$accountNumber}</strong> by";
    }

    protected static $logAttributes = [
        'entity_id',
        'entity_type',
        'org_bank_id',
        'payment_type',
        'amount',
        'account_type',
        'note',
        'date',
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'entity_id',
        'entity_type',
        'org_bank_id',
        'payment_type',
        'amount',
        'account_type',
        'note',
        'date',
        'created_by',
        'updated_by',
    ];

    public function bankInfo()
    {
        return $this->belongsTo(BankInfo::class, 'org_bank_id', 'id');
    }
    public function bank()
    {
    return $this->belongsTo(BankInfo::class, 'org_bank_id');
    }

    public function accounttype(){
        return $this->belongsTo(AccountType::class,'account_type','id');
    }

    public function paymentReceive(){
        return $this->belongsTo(PaymentReceive::class,'entity_id','id');
    }

    public function paymentPaid(){
        return $this->belongsTo(PaymentPaid::class,'entity_id','id');
    }

    // public function paymentReceive()
    // {
    //     return $this->morphTo()->where('entity_type', PaymentReceive::class);
    // }

    // public function paymentPaid()
    // {
    //     return $this->morphTo()->where('entity_type', PaymentPaid::class);
    // }

}
