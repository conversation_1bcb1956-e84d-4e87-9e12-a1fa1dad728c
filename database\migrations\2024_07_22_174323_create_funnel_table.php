<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('funnel', function (Blueprint $table) {
            $table->id();
            $table->string('customer_name');
            $table->string('dr_name')->nullable();
            $table->string('place');
            $table->string('company')->nullable();
            $table->string('product')->nullable();
            $table->double('order_value', 16, 2);
            $table->string('order_month');
            $table->enum('inquiry_type', ['Hot', 'Warm', 'Cold']);
            $table->longText('status')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('funnel');
    }
};
