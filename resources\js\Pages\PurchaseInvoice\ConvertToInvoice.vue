<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps, ref, computed, watch, onMounted } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    purchaseOrder: Object,
    existingInvoices: Array,
});

const invoiceType = ref('new'); // 'new' or 'existing'
const selectedExistingInvoice = ref(null);

const form = useForm('post', route('purchaseinvoice.save-convert-to-invoice'), {
    id: props.purchaseOrder.id,
    purchase_order_id: props.purchaseOrder.purchase_order_id,
    invoice_type: invoiceType.value,
    existing_invoice_id: null,
    customer_invoice_no: '',
    customer_invoice_date: '',
    total_price: props.purchaseOrder.total_price,
    total_gst_amount: props.purchaseOrder.total_gst_amount,
    total_amount: props.purchaseOrder.total_amount,
    products: props.purchaseOrder.purchase_order_receive_details.map(detail => ({
        id: detail.id,
        product_id: detail.product_id,
        purchase_order_detail_id: detail.purchase_order_detail_id,
        qty: detail.receive_qty,
        price: detail.serial_numbers[0].purchase_price,
        total_price: 0,
        gst: detail.purchase_order_detail.gst,
        total_gst_amount: 0,
        total_amount: 0,
        product_name: detail.product.name
    }))
});

watch(invoiceType, (newValue) => {
    form.invoice_type = newValue;
    if (newValue === 'existing' && selectedExistingInvoice.value) {
        console.log('newValue', newValue);
        // form.existing_invoice_id = selectedExistingInvoice.value.id;

    } else if (newValue === 'new') {
        form.existing_invoice_id = null;
        form.customer_invoice_no = '';
        form.customer_invoice_date = '';
    }
});

onMounted(() => {
    form.products.forEach(product => {
        calculateProductTotals(product);
    });
});

// watch(
//     () => form.products,
//     () => {
//         updateFormTotals();
//     },
//     { deep: true }
// );

const setInvoice = (id, name) => {
    form.existing_invoice_id = id;
    form.errors.existing_invoice_id = null;
    const invoice = props.existingInvoices.find(product => product.id === id);
    form.customer_invoice_no = invoice.customer_invoice_no;
    form.customer_invoice_date = invoice.customer_invoice_date;
};

const calculateProductTotals = (product) => {
    // Calculate total price (price * qty)
    product.total_price = parseFloat(product.price) * parseFloat(product.qty);

    // Calculate GST amount
    product.total_gst_amount = (product.total_price * parseFloat(product.gst)) / 100;

    // Calculate total amount (total price + GST amount)
    product.total_amount = product.total_price + product.total_gst_amount;

    // Update form totals
    updateFormTotals();
};

const updateFormTotals = () => {
    // Calculate total price, GST amount, and total amount for all products
    let totalPrice = 0;
    let totalGstAmount = 0;
    let totalAmount = 0;

    form.products.forEach(product => {
        totalPrice += parseFloat(product.total_price);
        totalGstAmount += parseFloat(product.total_gst_amount);
        totalAmount += parseFloat(product.total_amount);
    });

    form.total_price = totalPrice;
    form.total_gst_amount = totalGstAmount;
    form.total_amount = totalAmount;
};

const submit = () => {
    // Validate form based on invoice type
    if (invoiceType.value === 'new') {
        if (!form.customer_invoice_no) {
            form.errors.customer_invoice_no = 'Invoice number is required';
            return;
        }
        if (!form.customer_invoice_date) {
            form.errors.customer_invoice_date = 'Invoice date is required';
            return;
        }
    } else if (invoiceType.value === 'existing') {
        if (!form.existing_invoice_id) {
            form.errors.existing_invoice_id = 'Please select an existing invoice';
            return;
        }
    }

    form.submit({
        preserveScroll: true,
        onSuccess: () => {
            // Redirect handled by controller
        },
    });
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};
</script>

<template>
    <Head title="Convert Challan to Invoice" />
    <AdminLayout>
        <div class="animate-top">
            <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-2xl font-semibold leading-7 text-gray-900">Convert Challan to Invoice</h1>
                    </div>
                </div>
                <form @submit.prevent="submit" class="mt-6">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="po_number" value="PO Number" />
                                <div class="mt-2">
                                    <TextInput
                                        id="po_number"
                                        type="text"
                                        v-model="purchaseOrder.purchase_order.po_number"
                                        disabled
                                        class="bg-gray-100"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="company" value="Company" />
                                <div class="mt-2">
                                    <TextInput
                                        id="company"
                                        type="text"
                                        v-model="purchaseOrder.purchase_order.company.name"
                                        disabled
                                        class="bg-gray-100"
                                    />
                                </div>
                            </div>

                            <div class="sm:col-span-6">
                                <InputLabel value="Invoice Type" />
                                <div class="mt-2 flex space-x-4">
                                    <div class="flex items-center">
                                        <input
                                            id="invoice_type_new"
                                            type="radio"
                                            value="new"
                                            v-model="invoiceType"
                                            class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                                        />
                                        <label for="invoice_type_new" class="ml-2 block text-sm font-medium leading-6 text-gray-900 cursor-pointer">Create New Invoice</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input
                                            id="invoice_type_existing"
                                            type="radio"
                                            value="existing"
                                            v-model="invoiceType"
                                            class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                                            :disabled="existingInvoices.length === 0"
                                        />
                                        <label for="invoice_type_existing" class="ml-2 block text-sm font-medium leading-6 text-gray-900 cursor-pointer">
                                            Merge with Existing Invoice
                                            <span v-if="existingInvoices.length === 0" class="text-gray-500">(No existing invoices available)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div v-if="invoiceType === 'existing'" class="sm:col-span-4">
                                <InputLabel for="existing_invoice" value="Select Existing Invoice" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="existingInvoices"
                                    v-model="selectedExistingInvoice"
                                    @onchange="setInvoice"
                                    :class="{ 'error rounded-md': form.errors.existing_invoice_id }"
                                    />
                                </div>
                            </div>

                            <div v-if="invoiceType === 'new'" class="sm:col-span-3">
                                <InputLabel for="customer_invoice_no" value="Invoice Number" />
                                <div class="mt-2">
                                    <TextInput
                                        id="customer_invoice_no"
                                        type="text"
                                        v-model="form.customer_invoice_no"
                                        @change="clearError('customer_invoice_no')"
                                        :class="{ 'error rounded-md': form.errors.customer_invoice_no }"
                                    />
                                    <InputError :message="form.errors.customer_invoice_no" />
                                </div>
                            </div>
                            <div v-if="invoiceType === 'new'" class="sm:col-span-3">
                                <InputLabel for="customer_invoice_date" value="Invoice Date" />
                                <div class="mt-2">
                                    <input
                                        id="customer_invoice_date"
                                        type="date"
                                        v-model="form.customer_invoice_date"
                                        @change="clearError('customer_invoice_date')"
                                        :class="{ 'error rounded-md': form.errors.customer_invoice_date, 'mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6': true }"
                                    />
                                    <InputError :message="form.errors.customer_invoice_date" />
                                </div>
                            </div>

                            <div v-if="invoiceType === 'existing'" class="sm:col-span-3">
                                <InputLabel for="customer_invoice_no_display" value="Invoice Number" />
                                <div class="mt-2">
                                    <TextInput
                                        id="customer_invoice_no_display"
                                        type="text"
                                        v-model="form.customer_invoice_no"
                                        disabled
                                        class="bg-gray-100"
                                    />
                                </div>
                            </div>
                            <div v-if="invoiceType === 'existing'" class="sm:col-span-3">
                                <InputLabel for="customer_invoice_date_display" value="Invoice Date" />
                                <div class="mt-2">
                                    <TextInput
                                        id="customer_invoice_date_display"
                                        type="date"
                                        v-model="form.customer_invoice_date"
                                        disabled
                                        class="bg-gray-100"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-300">
                                        <thead>
                                            <tr>
                                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Product</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Quantity</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Price (₹)</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Total Price (₹)</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">GST (%)</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">GST Amount (₹)</th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Total Amount (₹)</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            <tr v-for="(product, index) in form.products" :key="product.id">
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">{{ product.product_name }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ product.qty }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                    <TextInput
                                                        type="number"
                                                        step="0.01"
                                                        v-model="product.price"
                                                        @change="calculateProductTotals(product)"
                                                        class="w-24"
                                                    />
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ formatAmount(product.total_price) }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                    <TextInput
                                                        type="number"
                                                        step="0.01"
                                                        v-model="product.gst"
                                                        @change="calculateProductTotals(product)"
                                                        class="w-16"
                                                    />
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ formatAmount(product.total_gst_amount) }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ formatAmount(product.total_amount) }}</td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3" class="py-4 pl-4 pr-3 text-right text-sm font-semibold text-gray-900">Total:</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900">{{ formatAmount(form.total_price) }}</td>
                                                <td></td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900">{{ formatAmount(form.total_gst_amount) }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900">{{ formatAmount(form.total_amount) }}</td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="mt-6 flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('purchaseinvoice.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">Convert to Invoice</PrimaryButton>
                    </div> -->
                    <div class="flex items-center justify-end mt-6 w-full">
                        <div class="flex items-center justify-end gap-x-6 w-1/2">
                            <SvgLink :href="route('purchaseinvoice.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                        Cancel
                                    </button>
                                </template>
                            </SvgLink>
                            <PrimaryButton :disabled="form.processing">
                                {{ invoiceType === 'new' ? 'Create New Invoice' : 'Merge with Existing Invoice' }}
                            </PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
