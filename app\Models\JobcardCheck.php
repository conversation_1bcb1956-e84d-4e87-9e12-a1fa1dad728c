<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class JobcardCheck extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'job_card_checks';

    protected static $logName = 'Jobcard-Check';

    public function getLogDescription(string $event): string
    {
        return "Jobcard check has been {$event} by";
    }

    protected static $logAttributes = [
        'job_card_id',
        'job_card_checklist_id',
        'created_by',
        'updated_by'
    ];
    protected $fillable = [
        'job_card_id',
        'job_card_checklist_id',
        'created_by',
        'updated_by'
    ];
}
