<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import FileUpload from '@/Components/FileUpload.vue';
import { Head , usePage } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const generalData = usePage().props.data;
const file = usePage().props.filepath.view;

defineProps({
    data: {
        type: Object,
    },
    filepath: {
        type: Object,
    }
});


const form = useForm('post', '/organization', {
    id: generalData.id,
    name: generalData ? generalData.name : '',
    address_line_1: generalData ? generalData.address_line_1 : '',
    address_line_2: generalData ? generalData.address_line_2 : '',
    pincode: generalData ? generalData.pincode : '',
    city: generalData ? generalData.city : '',
    state: generalData ? generalData.state : '',
    gst_no: generalData ? generalData.gst_no : '',
    pan_no: generalData ? generalData.pan_no : '',
    drug_licence_no: generalData ? generalData.drug_licence_no : '',
    email: generalData ? generalData.email : '',
    contact_no: generalData ? generalData.contact_no : '',
    remarks: generalData ? generalData.remarks : '',
    logo: generalData ? file+ generalData.logo : '/uploads/companyprofile/defaultimg.png',
    signature: generalData ? file+ generalData.signature : '/uploads/companyprofile/defaultimg.png'
});

const submit = () => form.submit({
    preserveScroll: true,
    resetOnSuccess: false,
});

const handleLogo = (file) => {
    form.logo = file;
};

const handleSignature = (file) => {
    form.signature = file;
};

</script>

<template>
    <Head title="Organization" />
    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Update Organization</h2>
            <form  @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-4">
                    <div class="mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-4">
                            <InputLabel for="name" value="Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                                autocomplete="name"
                                @change="form.validate('name')"
                            />
                            <InputError class="" :message="form.errors.name" />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="email" value="Email"/>
                            <TextInput
                                id="email"
                                type="text"
                                v-model="form.email"
                                autocomplete="email"
                                @change="form.validate('email')"
                            />
                            <InputError class="" :message="form.errors.email" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="gst_no" value="GST Number"/>
                            <TextInput
                                id="gst_no"
                                type="text"
                                v-model="form.gst_no"
                                maxLength="15"
                                autocomplete="gst_no"
                                @change="form.validate('gst_no')"
                            />
                            <InputError class="" :message="form.errors.gst_no" />
                        </div>
                        <div class="sm:col-span-1">
                            <InputLabel for="pan_no" value="PAN Number"/>
                            <TextInput
                                id="pan_no"
                                type="text"
                                v-model="form.pan_no"
                                maxLength="15"
                                autocomplete="pan_no"
                                @change="form.validate('pan_no')"
                            />
                            <InputError class="" :message="form.errors.pan_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="drug_licence_no" value="DrugLicence Number"/>
                            <TextInput
                                id="drug_licence_no"
                                type="text"
                                v-model="form.drug_licence_no"
                                autocomplete="drug_licence_no"
                                @change="form.validate('drug_licence_no')"
                            />
                            <InputError class="" :message="form.errors.drug_licence_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="contact_no" value="Contact Number"/>
                            <TextInput
                                id="contact_no"
                                type="text"
                                v-model="form.contact_no"
                                :numeric="true"
                                maxLength="10"
                                autocomplete="contact_no"
                                @change="form.validate('contact_no')"
                            />
                            <InputError class="" :message="form.errors.contact_no" />
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="address_line_1" value="Address Line 1"/>
                            <TextInput
                                id="address_line_1"
                                type="text"
                                v-model="form.address_line_1"
                                autocomplete="address_line_1"
                                @change="form.validate('address_line_1')"
                            />
                            <InputError class="" :message="form.errors.address_line_1" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="address_line_2" value="Address Line 2"/>
                            <TextInput
                                id="address_line_2"
                                type="text"
                                v-model="form.address_line_2"
                                autocomplete="address_line_2"
                            />
                            <InputError class="" :message="form.errors.address_line_2" />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="city" value="City"/>
                            <TextInput
                                id="city"
                                type="text"
                                v-model="form.city"
                                autocomplete="city"
                                @change="form.validate('city')"
                            />
                            <InputError class="" :message="form.errors.city" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="state" value="State"/>
                            <TextInput
                                id="state"
                                type="text"
                                v-model="form.state"
                                autocomplete="state"
                                @change="form.validate('state')"
                            />
                            <InputError class="" :message="form.errors.state" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="pincode" value="Pincode"/>
                            <TextInput
                                id="pincode"
                                type="text"
                                :numeric="true"
                                maxLength="6"
                                v-model="form.pincode"
                                autocomplete="pincode"
                                @change="form.validate('pincode')"
                            />
                            <InputError class="" :message="form.errors.pincode" />
                        </div>

                        <div class="sm:col-span-2">
                            <InputLabel for="logo" value="Company Logo"/>
                            <FileUpload
                                label="Company Logo"
                                inputId="logo"
                                inputName="logo"
                                :fileUrl="form.logo"
                                @file="handleLogo"
                            />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="signature" value="Company signature"/>
                            <FileUpload
                                label="Company signature"
                                inputId="signature"
                                inputName="signature"
                                :fileUrl="form.signature"
                                @file="handleSignature"
                            />
                        </div>
                         <div class="sm:col-span-6">
                            <InputLabel for="remarks" value="Remarks"/>
                            <TextArea
                                id="remarks"
                                type="text"
                                :rows="3"
                                v-model="form.remarks"
                                autocomplete="remarks"
                                @change="form.validate('remarks')"
                            />
                            <InputError class="" :message="form.errors.remarks" />
                        </div>
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('organization.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                    Cancel
                                </button>
                            </template>
                        </SvgLink>
                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
