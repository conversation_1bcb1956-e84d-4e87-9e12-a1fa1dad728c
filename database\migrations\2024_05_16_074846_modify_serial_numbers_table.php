<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->dropForeign('pordpor_id');
            $table->dropColumn('purchase_order_receives_id');
            $table->foreignId('purchase_order_receive_detail_id')->nullable()->after('id')->constrained( table:'purchase_order_receive_details', indexName: 'pords_id')->onDelete('cascade')->onUpdate('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->dropForeign(['purchase_order_receive_detail_id']);
            // Add back the old foreign key constraint
            $table->foreignId('purchase_order_receives_id')
                  ->constrained()
                  ->onDelete('cascade')
                  ->onUpdate('no action');
        });
    }
};
