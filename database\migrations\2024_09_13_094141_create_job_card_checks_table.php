<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_card_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_card_id')->constrained( table: 'job_cards', indexName: 'jc_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('job_card_checklist_id')->constrained( table: 'job_card_checklist', indexName: 'jcc_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_card_checks');
    }
};
