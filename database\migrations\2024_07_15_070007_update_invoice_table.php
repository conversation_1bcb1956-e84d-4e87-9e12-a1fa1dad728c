<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->integer('purchase_order_id')->nullable()->after('cr_dr_note');
            $table->integer('purchase_order_receive_id')->nullable()->after('purchase_order_id');
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->dropColumn('hsn_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
