<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProformaInvoiceDetails extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'proforma_invoice_details';

    protected static $logName = 'Prodorma-Invoice-Detail';

    public function getLogDescription(string $event): string
    {
        $proformaInvoice = $this->proformaInvoice;
        $PInumber = $proformaInvoice ? $proformaInvoice->order_number : 'Unknown Order Number';

        // Retrieve the product names
        $productNames = $this->product()->pluck('name')->implode(', ');

        return "ProformaInvoice detail has been {$event} for <strong>{$productNames}</strong> : {$PInumber} by";
    }

    protected static $logAttributes = [
        'pi_id',
        'product_id',
        'qty',
        'delivered_qty',
        'description',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'pi_id',
        'product_id',
        'qty',
        'delivered_qty',
        'description',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by'
    ];

    public function proformaInvoice()
    {
        return $this->belongsTo(ProformaInvoice::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // public static function boot()
    // {
    //     parent::boot();

    //     static::created(function ($model) {
    //         self::handleLogEntry($model, 'created');
    //     });

    //     static::updated(function ($model) {
    //         if ($model->isDirty()) {
    //             self::handleLogEntry($model, 'updated');
    //         }
    //     });

        // static::deleted(function ($model) {
        //     self::handleLogEntry($model, 'deleted');
        // });
    // }

    // protected static function handleLogEntry($model, $event)
    // {
    //     $logName = "Order $event for " . $model->order->order_number;
    //     self::addCustomLogEntry($model, $event, $logName);
    // }


}
