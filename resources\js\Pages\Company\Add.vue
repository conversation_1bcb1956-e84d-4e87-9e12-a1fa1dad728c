<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head  } from '@inertiajs/vue3';

const form = useForm('post', '/companies', {
    name:'',
    address:'',
    city:'',
    state:'',
    contact_no:'',
    email:'',
    drug_licence_no:'',
    gst_no:'',
    website:'',
    company_type:'Tax',
    gst_type:'',
});

defineProps({
    company_type: {
        type: Array,
    },
    gst_type:{
        type: Array,
    }
});

const setCustomerType = (id, name) => {
    form.company_type = name;
};

const setGSTtype = (id, name) => {
    form.gst_type = name;
    form.errors.gst_type = null;
};

const submit = () => form.submit({
 preserveScroll: true,
 onSuccess: () => form.reset(),
});

</script>

<template>
    <Head title="Company Add" />
    <AdminLayout>

        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Add New Company</h2>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                        <div class="sm:col-span-3">
                            <InputLabel for="name" value="Name" />
                            <TextInput
                                id="name"
                                type="text"
                                v-model="form.name"
                                @change="form.validate('name')"
                            />
                            <InputError  v-if="form.invalid('name')" class="" :message="form.errors.name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="website" value="Website" />
                            <TextInput
                                id="website"
                                type="text"
                                v-model="form.website"
                            />
                            <InputError  v-if="form.invalid('website')" class="" :message="form.errors.website" />
                        </div>
                        <!-- <div class="sm:col-span-2">
                            <InputLabel for="type" value="Company Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="company_type"
                                    v-model="form.company_type"
                                    @onchange="setCustomerType"
                                    :class="{ 'error rounded-md': form.errors.company_type }"
                                />
                            </div>
                            <InputError v-if="form.invalid('company_type')" class="" :message="form.errors.company_type" />
                        </div> -->
                        <div class="sm:col-span-2">
                            <InputLabel for="type" value="GST Type" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="gst_type"
                                    v-model="form.gst_type"
                                    @onchange="setGSTtype"
                                    :class="{ 'error rounded-md': form.errors.gst_type }"
                                />
                            </div>
                            <InputError v-if="form.invalid('gst_type')" class="" :message="form.errors.gst_type" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="gst_no" value="GST No" />
                            <TextInput
                                id="gst_no"
                                type="text"
                                maxLength="15"
                                v-model="form.gst_no"
                                @change="form.validate('gst_no')"
                            />
                            <InputError  v-if="form.invalid('gst_no')" class="" :message="form.errors.gst_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="drug_licence_no" value="Drug Licence No" />
                            <TextInput
                                id="drug_licence_no"
                                type="text"
                                v-model="form.drug_licence_no"
                            />
                            <InputError  v-if="form.invalid('drug_licence_no')" class="" :message="form.errors.drug_licence_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="email" value="Email" />
                            <TextInput
                                id="email"
                                type="email"
                                v-model="form.email"
                            />
                            <InputError v-if="form.invalid('email')" class="" :message="form.errors.email" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="contact_no" value="Contact No" />
                            <TextInput
                                id="contact_no"
                                type="text"
                                :numeric="true"
                                maxLength="10"
                                v-model="form.contact_no"
                            />
                            <InputError  v-if="form.invalid('contact_no')" class="" :message="form.errors.contact_no" />
                        </div>
                        <div class="sm:col-span-2"></div>
                        <div class="sm:col-span-2">
                            <InputLabel for="city" value="City" />
                            <TextInput
                                id="city"
                                type="text"
                                v-model="form.city"
                                @change="form.validate('city')"
                            />
                            <InputError  v-if="form.invalid('city')" class="" :message="form.errors.city" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="state" value="State" />
                            <TextInput
                                id="state"
                                type="text"
                                v-model="form.state"
                                @change="form.validate('state')"
                            />
                            <InputError  v-if="form.invalid('state')" class="" :message="form.errors.state" />
                        </div>
                        <div class="sm:col-span-4">
                            <InputLabel for="address" value="Address" />
                            <TextArea
                                id="address"
                                type="text"
                                :rows="4"
                                v-model="form.address"
                                @change="form.validate('address')"
                            />
                            <InputError  v-if="form.invalid('address')" class="" :message="form.errors.address" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('companies.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>



                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
    </AdminLayout>
</template>
