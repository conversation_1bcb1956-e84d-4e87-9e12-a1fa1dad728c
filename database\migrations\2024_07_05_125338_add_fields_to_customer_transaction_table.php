<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_transaction', function (Blueprint $table) {
            $table->integer('entity_id')->nullable()->after('customer_id');
            $table->string('entity_type')->nullable()->after('entity_id');
        });

        Schema::table('purchase_transaction', function (Blueprint $table) {
            $table->integer('entity_id')->nullable()->after('company_id');
            $table->string('entity_type')->nullable()->after('entity_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_transaction', function (Blueprint $table) {
            $table->dropColumn('entity_id');
            $table->dropColumn('entity_type');
        });

        Schema::table('purchase_transaction', function (Blueprint $table) {
            $table->dropColumn('entity_id');
            $table->dropColumn('entity_type');
        });
    }
};
