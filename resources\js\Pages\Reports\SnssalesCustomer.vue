<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'organization', 'search', 'customers', 'organizationId', 'customerId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('sns-cusomersales-report',{
    organization_id: props.organizationId,
    customer_id: props.customerId,
    from_date: props.from_date,
    to_date: props.to_date,
});

const activeLink = usePage().props.data.links.find(link => link.active === true);

// const form = useForm({
// });

const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const customerName = ref('ALL CUSTOMERS');
const from_date = ref('');
const to_date = ref('');
const searchValue = ref('');

watch([organizationId, customerId, from_date, to_date ], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
        from_date: from_date.value,
        to_date: to_date.value
    });
});

const columns = [
    { field: 'customer_name',           label: 'CUSTOMER NAME',       sortable: true, colSpan: 'col-span-8' },
    { field: 'details',                label: 'SALES DETAILS',       sortable: false, colSpan: 'col-span-4' },
];

const handleSearchChange = (value, organizationId, customerId, from_date, to_date) => {
    searchValue.value = value;
    form.get(route('sns-cusomersales-report',{search:value , organization_id: organizationId, customer_id: customerId, from_date:from_date , to_date:to_date}),  {
        preserveState: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

const setCustomer = (id, name) => {
    customerId.value = id;
    customerName.value = name;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, from_date.value, to_date.value);
};

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
};

const exportXls = () => {
    let organizationName = '';

    switch (organizationId.value) {
        case 1:
            organizationName = 'MC';
            break;
        case 2:
            organizationName = 'HC';
            break;
        case 3:
            organizationName = 'NOX';
            break;
        default:
            organizationName = 'All_Organizations';
            break;
    }

    const cleanedcustomerName = customerName.value.replace(/\s+/g, '_');
    const fileName = `SNS_Customer_Salse_Report_${organizationName}_${cleanedcustomerName}`;
    const params = {
        organization_id: organizationId.value, customer_id: customerId.value, from_date:from_date.value , to_date:to_date.value
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-sns-cusomersales-report?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName+'.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        // Handle error
        console.error('Error exporting data:', error);
    });
};

const truncateCompanyName = (name) => {
    return name && name.length > 27 ? name.substring(0, 27) + '...' : name;
};

const handleStartDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

const handleToDate = () => {
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};

const calculateTotalStock = (salesProducts) => {
    if (salesProducts && salesProducts.length > 0) {
        return salesProducts.reduce((total, product) => total + (product.qty), 0);
    } else {
        return '-';
    }
};

</script>

<template>
    <Head  title="Customer Sales Report"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="items-start">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">SNS Customer Sales Report</h1>
            </div>
            <div class="flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, customerId, from_date, to_date)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                            <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                    </div>
                </div>
                <div class="flex ml-6">
                    <CreateButton :href="route('reports')">
                            Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
             <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="inline-flex items-center space-x-4 justify-end w-full ">
                    <button @click="exportXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Customer Name" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="customers"
                          v-model="customerId"
                        @onchange="setCustomer"
                        />
                    </div>
                </div>
                 <div class="sm:col-span-3">
                    <InputLabel for="date" value="From Date" />
                    <input
                        v-model="from_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                            @change="handleStartDate"
                        :class="{ 'error rounded-md': form.errors.from_date }"
                    />
                 </div>
                 <div class="sm:col-span-3">
                    <InputLabel for="date" value="To Date" />
                    <input
                        v-model="to_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleToDate"
                        :class="{ 'error rounded-md': form.errors.to_date }"
                    />
                 </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2 grid grid-cols-12 gap-x-2">
                            <th v-for="(column, index) in columns" :key="index" scope="col" :class="['px-4 py-4 text-sm font-semibold text-gray-900 cursor-pointer', column.colSpan]" @click="sort(column.field, column.sortable)">
                                {{ column.label }}
                                <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-300 bg-white" v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12" v-for="(userData, index) in data.data" :key="userData.id">
                            <td class="sm:col-span-8 px-3 py-3 text-sm text-gray-900">{{userData.customer_name ?? '-'    }}</td>
                            <td class="sm:col-span-4 px-3 py-3 text-sm text-gray-500">
                                <div class="flex items-center justify-start gap-6">
                                    <button @click="toggleDetails(index)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <div v-if="showDetails[index] &&    userData.invoices.length > 0" class="divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4">
                                <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10 bg-gray-50">
                                    <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">PRODUCT CODE</th>
                                    <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">PRODUCT NAME</th>
                                    <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">INVOICE NO</th>
                                    <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">DATE</th>
                                    <th scope="col" class="py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">QTY</th>
                                </tr>

                                <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1">
                                    <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5" v-for="(invoicedetails, index) in userData.invoice_details" :key="index">
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ invoicedetails.invoice.invoice_no ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ formatDate(invoicedetails.invoice.date)  }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">{{ invoicedetails.invoice.customers.customer_name ?? '-' }}</td>
                                        <td class="py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6">{{ invoicedetails.qty ?? '-' }}</td>
                                    </tr>
                                </tbody>
                                <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1">
                                    <template v-for="(invoice, index) in userData.invoices" :key="index">
                                        <template v-if="invoice.invoice_detail.length">
                                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10 w-full">
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">
                                                    <div v-for="(detail, detailIndex) in invoice.invoice_detail" :key="detailIndex">
                                                        {{ detail.product.item_code ?? '-' }}
                                                    </div>
                                                </td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">
                                                    <div v-for="(detail, detailIndex) in invoice.invoice_detail" :key="detailIndex">
                                                        {{ detail.product.name ?? '-' }}
                                                    </div>
                                                </td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">
                                                    {{ invoice.invoice_no ?? '-' }}
                                                </td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">
                                                    {{ formatDate(invoice.date) ?? '-' }}
                                                </td>
                                                <td class="py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6">
                                                    <div v-for="(detail, detailIndex) in invoice.invoice_detail" :key="detailIndex">
                                                        {{ detail.qty ?? '-' }}
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                    </template>
                                </tbody>
                            </div>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="8" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
            </div>
        </div>
    </AdminLayout>

</template>
