<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    public function getLogDescription(string $event): string
    {
        return "<strong>{$this->customer_name}</strong> Customer has been {$event} by";
    }

    protected $table = 'customers';

    protected static $logName = 'Customer';

    protected static $logAttributes = [
        'customer_name',
        'customer_type',
        'gst_type',
        'person_name',
        'address',
        'city',
        'state',
        'contact_no',
        'email',
        'drug_licence_no',
        'gst_no',
        'type',
        'status',
        'is_organization',
        'organization_id',
        'party_id'
    ];

    protected $fillable = [
        'customer_name',
        'customer_type',
        'gst_type',
        'person_name',
        'address',
        'city',
        'state',
        'contact_no',
        'email',
        'drug_licence_no',
        'gst_no',
        'type',
        'status',
        'is_organization',
        'organization_id',
        'created_by',
        'updated_by',
        'party_id'
    ];

    public function invoices()
    {
        return $this->hasMany(Invoice::class,'customer_id','id');
    }

    public function challans()
    {
        return $this->hasMany(Challan::class,'customer_id','id');
    }

    public function quotations()
    {
        return $this->hasMany(Quotation::class,'customer_id','id');
    }

    public function credit(){
        return $this->belongsTo(CustomerCredit::class,'customer_id','id');
    }

    public function transactions(){
        return $this->hasMany(CustomerTransaction::class,'customer_id','id');
    }
}
