<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderDetails extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'order_details';

    protected static $logName = 'Order-Detail';

    public function getLogDescription(string $event): string
    {
        $oderNumber = $this->order->order_number;

        $productNames = $this->product()->pluck('name')->implode(', ');

        return "Order detail has been {$event} for <strong>{$productNames}</strong> : {$oderNumber} by";
    }

    protected static $logAttributes = [
        'order_id',
        'product_id',
        'hsn_code',
        'qty',
        'delivered_qty',
        'description',
        'return_qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'order_id',
        'product_id',
        'hsn_code',
        'qty',
        'delivered_qty',
        'description',
        'return_qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'created_by',
        'updated_by'
    ];

    public function order()
    {
        return $this->belongsTo(Orders::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function orderDeliver()
    {
        return $this->hasMany(OrderDeliver::class, 'order_details_id','id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Order $event for " . $model->order->order_number;
        self::addCustomLogEntry($model, $event, $logName);
    }

}
