import{r as b,j as H,l as O,o as d,c as u,a as l,u as a,w as F,F as D,Z as ne,b as s,t as v,f as _,d as le,k as I,P as J,g as B,n as y,v as ie,i as K,T as re,s as de,x as ue}from"./app-497d70e1.js";import{_ as me,a as ce}from"./AdminLayout-f002e683.js";import{_ as p}from"./InputLabel-5f63a3d9.js";import{P as _e}from"./PrimaryButton-8958b93e.js";import{_ as V}from"./TextInput-affa926c.js";import{_ as pe}from"./TextArea-a8869e21.js";import{_ as ve}from"./RadioButton-24e740cf.js";import{_ as $}from"./SearchableDropdown-1fe89ae6.js";import{u as ye}from"./index-25f94e24.js";/* empty css                                                                          */import{_ as fe}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ge}from"./Checkbox-c8631ef0.js";const j=g=>(de("data-v-7cbf1138"),g=g(),ue(),g),he={class:"h-screen animate-top"},xe={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},be={class:"sm:flex sm:items-center"},ke={class:"sm:flex-auto"},we={class:"text-2xl font-semibold leading-7 text-gray-900"},Ve={class:"flex items-center justify-between"},Ce={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Ne=["onSubmit"],Ae={class:"border-b border-gray-900/10 pb-12"},ze={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},$e={class:"sm:col-span-8"},Ue={class:"mt-2 flex space-x-6"},Fe={class:"flex items-center"},Se={class:"flex items-center"},Pe={class:"sm:col-span-3"},Te={class:"relative mt-2"},Oe={key:0,class:"sm:col-span-3"},De={class:"relative mt-2"},Ie={key:1,class:"sm:col-span-3"},Be={class:"relative mt-2"},je={class:"sm:col-span-2"},Ee={class:"relative mt-2"},Re={key:2,class:"sm:col-span-3"},Me={class:"relative mt-2"},Ye={key:3,class:"sm:col-span-3"},qe={key:4,class:"sm:col-span-2"},Le={key:5,class:"sm:col-span-1"},Ze={key:6,class:"sm:col-span-1"},Ge={key:7,class:"sm:col-span-1"},He={key:8,class:"sm:col-span-3"},Je={key:9,class:"sm:col-span-2"},Ke={class:"mt-4 flex justify-start"},Qe={class:"text-base font-semibold"},We={key:10,class:"sm:col-span-2"},Xe={key:11,class:"sm:col-span-2"},et={key:12,class:"sm:col-span-2"},tt={class:"relative mt-2"},ot={key:13,class:"sm:col-span-3"},st={class:"sm:col-span-6"},at={class:"overflow-x-auto divide-y divide-gray-300 w-full"},nt=j(()=>s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:""}),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),lt={style:{"overflow-y":"auto","max-height":"318px"}},it={class:"divide-y divide-gray-300 bg-white"},rt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},dt={class:"text-sm text-gray-900 leading-6 py-1.5"},ut={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},mt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ct={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},_t={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},pt={key:0,class:"text-red-500 text-xs absolute"},vt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},yt={class:"sm:col-span-2"},ft={class:"mt-4 flex justify-start"},gt={class:"text-base font-semibold"},ht={key:0,class:"text-red-500 text-xs absolute"},xt={key:14,class:"sm:col-span-6"},bt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},kt=j(()=>s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),wt={class:"divide-y divide-gray-300 bg-white"},Vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Nt={class:"flex flex-col"},At={class:"text-sm text-gray-900"},zt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},$t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ut={class:"flex mt-6 items-center justify-between"},Ft={class:"ml-auto flex items-center justify-end gap-x-6"},St=j(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Pt={key:0,class:"text-sm text-gray-600"},Tt={__name:"Add",props:["paymentType","bankinfo","organization","customers","companies","invoices","customerCredit","companyCredit"],setup(g){const h=g;b([]);const e=ye("post","/receipt",{entity_type:"customer",organization_id:"",customer_id:"",company_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),A=b(""),Q=()=>{e.settled_amount=S.value,e.advance_amount=R.value,e.total_unused_amount=k.value,e.is_credit=m.value,e.invoice=x.value,e.credit_data=f.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},W=(n,o)=>{A.value=o,e.payment_type=n,e.errors.payment_type=null,o==="Cash"?e.note="Cash":e.note==="Cash"&&(e.note="")},C=b([]),f=b([]),k=b(""),E=b([]),X=(n,o)=>{const t=h.bankinfo.filter(r=>r.organization_id===n);E.value=t;let c=[];e.entity_type==="customer"?c=h.invoices.filter(r=>r.organization_id===n&&r.entity_type==="customer"&&(!e.customer_id||r.customer_id===e.customer_id)):c=h.invoices.filter(r=>r.organization_id===n&&r.entity_type==="company"&&(!e.company_id||r.company_id===e.company_id)),C.value=c;let i=[];e.entity_type==="customer"?i=h.customerCredit.filter(r=>r.organization_id===n&&(!e.customer_id||r.customer_id===e.customer_id)):i=h.companyCredit.filter(r=>r.organization_id===n&&(!e.company_id||r.company_id===e.company_id)),f.value=i,k.value=f.value.reduce((r,w)=>r+w.unused_amount,0),e.organization_id=n,e.errors.organization_id=null},ee=(n,o)=>{const t=h.invoices.filter(i=>i.customer_id===n&&i.organization_id===e.organization_id&&i.entity_type==="customer");C.value=t;const c=h.customerCredit.filter(i=>i.customer_id===n&&i.organization_id===e.organization_id);f.value=c,k.value=f.value.reduce((i,r)=>i+r.unused_amount,0),e.customer_id=n,e.errors.customer_id=null},te=(n,o)=>{const t=h.invoices.filter(i=>i.company_id===n&&i.organization_id===e.organization_id&&i.entity_type==="company");C.value=t;const c=h.companyCredit.filter(i=>i.company_id===n&&i.organization_id===e.organization_id);f.value=c,k.value=f.value.reduce((i,r)=>i+r.unused_amount,0),e.company_id=n,e.errors.company_id=null},oe=(n,o)=>{e.org_bank_id=n,e.errors.org_bank_id=null},S=H(()=>x.value.reduce((n,o)=>n+(o.check&&o.amount?parseFloat(o.amount):0),0)),R=H(()=>{const n=parseFloat(e.amount||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0),o=parseFloat(e.round_off||0),t=S.value;return n-t-o}),P=()=>{},z=n=>{let o=n.toFixed(2).toString(),[t,c]=o.split("."),i=t.substring(t.length-3),r=t.substring(0,t.length-3);return r!==""&&(i=","+i),`${r.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${c}`},M=n=>{const o=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},m=b("No"),se=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],ae=n=>{const o=m.value==="Yes"?parseFloat(k.value||0):parseFloat(e.amount||0)+parseFloat(e.round_off||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0);if(!x.value[n].check){x.value[n].amount=0;return}let t=o;x.value.forEach((r,w)=>{r.check&&w!==n&&(t-=parseFloat(r.amount||0))});const c=parseFloat(x.value[n].pending_amount||0),i=Math.min(c,t);x.value[n].amount=i.toFixed(2)},x=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),U=()=>{x.value=C.value.map(n=>({id:n.id,date:n.date,invoice_no:n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.pending_amount||0).toFixed(2),check:!1,amount:"0.00"}))};O(C,()=>{U()}),O(m,()=>{U()}),O(()=>e.amount,()=>{m.value==="No"&&U()});const N=n=>{e.errors[n]=null,e.errors.settled_amount=null},Y=()=>{e.customer_id="",e.company_id="",C.value=[],f.value=[],k.value=0,U()};return(n,o)=>(d(),u(D,null,[l(a(ne),{title:"Receipt"}),l(me,null,{default:F(()=>[s("div",he,[s("div",xe,[s("div",be,[s("div",ke,[s("h1",we,v(a(e).entity_type==="customer"?"Receive Payment":"Pay to Company"),1)]),s("div",Ve,[f.value.length>0?(d(),u("div",Ce," Credits Available: ₹"+v(z(k.value)),1)):_("",!0)])]),s("form",{onSubmit:le(Q,["prevent"]),class:""},[s("div",Ae,[s("div",ze,[s("div",$e,[l(p,{value:"Transaction Type"}),s("div",Ue,[s("label",Fe,[I(s("input",{type:"radio","onUpdate:modelValue":o[0]||(o[0]=t=>a(e).entity_type=t),value:"customer",class:"mr-2",onChange:Y},null,544),[[J,a(e).entity_type]]),B(" Customer Payment (Receive) ")]),s("label",Se,[I(s("input",{type:"radio","onUpdate:modelValue":o[1]||(o[1]=t=>a(e).entity_type=t),value:"company",class:"mr-2",onChange:Y},null,544),[[J,a(e).entity_type]]),B(" Company Payment (Pay) ")])])]),s("div",Pe,[l(p,{for:"payment_type",value:"Organization"}),s("div",Te,[l($,{options:g.organization,modelValue:a(e).organization_id,"onUpdate:modelValue":o[2]||(o[2]=t=>a(e).organization_id=t),onOnchange:X,class:y({"error rounded-md":a(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),a(e).entity_type==="customer"?(d(),u("div",Oe,[l(p,{for:"customer",value:"Customer"}),s("div",De,[l($,{options:g.customers,modelValue:a(e).customer_id,"onUpdate:modelValue":o[3]||(o[3]=t=>a(e).customer_id=t),onOnchange:ee,class:y({"error rounded-md":a(e).errors.customer_id})},null,8,["options","modelValue","class"])])])):_("",!0),a(e).entity_type==="company"?(d(),u("div",Ie,[l(p,{for:"company",value:"Company"}),s("div",Be,[l($,{options:g.companies,modelValue:a(e).company_id,"onUpdate:modelValue":o[4]||(o[4]=t=>a(e).company_id=t),onOnchange:te,class:y({"error rounded-md":a(e).errors.company_id})},null,8,["options","modelValue","class"])])])):_("",!0),s("div",je,[l(p,{for:"role_id",value:"Payment Through Credit ?"}),s("div",Ee,[l(ve,{modelValue:m.value,"onUpdate:modelValue":o[5]||(o[5]=t=>m.value=t),options:se},null,8,["modelValue"])])]),m.value=="No"?(d(),u("div",Re,[l(p,{for:"payment_type",value:"Payment Type"}),s("div",Me,[l($,{options:g.paymentType,modelValue:a(e).payment_type,"onUpdate:modelValue":o[6]||(o[6]=t=>a(e).payment_type=t),onOnchange:W,class:y({"error rounded-md":a(e).errors.payment_type})},null,8,["options","modelValue","class"])])])):_("",!0),m.value=="No"?(d(),u("div",Ye,[l(p,{for:"date",value:"Payment Date"}),I(s("input",{"onUpdate:modelValue":o[7]||(o[7]=t=>a(e).date=t),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":a(e).errors.date}]),type:"date",onChange:o[8]||(o[8]=t=>N("date"))},null,34),[[ie,a(e).date]])])):_("",!0),m.value=="No"?(d(),u("div",qe)):_("",!0),m.value=="No"?(d(),u("div",Le,[l(p,{for:"tds_amount",value:"TDS Amount"}),l(V,{type:"text",onChange:o[9]||(o[9]=t=>N("tds_amount")),onInput:o[10]||(o[10]=t=>P()),modelValue:a(e).tds_amount,"onUpdate:modelValue":o[11]||(o[11]=t=>a(e).tds_amount=t),class:y({"error rounded-md":a(e).errors.tds_amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(d(),u("div",Ze,[l(p,{for:"discount_amount",value:"Discount Amount"}),l(V,{type:"text",onChange:o[12]||(o[12]=t=>N("discount_amount")),onInput:o[13]||(o[13]=t=>P()),modelValue:a(e).discount_amount,"onUpdate:modelValue":o[14]||(o[14]=t=>a(e).discount_amount=t),class:y({"error rounded-md":a(e).errors.discount_amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(d(),u("div",Ge,[l(p,{for:"round_off",value:"Round Off"}),l(V,{type:"text",onChange:o[15]||(o[15]=t=>N("round_off")),modelValue:a(e).round_off,"onUpdate:modelValue":o[16]||(o[16]=t=>a(e).round_off=t),class:y({"error rounded-md":a(e).errors.round_off})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(d(),u("div",He,[l(p,{for:"amount",value:"Amount"}),l(V,{id:"amount",type:"text",onChange:o[17]||(o[17]=t=>N("amount")),onInput:o[18]||(o[18]=t=>P()),modelValue:a(e).amount,"onUpdate:modelValue":o[19]||(o[19]=t=>a(e).amount=t),class:y({"error rounded-md":a(e).errors.amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(d(),u("div",Je,[l(p,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Ke,[s("p",Qe,v(z(R.value)),1)])])):_("",!0),A.value=="Cheque"&&m.value=="No"?(d(),u("div",We,[l(p,{for:"check_number",value:"Cheque Number"}),l(V,{id:"check_number",type:"text",modelValue:a(e).check_number,"onUpdate:modelValue":o[20]||(o[20]=t=>a(e).check_number=t),class:y({"error rounded-md":a(e).errors["data.check_number"]})},null,8,["modelValue","class"])])):_("",!0),A.value=="Cheque"&&m.value=="No"?(d(),u("div",Xe,[l(p,{for:"bank_name",value:"Bank Name"}),l(V,{id:"bank_name",type:"text",modelValue:a(e).bank_name,"onUpdate:modelValue":o[21]||(o[21]=t=>a(e).bank_name=t),class:y({"error rounded-md":a(e).errors["data.bank_name"]})},null,8,["modelValue","class"])])):_("",!0),A.value!="Cash"&&m.value=="No"?(d(),u("div",et,[l(p,{for:"org_bank_id",value:"Our Bank"}),s("div",tt,[l($,{options:E.value,modelValue:a(e).org_bank_id,"onUpdate:modelValue":o[22]||(o[22]=t=>a(e).org_bank_id=t),onOnchange:oe,class:y({"error rounded-md":a(e).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):_("",!0),A.value!="Cash"&&m.value=="No"?(d(),u("div",ot)):_("",!0),s("div",st,[s("table",at,[nt,s("div",lt,[s("tbody",it,[(d(!0),u(D,null,K(x.value,(t,c)=>(d(),u("tr",{key:c},[s("td",rt,[s("div",dt,[l(ge,{name:"check",checked:t.check,"onUpdate:checked":i=>t.check=i,onChange:i=>ae(c)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",ut,v(t.invoice_no),1),s("td",mt,v(t.total_amount),1),s("td",ct,v(t.pending_amount),1),s("td",_t,[l(V,{id:"amount",type:"text",modelValue:t.amount,"onUpdate:modelValue":i=>t.amount=i,onChange:i=>N("invoice."+c+".amount"),class:y({error:a(e).errors[`invoice.${c}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),a(e).errors[`invoice.${c}.amount`]?(d(),u("p",pt,v(a(e).errors[`invoice.${c}.amount`]),1)):_("",!0)]),s("td",vt,v(M(t.date)),1)]))),128))])])])]),s("div",yt,[l(p,{for:"note",value:"Total Settled Amount"}),s("div",ft,[s("p",gt,v(z(S.value)),1)]),a(e).errors.settled_amount?(d(),u("p",ht,v(a(e).errors.settled_amount),1)):_("",!0)]),m.value=="No"?(d(),u("div",xt,[l(p,{for:"note",value:"Note"}),l(pe,{id:"note",type:"text",rows:2,modelValue:a(e).note,"onUpdate:modelValue":o[23]||(o[23]=t=>a(e).note=t)},null,8,["modelValue"])])):_("",!0)]),f.value.length>0&&m.value=="Yes"?(d(),u("table",bt,[kt,s("tbody",wt,[(d(!0),u(D,null,K(f.value,(t,c)=>{var i,r,w,T,q,L,Z,G;return d(),u("tr",{key:c},[s("td",Vt,v(M(t.date)),1),s("td",Ct,[s("div",Nt,[s("div",At,v((r=(i=t.paymentreceive)==null?void 0:i.bank_info)!=null&&r.bank_name?(T=(w=t.paymentreceive)==null?void 0:w.bank_info)==null?void 0:T.bank_name:"Cash")+" - "+v((L=(q=t.paymentreceive)==null?void 0:q.bank_info)!=null&&L.account_number?(G=(Z=t.paymentreceive)==null?void 0:Z.bank_info)==null?void 0:G.account_number:""),1)])]),s("td",zt,v(z(t.amount)),1),s("td",$t,v(z(t.unused_amount)),1)])}),128))])])):_("",!0)]),s("div",Ut,[s("div",Ft,[l(ce,{href:n.route("receipt.index")},{svg:F(()=>[St]),_:1},8,["href"]),l(_e,{disabled:a(e).processing},{default:F(()=>[B("Save")]),_:1},8,["disabled"]),l(re,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:F(()=>[a(e).recentlySuccessful?(d(),u("p",Pt,"Saved.")):_("",!0)]),_:1})])])],40,Ne)])])]),_:1})],64))}},Gt=fe(Tt,[["__scopeId","data-v-7cbf1138"]]);export{Gt as default};
