import{o as v,c as f,a,u as o,w as r,F as y,Z as x,b as t,d as b,n as u,k as h,v as V,g as z}from"./app-2ecbacfc.js";import{_ as w,a as C}from"./AdminLayout-42d5bb92.js";import{_ as n}from"./InputLabel-f62a278f.js";import{P as U}from"./PrimaryButton-0d76f021.js";import{_ as d}from"./TextInput-73b24943.js";import{_ as c}from"./SearchableDropdown-6058bf5f.js";import{u as S}from"./index-35fd125b.js";import"./_plugin-vue_export-helper-c27b6911.js";const $={class:"animate-top h-screen"},N={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},T=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Tag",-1),k=["onSubmit"],B={class:"border-b border-gray-900/10 pb-12"},O={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},F={class:"sm:col-span-4"},j={class:"relative mt-2"},A={class:"sm:col-span-6"},D={class:"relative mt-2"},E={class:"sm:col-span-2"},I={class:"flex space-x-2 items-center"},M={class:"sm:col-span-4"},P={class:"sm:col-span-4"},Z={class:"sm:col-span-4"},q={class:"flex mt-6 items-center justify-between"},G={class:"ml-auto flex items-center justify-end gap-x-6"},H=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),ee={__name:"Add",props:["organization","customers"],setup(m){const e=S("post","/email-tag",{organization_id:"",customer_id:"",invoice_type:"",sales_user_id:22,category:"Sales",entity_type:"invoice",invoice_no:"",cgst:0,sgst:0,igst:0,date:"",status:"Unpaid",total_gst:0,sub_total:"",total_amount:"",discount_before_tax:0,total_discount:0}),_=(l,s)=>{e.organization_id=l,e.errors.organization_id=null},p=(l,s)=>{e.customer_id=l,e.errors.customer_id=null},g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});return(l,s)=>(v(),f(y,null,[a(o(x),{title:"Email Tags"}),a(w,null,{default:r(()=>[t("div",$,[t("div",N,[T,t("form",{onSubmit:b(g,["prevent"]),class:""},[t("div",B,[t("div",O,[t("div",F,[a(n,{for:"company_name",value:"Organization"}),t("div",j,[a(c,{options:m.organization,modelValue:o(e).organization_id,"onUpdate:modelValue":s[0]||(s[0]=i=>o(e).organization_id=i),onOnchange:_,class:u({"error rounded-md":o(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",A,[a(n,{for:"customer_id",value:"Customer Name"}),t("div",D,[a(c,{options:m.customers,modelValue:o(e).customer_id,"onUpdate:modelValue":s[1]||(s[1]=i=>o(e).customer_id=i),onOnchange:p,class:u({"error rounded-md":o(e).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",E,[a(n,{for:"customer_id",value:"Date"}),t("div",I,[h(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":s[2]||(s[2]=i=>o(e).date=i),onChange:s[3]||(s[3]=i=>o(e).validate("date"))},null,544),[[V,o(e).date]])])]),t("div",M,[a(n,{for:"amount",value:"Amount"}),a(d,{id:"total_amount",type:"text",modelValue:o(e).total_amount,"onUpdate:modelValue":s[4]||(s[4]=i=>o(e).total_amount=i)},null,8,["modelValue"])]),t("div",P,[a(n,{for:"customer_id",value:"Invoice No"}),a(d,{id:"amount",type:"text",modelValue:o(e).invoice_no,"onUpdate:modelValue":s[5]||(s[5]=i=>o(e).invoice_no=i)},null,8,["modelValue"])]),t("div",Z,[a(n,{for:"customer_id",value:"Invoice Type"}),a(d,{id:"amount",type:"text",modelValue:o(e).invoice_type,"onUpdate:modelValue":s[6]||(s[6]=i=>o(e).invoice_type=i)},null,8,["modelValue"])])])]),t("div",q,[t("div",G,[a(C,{href:l.route("email-tag.index")},{svg:r(()=>[H]),_:1},8,["href"]),a(U,{disabled:o(e).processing},{default:r(()=>[z("Save")]),_:1},8,["disabled"])])])],40,k)])])]),_:1})],64))}};export{ee as default};
