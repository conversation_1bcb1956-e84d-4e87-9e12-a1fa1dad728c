<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('serial_numbers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained( table:'products', indexName: 'snp_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('purchase_order_receives_id')->constrained( table:'purchase_order_receives', indexName: 'pordpor_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('challan_id')->nullable();
            $table->integer('invoice_id')->nullable();
            $table->string('batch');
            $table->string('serial_no');
            $table->string('lot_no');
            $table->date('expiry_date');
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_receives_details');
    }
};
