import{r as y,o as r,c as _,a,u as m,w as i,F as z,Z as te,b as e,k as O,v as j,N as se,e as h,g as v,f as u,i as I,n as f,t as g,s as oe,x as ae}from"./app-2ecbacfc.js";import{s as ne}from"./sortAndSearch-94154e09.js";import{_ as le,b as re,a as k}from"./AdminLayout-42d5bb92.js";import{_ as ie}from"./CreateButton-1fa2a774.js";import{_ as E}from"./SecondaryButton-be49842d.js";import{D as de}from"./DangerButton-3e1103de.js";import{P as ce}from"./PrimaryButton-0d76f021.js";import{_ as B}from"./TextInput-73b24943.js";import{_ as me}from"./TextArea-00d3f05d.js";import{_ as S}from"./SearchableDropdown-6058bf5f.js";import{M as P}from"./Modal-54f7c77a.js";import{_ as ue}from"./SwitchButton-6025594c.js";import{_ as _e}from"./Pagination-56593f88.js";import{_ as pe}from"./ArrowIcon-0ab7616b.js";import{_ as p}from"./InputLabel-f62a278f.js";import{_ as he}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=l=>(oe("data-v-7bb42a41"),l=l(),ae(),l),fe={class:"animate-top"},ge={class:"flex justify-between items-center"},ye=d(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Customers")],-1)),ve={class:"flex justify-end"},xe={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},be={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},we=d(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),ke={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Ce={class:"flex justify-end"},Ve={class:"mt-8 overflow-x-auto sm:rounded-lg"},Me={class:"shadow sm:rounded-lg"},ze={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Be={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Se={class:"border-b-2"},$e=["onClick"],Te={key:0},Ae={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ue={class:"px-4 py-2.5 min-w-36"},Le={class:"px-4 py-2.5 min-w-36"},Ne={class:"px-4 py-2.5 min-w-32"},Oe={class:"px-4 py-2.5 min-w-32"},je={class:"px-4 py-2.5 min-w-36"},Ie={class:"items-center px-4 py-2.5"},Ee={class:"items-center px-4 py-2.5"},Pe={class:"flex items-center justify-start gap-4"},Re=d(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),He=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),qe=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),We=["onClick"],Fe=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ke=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Ye=[Fe,Ke],Ze=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 6l4 4M4 6l4-4M20 18H4m16 0l-4-4m4 4l-4 4"})],-1)),Ge=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Transaction ",-1)),Je=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Qe=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Credit ",-1)),Xe=["onClick"],De=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),et=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Advance Payment ",-1)),tt=[De,et],st=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2zm2 5h10M7 11h10M7 15h10M7 19h10"})],-1)),ot=d(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Service Reports ",-1)),at={key:1},nt=d(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),lt=[nt],rt={class:"p-6"},it=d(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),dt={class:"mt-6 flex justify-end"},ct={class:"p-6"},mt=d(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Advance Payment",-1)),ut={class:"border-b border-gray-900/10 pb-12"},_t={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},pt={class:"sm:col-span-3"},ht={class:"relative mt-2"},ft={class:"sm:col-span-3"},gt={class:"relative mt-2"},yt={class:"sm:col-span-3"},vt={key:0,class:"sm:col-span-3"},xt={key:1,class:"sm:col-span-3"},bt={class:"sm:col-span-3"},wt={key:2,class:"sm:col-span-3"},kt={class:"relative mt-2"},Ct={class:"sm:col-span-6"},Vt={class:"mt-6 px-4 flex justify-end"},Mt={class:"w-36"},zt={__name:"List",props:["data","bankinfo","organization","permissions","paymentType"],setup(l){const R=l,{form:c,search:C,sort:H,fetchData:$,sortKey:q,sortDirection:W}=ne("customers.index"),V=y(!1),T=y(null),F=n=>{T.value=n,V.value=!0},M=()=>{V.value=!1},K=()=>{c.delete(route("customers.destroy",{id:T.value}),{onSuccess:()=>M()})},Y=(n,o)=>{c.post(route("customers.activation",{id:o,status:n}),{})},A=y([]),x=y(!1),Z=y("custom2"),b=y(""),G=[{field:"customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"contact_no",label:"PHONE",sortable:!1},{field:"email",label:"EMAIL",sortable:!0},{field:"type",label:"TYPE",sortable:!0},{field:"city",label:"LOCATION",sortable:!0},{field:"balance",label:"BALANCE",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],s={organization_id:"",customer_id:"",org_bank_id:"",invoice_id:"",invoice_no:"",payment_type:"",amount:"",check_number:"",bank_name:"",date:"",note:""},J=(n,o)=>{s.organization_id=n;const t=R.bankinfo.filter(w=>w.organization_id===n);A.value=t,c.errors["data.organization_id"]=null},Q=(n,o)=>{s.payment_type=n,b.value=o,c.errors["data.payment_type"]=null},X=(n,o)=>{s.org_bank_id=n,c.errors["data.org_bank_id"]=null},D=n=>{s.organization_id="",s.customer_id="",s.org_bank_id="",s.invoice_id="",s.payment_type="",s.amount="",s.check_number="",s.bank_name="",s.date="",s.note="",x.value=!0,s.customer_id=n},U=()=>{x.value=!1},ee=()=>{c.post(route("customers.advancepayment",{data:s}),{onSuccess:()=>{c.reset(),x.value=!1},onError:n=>{}})},L=n=>{c.errors[n]=null};return(n,o)=>(r(),_(z,null,[a(m(te),{title:"Customers"}),a(le,null,{default:i(()=>[e("div",fe,[e("div",ge,[ye,e("div",ve,[e("div",xe,[e("div",be,[we,O(e("input",{id:"search-field","onUpdate:modelValue":o[0]||(o[0]=t=>se(C)?C.value=t:null),onInput:o[1]||(o[1]=(...t)=>m($)&&m($)(...t)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[j,m(C)]])])]),l.permissions.canCreateCustomer?(r(),_("div",ke,[e("div",Ce,[l.permissions.canCreateCustomer?(r(),h(ie,{key:0,href:n.route("customers.create")},{default:i(()=>[v(" Add Customer ")]),_:1},8,["href"])):u("",!0)])])):u("",!0)])]),e("div",Ve,[e("div",Me,[e("table",ze,[e("thead",Be,[e("tr",Se,[(r(),_(z,null,I(G,(t,w)=>e("th",{key:w,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:N=>m(H)(t.field,t.sortable)},[v(g(t.label)+" ",1),t.sortable?(r(),h(pe,{key:0,isSorted:m(q)===t.field,direction:m(W)},null,8,["isSorted","direction"])):u("",!0)],8,$e)),64))])]),l.data.data&&l.data.data.length>0?(r(),_("tbody",Te,[(r(!0),_(z,null,I(l.data.data,(t,w)=>(r(),_("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ae,g(t.customer_name??"-"),1),e("td",Ue,g(t.contact_no??"-"),1),e("td",Le,g(t.email??"-"),1),e("td",Ne,g(t.type??"-"),1),e("td",Oe,g(t.city??"-"),1),e("td",je,g(t.balance??"-"),1),e("td",Ie,[a(ue,{switchValue:t.status,userId:t.id,onUpdateSwitchValue:Y},null,8,["switchValue","userId"])]),e("td",Ee,[e("div",Pe,[a(re,{align:"right",width:"48"},{trigger:i(()=>[Re]),content:i(()=>[l.permissions.canEditCustomer?(r(),h(k,{key:0,href:n.route("customers.edit",{id:t.id})},{svg:i(()=>[He]),text:i(()=>[qe]),_:2},1032,["href"])):u("",!0),l.permissions.canDeleteCustomer?(r(),_("button",{key:1,type:"button",onClick:N=>F(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ye,8,We)):u("",!0),l.permissions.canTransactionCustomer?(r(),h(k,{key:2,href:n.route("customers.transaction",{id:t.id})},{svg:i(()=>[Ze]),text:i(()=>[Ge]),_:2},1032,["href"])):u("",!0),l.permissions.canTransactionCustomer?(r(),h(k,{key:3,href:n.route("customers.credit",{id:t.id})},{svg:i(()=>[Je]),text:i(()=>[Qe]),_:2},1032,["href"])):u("",!0),e("button",{type:"button",onClick:N=>D(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},tt,8,Xe),l.permissions.canTransactionCustomer?(r(),h(k,{key:4,href:n.route("service-reports.show",{id:t.id})},{svg:i(()=>[st]),text:i(()=>[ot]),_:2},1032,["href"])):u("",!0)]),_:2},1024)])])]))),128))])):(r(),_("tbody",at,lt))])])]),l.data.data&&l.data.data.length>0?(r(),h(_e,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):u("",!0)]),a(P,{show:V.value,onClose:M},{default:i(()=>[e("div",rt,[it,e("div",dt,[a(E,{onClick:M},{default:i(()=>[v(" Cancel ")]),_:1}),a(de,{class:"ml-3",onClick:K},{default:i(()=>[v(" Delete ")]),_:1})])])]),_:1},8,["show"]),a(P,{show:x.value,onClose:U,maxWidth:Z.value},{default:i(()=>[e("div",ct,[mt,e("div",ut,[e("div",_t,[e("div",pt,[a(p,{for:"role_id",value:"Organization"}),e("div",ht,[a(S,{options:l.organization,modelValue:s.organization_id,"onUpdate:modelValue":o[2]||(o[2]=t=>s.organization_id=t),onOnchange:J,class:f({"error rounded-md":m(c).errors["data.organization_id"]})},null,8,["options","modelValue","class"])])]),e("div",ft,[a(p,{for:"role_id",value:"Payment Type"}),e("div",gt,[a(S,{options:l.paymentType,modelValue:s.payment_type,"onUpdate:modelValue":o[3]||(o[3]=t=>s.payment_type=t),onOnchange:Q,class:f({"error rounded-md":m(c).errors["data.payment_type"]})},null,8,["options","modelValue","class"])])]),e("div",yt,[a(p,{for:"amount",value:"Amount"}),a(B,{id:"amount",type:"text",onChange:o[4]||(o[4]=t=>L("data.amount")),modelValue:s.amount,"onUpdate:modelValue":o[5]||(o[5]=t=>s.amount=t),class:f({"error rounded-md":m(c).errors["data.amount"]})},null,8,["modelValue","class"])]),b.value=="Cheque"?(r(),_("div",vt,[a(p,{for:"check_number",value:"Cheque Number"}),a(B,{id:"check_number",type:"text",modelValue:s.check_number,"onUpdate:modelValue":o[6]||(o[6]=t=>s.check_number=t),class:f({"error rounded-md":m(c).errors["data.check_number"]})},null,8,["modelValue","class"])])):u("",!0),b.value=="Cheque"?(r(),_("div",xt,[a(p,{for:"bank_name",value:"Our Bank"}),a(B,{id:"bank_name",type:"text",modelValue:s.bank_name,"onUpdate:modelValue":o[7]||(o[7]=t=>s.bank_name=t),class:f({"error rounded-md":m(c).errors["data.bank_name"]})},null,8,["modelValue","class"])])):u("",!0),e("div",bt,[a(p,{for:"date",value:"Payment Date"}),O(e("input",{"onUpdate:modelValue":o[8]||(o[8]=t=>s.date=t),class:f(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":m(c).errors["data.date"]}]),type:"date",onChange:o[9]||(o[9]=t=>L("data.date"))},null,34),[[j,s.date]])]),b.value!="Cash"?(r(),_("div",wt,[a(p,{for:"org_bank_id",value:"Our Bank"}),e("div",kt,[a(S,{options:A.value,modelValue:s.org_bank_id,"onUpdate:modelValue":o[10]||(o[10]=t=>s.org_bank_id=t),onOnchange:X,class:f({"error rounded-md":m(c).errors["data.org_bank_id"]})},null,8,["options","modelValue","class"])])])):u("",!0),e("div",Ct,[a(p,{for:"note",value:"Note"}),a(me,{id:"note",type:"text",rows:2,modelValue:s.note,"onUpdate:modelValue":o[11]||(o[11]=t=>s.note=t)},null,8,["modelValue"])])])]),e("div",Vt,[a(E,{onClick:U},{default:i(()=>[v(" Cancel ")]),_:1}),e("div",Mt,[a(ce,{class:"ml-3 w-20",onClick:ee},{default:i(()=>[v(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Ft=he(zt,[["__scopeId","data-v-7bb42a41"]]);export{Ft as default};
