<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('product_activity_log', function (Blueprint $table) {
            $table->id();
            $table->string('model');
            $table->unsignedBigInteger('model_id');
            $table->string('log_name');
            $table->unsignedBigInteger('product_id');
            $table->string('action');
            $table->text('description')->nullable();
            $table->integer('created_by');
            $table->timestamp('created_at')->useCurrent();

            // Define foreign key constraints if needed
            //$table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_activity_log');
    }
};
