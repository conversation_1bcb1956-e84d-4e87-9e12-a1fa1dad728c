<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_paid', function (Blueprint $table) {
            DB::statement('ALTER TABLE payment_paid CHANGE po_number invoice_no VARCHAR(255)');
            $table->dropForeign('poin_id');
            $table->dropColumn('purchase_order_id');
            $table->foreignId('purchase_order_receive_id')->nullable()->after('id')->constrained( table:'purchase_order_receives', indexName: 'porppd_id')->onDelete('cascade')->onUpdate('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
