<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->enum('customer_type', ['Retail', 'Tax'])->after('customer_name')->default('Tax');
            $table->enum('gst_type', ['IGST', 'CGST/SGST'])->after('customer_name')->default('CGST/SGST');
        });

        Schema::table('invoice', function (Blueprint $table) {
            $table->double('cgst', 16, 2)->defalt(0)->after('status');
            $table->double('sgst', 16, 2)->defalt(0)->after('status');
            $table->double('igst', 16, 2)->defalt(0)->after('status');
            $table->double('total_discount', 16, 2)->defalt(0)->after('total_gst');
        });

        Schema::table('invoice_details', function (Blueprint $table) {
            $table->double('discount', 16, 2)->defalt(0)->after('total_gst_amount');
            $table->double('discount_amount', 16, 2)->defalt(0)->after('discount');
        });

        Schema::table('serial_numbers', function (Blueprint $table) {
            $table->string('batch')->nullable()->change();
            $table->string('serial_no')->nullable()->change();
            $table->string('lot_no')->nullable()->change();
            $table->date('expiry_date')->nullable()->change();
            $table->string('unique_id')->after('purchase_order_receives_id');
            $table->double('mrp', 16, 2)->nullable()->after('expiry_date');
            $table->double('purchase_price', 16, 2)->nullable()->after('expiry_date');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('customers', function (Blueprint $table) {
        //     //
        // });
    }
};
