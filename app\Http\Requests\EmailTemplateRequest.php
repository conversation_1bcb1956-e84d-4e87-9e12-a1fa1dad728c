<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\EmailTemplateDTO;
use Support\Contracts\HasDTO;

class EmailTemplateRequest extends FormRequest implements HasDTO
{
    /** 
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Change this if you want authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email_subject' => 'required|string|max:255',
            'template_name' => 'required|string|max:255',
            'content' => 'required|string',
        ];
    }

    public function DTO()
    {
        return EmailTemplateDTO::LazyFromArray($this->input());
    }

}
