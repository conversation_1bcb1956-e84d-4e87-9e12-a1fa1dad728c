<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class invoicePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            // 'form.payment_type' => 'required|string|max:255',
            // 'form.amount'       => 'required|numeric|gt:0',
            // 'form.date'         => 'required|date',
        ];

        // if($this->input('form.payment_type') != 'cash') {
        //     $rules['form.org_bank_id'] = 'required|integer';
        // }

        return $rules;
    }
}
