<?php

namespace App\Http\Controllers;
use App\Models\Quotation;
use App\Models\QuotationDetail;
use App\Models\Customer;
use App\Models\User;
use App\Models\Product;
use App\Models\Organization;
use App\Models\Document;
use App\Models\Orders;
use App\Models\OrderDetails;
use App\Models\ProformaInvoice;
use App\Models\ProformaInvoiceDetails;
use App\Models\EmailTemplate;
use App\Models\MailConfig;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\QuotationStoreRequest;
use App\Http\Requests\QuotationToOrder;
use App\Traits\CommonTrait;
use App\Traits\QueryTrait;
use Config;
use PDF;
use Carbon\Carbon;

class QuotationController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Quotation')->only(['index']);
        $this->middleware('permission:Create Quotation')->only(['create', 'store']);
        $this->middleware('permission:Edit Quotation')->only(['edit', 'store']);
        $this->middleware('permission:Delete Quotation')->only('destroy');
        $this->middleware('permission:View Quotation')->only('view');
        $this->middleware('permission:Convert Order Quotation')->only('convertOrder');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $salesUserId = $request->input('sales_user_id');
        $categoryId = $request->input('category');
        $createdBy = $request->input('created_by');
        $financialYear = $request->input('financial_year');
        $financialYears = $this->getFinancialYears();

        $startDate = null;
        $endDate = null;
        if (!$financialYear && count($financialYears)) {
            $financialYear = $financialYears[0]['id']; // assuming first is current year, else write logic to detect current range
        }
        if($financialYear) {
            [$startDateStr, $endDateStr] = explode(' to ', $financialYear);
            $startDate = Carbon::createFromFormat('d-m-Y', $startDateStr)->startOfDay();
            $endDate = Carbon::createFromFormat('d-m-Y', $endDateStr)->endOfDay();
        }

        $query  = Quotation::with('quotationDetail.product.serialNumbers','customers', 'users', 'organization');
        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if($createdBy) {
            $query->where('created_by', $createdBy);
        }

        if($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        if(auth()->user()->can('Create Quotation') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }
        if(!empty($search)){
            if(!empty($search)){
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })->orWhereHas('users', function ($subquery) use ($search) {
                    $subquery->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%$search%"]);
                })
                ->orWhere('quotation_number', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            }
        }

        $searchableFields = ['quotation_number', 'customers.customer_name', 'users.first_name', 'date', 'total_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        $statusOrder = ['Pending', 'Accepted', 'Rejected'];
        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");

        $data = $query->orderBy('id', 'desc')->paginate(20);

        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $category = Config::get('constants.quotationList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $organization->prepend($allOrganization);
        $customers->prepend($allCustomers);
        $salesuser->prepend($allSalesuser);
        $data->withQueryString()->links();
        $pagetypes = collect(Config::get('constants.pageTypes'));
        $productpath = Config::get('constants.uploadFilePath.productImages');
        $user_id = Auth::user()->id;
        $emailTemplates = EmailTemplate::select('template_name as name', 'id', 'email_subject', 'content')->get();
        $email = MailConfig::select('email as name','id')->get();
        $permissions = [
            'canCreateQuotation'    => auth()->user()->can('Create Quotation'),
            'canEditQuotation'      => auth()->user()->can('Edit Quotation'),
            'canDeleteQuotation'    => auth()->user()->can('Delete Quotation'),
            'canViewQuotation'      => auth()->user()->can('View Quotation'),
            'canConvertQuotation'   => auth()->user()->can('Convert Order Quotation')
        ];

        return Inertia::render('Quotation/List', compact('data','financialYears', 'financialYear', 'user_id', 'emailTemplates', 'email', 'permissions', 'filepath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank', 'organization', 'customers', 'organizationId', 'customerId', 'salesuser', 'category', 'salesUserId', 'categoryId', 'createdBy', 'permissions', 'productpath', 'pagetypes'));
    }

    public function store(QuotationStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            if(isset($data['quotation_id'])){
                $quotation = Quotation::findOrFail($data['quotation_id']);
                foreach ($data['selectedProductItem'] as $quotationDetails) {
                    $quotationDetails['quotation_id'] = $data['quotation_id'];
                    $quotationDetails['gst'] = isset($quotationDetails['gst']) ? $quotationDetails['gst'] : 0;
                    if (!empty($quotationDetails['quotation_detail_id'])) {
                        $quotationDetails['updated_by'] = Auth::user()->id;
                        $quotationDetail = QuotationDetail::find($quotationDetails['quotation_detail_id']);
                        $quotationDetail->update($quotationDetails);
                    } else {
                        $quotationDetails['created_by'] = $quotationDetails['updated_by'] = Auth::user()->id;
                        $quotationDetail = QuotationDetail::create($quotationDetails);
                    }
                }
                $quotation->update([
                    'customer_id'       => $data['customer_id'],
                    'date'              => $data['date'],
                    'organization_id'   => $data['organization_id'],
                    'category'          => $data['category'],
                    'sales_user_id'     => $data['sales_user_id'],
                    'total_amount'      => $data['total_amount'],
                    'igst'              => $data['igst'],
                    'sgst'              => $data['sgst'],
                    'cgst'              => $data['cgst'],
                    'sub_total'         => $data['sub_total'],
                    'overall_discount'  => $data['overall_discount'],
                    'total_discount'    => $data['total_discount'],
                    'total_gst'         => $data['total_gst'],
                    'validity'          => $data['validity'],
                    'delivery'          => $data['delivery'],
                    'payment_terms'     => $data['payment_terms'],
                    'warranty'          => $data['warranty'],
                    'note'              => $data['note'],
                ]);
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['quotation_id']);
                }
                DB::commit();
                return Redirect::to('/quotation')->with('success','Quotation Updated Successfully');
            } else {
                if($data['is_customer'] == 'No'){
                    $customerData['customer_type'] = 'Retail';
                    $customerData['customer_name'] = $data['customer_name'];
                    $customerData['address'] = $data['address'];
                    $customerData['city'] = $data['city'];
                    $customerData['gst_type'] = $data['gst_type'];
                    $customerData['type'] = $data['type'];
                    $customerData['created_by'] = $customerData['updated_by'] =Auth::user()->id;
                    $customerData['type'] = $data['type'];
                    $createCustomer = Customer::create($customerData);
                    $data['customer_id'] = $createCustomer->id;
                };
                $data['overall_discount'] = $data['overall_discount'] ?? 0;
                $data['status'] = "Pending";

                $quotationNumberByOrganization = $this->generateQUTNumber();
                $quotation_number = $quotationNumberByOrganization[$data['organization_id']] ?? '';
                $data['quotation_number'] = $quotation_number;

                // $this->updateQUTNumber($data['quotation_number'], $data['organization_id']);

                $quotation = Quotation::create($data);
                if($quotation){
                    foreach ($data['selectedProductItem'] as $quotationDetails) {
                        $quotationDetails['gst'] = isset($quotationDetails['gst']) ? $quotationDetails['gst'] : 0;
                        $quotationDetails['quotation_id'] = $quotation->id;
                        $quotationDetails['created_by'] = $quotationDetails['updated_by'] = Auth::user()->id;
                        $quotationDetail = QuotationDetail::create($quotationDetails);
                    }
                }
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $quotation->id);
                }

                $this->updateQUTNumber($quotation_number, $data['organization_id']);

                DB::commit();
                return Redirect::to('/quotation')->with('success','Quotation Created Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/quotation')->with('error',$e->getMessage());
        }
    }

    public function create()
    {
        $quotation_number = $this->generateQUTNumber();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price, image")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.productCategoryList');
        $terms = Config::get('constants.termsAndConditions');
        $addNewCustomer = ['id' => 'new_customer', 'name' => 'NEW CUSTOMER'];
        $types = Config::get('constants.occupationType');
        $gst_types = Config::get('constants.gstType');
        $customers->prepend($addNewCustomer);
        $filepath = Config::get('constants.uploadFilePath.productImages');
        return Inertia::render('Quotation/Add', compact('quotation_number', 'customers', 'gst_types', 'types', 'salesuser', 'products', 'organization', 'category', 'terms', 'filepath'));
    }

    public function edit(string $id)
    {
        $data     = Quotation::where('id', $id)->with('quotationDetail.product.serialNumbers', 'customers', 'documents', 'organization')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.quotationDocument');
        $customers= Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser= User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price, image")->get();
        $category = Config::get('constants.productCategoryList');
        $organization  = Organization::select('id', 'name')->get();
        $productpath = Config::get('constants.uploadFilePath.productImages');
        return Inertia::render('Quotation/Edit', compact('data', 'filepath', 'customers', 'salesuser', 'products', 'category', 'organization', 'productpath'));
    }

    public function view(Request $request, $id)
    {
        $data = Quotation::where('id', $id)->with('quotationDetail.product.serialNumbers', 'customers', 'organization',  'documents', 'users')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.quotationDocument');
        return Inertia::render('Quotation/View', compact('data', 'filepath'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $quotation = Quotation::find($id);
            if($quotation){
                $quotationDetail = QuotationDetail::where('quotation_id', $id)->get();
                foreach ($quotationDetail as $detail) {
                    $detail->delete();
                }
                $quotation->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Quotation Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function convertOrder(QuotationToOrder $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['status'] = "Pending";
            $order_number = $this->generateORNumber();
            $data['order_number'] = $order_number[$data['organization_id']];
            $createOrder = Orders::create($data);
            $orderID = $createOrder->id;
            if($createOrder){
                foreach ($data['selectedProductItem'] as $orderDetails) {
                    if($orderDetails['check'] == true){
                        $orderDetails['gst'] = isset($orderDetails['gst']) ? $orderDetails['gst'] : 0;
                        $orderDetails['created_by'] = $orderDetails['updated_by'] = Auth::user()->id;
                        $orderDetails['order_id'] = $orderID;
                        $createOrderDetails = OrderDetails::create($orderDetails);
                        $quotationDetail = QuotationDetail::find($orderDetails['quotation_detail_id']);
                        $quotationDetail->update(['is_ordered' => 'yes']);
                    }
                }
            }
            $this->updateORNumber($data['order_number'], $data['organization_id']);
            $status = Quotation::find($data['quotation_id'])->update(['status' => 'Accepted']);
            DB::commit();
            return Redirect::to('/quotation')->with('success','Order Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/quotation')->with('error',  $e->getMessage());
        }
    }

    public function rejectQuotation($id)
    {
        $quotation = Quotation::findOrFail($id);
        $quotation->status = 'Rejected';
        $quotation->save();
        return redirect()->back()->with('success', 'Quotation rejected successfully.');
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.quotationDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "quotation";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    public function downloadQuotation($id , $type){
        $data = Quotation::where('id', $id)->with('quotationDetail.product.serialNumbers', 'customers', 'documents', 'organization')->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $productpath = Config::get('constants.uploadFilePath.productImages');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $pdf = PDF::loadView('pdf.quotation', compact('data', 'filepath', 'productpath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank'))->setPaper('A4', $type);
        $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
        return $pdf->download("Quotation_{$sanitizedFilename}.pdf");
        // return $pdf->stream();
    }

    public function convertPI($id)
    {
        DB::beginTransaction();
        try {
            $quotation = Quotation::where('id', $id)->get()->toArray();
            $piData = $quotation[0];
            $piData['quotation_id'] = $quotation[0]['id'];
            $pi_number = $this->generatePINumber();
            $piData['order_number'] = $pi_number[$piData['organization_id']];
            $piData['status'] = 'Open';
            $piData['date'] = date('Y-m-d');
            $createPI = ProformaInvoice::create($piData);
            $quotationDetails = QuotationDetail::where('quotation_id', $id)->get()->toArray();
            $PIID = $createPI->id;
            foreach ($quotationDetails as $quotationDetail) {
                $quotationDetail['pi_id'] = $PIID;
                $createPI = ProformaInvoiceDetails::create($quotationDetail);
            }

            $this->updatePINumber($piData['order_number'], $piData['organization_id']);
            $status = Quotation::find($id)->update(['status' => 'Pending']);
            DB::commit();
            return Redirect::back()->with('success','Proforma Invoice Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error',  $e->getMessage());
        }
    }

    public function quotationToOrder(string $id)
    {
        $data     = Quotation::where('id', $id)->with('quotationDetail.product.serialNumbers', 'customers', 'documents', 'organization')->get()->toArray();
        $filepath = Config::get('constants.uploadFilePath.quotationDocument');
        $customers= Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id ,person_name, gst_type')->orderByRaw('customer_name')->get();
        $salesuser= User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, description, gst, category, item_code, price, image")->get();
        $category = Config::get('constants.productCategoryList');
        $organization  = Organization::select('id', 'name')->get();
        $productpath = Config::get('constants.uploadFilePath.productImages');
        return Inertia::render('Quotation/QuotationToOrder', compact('data', 'filepath', 'customers', 'salesuser', 'products', 'category', 'organization', 'productpath'));
    }

}
