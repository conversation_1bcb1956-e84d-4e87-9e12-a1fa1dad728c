<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerCreditDetails extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'customer_credit_details';

    protected static $logName = 'Customer Credit Details';

    public function getLogDescription(string $event): string
    {
        return "Customer Credit Details has been {$event} for <strong>{$this->amount}</strong>by";
    }

    protected static $logAttributes = [
        'credit_id',
        'invoice_id',
        'amount',
        'date',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'credit_id',
        'invoice_id',
        'amount',
        'date',
        'created_by',
        'updated_by'
    ];

    public function credit(){
        return $this->belongsTo(CustomerCredit::class,'credit_id','id');
    }

    public function invoice(){
        return $this->belongsTo(Invoice::class,'invoice_id','id');
    }
}
