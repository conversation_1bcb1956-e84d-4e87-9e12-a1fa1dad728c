<script setup>
import { ref, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head , useForm , usePage } from '@inertiajs/vue3';

const props = defineProps(['data', 'organization', 'customers', 'fromDate', 'toDate', 'organizationId', 'customerId']);

// const companyID = usePage().props.organization.id;
const form = useForm({});
const from_date = ref('');
const to_date = ref('');
const customerName = ref('ALL CUSTOMERS');
const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const searchValue = ref('');


const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmountNew = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};


const handleSearchChange = (value, organizationId, customerId, from_date, to_date) => {
    searchValue.value = value;
    form.get(route('tds.report',
    {search:value,
          organization_id: organizationId,
          customer_id: customerId,
          from_date: from_date,
          to_date: to_date
}),  {
        preserveState: true,
        // replace: true,
    });
};


const exportXls = () => {
    let organizationName = '';

    switch (organizationId.value) {
        case 1:
            organizationName = 'MC';
            break;
        case 2:
            organizationName = 'HC';
            break;
        case 3:
            organizationName = 'NOX';
            break;
        default:
            organizationName = 'All_Organizations';
            break;
    }

    const cleanedcustomerName = customerName.value.replace(/\s+/g, '_');
    const fileName = `TDS_Data_Report_${organizationName}_${cleanedcustomerName}`;
    const params = {
        organization_id: organizationId.value || '',
        customer_id: customerId.value ||'',
        from_date: from_date.value || '',
        to_date: to_date.value || ''
    };


    const queryString = new URLSearchParams(params).toString();
    const url = `/export-tds-report?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${fileName}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        console.error('Error exporting data:', error);
    });
};

const handleStartDate = () => {
    handleSearchChange(searchValue.value, organizationId.value, customerId.value, from_date.value, to_date.value);
};

const handleToDate = () => {
    handleSearchChange(searchValue.value, organizationId.value, customerId.value, from_date.value, to_date.value);
};


const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, from_date.value, to_date.value);
};


const setCustomer = (id, name) => {
    customerId.value = id;
    customerName.value = name;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, from_date.value, to_date.value);
};
</script>

<template>
    <Head title="TDS Data Report"/>
<AdminLayout>
    <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">TDS Data Report</h1>
            </div>
            <div class="flex justify-end">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                     <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field" @input="handleSearchChange($event.target.value, organizationId, customerId, from_date, to_date)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
                <div class="flex ml-6">
                    <CreateButton :href="route('reports')">
                            Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="inline-flex items-center space-x-4 justify-end w-full">
                    <button @click="exportXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                        v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>

                <div class="sm:col-span-3">
                    <InputLabel for="customer_id" value="Customer Name" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="customers"
                        v-model="customerId"
                        @onchange="setCustomer"
                        />
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <InputLabel for="date" value="From Date" />
                    <input
                        v-model="from_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleStartDate"
                        :class="{ 'error rounded-md': form.errors.from_date }"
                    />
                </div>
                <div class="sm:col-span-3">
                    <InputLabel for="date" value="To Date" />
                    <input
                        v-model="to_date"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        type="date"
                        @change="handleToDate"
                        :class="{ 'error rounded-md': form.errors.to_date }"
                    />
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">DATE</th>
                            <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">CUSTOMER NAME</th>
                            <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">INVOICE NO</th>
                            <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">BANK</th>
                            <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">AMOUNT (₹)</th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr v-for="(userData, index) in data.data" :key="userData.id" class="odd:bg-white even:bg-gray-50 border-b">
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500 min-w-32">{{ formatDate(userData.date) ?? '-' }}</td>
                            <td class="px-3 py-3 text-sm text-gray-900 font-medium text-gray-900 truncate">
                                {{ userData.customers.customer_name ?? '-' }} :
                                {{ userData.customers.city ?? '-' }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{  userData?.invoice_data ? userData.invoice_data.map(invoice => invoice.invoice_no).join(', ') : userData.invoice_no }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500">{{ userData?.bank_info ? userData?.bank_info.bank_name +'-'+  userData?.bank_info.account_number: '-' }}</td>
                            <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-500 min-w-36">{{ formatAmountNew(userData.tds_amount) ?? '-' }}</td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
         <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
    </div>
</AdminLayout>
</template>
