<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\MaintenanceContractDTO;
use Support\Contracts\HasDTO;

class MaintenanceContractStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hospital_name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'contact_no' => 'nullable|string|max:20',
            'contract_start_date' => 'required|date',
            'contract_end_date' => 'required|date|after:contract_start_date',
            'maintenance_type' => 'required|string|in:AMC,CMC', // Adjust allowed values if needed
            'time_period' => 'required|string|max:255',
            'product_name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'company_name' => 'required|string|max:255',
            'invoice_number' => 'required|string|max:50',
            'pm_date_1' => 'required|date',
            // 'pm_date_2' => 'required|date|after:pm_date_1',
            // 'name' => 'nullable|string|max:255', // If this is a file path, ensure it's handled properly
        ];
    }


    public function DTO()
    {
        return MaintenanceContractDTO::LazyFromArray($this->input());
    }

}
