import{r as p,o as d,c as u,a,u as m,w as i,F as z,Z as te,b as e,k as T,v as L,N as se,e as f,g,f as _,i as j,n as y,t as v,s as oe,x as ae}from"./app-8a557454.js";import{s as ne}from"./sortAndSearch-c3af03c0.js";import{_ as le,b as ie,a as b}from"./AdminLayout-301d54ca.js";import{_ as re}from"./CreateButton-d5560e12.js";import{_ as U}from"./SecondaryButton-e65b5ab9.js";import{D as de}from"./DangerButton-41cc1b93.js";import{P as ce}from"./PrimaryButton-9d9bcdd8.js";import{_ as O}from"./TextInput-ab168ee4.js";import{_ as me}from"./TextArea-3588e81e.js";import{_ as B}from"./SearchableDropdown-51a69527.js";import{M as E}from"./Modal-3bbbc3d3.js";import{_ as ue}from"./SwitchButton-a5f8c395.js";import{_ as _e}from"./Pagination-da60561b.js";import{_ as pe}from"./ArrowIcon-10b49f20.js";import{_ as h}from"./InputLabel-07f3a6e8.js";import{_ as he}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const r=l=>(oe("data-v-9731c47a"),l=l(),ae(),l),fe={class:"animate-top"},ge={class:"flex justify-between items-center"},ye=r(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Companies")],-1)),ve={class:"flex justify-end"},xe={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},we={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},be=r(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),ke={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Ce={class:"flex justify-end"},Ve={class:"mt-8 overflow-x-auto sm:rounded-lg"},Me={class:"shadow sm:rounded-lg"},ze={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Be={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},$e={class:"border-b-2"},Se=["onClick"],Pe={key:0},Ae={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ie={class:"px-4 py-2.5 min-w-36"},Ne={class:"px-4 py-2.5 min-w-36"},Te={class:"px-4 py-2.5 min-w-32"},Le={class:"px-4 py-2.5 min-w-36"},je={class:"items-center px-4 py-2.5"},Ue={class:"items-center px-4 py-2.5"},Oe={class:"flex items-center justify-start gap-4"},Ee=r(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),He=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),We=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),qe=["onClick"],Fe=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ge=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Ke=[Fe,Ge],Re=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h7v7H3zM14 3h7v7h-7zM3 14h7v7H3zM14 14h7v7h-7z"})],-1)),Ye=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Products ",-1)),Ze=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 6l4 4M4 6l4-4M20 18H4m16 0l-4-4m4 4l-4 4"})],-1)),Je=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Transaction ",-1)),Qe=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Xe=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Credit ",-1)),De=["onClick"],et=r(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),tt=r(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Advance Payment ",-1)),st=[et,tt],ot={key:1},at=r(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),nt=[at],lt={class:"p-6"},it=r(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),rt={class:"mt-6 flex justify-end"},dt={class:"p-6"},ct=r(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment",-1)),mt={class:"border-b border-gray-900/10 pb-12"},ut={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},_t={class:"sm:col-span-3"},pt={class:"relative mt-2"},ht={class:"sm:col-span-3"},ft={class:"relative mt-2"},gt={class:"sm:col-span-3"},yt={key:0,class:"sm:col-span-3"},vt={class:"sm:col-span-3"},xt={key:1,class:"sm:col-span-3"},wt={class:"relative mt-2"},bt={class:"sm:col-span-6"},kt={class:"mt-6 px-4 flex justify-end"},Ct={class:"w-36"},Vt={__name:"List",props:["data","search","permissions","bankInfo","organization","paymentType"],setup(l){const H=l,{form:c,search:k,sort:W,fetchData:$,sortKey:q,sortDirection:F}=ne("companies.index"),C=p(!1),S=p(null),x=p(!1);p("custom");const G=p("custom2"),P=p([]),V=p(""),K=[{field:"name",label:"COMPANY NAME",sortable:!0},{field:"gst_no",label:"GST NO",sortable:!0},{field:"contact_no",label:"CONTACT",sortable:!1},{field:"email",label:"EMAIL",sortable:!0},{field:"balance",label:"BALANCE",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],s={organization_id:"",company_id:"",org_bank_id:"",invoice_no:"",purchase_order_receive_id:"",payment_type:"",amount:"",check_number:"",date:"",note:""},R=(n,o)=>{s.organization_id=n;const t=H.bankInfo.filter(w=>w.organization_id===n);P.value=t,c.errors["data.organization_id"]=null},Y=(n,o)=>{s.payment_type=n,V.value=o,c.errors["data.payment_type"]=null},Z=(n,o)=>{s.org_bank_id=n,c.errors["data.org_bank_id"]=null},J=n=>{s.organization_id="",s.company_id="",s.org_bank_id="",s.purchase_order_receive_id="",s.payment_type="",s.amount="",s.check_number="",s.date="",s.note="",x.value=!0,s.company_id=n},Q=()=>{c.post(route("companies.advancepayment",{data:s}),{onSuccess:()=>{c.reset(),x.value=!1},onError:n=>{}})},A=n=>{c.errors[n]=null},I=()=>{x.value=!1},X=n=>{S.value=n,C.value=!0},M=()=>{C.value=!1},D=()=>{c.delete(route("companies.destroy",{id:S.value}),{onSuccess:()=>M()})},ee=(n,o)=>{c.post(route("companies.activation",{id:o,status:n}),{})};return(n,o)=>(d(),u(z,null,[a(m(te),{title:"Company List"}),a(le,null,{default:i(()=>[e("div",fe,[e("div",ge,[ye,e("div",ve,[e("div",xe,[e("div",we,[be,T(e("input",{id:"search-field","onUpdate:modelValue":o[0]||(o[0]=t=>se(k)?k.value=t:null),onInput:o[1]||(o[1]=(...t)=>m($)&&m($)(...t)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[L,m(k)]])])]),l.permissions.canCreateCompany?(d(),u("div",ke,[e("div",Ce,[l.permissions.canCreateCompany?(d(),f(re,{key:0,href:n.route("companies.create")},{default:i(()=>[g(" Add Company ")]),_:1},8,["href"])):_("",!0)])])):_("",!0)])]),e("div",Ve,[e("div",Me,[e("table",ze,[e("thead",Be,[e("tr",$e,[(d(),u(z,null,j(K,(t,w)=>e("th",{key:w,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:N=>m(W)(t.field,t.sortable)},[g(v(t.label)+" ",1),t.sortable?(d(),f(pe,{key:0,isSorted:m(q)===t.field,direction:m(F)},null,8,["isSorted","direction"])):_("",!0)],8,Se)),64))])]),l.data.data&&l.data.data.length>0?(d(),u("tbody",Pe,[(d(!0),u(z,null,j(l.data.data,(t,w)=>(d(),u("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ae,v(t.name??"-"),1),e("td",Ie,v(t.gst_no??"-"),1),e("td",Ne,v(t.contact_no??"-"),1),e("td",Te,v(t.email??"-"),1),e("td",Le,v(t.balance??"-"),1),e("td",je,[a(ue,{switchValue:t.status,userId:t.id,onUpdateSwitchValue:ee},null,8,["switchValue","userId"])]),e("td",Ue,[e("div",Oe,[a(ie,{align:"right",width:"48"},{trigger:i(()=>[Ee]),content:i(()=>[l.permissions.canEditCompany?(d(),f(b,{key:0,href:n.route("companies.edit",{id:t.id})},{svg:i(()=>[He]),text:i(()=>[We]),_:2},1032,["href"])):_("",!0),l.permissions.canDeleteCompany?(d(),u("button",{key:1,type:"button",onClick:N=>X(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ke,8,qe)):_("",!0),l.permissions.canViewProducts?(d(),f(b,{key:2,href:n.route("products.show",{id:t.id})},{svg:i(()=>[Re]),text:i(()=>[Ye]),_:2},1032,["href"])):_("",!0),l.permissions.canTransactionCompany?(d(),f(b,{key:3,href:n.route("companies.transaction",{id:t.id})},{svg:i(()=>[Ze]),text:i(()=>[Je]),_:2},1032,["href"])):_("",!0),a(b,{href:n.route("companies.credit",{id:t.id})},{svg:i(()=>[Qe]),text:i(()=>[Xe]),_:2},1032,["href"]),e("button",{type:"button",onClick:N=>J(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},st,8,De)]),_:2},1024)])])]))),128))])):(d(),u("tbody",ot,nt))])])]),l.data.data&&l.data.data.length>0?(d(),f(_e,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):_("",!0)]),a(E,{show:C.value,onClose:M},{default:i(()=>[e("div",lt,[it,e("div",rt,[a(U,{onClick:M},{default:i(()=>[g(" Cancel ")]),_:1}),a(de,{class:"ml-3",onClick:D},{default:i(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"]),a(E,{show:x.value,onClose:I,maxWidth:G.value},{default:i(()=>[e("div",dt,[ct,e("div",mt,[e("div",ut,[e("div",_t,[a(h,{for:"role_id",value:"Organization"}),e("div",pt,[a(B,{options:l.organization,modelValue:s.organization_id,"onUpdate:modelValue":o[2]||(o[2]=t=>s.organization_id=t),onOnchange:R,class:y({"error rounded-md":m(c).errors["data.organization_id"]})},null,8,["options","modelValue","class"])])]),e("div",ht,[a(h,{for:"role_id",value:"Payment Type"}),e("div",ft,[a(B,{options:l.paymentType,modelValue:s.payment_type,"onUpdate:modelValue":o[3]||(o[3]=t=>s.payment_type=t),onOnchange:Y,class:y({"error rounded-md":m(c).errors["data.payment_type"]})},null,8,["options","modelValue","class"])])]),e("div",gt,[a(h,{for:"amount",value:"Amount"}),a(O,{id:"amount",type:"text",onChange:o[4]||(o[4]=t=>A("data.amount")),modelValue:s.amount,"onUpdate:modelValue":o[5]||(o[5]=t=>s.amount=t),class:y({"error rounded-md":m(c).errors["data.amount"]})},null,8,["modelValue","class"])]),V.value=="Cheque"?(d(),u("div",yt,[a(h,{for:"check_number",value:"Cheque Number"}),a(O,{id:"check_number",type:"text",modelValue:s.check_number,"onUpdate:modelValue":o[6]||(o[6]=t=>s.check_number=t),class:y({"error rounded-md":m(c).errors["data.check_number"]})},null,8,["modelValue","class"])])):_("",!0),e("div",vt,[a(h,{for:"date",value:"Payment Date"}),T(e("input",{"onUpdate:modelValue":o[7]||(o[7]=t=>s.date=t),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":m(c).errors["data.date"]}]),type:"date",onChange:o[8]||(o[8]=t=>A("data.date"))},null,34),[[L,s.date]])]),V.value!="Cash"?(d(),u("div",xt,[a(h,{for:"org_bank_id",value:"Our Bank"}),e("div",wt,[a(B,{options:P.value,modelValue:s.org_bank_id,"onUpdate:modelValue":o[9]||(o[9]=t=>s.org_bank_id=t),onOnchange:Z,class:y({"error rounded-md":m(c).errors["data.org_bank_id"]})},null,8,["options","modelValue","class"])])])):_("",!0),e("div",bt,[a(h,{for:"note",value:"Note"}),a(me,{id:"note",type:"text",rows:2,modelValue:s.note,"onUpdate:modelValue":o[10]||(o[10]=t=>s.note=t)},null,8,["modelValue"])])])]),e("div",kt,[a(U,{onClick:I},{default:i(()=>[g(" Cancel ")]),_:1}),e("div",Ct,[a(ce,{class:"ml-3 w-20",onClick:Q},{default:i(()=>[g(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},qt=he(Vt,[["__scopeId","data-v-9731c47a"]]);export{qt as default};
