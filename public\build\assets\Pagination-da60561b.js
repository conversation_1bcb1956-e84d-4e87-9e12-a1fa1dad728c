import{o as t,c as n,b as c,F as r,i as g,a as i,u as o,y as a,n as f,f as u}from"./app-8a557454.js";const d={key:0},m={class:"flex flex-wrap justify-end isolate rounded-md"},h={key:0},y={key:1},p={__name:"Pagination",props:["links"],setup(s){return(x,_)=>s.links.length>1?(t(),n("div",d,[c("div",m,[(t(!0),n(r,null,g(s.links,(e,l)=>(t(),n(r,{key:l},[e.url===null?(t(),n("div",h,[i(o(a),{innerHTML:e.label,href:"#",class:"inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-white hover:bg-gray-50 focus:z-20 focus:outline-offset-0"},null,8,["innerHTML"])])):(t(),n("div",y,[i(o(a),{innerHTML:e.label,href:e.url,class:f([{"bg-indigo-600 text-white hover:bg-indigo-600":e.active},"bg-white inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"])},null,8,["innerHTML","href","class"])]))],64))),128))])])):u("",!0)}};export{p as _};
