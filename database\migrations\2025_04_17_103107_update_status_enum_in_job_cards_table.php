<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
{
    DB::statement("ALTER TABLE job_cards MODIFY status ENUM('Open', 'Close', 'quotation_required') NOT NULL");
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
{
    DB::statement("ALTER TABLE job_cards MODIFY status ENUM('Open', 'Close') NOT NULL");
}

};
