<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challan_detail', function (Blueprint $table) {
            $table->bigInteger('serial_number_id')->nullable()->change();
            $table->bigInteger('product_id')->nullable()->after('challan_id');
            $table->bigInteger('invoiced_qty')->nullable()->after('qty');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('stock');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('challan_details', function (Blueprint $table) {
            //
        });
    }
};
