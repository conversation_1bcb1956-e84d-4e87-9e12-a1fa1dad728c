<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import FileUpload from '@/Components/FileUpload.vue';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { Head , Link, usePage } from '@inertiajs/vue3';
import { defineProps, ref } from 'vue';

const props = defineProps(['data', 'checklist', 'permissions', 'engineer', 'filePath']);

const userData = usePage().props.data;
const checkedValues = ref([]);

// Document preview modal state
const documentPreviewModal = ref(false);
const selectedDocument = ref('');

// PO document delete modal state
const poDocumentDeleteModal = ref(false);
const selectedPoDocumentId = ref(null);

// Quotation document delete modal state
const quotationDocumentDeleteModal = ref(false);
const selectedQuotationDocumentId = ref(null);

checkedValues.value = userData.job_card_checks.map(check => check.job_card_checklist_id);
const form = useForm('post', '/jobcard', {
    id: userData.id,
    type:userData.type,
    job_card_number: userData.job_card_number,
    engineer_id:userData.engineer_id,
    hospital_name:userData.hospital_name,
    address:userData.address,
    city:userData.city,
    contact_no:userData.contact_no,
    product_name:userData.product_name,
    product_code:userData.product_code,
    serial_no:userData.serial_no,
    accessories:userData.accessories,
    problem_description:userData.problem_description,
    parts_required:userData.parts_required,
    warranty_status:userData.warranty_status,
    jobchecks:[],
    date: userData.date,
    quotation_document: null,
    po_document: null,
    _method: 'PUT'
});

const handleQuotationDocument = (file) => {
    form.quotation_document = file;
};

const handlePoDocument = (file) => {
    form.po_document = file;
};

const updateChecked = (newCheckedValues) => {
  checkedValues.value = newCheckedValues;
};

const submit = () => {
    form.jobchecks = checkedValues.value;

    // Use POST with _method for file uploads
    form.post(route('jobcard.update', form.id), {
        preserveScroll: true,
        onSuccess: () => {
            // Don't reset the form completely, just clear the file fields
            form.quotation_document = null;
            form.po_document = null;
        },
    });
};

const setEngineer = (id, name) => {
    form.engineer_id = id;
    form.errors.engineer_id = null;
};

// Document preview functionality
const openPreviewModal = (documentName) => {
    selectedDocument.value = documentName;
    documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
    selectedDocument.value = '';
};

// PO document delete functionality
const openPoDeleteModal = (id) => {
    selectedPoDocumentId.value = id;
    poDocumentDeleteModal.value = true;
};

const deletePoDocument = () => {
    form.get(route('removedocument', {id: selectedPoDocumentId.value, name: 'jobcardDocument'}), {
        onSuccess: () => {
            closePoDocumentModal()
        }
    });
};

const closePoDocumentModal = () => {
    poDocumentDeleteModal.value = false;
    selectedPoDocumentId.value = null;
};

// Quotation document delete functionality
const openQuotationDeleteModal = (id) => {
    selectedQuotationDocumentId.value = id;
    quotationDocumentDeleteModal.value = true;
};

const deleteQuotationDocument = () => {
    form.get(route('removedocument', {id: selectedQuotationDocumentId.value, name: 'jobcardDocument'}), {
        onSuccess: () => {
            closeQuotationDocumentModal()
        }
    });
};

const closeQuotationDocumentModal = () => {
    quotationDocumentDeleteModal.value = false;
    selectedQuotationDocumentId.value = null;
};

</script>

<template>
    <Head title="Jobcard Checklist" />
    <AdminLayout>
        <div class="animate-top h-screen">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Edit Jobcard</h1>
                </div>
                <div class="w-auto">
                    <div class="flex space-x-2">
                        <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Jobcard Number:</span>
                        <span class="text-sm font-semibold text-gray-900 leading-6">{{ userData.job_card_number }}</span>
                    </div>
                    <div class="flex space-x-2 items-center">
                        <span class="text-sm font-semibold text-gray-900 leading-6 w-28">Date :</span>
                        <input
                            class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            type="date"
                            v-model="form.date"
                            @change="form.validate('date')"
                            :disabled="permissions.isServiceEngineer"
                        />
                    </div>
                </div>
            </div>
            <div class="mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                            <div class="sm:col-span-4">
                                <InputLabel for="hospital_name" value="Hospital Name" />
                                <TextInput
                                    id="hospital_name"
                                    hospital_name="text"
                                    v-model="form.hospital_name"
                                    autocomplete="hospital_name"
                                    @change="form.validate('hospital_name')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.hospital_name" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="contact_no" value="Contact No" />
                                <TextInput
                                    id="contact_no"
                                    contact_no="text"
                                    v-model="form.contact_no"
                                    :numeric="true"
                                    autocomplete="contact_no"
                                    @change="form.validate('contact_no')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.contact_no" />
                            </div>
                            <div class="sm:col-span-2">
                                <InputLabel for="city" value="City" />
                                <TextInput
                                    id="city"
                                    city="text"
                                    v-model="form.city"
                                    autocomplete="city"
                                    @change="form.validate('city')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.city" />
                            </div>
                            <div class="sm:col-span-4">
                                <InputLabel for="address" value="Address" />
                                <TextInput
                                    id="address"
                                    type="text"
                                    v-model="form.address"
                                    @change="form.validate('address')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.address" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="product_name" value="Equipment" />
                                <TextInput
                                    id="product_name"
                                    product_name="text"
                                    v-model="form.product_name"
                                    autocomplete="product_name"
                                    @change="form.validate('product_name')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.product_name" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="product_code" value="Model" />
                                <TextInput
                                    id="product_code"
                                    product_code="text"
                                    v-model="form.product_code"
                                    autocomplete="product_code"
                                    @change="form.validate('product_code')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.product_code" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="serial_no" value="Serial No" />
                                <TextInput
                                    id="serial_no"
                                    serial_no="text"
                                    v-model="form.serial_no"
                                    autocomplete="serial_no"
                                    @change="form.validate('serial_no')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError class="" :message="form.errors.serial_no" />
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="accessories" value="Accessories" />
                                <TextInput
                                    id="accessories"
                                    accessories="text"
                                    v-model="form.accessories"
                                    autocomplete="accessories"
                                    @change="form.validate('accessories')"
                                    :disabled="permissions.isServiceEngineer"
                                />
                                <InputError v-if="form.invalid('accessories')" class="" :message="form.errors.accessories" />
                            </div>
                            <div  class="sm:col-span-6">
                                <InputLabel for="problem_description" value="Description" />
                                <TextArea
                                    id="problem_description"
                                    type="text"
                                    :rows="3"
                                    v-model="form.problem_description"
                                    @change="form.validate('problem_description')"
                                />
                                <InputError class="" :message="form.errors.problem_description" />
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="parts_required" value="Parts Required" />
                                <TextInput
                                    id="parts_required"
                                    type="text"
                                    v-model="form.parts_required"
                                    autocomplete="parts_required"
                                    @change="form.validate('parts_required')"
                                />
                                <InputError class="" :message="form.errors.parts_required" />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="engineer_id" value="Engineer Name" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="engineer"
                                    v-model="form.engineer_id"
                                    @onchange="setEngineer"
                                    :class="{ 'error rounded-md': form.errors.engineer_id }"
                                    :disabled="permissions.isServiceEngineer"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-12">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel for="quotation_document" value="Upload Job Quotation Document"/>
                                        <FileUpload
                                            inputId="quotation_document"
                                            inputName="quotation_document"
                                            @file="handleQuotationDocument"
                                        />
                                    </div>
                                    <div>
                                        <InputLabel for="po_document" value="Upload Job PO Document"/>
                                        <FileUpload
                                            inputId="po_document"
                                            inputName="po_document"
                                            @file="handlePoDocument"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="warranty_status" value="Warranty Status" />
                                <div class="mt-2 space-y-2">
                                    <label class="flex items-center space-x-2">
                                        <input
                                            type="radio"
                                            id="warranty"
                                            value="warranty"
                                            v-model="form.warranty_status"
                                            @change="form.validate('warranty_status')"
                                            class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                            :disabled="permissions.isServiceEngineer"
                                        />
                                        <span>Warranty</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input
                                            type="radio"
                                            id="out_of_warranty"
                                            value="out_of_warranty"
                                            v-model="form.warranty_status"
                                            @change="form.validate('warranty_status')"
                                            class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                            :disabled="permissions.isServiceEngineer"
                                        />
                                        <span>Out of Warranty</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input
                                            type="radio"
                                            id="amc"
                                            value="amc"
                                            v-model="form.warranty_status"
                                            @change="form.validate('warranty_status')"
                                            class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                            :disabled="permissions.isServiceEngineer"
                                        />
                                        <span>AMC</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input
                                            type="radio"
                                            id="cmc"
                                            value="cmc"
                                            v-model="form.warranty_status"
                                            @change="form.validate('warranty_status')"
                                            class="text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                            :disabled="permissions.isServiceEngineer"
                                        />
                                        <span>CMC</span>
                                    </label>
                                </div>
                                <InputError v-if="form.invalid('warranty_status')" :message="form.errors.warranty_status" />
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="engineer_id" value="Checklist" />
                                <div class="grid sm:grid-cols-6 relative mt-2">
                                    <CheckboxWithLabel
                                        v-for="item in checklist"
                                        :key="item.id"
                                        :checked="checkedValues"
                                        :value="item.id"
                                        :label="item.type"
                                        @update:checked="updateChecked"
                                        :disabled="permissions.isServiceEngineer"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                            <!-- Display existing quotation documents -->
                            <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="userData.quotation_documents && userData.quotation_documents.length > 0">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">QUOTATION DOCUMENT</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                        <tr v-for="document in userData.quotation_documents" :key="document.id">
                                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ document.orignal_name }}
                                            </td>
                                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                                <button type="button" @click="openPreviewModal(document.name)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                                </button>
                                                <button type="button" @click="openQuotationDeleteModal(document.id)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Display existing PO documents -->
                            <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="userData.po_documents && userData.po_documents.length > 0">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">PO DOCUMENT</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                        <tr v-for="document in userData.po_documents" :key="document.id">
                                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ document.orignal_name }}
                                            </td>
                                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                                <button type="button" @click="openPreviewModal(document.name)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                                </button>
                                                <button type="button" @click="openPoDeleteModal(document.id)">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                                </svg>
                                            </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('jobcard.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                                </template>
                            </SvgLink>
                            <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                            <Transition
                                enter-active-class="transition ease-in-out"
                                enter-from-class="opacity-0"
                                leave-active-class="transition ease-in-out"
                                leave-to-class="opacity-0"
                            >
                                <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                            </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" maxWidth="xl">
            <div class="p-6">
                <FileViewer :fileUrl="filePath.view + selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal">Close</SecondaryButton>
                </div>
            </div>
        </Modal>
        <!-- PO Document Delete Modal -->
        <Modal :show="poDocumentDeleteModal" @close="closePoDocumentModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this PO document?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closePoDocumentModal"> Cancel</SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deletePoDocument"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <!-- Quotation Document Delete Modal -->
        <Modal :show="quotationDocumentDeleteModal" @close="closeQuotationDocumentModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this quotation document?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeQuotationDocumentModal"> Cancel</SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteQuotationDocument"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
