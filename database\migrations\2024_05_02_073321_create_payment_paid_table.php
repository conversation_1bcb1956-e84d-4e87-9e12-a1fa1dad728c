<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_paid', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained( table: 'organizations', indexName: 'orgpp_id')->onDelete('cascade')->onUpdate('no action');
            $table->foreignId('purchase_order_id')->constrained( table: 'invoice', indexName: 'poin_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('company_id');
            $table->integer('org_bank_id');
            $table->string('po_number');
            $table->enum('payment_type', ['cash', 'check', 'NEFT']);
            $table->double('amount', 16, 2);
            $table->string('check_number')->nullable();
            $table->date('date')->nullable();
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_paid');
    }
};
