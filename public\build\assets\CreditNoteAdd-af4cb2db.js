import{K as M,r as I,C as J,j as $,l as W,o as x,c as g,a as m,u as c,w as k,F as E,Z as Y,b as t,t as r,k as tt,v as et,d as st,f as h,i as ot,n as w,g as at,s as lt,x as nt}from"./app-2ecbacfc.js";import{_ as it,a as ct}from"./AdminLayout-42d5bb92.js";import{_ as rt}from"./InputError-aa79d601.js";import{_ as L}from"./InputLabel-f62a278f.js";import{P as dt}from"./PrimaryButton-0d76f021.js";import{_ as v}from"./TextInput-73b24943.js";import{_ as _t}from"./TextArea-00d3f05d.js";import{_ as mt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */import{u as ut}from"./index-35fd125b.js";const i=T=>(lt("data-v-22a3f93b"),T=T(),nt(),T),pt={class:"animate-top"},xt={class:"sm:flex sm:items-center"},gt=i(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Customer Credit")],-1)),ft={class:"w-auto"},yt={class:"flex space-x-2 items-center"},ht=i(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-36"},"Credit Note Number:",-1)),vt={class:"text-sm font-semibold text-gray-900 leading-6"},bt={class:"flex space-x-2 items-center"},wt=i(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),St=["onSubmit"],Ft={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},It={class:"inline-flex items-start space-x-6 justify-start w-full"},Ct={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Tt={class:"inline-flex items-center justify-start w-full space-x-2"},Vt=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),Pt={class:"text-sm leading-6 text-gray-700"},Gt={class:"inline-flex items-center justify-start w-full space-x-2"},$t=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),Nt={class:"text-sm leading-6 text-gray-700"},jt={class:"inline-flex items-center justify-start w-full space-x-2"},kt=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),At={class:"text-sm leading-6 text-gray-700"},Dt={class:"inline-flex items-center justify-start w-full space-x-2"},Ut=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),qt={class:"text-sm leading-6 text-gray-700"},Bt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},zt={class:"inline-flex items-center justify-start w-full space-x-2"},Mt=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Invoice Number:",-1)),Et={class:"text-sm leading-6 text-gray-700"},Lt={class:"inline-flex items-center justify-start w-full space-x-2"},Ot=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1)),Ht={class:"text-sm leading-6 text-gray-700"},Qt={class:"inline-flex items-center justify-start w-full space-x-2"},Rt=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Type:",-1)),Kt={class:"text-sm leading-6 text-gray-700"},Xt={class:"inline-flex items-center justify-start w-full space-x-2"},Zt=i(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Organization:",-1)),Jt={class:"text-sm leading-6 text-gray-700"},Wt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Yt={class:"overflow-x-auto w-full"},te={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},ee=i(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),se=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),oe=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),ae=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),le=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),ne=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),ie=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ce=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),re=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),de=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),_e={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},me={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ue={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},pe={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},xe={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ge=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),fe=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),ye=i(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),he={class:"divide-y divide-gray-300 bg-white"},ve={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},we={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ge={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},$e={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ne={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},je={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ae={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},De={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},Ue={class:"px-3 py-3 text-sm text-gray-900"},qe=["onClick"],Be=i(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ze=[Be],Me={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ee={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Le={class:"sm:col-span-3 space-y-4"},Oe={class:"flex space-x-4"},He={class:"w-full"},Qe={class:"sm:col-span-3"},Re={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Ke={class:"inline-flex items-center justify-end w-full space-x-3"},Xe=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Ze={class:"text-base font-semibold text-gray-900 w-w-32"},Je={class:"inline-flex items-center justify-end w-full space-x-3"},We=i(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Ye={class:"w-40"},ts={class:"inline-flex items-center justify-end w-full space-x-3"},es=i(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),ss={class:"w-40"},os={class:"inline-flex items-center justify-end w-full space-x-3"},as=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),ls={class:"text-base font-semibold text-gray-900 w-w-32"},ns={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},is=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),cs={class:"text-base font-semibold text-gray-900 w-w-32"},rs={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},ds=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),_s={class:"text-base font-semibold text-gray-900 w-w-32"},ms={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},us=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ps={class:"text-base font-semibold text-gray-900 w-w-32"},xs={class:"inline-flex items-center justify-end w-full space-x-3"},gs=i(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),fs={class:"text-base font-semibold text-gray-900 w-w-32"},ys={class:"flex mt-6 items-center justify-between"},hs={class:"ml-auto flex items-center justify-end gap-x-6"},vs=i(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),bs={__name:"CreditNoteAdd",props:["credit_no","serialno","category","products","salesuser"],setup(T){const N=T;M().props.filepath.view;const d=M().props.data[0],O=I([]),H=N.products.filter(s=>s.serial_numbers.some(e=>e.organization_id===d.organization_id));O.value=H;const Q=I([]),R=N.serialno;Q.value=R;const p=I([{invoice_detail_id:"",editmode:"",serial_number_id:"",product_id:"",product_name:"",batch:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",expiry_date:"",mrp:"",description:""}]);J(()=>{p.value=d.invoice_detail.map(s=>({invoice_detail_id:s.id,editmode:"editMode",serial_number_id:s.serial_number_id,product_name:s.serialnumbers.product.name,batch:s.serialnumbers.unique_id,hsn_code:s.serialnumbers.product.hsn_code,item_code:s.serialnumbers.product.item_code,discount:parseFloat(s.discount).toFixed(2),price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),product_id:s.serialnumbers.product.id,qty:s.qty,expiry_date:s.serialnumbers.expiry_date,mrp:s.serialnumbers.mrp?parseFloat(s.serialnumbers.mrp).toFixed(2):"-",description:s.description,sell_price:parseFloat(s.price).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0"}))});const u=I(d.customers.gst_type);I(!1),I(null),I(null);const K=(s,l)=>{p.value.splice(s,1)},b=s=>{a.errors[s]=null},C=s=>{if(s==null||isNaN(s))return"0.00";let l=Number(s).toFixed(2),[e,n]=l.split("."),o=e.substring(e.length-3),_=e.substring(0,e.length-3);return _!==""&&(o=","+o),`${_.replace(/\B(?=(\d{2})+(?!\d))/g,",")+o}.${n}`},X=(s,l)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount_before_tax_product)||0,o=parseFloat(s.discount)||0,_=u.value=="IGST"?parseFloat(s.gst):parseFloat(s.sgst*2),y=parseFloat(s.qty);let f=0,G=0;o>0||n>0?f=e*y:f=e*y*(1+_/100);const V=f*(o/100)||0,B=e*1*(_/100),j=(e*y-V-n)*(_/100);o>0||n>0?G=f-V-n+j:G=f-V;const z=e*y;return s.total_price=isNaN(z)?"":parseFloat(z).toFixed(2),s.gst_amount=isNaN(B)?"":parseFloat(B).toFixed(2),s.total_gst_amount=isNaN(j)?"":parseFloat(j).toFixed(2),s.discount_amount=isNaN(V)?"":parseFloat(V).toFixed(2),isNaN(G)?"":parseFloat(G).toFixed(2)},S=(s,l)=>{q(),s.total_amount=X(s)},A=$(()=>{const s=Math.round(p.value.reduce((e,n)=>e+(n.total_amount?parseFloat(n.total_amount):0),0)),l=a.overall_discount?parseFloat(a.overall_discount):0;return s-l}),F=$(()=>p.value.reduce((s,l)=>s+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),D=$(()=>p.value.reduce((s,l)=>s+(l.total_price?parseFloat(l.total_price):0),0)),U=$(()=>{const s=p.value.reduce((n,o)=>n+(o.discount_amount?parseFloat(o.discount_amount):0),0),l=a.overall_discount?parseFloat(a.overall_discount):0,e=a.discount_before_tax?parseFloat(a.discount_before_tax):0;return s+l+e}),a=ut("post","/creditnotesave",{organization_id:d.organization.id,date:new Date().toISOString().slice(0,10),invoice_id:d.id,reason:"",selectedProductItem:[],customer_id:d.customer_id,credit_note_no:N.credit_no[d.organization_id],invoice_no:d.invoice_no,cgst:d.cgst,sgst:d.sgst,igst:d.igst,total_gst:d.total_gst,sub_total:d.sub_total,total_amount:d.total_amount,total_discount:d.total_discount,overall_discount:d.overall_discount,discount_before_tax:d.discount_before_tax,debit_note_number:""}),Z=()=>{a.sub_total=D.value,a.cgst=u.value=="CGST/SGST"?F.value/2:"0",a.sgst=u.value=="CGST/SGST"?F.value/2:"0",a.igst=u.value=="IGST"?F.value:"0",a.total_gst=F.value,a.total_amount=A.value,a.total_discount=U.value,a.selectedProductItem=p.value,a.submit({preserveScroll:!0,onSuccess:()=>a.reset()})},P=(s,l)=>{const e=l.length,n=l.reduce((y,f)=>y+(f.total_price?parseFloat(f.total_price):0),0),o=p.value.reduce((y,f)=>y+(f.total_price?parseFloat(f.total_price):0),0),_=s*n/o/e;l.forEach(y=>{y.discount_before_tax_product=_})},q=()=>{const s=parseFloat(a.discount_before_tax)||0,l=p.value.filter(_=>_.gst==5&&_.total_price>0),e=p.value.filter(_=>_.gst==12&&_.total_price>0),n=p.value.filter(_=>_.gst==18&&_.total_price>0),o=p.value.filter(_=>_.gst==28&&_.total_price>0);P(s,l),P(s,e),P(s,n),P(s,o)};return W(()=>a.discount_before_tax,s=>{q(),p.value.forEach(l=>{S(l)})}),(s,l)=>(x(),g(E,null,[m(c(Y),{title:"Invoice"}),m(it,null,{default:k(()=>[t("div",pt,[t("div",xt,[gt,t("div",ft,[t("div",yt,[ht,t("span",vt,r(c(a).credit_note_no),1)]),t("div",bt,[wt,tt(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>c(a).date=e),onChange:l[1]||(l[1]=e=>c(a).validate("date"))},null,544),[[et,c(a).date]])])])]),t("form",{onSubmit:st(Z,["prevent"]),class:""},[t("div",Ft,[t("div",It,[t("div",Ct,[t("div",Tt,[Vt,t("p",Pt,r(c(d).customers.customer_name),1)]),t("div",Gt,[$t,t("p",Nt,r(c(d).customers.gst_no??"-"),1)]),t("div",jt,[kt,t("p",At,r(c(d).customers.email??"-"),1)]),t("div",Dt,[Ut,t("p",qt,r(c(d).customers.contact_no??"-"),1)])]),t("div",Bt,[t("div",zt,[Mt,t("p",Et,r(c(d).invoice_no),1)]),t("div",Lt,[Ot,t("p",Ht,r(c(d).category),1)]),t("div",Qt,[Rt,t("p",Kt,r(c(d).invoice_type??"-"),1)]),t("div",Xt,[Zt,t("p",Jt,r(c(d).organization.name??"-"),1)])])])]),t("div",Wt,[t("div",Yt,[t("table",te,[t("thead",null,[t("tr",null,[ee,se,oe,ae,le,ne,ie,ce,re,de,u.value=="IGST"?(x(),g("th",_e,"IGST (%)")):h("",!0),u.value=="IGST"?(x(),g("th",me,"IGST (₹)")):h("",!0),u.value=="CGST/SGST"?(x(),g("th",ue,"CGST (%)")):h("",!0),u.value=="CGST/SGST"?(x(),g("th",pe,"SGST (%)")):h("",!0),u.value=="CGST/SGST"?(x(),g("th",xe,"Total GST (₹)")):h("",!0),ge,fe,ye])]),t("tbody",he,[(x(!0),g(E,null,ot(p.value,(e,n)=>(x(),g("tr",{key:n},[t("td",ve,r(e.item_code)+" "+r(e.product_name)+" ",1),t("td",be,r(e.batch)+" ",1),t("td",we,r(e.hsn_code??"-"),1),t("td",Se,r(e.expiry_date??"-"),1),t("td",Fe,[m(v,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":o=>e.description=o,onInput:o=>S(e,n),onChange:o=>b("selectedProductItem."+n+".description"),class:w({error:c(a).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ie,r(e.mrp??"-"),1),t("td",Ce,r(e.price),1),t("td",Te,[m(v,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":o=>e.qty=o,onInput:o=>S(e,n),onChange:o=>b("selectedProductItem."+n+".qty"),class:w({error:c(a).errors[`selectedProductItem.${n}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),m(rt,{message:c(a).errors[`selectedProductItem.${n}.qty`]},null,8,["message"])]),t("td",Ve,[m(v,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":o=>e.sell_price=o,onInput:o=>S(e,n),disabled:e.editmode,onChange:o=>b("selectedProductItem."+n+".sell_price"),class:w({error:c(a).errors[`selectedProductItem.${n}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","disabled","onChange","class"])]),t("td",Pe,r(e.total_price),1),u.value=="IGST"?(x(),g("td",Ge,[m(v,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":o=>e.gst=o,onInput:o=>S(e,n),disabled:e.editmode,onChange:o=>b("selectedProductItem."+n+".gst"),class:w({error:c(a).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","disabled","onChange","class"])])):h("",!0),u.value=="CGST/SGST"?(x(),g("td",$e,[m(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":o=>e.sgst=o,disabled:e.editmode,onInput:o=>S(e,n),onChange:o=>b("selectedProductItem."+n+".gst"),class:w({error:c(a).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","disabled","onInput","onChange","class"])])):h("",!0),u.value=="CGST/SGST"?(x(),g("td",Ne,[m(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":o=>e.sgst=o,disabled:e.editmode,onInput:o=>S(e,n),onChange:o=>b("selectedProductItem."+n+".gst"),class:w({error:c(a).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","disabled","onInput","onChange","class"])])):h("",!0),t("td",je,r(e.total_gst_amount),1),t("td",ke,[m(v,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":o=>e.discount=o,onInput:o=>S(e,n),disabled:e.editmode,onChange:o=>b("selectedProductItem."+n+".discount"),class:w({error:c(a).errors[`selectedProductItem.${n}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","disabled","onChange","class"])]),t("td",Ae,r(e.discount_amount),1),t("td",De,[t("div",Ue,r(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:o=>K(n,e.invoice_detail_id)},ze,8,qe)])]))),128))])])])]),t("div",Me,[t("div",Ee,[t("div",Le,[t("div",Oe,[t("div",He,[m(L,{for:"debit_note_number",value:"Customer Debit Note No"}),m(v,{id:"debit_note_number",type:"text",modelValue:c(a).debit_note_number,"onUpdate:modelValue":l[2]||(l[2]=e=>c(a).debit_note_number=e),class:w({error:c(a).errors.debit_note_number}),onChange:l[3]||(l[3]=e=>b("debit_note_number"))},null,8,["modelValue","class"])])]),t("div",null,[m(L,{for:"reason",value:"Reason For Credit Note"}),m(_t,{id:"reason",type:"text",modelValue:c(a).reason,"onUpdate:modelValue":l[4]||(l[4]=e=>c(a).reason=e),class:w({error:c(a).errors.reason}),onChange:l[5]||(l[5]=e=>b("reason"))},null,8,["modelValue","class"])])]),t("div",Qe,[t("div",Re,[t("div",Ke,[Xe,t("p",Ze,r(C(D.value)),1)]),t("div",Je,[We,t("div",Ye,[m(v,{id:"discount_before_tax",type:"text",modelValue:c(a).discount_before_tax,"onUpdate:modelValue":l[6]||(l[6]=e=>c(a).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",ts,[es,t("div",ss,[m(v,{id:"overall_discount",type:"text",modelValue:c(a).overall_discount,"onUpdate:modelValue":l[7]||(l[7]=e=>c(a).overall_discount=e)},null,8,["modelValue"])])]),t("div",os,[as,t("p",ls,r(C(U.value)),1)]),u.value=="IGST"?(x(),g("div",ns,[is,t("p",cs,r(C(F.value)),1)])):h("",!0),u.value=="CGST/SGST"?(x(),g("div",rs,[ds,t("p",_s,r(C(F.value/2)),1)])):h("",!0),u.value=="CGST/SGST"?(x(),g("div",ms,[us,t("p",ps,r(C(F.value/2)),1)])):h("",!0),t("div",xs,[gs,t("p",fs,r(C(A.value)),1)])])])])]),t("div",ys,[t("div",hs,[m(ct,{href:s.route("invoice.index")},{svg:k(()=>[vs]),_:1},8,["href"]),m(dt,{disabled:c(a).processing},{default:k(()=>[at("Submit")]),_:1},8,["disabled"])])])],40,St)])]),_:1})],64))}},Ns=mt(bs,[["__scopeId","data-v-22a3f93b"]]);export{Ns as default};
