<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->dropForeign('pod_id');
            $table->dropColumn('purchase_order_detail_id');
            $table->dropColumn('receive_qty');
            $table->double('total_price', 16, 2)->after('customer_invoice_date');
            $table->double('total_gst_amount', 16, 2)->after('total_price');
            $table->double('total_amount', 16, 2)->after('total_gst_amount');
        });

        // Schema::table('service_products', function (Blueprint $table) {
        //     $table->dropColumn('purchase_price');
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_order_receives', function (Blueprint $table) {
            $table->dropColumn('total_price');
            $table->dropColumn('total_gst_amount');
            $table->dropColumn('total_amount');
        });

    }
};
