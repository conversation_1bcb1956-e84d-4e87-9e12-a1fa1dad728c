<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class PurchaseReportExport implements FromCollection, WithHeadings, WithStyles
{
    protected $purchaseReports;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;

    public function __construct($purchaseReports, $fromDate, $toDate, $organizationName)
    {
        $this->purchaseReports = $purchaseReports;
        $this->fromDate = $fromDate ? Carbon::parse($fromDate)->format('M d, Y') : 'N/A';
        $this->toDate = $toDate ? Carbon::parse($toDate)->format('M d, Y') : 'N/A';
        $this->organizationName = $organizationName;
    }

    public function collection()
    {
        $data = [];
        $totalAmount = 0;

        $sortedReports = $this->purchaseReports->sortBy('customer_invoice_date')->values();

        foreach ($sortedReports as $index => $report) {
            $amount = $report->total_amount;
            $totalAmount += $amount;

            $data[] = [
                'Sno'            => $index + 1,
                'Invoice Number' => $report->customer_invoice_no ?? '-',
                'PO Number'      => $report->purchaseOrder->po_number ?? 'N/A',
                'Company Name'   => $report->purchaseOrder->company->name ?? '-',
                'Date'           => $report->customer_invoice_date
                    ? Carbon::parse($report->customer_invoice_date)->format('d-m-Y')
                    : '',
                'Amount (₹)'     => number_format($amount, 2),
            ];
        }

        $data[] = [
            'Sno'            => 'TOTAL',
            'Invoice Number' => '',
            'PO Number'      => '',
            'Company Name'   => '',
            'Date'           => '',
            'Amount (₹)'     => number_format($totalAmount, 2),
        ];

        return collect($data);
    }

    public function headings(): array
    {
        return [
            ["{$this->organizationName}"],
            ["From: {$this->fromDate} To: {$this->toDate}"],
            ['Sno', 'Invoice Number', 'PO Number', 'Company Name', 'Date', 'Amount (₹)'],
        ];
    }

    public function styles($sheet)
    {
        $rowCount = count($this->purchaseReports) + 4;

        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A3:F3')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A3:F3')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFFFCC');
        $sheet->getStyle('A3:F3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle("A3:F{$rowCount}")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle("A4:F{$rowCount}")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        $sheet->getColumnDimension('B')->setAutoSize(true);
        $sheet->getColumnDimension('C')->setAutoSize(true);
        $sheet->getColumnDimension('D')->setAutoSize(true);
        $sheet->getColumnDimension('E')->setAutoSize(true);
        $sheet->getColumnDimension('F')->setAutoSize(true);

        $sheet->getStyle("A{$rowCount}:F{$rowCount}")->getFont()->setBold(true);
        $sheet->getStyle("A{$rowCount}:F{$rowCount}")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('D9E1F2');
        $sheet->getStyle("A{$rowCount}:F{$rowCount}")->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        foreach (range(4, $rowCount - 1) as $row) {
            if ($row % 2 == 0) {
                $sheet->getStyle("A$row:F$row")->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('F9F9F9');
            } else {
                $sheet->getStyle("A$row:F$row")->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('FFFFFF');
            }
        }

        return $sheet;
    }
}
