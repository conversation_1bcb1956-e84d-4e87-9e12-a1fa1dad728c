<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class AccountTypeTransactionExport implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles, ShouldAutoSize
{
    protected $transactions;
    protected $accountTypeName;
    protected $bankName;
    protected $fromDate;
    protected $toDate;
    protected $totalRows;

    public function __construct($transactions, $accountTypeName, $bankName, $fromDate, $toDate)
    {
        $this->transactions = $transactions;
        $this->accountTypeName = $accountTypeName;
        $this->bankName = $bankName;
        $this->fromDate = $fromDate ? Carbon::parse($fromDate)->format('d-m-Y') : 'N/A';
        $this->toDate = $toDate ? Carbon::parse($toDate)->format('d-m-Y') : 'N/A';
        $this->totalRows = count($transactions) + 5;
    }

public function collection()
{
    $data = [];
    $balance = 0;
    $sno = 1;
    $totalDebit = 0;
    $totalCredit = 0;

    foreach ($this->transactions as $transaction) {
        $amount = (float)$transaction->amount;
        $isCredit = $transaction->payment_type === 'cr';
        
        if ($isCredit) {
            $creditAmount = $this->formatAmount($amount);
            $debitAmount = '';
            $totalCredit += $amount;
        } else {
            $creditAmount = '';
            $debitAmount = $this->formatAmount($amount);
            $totalDebit += $amount;
            $balance -= $amount;
        }
        
        $bankInfo = $transaction->bank ? 
            $transaction->bank->bank_name . ' - ' . $transaction->bank->account_number : 
            'N/A';
            
        $narration = $transaction->note ?? '-';
        
        $additionalInfo = '';
        if ($transaction->paymentReceive && $transaction->paymentReceive->customers) {
            $additionalInfo = 'Receipt from: ' . $transaction->paymentReceive->customers->customer_name;
        } elseif ($transaction->paymentPaid && $transaction->paymentPaid->company) {
            $additionalInfo = 'Payment to: ' . $transaction->paymentPaid->company->name;
        }
        
        $fullNarration = $bankInfo;
        if (!empty($additionalInfo)) {
            $fullNarration .= "\n" . $additionalInfo;
        }
        if (!empty($narration) && $narration != '-') {
            $fullNarration .= "\n" . $narration;
        }

        $displayBalance = $isCredit ? '0.00' : $this->formatAmount(abs($balance)) . ' ' . ($balance >= 0 ? 'cr' : 'dr');

        $data[] = [
            'Sno'           => $sno++,
            'Date'          => Carbon::parse($transaction->date)->format('d-m-Y'),
            'Narration'     => $fullNarration,
            'Debit (₹)'     => $debitAmount,
            'Credit (₹)'    => $creditAmount,
            'Balance (₹)'   => $displayBalance,
        ];
    }

    $finalBalance = $balance;

    $data[] = [
        'Sno'           => 'TOTAL',
        'Date'          => '',
        'Narration'     => '',
        'Debit (₹)'     => '',
        'Credit (₹)'    => $this->formatAmount($totalCredit),
        'Balance (₹)'   => $this->formatAmount(abs($finalBalance)) . ' ' . ($finalBalance >= 0 ? 'cr' : 'dr'),
    ];

    return collect($data);
}

    private function formatAmount($amount)
    {
        $amountStr = number_format(abs($amount), 2, '.', '');
        list($integerPart, $decimalPart) = explode('.', $amountStr);

        $lastThree = substr($integerPart, -3);
        $otherNumbers = substr($integerPart, 0, -3);

        $formattedIntegerPart = $otherNumbers !== ''
            ? preg_replace('/\B(?=(\d{2})+(?!\d))/', ',', $otherNumbers) . ',' . $lastThree
            : $lastThree;

        return $formattedIntegerPart . '.' . $decimalPart;
    }

    public function headings(): array
    {
        return [
            ['Account Type Transactions'],
            ["Account Type: {$this->accountTypeName}"],
            ["Bank: {$this->bankName}"],
            ["From: {$this->fromDate} To: {$this->toDate}"],
            ['Sno', 'Date', 'Narration', 'Debit (₹)', 'Credit (₹)', 'Balance (₹)'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function styles($sheet)
    {
        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');
        $sheet->mergeCells('A3:F3');
        $sheet->mergeCells('A4:F4');

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A4')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A5:F5')->getFont()->setBold(true);
        $sheet->getStyle('A5:F5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A5:F5')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');

        $totalRows = count($this->transactions) + 6;

        $sheet->getStyle("A5:F$totalRows")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle("A6:F$totalRows")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        
        $sheet->getColumnDimension('A')->setWidth(10);  
        $sheet->getColumnDimension('B')->setWidth(15);  
        $sheet->getColumnDimension('C')->setWidth(40);  
        $sheet->getColumnDimension('D')->setWidth(18);  
        $sheet->getColumnDimension('E')->setWidth(18);  
        $sheet->getColumnDimension('F')->setWidth(18);  

        foreach ($sheet->getRowDimensions() as $dimension) {
            $dimension->setRowHeight(-1);
        }

        $sheet->getStyle('C6:C' . $totalRows)->getAlignment()->setWrapText(true);

        $sheet->getStyle("A$totalRows:F$totalRows")->getFont()->setBold(true);
        $sheet->getStyle("A$totalRows:F$totalRows")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFFF00');
        $sheet->getStyle("A$totalRows:F$totalRows")->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        return $sheet;
    }
}



