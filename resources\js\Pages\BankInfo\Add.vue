<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['organization', 'amounttype']);

const form = useForm('post', '/bankinfo', {
    organization_id: '',
    bank_name: '',
    account_number: '',
    balance: '',
    amount_type: '',
    ifsc_code: ''
});

const submit = () => {
    form.submit({
        preserveScroll: true,
        onSuccess: () => {
        },
        onError: (errors) => {
            if (errors.account_number) {
                form.errors.account_number = 
                'This account number already exists in the bank.';
            }
        },
    });
};

const setOrganization = (id, name) => {
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setAmountType = (id, name) => {
    form.amount_type = id;
    form.errors.amount_type = null;
};
</script>

<template>
    <Head title="Bank Info" />
    <AdminLayout>
        <div class="animate-top h-screen">
            <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Add New Bank</h2>
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">

                            <div class="sm:col-span-3">
                                <InputLabel for="company_name" value="Organization"/>
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="organization"
                                        v-model="form.organization_id"
                                        @onchange="setOrganization"
                                        :class="{ 'error rounded-md': form.errors.organization_id }"
                                    />
                                    <InputError v-if="form.invalid('organization_id')" :message="form.errors.organization_id" />
                                </div>
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="bank_name" value="Bank Name" />
                                <TextInput
                                    id="bank_name"
                                    type="text"
                                    v-model="form.bank_name"
                                    autocomplete="bank_name"
                                    @change="form.validate('bank_name')"
                                />
                                <InputError v-if="form.invalid('bank_name')" :message="form.errors.bank_name" />
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="account_number" value="Account Number" />
                                <TextInput
                                    id="account_number"
                                    type="text"
                                    v-model="form.account_number"
                                    autocomplete="account_number"
                                    @change="form.validate('account_number')"
                                />
                                <InputError v-if="form.invalid('account_number')" :message="form.errors.account_number" />
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="ifsc_code" value="IFSC Code" />
                                <TextInput
                                    id="ifsc_code"
                                    type="text"
                                    v-model="form.ifsc_code"
                                    autocomplete="ifsc_code"
                                    @change="form.validate('ifsc_code')"
                                />
                                <InputError v-if="form.invalid('ifsc_code')" :message="form.errors.ifsc_code" />
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="balance" value="Opening Balance" />
                                <TextInput
                                    id="balance"
                                    type="text"
                                    v-model="form.balance"
                                    autocomplete="balance"
                                    @change="form.validate('balance')"
                                />
                                <InputError v-if="form.invalid('balance')" :message="form.errors.balance" />
                            </div>

                            <div class="sm:col-span-3">
                                <InputLabel for="company_name" value="Amount Type"/>
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="amounttype"
                                        v-model="form.amount_type"
                                        @onchange="setAmountType"
                                        :class="{ 'error rounded-md': form.errors.amount_type }"
                                    />
                                    <InputError v-if="form.invalid('amount_type')" :message="form.errors.amount_type" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('bankinfo.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                                </template>
                            </SvgLink>
                            <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                            <Transition
                                enter-active-class="transition ease-in-out"
                                enter-from-class="opacity-0"
                                leave-active-class="transition ease-in-out"
                                leave-to-class="opacity-0"
                            >
                                <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                            </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
