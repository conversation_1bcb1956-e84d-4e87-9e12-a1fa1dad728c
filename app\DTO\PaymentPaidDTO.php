<?php

namespace App\DTO;

use App\Traits\ArrayToProps;

class PaymentPaidDTO
{
    use ArrayToProps;

    public $organization_id;
    public $company_id;
    public $payment_type;
    public $date;
    public $note;
    public $amount;
    public $discount_amount;
    public $round_off;
    public $check_number;
    public $org_bank_id;
    public $is_credit;
    public $credit_data;
    public $total_unused_amount;
    public $invoice;
    public $settled_amount;
    public $advance_amount;
    public $purchase_order_receive_id;
    public $invoice_data;
    public $invoice_no;
    public $id;
    public $created_by;
    public $updated_by;

}
