<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\AccountType;
use App\Models\Organization;
use App\Models\BankInfo;
use App\Models\BankTransaction;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\AccountTypeRequest;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AccountTypeTransactionExport;

class AccountTypeController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Account Types')->only(['index']);
        $this->middleware('permission:Account Type')->only(['index','create', 'store','edit', 'update','destroy']);
        $this->middleware('permission:Create Account Types')->only(['create', 'store']);
        $this->middleware('permission:Edit Account Types')->only(['edit', 'update']);
        $this->middleware('permission:Delete Account Types')->only('destroy');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $data = AccountType::orderBy('id', 'desc')->paginate(10);
        $permissions = [
            'canCreateAccountTypes'      => auth()->user()->can('Create Account Types'),
            'canEditAccountTypes'        => auth()->user()->can('Edit Account Types'),
            'canDeleteAccountTypes'      => auth()->user()->can('Delete Account Types')
        ];
        return Inertia::render('AccountType/List', compact('data', 'permissions'));
    }

    public function create(Request $request)
    {
        return Inertia::render('AccountType/Add');
    }

    public function store(AccountTypeRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            AccountType::create($data);
            DB::commit();
            return Redirect::to('/account-type')->with('success', 'Account Type Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = AccountType::find($id);
        return Inertia::render('AccountType/Edit', compact('data'));
    }

    public function update(AccountTypeRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();;
            $user = AccountType::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to('/account-type')->with('success', 'Account Type Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = AccountType::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Account Type Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function view(Request $request, $id)
    {
        // $fromDate = $request->input('from_date');
        // $toDate = $request->input('to_date');

        $bank_id = $request->input('bank_id');

        $query = BankTransaction::with('bank', 'paymentReceive.customers', 'accounttype', 'paymentPaid')
            ->where('account_type', $id);

        if ($bank_id) {
            $query->where('org_bank_id', $bank_id);
        }

        $data = $query->orderBy('date', 'asc')->get();

        $accountType = AccountType::findOrFail($id);
        $accountId =  $id;
        $bankList  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        return Inertia::render('AccountType/Transactions', [
            'data' => $data,
            'accountType' => $accountType,
            'bankList' => $bankList,
            'bank_id' => $bank_id,
            'accountId' => $accountId
        ]);
    }

    public function exportAccountTransactions(Request $request, $id)
    {
        $validated = $request->validate([
            'account_id' => 'required|integer',
            'bank_id' => 'nullable|integer',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
        ]);

        $accountId = $validated['account_id'];
        $bankId = $validated['bank_id'] ?? null;
        $fromDate = $validated['from_date'] ?? null;
        $toDate = $validated['to_date'] ?? null;

        $query = BankTransaction::with('bank', 'paymentReceive.customers', 'accounttype', 'paymentPaid')
            ->where('account_type', $accountId);

        if ($bankId) {
            $query->where('org_bank_id', $bankId);
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('date', [$fromDate, $toDate]);
        }

        $transactions = $query->orderBy('date', 'asc')->get();

        $accountType = AccountType::findOrFail($accountId);
        $bankName = $bankId ? BankInfo::find($bankId)->bank_name . ' - ' . BankInfo::find($bankId)->account_number : 'All Banks';

        return Excel::download(
            new AccountTypeTransactionExport($transactions, $accountType->name, $bankName, $fromDate, $toDate),
            'account_type_transactions.xlsx'
        );
    }
}

