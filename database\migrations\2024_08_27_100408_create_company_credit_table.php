<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_credit', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_paid_id')->constrained( table: 'payment_paid', indexName: 'ccpp_id')->onDelete('cascade')->onUpdate('no action');
            $table->integer('organization_id');
            $table->integer('company_id');
            $table->double('amount', 16, 2);
            $table->double('unused_amount', 16, 2);
            $table->date('date')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_credit');
    }
};
