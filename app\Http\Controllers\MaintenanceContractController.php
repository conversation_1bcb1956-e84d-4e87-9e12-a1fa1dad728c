<?php

namespace App\Http\Controllers;
use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use App\Models\MaintenanceContract;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\MaintenanceContractStoreRequest;

use Inertia\Inertia;
use App\Exports\MaintenanceExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\QueryTrait;
use Config;
class MaintenanceContractController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Maintenance')->only(['index']);
        $this->middleware('permission:Create Maintenance')->only(['create', 'store']);
        $this->middleware('permission:Edit Maintenance')->only(['edit', 'update']);
        $this->middleware('permission:Delete Maintenance')->only('destroy');
    }

    public function index(Request $request)
    {
        MaintenanceContract::where('contract_end_date', '<', now())
        ->where('status', '!=', 'Close')
        ->update(['status' => 'Close']);

        $search = $request->input('search');
        $statusId = $request->input('status');
        $query = MaintenanceContract::query();

        if ($statusId) {
            $query->where('status', $statusId);
        }

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('hospital_name', 'like', "%$search%")
                      ->orWhere('product_name', 'like', "%$search%");
            });
        }

        $statusOrder = ['Open', 'Close'];

        $searchableFields = [
            'hospital_name', 'product_name', 'price', 'contract_start_date', 'contract_end_date',
            'pm_date_1', 'pm_date_2', 'pm_date_3', 'pm_date_4', 'maintenance_type', 'status'
        ];
        $this->searchAndSort($query, $request, $searchableFields);

        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate(20);

        $currentMonth = now()->month;
        $currentYear = now()->year;

        $data->getCollection()->transform(function ($item) use ($currentMonth, $currentYear) {

            $pmDates = [$item->pm_date_1, $item->pm_date_2, $item->pm_date_3, $item->pm_date_4];

            $item->highlight = collect($pmDates)->contains(function ($date) use ($currentMonth, $currentYear) {
                if ($date) {
                    $pmDate = \Carbon\Carbon::parse($date);
                    return $pmDate->month == $currentMonth && $pmDate->year == $currentYear;
                }
                return false;
            });

            return $item;
        });

        $permissions = [
            'canCreateMaintenance' => auth()->user()->can('Create Maintenance'),
            'canEditMaintenance' => auth()->user()->can('Edit Maintenance'),
            'canDeleteMaintenance' => auth()->user()->can('Delete Maintenance'),
        ];

        return Inertia::render('Maintenance/List', compact('data', 'permissions', 'statusId'));
    }

    public function create(Request $request)

    {
        $maintenance_type =  Config::get('constants.maintenanceType');
        $filepath = Config::get('constants.uploadFilePath.maintenanceContract');

        return Inertia::render('Maintenance/Add',compact('maintenance_type','filepath'));
    }

    public function store(MaintenanceContractStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $files = $request->file();
            if(isset($data['id'])){
                $data['updated_by'] = auth()->id();

                $user = MaintenanceContract::findOrFail($request->id);
                $user->update($data);
                if($files){
                    $this->uploadPhoto($files, $user);
                }
                DB::commit();
                return Redirect::to('/maintenance-contract')->with('success', 'Maintenance-Contract Updated Successfully');
            } else {
                $data['created_by'] = $data['updated_by'] = auth()->id();
                $user = MaintenanceContract::create(attributes: $data);
                if($files){
                    $this->uploadPhoto($files, $user);
                }
                DB::commit();
                return Redirect::to('/maintenance-contract')->with('success', 'Maintenance-Contract Added Successfully');

            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = MaintenanceContract::find($id);

        $maintenance_type =  Config::get('constants.maintenanceType');
        $filepath = Config::get('constants.uploadFilePath.maintenanceDocument');

        return Inertia::render('Maintenance/Edit', compact('data', 'maintenance_type', 'filepath'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = MaintenanceContract::find($id);
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Maintenance-Contract Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    private function uploadPhoto($files, $user)
    {
        $filePath = Config::get('constants.uploadFilePath.maintenanceDocument');

        if ($files) {
            foreach ($files as $key => $file) {
                $originalName = $file->getClientOriginalName();
                $fileName = str_replace(' ', '-', $originalName);
                $path = $filePath['default'];
                if(!is_dir($path)) {
                    mkdir($path, 755, true);
                }
                if($user->image) {
                    $existingImagePath = $path . '/' . $user->image;
                    if (file_exists($existingImagePath)) {
                        unlink($existingImagePath); // Delete the existing image
                    }
                }
                $upload_success = $file->move($path, $fileName);
                if($upload_success){
                    $user->update([$key => $fileName]);
                }
            }
        }
    }

    public function exportMaintenance(Request $request)
    {
        $search = $request->input('search');
        $statusId = $request->input('status');
        $query = MaintenanceContract::query();

        if ($statusId) {
            $query->where('status', $statusId);
        }

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('hospital_name', 'like', "%$search%")
                    ->orWhere('product_name', 'like', "%$search%");
            });
        }

        $data = $query->orderBy('id', 'desc')->get();

        $currentMonth = now()->month;
        $currentYear = now()->year;

        $data->transform(function ($item) use ($currentMonth, $currentYear) {
            $pmDates = [$item->pm_date_1, $item->pm_date_2, $item->pm_date_3, $item->pm_date_4];

            $item->highlight = collect($pmDates)->contains(function ($date) use ($currentMonth, $currentYear) {
                if ($date) {
                    $pmDate = \Carbon\Carbon::parse($date);
                    return $pmDate->month == $currentMonth && $pmDate->year == $currentYear;
                }
                return false;
            });

            return $item;
        });

        return Excel::download(new MaintenanceExport($data), 'maintenance_contracts.xlsx');
    }

}

