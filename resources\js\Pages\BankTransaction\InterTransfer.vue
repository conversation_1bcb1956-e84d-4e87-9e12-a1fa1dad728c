<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdownNew.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['bankinfo', 'accounttype']);

const form = useForm('post', '/saveinternalbanktransfer', {
    from_bank: '',
    from_bank_name: '',
    to_bank: '',
    to_bank_name: '',
    account_type:'',
    date:'',
    // note:'',
    amount:''
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

const setFromBank = (id, name) => {
    form.from_bank = id;
    form.from_bank_name = name;
    form.errors.from_bank = null;
};

const setToBank = (id, name) => {
    form.to_bank = id;
    form.to_bank_name = name;
    form.errors.to_bank = null;
};

const setAccountType = (id, name) => {
    form.account_type = id;
    form.errors.to_bank = null;
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

</script>

<template>
    <Head title="Internal Bank Transaction" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Internal Bank Transaction</h1>
                </div>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">
                        <div class="sm:col-span-3">
                            <InputLabel for="from_bank" value="From Bank"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="bankinfo"
                                    v-model="form.from_bank"
                                    @onchange="setFromBank"
                                    :class="{ 'error rounded-md': form.errors.from_bank }"
                                />
                                <InputError  v-if="form.invalid('from_bank')" class="" :message="form.errors.from_bank" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="to_bank" value="To Bank"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="bankinfo"
                                    v-model="form.to_bank"
                                    @onchange="setToBank"
                                    :class="{ 'error rounded-md': form.errors.to_bank }"
                                />
                                <InputError  v-if="form.invalid('to_bank')" class="" :message="form.errors.to_bank" />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="form.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                                <InputError  v-if="form.invalid('date')" class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="amount" value="Amount" />
                            <TextInput
                                id="amount"
                                type="text"
                                @change="clearError('data.amount')"
                                v-model="form.amount"
                                :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                            />
                            <InputError  v-if="form.invalid('amount')" class="" :message="form.errors.amount" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="account_type" value="Account Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="accounttype"
                                    v-model="form.account_type"
                                    @onchange="setAccountType"
                                    :class="{ 'error rounded-md': form.errors.account_type }"
                                />
                                <InputError  v-if="form.invalid('account_type')" class="" :message="form.errors.account_type" />
                            </div>
                        </div>
                        <!-- <div class="sm:col-span-6">
                            <InputLabel for="note" value="Narration" />
                            <TextInput
                                id="note"
                                type="text"
                                v-model="form.note"
                                :class="{ 'error rounded-md': form.errors[`data.note`] }"
                            />
                            <InputError  v-if="form.invalid('note')" class="" :message="form.errors.note" />
                        </div> -->
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('banktransaction.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>

