<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class EmailTags extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'email_tags';

    protected static $logName = 'Email Tags';

    public function getLogDescription(string $event): string
    {
        return "Email-Tag has been {$event} for <strong>{$this->name}</strong> by";
    }

    protected static $logAttributes = [
        'name',
        'description',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'name',
        'description',
        'created_by',
        'updated_by'
    ];

}
