import{_ as bt,b as kt,a as Z}from"./AdminLayout-42d5bb92.js";import{_ as St}from"./CreateButton-1fa2a774.js";import{_ as $}from"./SecondaryButton-be49842d.js";import{D as Ct}from"./DangerButton-3e1103de.js";import{P as J}from"./PrimaryButton-0d76f021.js";import{M as L}from"./Modal-54f7c77a.js";import{_ as Tt}from"./Pagination-56593f88.js";import{_ as E}from"./SimpleDropdown-5dc59147.js";import{_ as It}from"./SearchableDropdown-6058bf5f.js";import{_ as F}from"./SearchableDropdownNew-ade24f0e.js";import{K as zt,r as h,l as Bt,o as a,c as r,a as c,u as B,w as _,F as P,Z as Nt,b as t,g as S,f as u,i as H,e as M,t as o,n as X,s as Pt,x as Mt}from"./app-2ecbacfc.js";import"./html2canvas.esm-8f0b674a.js";import{_ as C}from"./InputLabel-f62a278f.js";import{_ as Ot}from"./ArrowIcon-0ab7616b.js";import{s as jt}from"./sortAndSearch-94154e09.js";import{_ as Vt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const s=i=>(Pt("data-v-177a7056"),i=i(),Mt(),i),Gt={class:"animate-top"},At={class:"flex justify-between items-center"},Ut=s(()=>t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Proforma Invoice")],-1)),qt={class:"flex justify-end"},$t={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},Lt={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Et=s(()=>t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Ft={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Ht={class:"flex justify-end"},Rt={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Kt={class:"flex justify-between mb-2"},Wt={class:"flex"},Qt=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),Yt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Zt={class:"sm:col-span-4"},Jt={class:"relative mt-2"},Xt={class:"sm:col-span-4"},Dt={class:"relative mt-2"},te={key:0,class:"sm:col-span-4"},ee={class:"relative mt-2"},se={class:"sm:col-span-4"},oe={class:"relative mt-2"},le={class:"sm:col-span-4"},ne={class:"relative mt-2"},ae={class:"sm:col-span-4"},ie={class:"relative mt-2"},de={class:"mt-8 overflow-x-auto sm:rounded-lg"},re={class:"shadow sm:rounded-lg"},ue={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ce={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},_e={class:"border-b-2"},he=["onClick"],me={key:0},ve={class:"px-4 py-2.5 min-w-36"},pe={class:"px-4 py-2.5 min-w-44"},ge={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},ye={class:"px-4 py-2.5 min-w-52"},fe={class:"px-4 py-2.5 min-w-32"},xe={class:"px-4 py-2.5 min-w-32"},we={class:"flex flex-1 items-center px-4 py-2.5"},be={class:"items-center px-4 py-2.5"},ke={class:"flex items-center justify-start gap-4"},Se=s(()=>t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Ce=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Te=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Ie=["onClick"],ze=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Be=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Ne=[ze,Be],Pe=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Me=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," View PI ",-1)),Oe=["onClick"],je=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 22v-8m0 0l4 4m-4-4l-4 4 M12 2v8m0 0l4-4m-4 4l-4-4"})],-1)),Ve=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Update Status ",-1)),Ge=[je,Ve],Ae=["onClick"],Ue=s(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),qe=s(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),$e=[Ue,qe],Le={key:1},Ee=s(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Fe=[Ee],He={class:"p-6"},Re=s(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Ke={class:"mt-6 flex justify-end"},We={class:"p-6"},Qe=s(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to close this proforma invoice ? ",-1)),Ye={class:"mt-6 flex justify-end space-x-3"},Ze={class:"w-32"},Je={class:"p-6"},Xe={id:"pdf-content"},De={class:"container1"},ts={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},es=["src"],ss=s(()=>t("p",null,[t("strong",{style:{"font-size":"20px"}},"Proforma invoice")],-1)),os=s(()=>t("h1",null,[t("div",{style:{width:"120px"}})],-1)),ls={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},ns=["src"],as=s(()=>t("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[t("p",{style:{"font-size":"20px"}},[t("strong",null,"Proforma invoice")])],-1)),is={style:{display:"flex","justify-content":"space-between"}},ds={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},rs={style:{"font-size":"14px","margin-top":"10px"}},us=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),cs=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),_s={style:{display:"flex"}},hs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Phone")],-1)),ms={style:{display:"flex"}},vs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Email")],-1)),ps={style:{display:"flex"}},gs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"GST")],-1)),ys={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},fs={style:{display:"flex"}},xs=s(()=>t("p",{style:{width:"120px"}},[t("strong",null,"PI Number")],-1)),ws={style:{display:"flex"}},bs=s(()=>t("p",{style:{width:"120px"}},[t("strong",null,"PI Date")],-1)),ks=s(()=>t("p",{style:{"margin-bottom":"10px"}},[t("strong")],-1)),Ss={style:{"font-size":"14px","margin-top":"10px"}},Cs=s(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),Ts={style:{display:"flex"}},Is=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"Phone")],-1)),zs={style:{display:"flex"}},Bs=s(()=>t("p",{style:{width:"40px"}},[t("strong",null,"GST")],-1)),Ns={style:{"overflow-x":"auto"}},Ps=s(()=>t("th",null,"SN",-1)),Ms=s(()=>t("th",null,"MODEL",-1)),Os=s(()=>t("th",null,"HSN",-1)),js=s(()=>t("th",null,"DESCRIPTION",-1)),Vs=s(()=>t("th",null,"MRP",-1)),Gs=s(()=>t("th",null,"PRICE (₹)",-1)),As=s(()=>t("th",null,"QTY",-1)),Us=s(()=>t("th",null,"TOTAL PRICE (₹)",-1)),qs={key:0},$s={key:1},Ls={key:2},Es=s(()=>t("th",null,"GST (₹)",-1)),Fs=s(()=>t("th",null,"DIS.(₹)",-1)),Hs=s(()=>t("th",null,"TOTAL AMOUNT",-1)),Rs=["innerHTML"],Ks={key:0},Ws={key:1},Qs={key:2},Ys={class:"",style:{"margin-bottom":"10px","justify-items":"start",width:"400"}},Zs=s(()=>t("p",null,[t("strong")],-1)),Js={style:{display:"flex","justify-content":"space-between"}},Xs={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},Ds=s(()=>t("p",null,[t("strong",null,"OUR BANK DETAILS")],-1)),to={key:0,style:{display:"flex"}},eo=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),so={key:1,style:{display:"flex"}},oo=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),lo={key:2,style:{display:"flex"}},no=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),ao={key:3,style:{display:"flex"}},io=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),ro={key:4,style:{display:"flex"}},uo=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),co={key:5,style:{display:"flex"}},_o=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),ho={key:6,style:{display:"flex"}},mo=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),vo={key:7,style:{display:"flex"}},po=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),go={key:8,style:{display:"flex"}},yo=s(()=>t("p",null,[t("strong",null,"Bank Name")],-1)),fo={key:9,style:{display:"flex"}},xo=s(()=>t("p",null,[t("strong",null,"Branch Name")],-1)),wo={key:10,style:{display:"flex"}},bo=s(()=>t("p",null,[t("strong",null,"Account No")],-1)),ko={key:11,style:{display:"flex"}},So=s(()=>t("p",null,[t("strong",null,"IFSC Code")],-1)),Co={style:{display:"flex"}},To=s(()=>t("p",null,[t("strong",null,"GSTIN")],-1)),Io={style:{display:"flex"}},zo=s(()=>t("p",null,[t("strong",null,"PAN")],-1)),Bo={style:{display:"flex"}},No=s(()=>t("p",null,[t("strong",null,"Payment Terms")],-1)),Po={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Mo={style:{display:"flex"}},Oo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Sub Total (₹)")],-1)),jo={style:{display:"flex"}},Vo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total Discount (₹)")],-1)),Go={key:0,style:{display:"flex"}},Ao=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total IGST (₹):")],-1)),Uo={key:1,style:{display:"flex"}},qo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total CGST (₹):")],-1)),$o={key:2,style:{display:"flex"}},Lo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total SGST (₹):")],-1)),Eo={style:{display:"flex"}},Fo=s(()=>t("p",{style:{width:"105px"}},[t("strong",null,"Total Amount (₹)")],-1)),Ho={style:{display:"flex","justify-content":"space-between"}},Ro=s(()=>t("div",{class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},null,-1)),Ko={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Wo=s(()=>t("p",null,[t("strong",null,"FOR,")],-1)),Qo=["src"],Yo={class:"mt-6 px-4 flex justify-end"},Zo={class:"flex flex-col justify-end space-y-6"},Jo={class:"flex items-center space-x-2"},Xo={class:"flex justify-end"},Do={class:"w-36"},tl={__name:"List",props:["data","permissions","administration","quotationbank","quotationHealthCareBankinfo","quotationNoxBank","organization","customers","salesuser","category","organizationId","customerId","salesUserId","categoryId","createdBy","statusId","piStatus","pagetypes"],setup(i){const v=i,{form:O,search:el,sort:D,fetchData:sl,sortKey:tt,sortDirection:et,updateParams:st}=jt("proforma-invoice.index",{organization_id:v.organizationId,customer_id:v.customerId,sales_user_id:v.salesUserId,category:v.categoryId,created_by:v.createdBy,status:v.status}),j=zt().props.filepath.view,l=h([]),V=h(!1);h(!1);const R=h(null),K=h(null),ot=[{field:"order_number",label:"PI NUMBER",sortable:!0},{field:"quotation.quotation_number",label:"QUOTATION NUMBER",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"users.first_name",label:"SALES PERSON",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],lt=n=>{R.value=n,V.value=!0},G=()=>{V.value=!1},nt=()=>{O.delete(route("proforma-invoice.destroy",{id:R.value}),{onSuccess:()=>G()})},p=h(v.organizationId),g=h(v.customerId),y=h(v.salesUserId),f=h(v.categoryId),x=h(v.createdBy),w=h(v.statusId),T=h("");Bt([p,g,y,f,x,w],()=>{st({organization_id:p.value,customer_id:g.value,sales_user_id:y.value,category:f.value,created_by:x.value,status:w.value})});const I=(n,d,e,k,b,z,Y)=>{T.value=n,O.get(route("proforma-invoice.index",{search:n,organization_id:d,customer_id:e,sales_user_id:k,category:b,created_by:z,status:Y}),{preserveState:!0})},at=(n,d)=>{p.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},it=(n,d)=>{g.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},dt=(n,d)=>{f.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},rt=(n,d)=>{y.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},ut=(n,d)=>{x.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},ct=(n,d)=>{w.value=n,I(T.value,p.value,g.value,y.value,f.value,x.value,w.value)},A=h(!1),_t=h("custom"),ht=n=>{const d=v.data.data.find(e=>e.id===n);l.value=d,A.value=!0},W=()=>{A.value=!1},mt=n=>{switch(n){case"Open":return"bg-blue-100";case"Close":return"bg-red-100";default:return"bg-red-100"}},vt=n=>{switch(n){case"Open":return"text-blue-600";case"Close":return"text-red-600";default:return"text-red-600"}},N=h("portrait"),pt=(n,d)=>{N.value=n},gt=(n,d)=>{window.open(`/proforma-invoice/download/${n}/${d}`,"_blank")},Q=n=>{const d=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return d.toLocaleDateString("en-US",e)},m=n=>{let d=n.toFixed(2).toString(),[e,k]=d.split("."),b=e.substring(e.length-3),z=e.substring(0,e.length-3);return z!==""&&(b=","+b),`${z.replace(/\B(?=(\d{2})+(?!\d))/g,",")+b}.${k}`},yt=n=>n&&n.length>40?n.substring(0,40)+"...":n,U=h(!1),ft=n=>{K.value=n,U.value=!0},q=()=>{U.value=!1},xt=()=>{O.get(route("pi.reject",{id:K.value}),{onSuccess:()=>q()})},wt=n=>n?n.replace(/\n/g,"<br>"):"";return(n,d)=>(a(),r(P,null,[c(B(Nt),{title:"Proforma Invoice"}),c(bt,null,{default:_(()=>[t("div",Gt,[t("div",At,[Ut,t("div",qt,[t("div",$t,[t("div",Lt,[Et,t("input",{id:"search-field",onInput:d[0]||(d[0]=e=>I(e.target.value,p.value,g.value,y.value,f.value,x.value,w.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),i.permissions.canCreateOrders?(a(),r("div",Ft,[t("div",Ht,[c(St,{href:n.route("proforma-invoice.create")},{default:_(()=>[S(" Add Proforma Invoice ")]),_:1},8,["href"])])])):u("",!0)])]),t("div",Rt,[t("div",Kt,[t("div",Wt,[Qt,c(C,{for:"customer_id",value:"Filters"})])]),t("div",Yt,[t("div",Zt,[c(C,{for:"customer_id",value:"Organization Name"}),t("div",Jt,[c(E,{options:i.organization,modelValue:p.value,"onUpdate:modelValue":d[1]||(d[1]=e=>p.value=e),onOnchange:at},null,8,["options","modelValue"])])]),t("div",Xt,[c(C,{for:"customer_id",value:"Customer Name"}),t("div",Dt,[c(F,{options:i.customers,modelValue:g.value,"onUpdate:modelValue":d[2]||(d[2]=e=>g.value=e),onOnchange:it},null,8,["options","modelValue"])])]),i.permissions.canCreateOrders?(a(),r("div",te,[c(C,{for:"customer_id",value:"Sales Person"}),t("div",ee,[c(F,{options:i.salesuser,modelValue:y.value,"onUpdate:modelValue":d[3]||(d[3]=e=>y.value=e),onOnchange:rt},null,8,["options","modelValue"])])])):u("",!0),t("div",se,[c(C,{for:"customer_id",value:"Category"}),t("div",oe,[c(E,{options:i.category,modelValue:f.value,"onUpdate:modelValue":d[4]||(d[4]=e=>f.value=e),onOnchange:dt},null,8,["options","modelValue"])])]),t("div",le,[c(C,{for:"customer_id",value:"Created By"}),t("div",ne,[c(F,{options:i.salesuser,modelValue:x.value,"onUpdate:modelValue":d[5]||(d[5]=e=>x.value=e),onOnchange:ut},null,8,["options","modelValue"])])]),t("div",ae,[c(C,{for:"customer_id",value:"Status"}),t("div",ie,[c(E,{options:i.piStatus,modelValue:w.value,"onUpdate:modelValue":d[6]||(d[6]=e=>w.value=e),onOnchange:ct},null,8,["options","modelValue"])])])])]),t("div",de,[t("div",re,[t("table",ue,[t("thead",ce,[t("tr",_e,[(a(),r(P,null,H(ot,(e,k)=>t("th",{key:k,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:b=>B(D)(e.field,e.sortable)},[S(o(e.label)+" ",1),e.sortable?(a(),M(Ot,{key:0,isSorted:B(tt)===e.field,direction:B(et)},null,8,["isSorted","direction"])):u("",!0)],8,he)),64))])]),i.data.data&&i.data.data.length>0?(a(),r("tbody",me,[(a(!0),r(P,null,H(i.data.data,(e,k)=>{var b;return a(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",ve,o(e.order_number),1),t("td",pe,o(((b=e==null?void 0:e.quotation)==null?void 0:b.quotation_number)??"-"),1),t("th",ge,o(yt(e.customers.customer_name)??"-"),1),t("td",ye,o(e.users.first_name)+" "+o(e.users.last_name),1),t("td",fe,o(Q(e.date)),1),t("td",xe,o(m(e.total_amount)),1),t("td",we,[t("div",{class:X(["flex rounded-full px-4 py-1",mt(e.status)])},[t("span",{class:X(["text-sm font-semibold",vt(e.status)])},o(e.status),3)],2)]),t("td",be,[t("div",ke,[c(kt,{align:"right",width:"48"},{trigger:_(()=>[Se]),content:_(()=>[e.status=="Open"&&i.permissions.canEditOrders?(a(),M(Z,{key:0,href:n.route("proforma-invoice.edit",{id:e.id})},{svg:_(()=>[Ce]),text:_(()=>[Te]),_:2},1032,["href"])):u("",!0),i.permissions.canDeleteOrders?(a(),r("button",{key:1,type:"button",onClick:z=>lt(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ne,8,Ie)):u("",!0),i.permissions.canViewOrders?(a(),M(Z,{key:2,href:n.route("proforma-invoice.view",{id:e.id})},{svg:_(()=>[Pe]),text:_(()=>[Me]),_:2},1032,["href"])):u("",!0),e.status=="Open"&&i.permissions.canCreateOrders?(a(),r("button",{key:3,type:"button",onClick:z=>ft(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ge,8,Oe)):u("",!0),t("button",{type:"button",onClick:z=>ht(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},$e,8,Ae)]),_:2},1024)])])])}),128))])):(a(),r("tbody",Le,Fe))])])]),i.data.data&&i.data.data.length>0?(a(),M(Tt,{key:0,class:"mt-6",links:i.data.links},null,8,["links"])):u("",!0)]),c(L,{show:V.value,onClose:G},{default:_(()=>[t("div",He,[Re,t("div",Ke,[c($,{onClick:G},{default:_(()=>[S(" Cancel ")]),_:1}),c(Ct,{class:"ml-3",onClick:nt},{default:_(()=>[S(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(L,{show:U.value,onClose:q},{default:_(()=>[t("div",We,[Qe,t("div",Ye,[c($,{onClick:q},{default:_(()=>[S(" Cancel ")]),_:1}),t("div",Ze,[c(J,{onClick:xt,type:"button"},{default:_(()=>[S("Close PI")]),_:1})])])])]),_:1},8,["show"]),c(L,{show:A.value,onClose:W,maxWidth:_t.value},{default:_(()=>[t("div",Je,[t("div",Xe,[t("div",De,[l.value.organization.id=="3"?(a(),r("div",ts,[t("img",{class:"w-20 h-20",src:B(j)+l.value.organization.logo,alt:"logo"},null,8,es),ss,os])):u("",!0),l.value.organization.id=="1"||l.value.organization.id=="2"?(a(),r("div",ls,[t("img",{class:"w-full h-10",src:B(j)+l.value.organization.logo,alt:"logo"},null,8,ns),as])):u("",!0),t("div",is,[t("div",ds,[t("p",null,[t("strong",rs,o(l.value.organization.name),1)]),us,t("p",null,o(l.value.organization.address_line_1),1),t("p",null,o(l.value.organization.address_line_2),1),t("p",null,o(l.value.organization.pincode)+" , "+o(l.value.organization.city),1),cs,t("div",_s,[hs,t("p",null,": "+o(l.value.organization.contact_no),1)]),t("div",ms,[vs,t("p",null,": "+o(l.value.organization.email),1)]),t("div",ps,[gs,t("p",null,": "+o(l.value.organization.gst_no),1)])]),t("div",ys,[t("div",fs,[xs,t("p",null,": "+o(l.value.order_number),1)]),t("div",ws,[bs,t("p",null,": "+o(Q(l.value.date)),1)]),ks,t("p",null,[t("strong",Ss,o(l.value.customers.customer_name),1)]),t("p",null,o(l.value.customers.address),1),Cs,t("div",Ts,[Is,t("p",null,": "+o(l.value.customers.contact_no??"-"),1)]),t("div",zs,[Bs,t("p",null,": "+o(l.value.customers.gst_no??"-"),1)])])]),t("div",Ns,[t("table",null,[t("thead",null,[t("tr",null,[Ps,Ms,Os,js,Vs,Gs,As,Us,l.value.customers.gst_type=="IGST"?(a(),r("th",qs,"IGST (%)")):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("th",$s,"CGST (%)")):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("th",Ls,"SGST (%)")):u("",!0),Es,Fs,Hs])]),t("tbody",null,[(a(!0),r(P,null,H(l.value.proforma_invoice_details,(e,k)=>{var b;return a(),r("tr",{key:e.id},[t("td",null,o(k+1),1),t("td",null,o(e.product.item_code),1),t("td",null,o(e.product.hsn_code??"-"),1),t("td",{innerHTML:wt(e.description)},null,8,Rs),t("td",null,o((b=e.product.serial_numbers[0])!=null&&b.mrp?m(e.product.serial_numbers[0].mrp):"-"),1),t("td",null,o(m(e.price)),1),t("td",null,o(e.qty),1),t("td",null,o(m(e.total_price)),1),l.value.customers.gst_type=="IGST"?(a(),r("td",Ks,o(m(e.gst)??"-"),1)):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("td",Ws,o(m(e.gst/2)??"-"),1)):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("td",Qs,o(m(e.gst/2)??"-"),1)):u("",!0),t("td",null,o(m(e.total_gst_amount)),1),t("td",null,o(m(e.discount_amount)),1),t("td",null,o(m(e.total_amount)),1)])}),128))])])]),t("div",Ys,[Zs,t("p",null,o(l.value.note),1)]),t("div",Js,[t("div",Xs,[Ds,l.value.organization.id=="1"?(a(),r("div",to,[eo,t("p",null,": "+o(i.quotationbank.bank_name),1)])):u("",!0),l.value.organization.id=="1"?(a(),r("div",so,[oo,t("p",null,": "+o(i.quotationbank.branch_name),1)])):u("",!0),l.value.organization.id=="1"?(a(),r("div",lo,[no,t("p",null,": "+o(i.quotationbank.account_no),1)])):u("",!0),l.value.organization.id=="1"?(a(),r("div",ao,[io,t("p",null,": "+o(i.quotationbank.ifsc_code),1)])):u("",!0),l.value.organization.id=="2"?(a(),r("div",ro,[uo,t("p",null,": "+o(i.quotationHealthCareBankinfo.bank_name),1)])):u("",!0),l.value.organization.id=="2"?(a(),r("div",co,[_o,t("p",null,": "+o(i.quotationHealthCareBankinfo.branch_name),1)])):u("",!0),l.value.organization.id=="2"?(a(),r("div",ho,[mo,t("p",null,": "+o(i.quotationHealthCareBankinfo.account_no),1)])):u("",!0),l.value.organization.id=="2"?(a(),r("div",vo,[po,t("p",null,": "+o(i.quotationHealthCareBankinfo.ifsc_code),1)])):u("",!0),l.value.organization.id=="3"?(a(),r("div",go,[yo,t("p",null,": "+o(i.quotationNoxBank.bank_name),1)])):u("",!0),l.value.organization.id=="3"?(a(),r("div",fo,[xo,t("p",null,": "+o(i.quotationNoxBank.branch_name),1)])):u("",!0),l.value.organization.id=="3"?(a(),r("div",wo,[bo,t("p",null,": "+o(i.quotationNoxBank.account_no),1)])):u("",!0),l.value.organization.id=="3"?(a(),r("div",ko,[So,t("p",null,": "+o(i.quotationNoxBank.ifsc_code),1)])):u("",!0),t("div",Co,[To,t("p",null,": "+o(l.value.organization.gst_no??""),1)]),t("div",Io,[zo,t("p",null,": "+o(l.value.organization.pan_no??""),1)]),t("div",Bo,[No,t("p",null,": "+o(l.value.payment_terms),1)])]),t("div",Po,[t("div",Mo,[Oo,t("p",null,": "+o(m(l.value.sub_total)),1)]),t("div",jo,[Vo,t("p",null,": "+o(l.value.total_discount),1)]),l.value.customers.gst_type=="IGST"?(a(),r("div",Go,[Ao,t("p",null,": "+o(m(l.value.igst)),1)])):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("div",Uo,[qo,t("p",null,": "+o(m(l.value.cgst)),1)])):u("",!0),l.value.customers.gst_type=="CGST/SGST"?(a(),r("div",$o,[Lo,t("p",null,": "+o(m(l.value.sgst)),1)])):u("",!0),t("div",Eo,[Fo,t("p",null,": "+o(m(l.value.total_amount)),1)])])]),t("div",Ho,[Ro,t("div",Ko,[Wo,t("p",null,[t("strong",null,o(l.value.organization.name),1)]),t("img",{class:"h-28",src:B(j)+l.value.organization.signature,alt:"logo"},null,8,Qo)])])])]),t("div",Yo,[t("div",Zo,[t("div",Jo,[c(C,{for:"customer_id",value:"Page Type :"}),c(It,{options:i.pagetypes,modelValue:N.value,"onUpdate:modelValue":d[7]||(d[7]=e=>N.value=e),onOnchange:pt},null,8,["options","modelValue"])]),t("div",Xo,[c($,{onClick:W},{default:_(()=>[S(" Cancel ")]),_:1}),t("div",Do,[c(J,{class:"ml-3 w-20",onClick:d[8]||(d[8]=e=>gt(l.value.id,N.value))},{default:_(()=>[S(" Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},wl=Vt(tl,[["__scopeId","data-v-177a7056"]]);export{wl as default};
