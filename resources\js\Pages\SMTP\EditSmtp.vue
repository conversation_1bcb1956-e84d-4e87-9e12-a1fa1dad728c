<script setup>
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SvgLink from '@/Components/ActionLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps({
  smtp: Object,  
});

const form = useForm({
  host: props.smtp.host || '',
  port: props.smtp.port || '',
  username: props.smtp.username || '',
  password: props.smtp.password || '',
  email: props.smtp.email || '',
  encryption: props.smtp.encryption || '',
  name: props.smtp.name || '',
});

const submit = () => {
  form.put(`/mail-configs/${props.smtp.id}`, {
    onSuccess: () => {
      form.reset();
    },
  });
};

</script>


<template>
  <Head title="SMTP" />
  <AdminLayout>
  <div class="animate-top">
      <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
          <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit SMTP</h2>
          <form @submit.prevent="submit" class="">
              <div class="border-b border-gray-900/10 pb-12">
                  <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                      <div class="sm:col-span-3">
                          <InputLabel for="host" value="Host" />
                          <TextInput
                              id="host"
                              type="text"
                              v-model="form.host"
                              autocomplete="host"
                              @change="form.validate('host')"
                          />
                          <p v-if="form.errors.host" class="mt-1 text-sm text-red-500">{{ form.errors.host }}</p>
                      </div>
                      <div class="sm:col-span-3">
                          <InputLabel for="port" value="Port" />
                          <TextInput
                              id="port"
                              type="text"
                              v-model="form.port"
                              autocomplete="port"
                              @change="form.validate('port')"
                          />
                          <p v-if="form.errors.port" class="mt-1 text-sm text-red-500">{{ form.errors.port }}</p>
                      </div>

                      <div class="sm:col-span-3">
                          <InputLabel for="username" value="Username" />
                          <TextInput
                              id="username"
                              type="text"
                              v-model="form.username"
                              autocomplete="username"
                              @change="form.validate('username')"
                          />
                          <p v-if="form.errors.username" class="mt-1 text-sm text-red-500">{{ form.errors.username }}</p>
                      </div>

                      <div class="sm:col-span-3">
                          <InputLabel for="password" value="Password" />
                          <TextInput
                              id="password"
                              type="text"
                              v-model="form.password"
                              autocomplete="password"
                              @change="form.validate('password')"
                          />
                          <p v-if="form.errors.password" class="mt-1 text-sm text-red-500">{{ form.errors.password }}</p>
                      </div>

                      <div class="sm:col-span-3">
                          <InputLabel for="email" value="Email" />
                          <TextInput
                              id="email"
                              type="text"
                              v-model="form.email"
                              autocomplete="email"
                              @change="form.validate('email')"
                          />
                          <p v-if="form.errors.email" class="mt-1 text-sm text-red-500">{{ form.errors.email }}</p>
                      </div>

                      <div class="sm:col-span-3">
                          <InputLabel for="encryption" value="Encryption" />
                          <TextInput
                              id="encryption"
                              type="text"
                              v-model="form.encryption"
                              autocomplete="encryption"
                              @change="form.validate('encryption')"
                          />
                          <p v-if="form.errors.encryption" class="mt-1 text-sm text-red-500">{{ form.errors.encryption }}</p>
                      </div>

                      <div class="sm:col-span-3">
                          <InputLabel for="name" value="Name" />
                          <TextInput
                              id="name"
                              type="text"
                              v-model="form.name"
                              autocomplete="name"
                              @change="form.validate('name')"
                          />
                          <p v-if="form.errors.name" class="mt-1 text-sm text-red-500">{{ form.errors.name }}</p>
                      </div>

                  </div>
              </div>

              <div class="flex mt-6 items-center justify-between">

                  <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('mail-configs.index')">
                      <template #svg>
                           <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                        </template>
                  </SvgLink>

                  <PrimaryButton :disabled="form.processing">Save</PrimaryButton>



                  <Transition
                      enter-active-class="transition ease-in-out"
                      enter-from-class="opacity-0"
                      leave-active-class="transition ease-in-out"
                      leave-to-class="opacity-0"
                  >
                      <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                  </Transition>
                  </div>

              </div>
          </form>
      </div>
  </div>
  </AdminLayout>
</template>


<!-- <template>
  <AdminLayout>
    <Head title="Edit SMTP" />
    <div class="p-6 bg-white shadow-md rounded-lg">
      <h1 class="text-2xl font-bold mb-4">Edit SMTP</h1>

      <form @submit.prevent="submit" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="col-span-1">
          <label for="host" class="block text-sm font-medium text-gray-700">Host</label>
          <input id="host" type="text" v-model="form.host" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.host" class="mt-1 text-sm text-red-500">{{ form.errors.host }}</p>
        </div>

        <div class="col-span-1">
          <label for="port" class="block text-sm font-medium text-gray-700">Port</label>
          <input id="port" type="text" v-model="form.port" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.port" class="mt-1 text-sm text-red-500">{{ form.errors.port }}</p>
        </div>

        <div class="col-span-1">
          <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
          <input id="username" type="text" v-model="form.username" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.username" class="mt-1 text-sm text-red-500">{{ form.errors.username }}</p>
        </div>

        <div class="col-span-1">
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input id="password" type="password" v-model="form.password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.password" class="mt-1 text-sm text-red-500">{{ form.errors.password }}</p>
        </div>

        <div class="col-span-1">
          <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
          <input id="email" type="email" v-model="form.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.email" class="mt-1 text-sm text-red-500">{{ form.errors.email }}</p>
        </div>

        <div class="col-span-1">
          <label for="encryption" class="block text-sm font-medium text-gray-700">Encryption</label>
          <input id="encryption" type="text" v-model="form.encryption" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.encryption" class="mt-1 text-sm text-red-500">{{ form.errors.encryption }}</p>
        </div>

        <div class="col-span-1">
          <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
          <input id="name" type="text" v-model="form.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          <p v-if="form.errors.name" class="mt-1 text-sm text-red-500">{{ form.errors.name }}</p>
        </div>

        <div class="col-span-2 flex justify-end space-x-4 mt-4">
          <SvgLink :href="route('mail-configs.index')">
            <button type="button" class="text-sm font-semibold text-gray-900">Cancel</button>
          </SvgLink>
          <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
        </div>
      </form>

    </div>
  </AdminLayout>
</template> -->
