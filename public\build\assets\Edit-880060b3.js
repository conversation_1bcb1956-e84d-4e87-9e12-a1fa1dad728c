import{r as b,j as R,l as O,m as X,o as c,c as u,a as d,u as s,w as $,F as D,Z as ee,b as o,t as p,f as y,d as te,n as g,k as oe,v as ae,i as q,g as ne,T as se}from"./app-8a557454.js";import{_ as ie,a as le}from"./AdminLayout-301d54ca.js";import{_ as v}from"./InputLabel-07f3a6e8.js";import{P as re}from"./PrimaryButton-9d9bcdd8.js";import{_ as P}from"./TextInput-ab168ee4.js";import{_ as de}from"./TextArea-3588e81e.js";import{_ as me}from"./RadioButton-c5ab2b34.js";import{_ as T}from"./SearchableDropdown-51a69527.js";import{u as ce}from"./index-62ab7306.js";/* empty css                                                                          */import{_ as ue}from"./Checkbox-b528928a.js";import"./_plugin-vue_export-helper-c27b6911.js";const _e={class:"h-screen animate-top"},pe={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ye={class:"sm:flex sm:items-center"},ve=o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),fe={class:"flex items-center justify-between"},he={key:0,class:"text-base font-semibold leading-6 text-gray-900"},ge=["onSubmit"],xe={class:"border-b border-gray-900/10 pb-12"},be={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},ke={class:"sm:col-span-3"},we={class:"relative mt-2"},Ve={class:"sm:col-span-3"},ze={class:"relative mt-2"},Ce={class:"sm:col-span-2 hidden"},Ne={class:"relative mt-2"},Ae={key:0,class:"sm:col-span-3"},Fe={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-1"},$e={key:2,class:"sm:col-span-2"},Pe={key:3,class:"sm:col-span-2"},Te={class:"mt-4 flex justify-start"},Se={class:"text-base font-semibold"},Oe={key:4,class:"sm:col-span-3"},De={key:5,class:"sm:col-span-3"},Ie={key:6,class:"sm:col-span-3"},Be={key:7,class:"sm:col-span-3"},Ee={class:"relative mt-2"},je={class:"sm:col-span-6"},Me={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ye=o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Le={style:{"overflow-y":"auto","max-height":"318px"}},Re={class:"divide-y divide-gray-300 bg-white"},qe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Ze={class:"text-sm text-gray-900 leading-6 py-1.5"},Ge={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},He={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Je={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Ke={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},Qe={key:0,class:"text-red-500 text-xs absolute"},We={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Xe={class:"sm:col-span-2"},et={class:"mt-4 flex justify-start"},tt={class:"text-base font-semibold"},ot={key:0,class:"text-red-500 text-xs absolute"},at={key:8,class:"sm:col-span-6"},nt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},st=o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),it={class:"divide-y divide-gray-300 bg-white"},lt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},rt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},dt={class:"flex flex-col"},mt={class:"text-sm text-gray-900"},ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ut={class:"whitespace-nowrap py-3 text-sm text-gray-900"},_t={class:"flex mt-6 items-center justify-between"},pt={class:"ml-auto flex items-center justify-end gap-x-6"},yt=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),vt={key:0,class:"text-sm text-gray-600"},Ft={__name:"Edit",props:["payment","paymentType","bankinfo","organization","companies","invoices","credit"],setup(C){const l=C;b([]);const N=b([]),Z=l.bankinfo.filter(n=>n.organization_id===l.payment.organization_id);N.value=Z;const e=ce("post","/payment",{id:l.payment.id,organization_id:l.payment.organization_id,company_id:l.payment.company_id,payment_type:l.payment.payment_type,date:l.payment.date,note:l.payment.note,amount:l.payment.amount,discount_amount:l.payment.discount_amount||0,round_off:l.payment.round_off||0,check_number:l.payment.check_number,org_bank_id:l.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""});e._method="PUT";const A=b(l.payment.payment_type),G=()=>{e.settled_amount=S.value,e.advance_amount=B.value,e.total_unused_amount=k.value,e.is_credit=_.value,e.invoice=h.value,e.credit_data=f.value,e.submit({preserveScroll:!0,onSuccess:()=>{}})},H=(n,a)=>{A.value=n,e.payment_type=n,e.errors.payment_type=null,a==="Cash"?e.note="Cash":e.note==="Cash"&&(e.note="")},w=b([]),f=b([]),k=b(""),I=(n,a)=>{const t=l.bankinfo.filter(m=>m.organization_id===n);N.value=t;const i=l.invoices.filter(m=>m.purchase_order&&m.purchase_order.organization_id===n&&m.purchase_order.company_id===e.company_id);w.value=i;const r=l.credit.filter(m=>m.organization_id===n&&m.company_id===e.company_id);f.value=r,k.value=f.value.reduce((m,x)=>m+x.unused_amount,0),e.organization_id=n,e.errors.organization_id=null},J=(n,a)=>{e.company_id=n;const t=l.invoices.filter(r=>r.purchase_order&&r.purchase_order.organization_id===e.organization_id&&r.purchase_order.company_id===n);w.value=t;const i=l.credit.filter(r=>r.company_id===n&&r.organization_id===e.organization_id);f.value=i,k.value=f.value.reduce((r,m)=>r+m.unused_amount,0),e.errors.company_id=null},K=(n,a)=>{e.org_bank_id=n,e.errors.org_bank_id=null},S=R(()=>{const n=h.value.reduce((a,t)=>a+(t.check&&t.amount?parseFloat(t.amount):0),0);return parseFloat(n.toFixed(2))}),B=R(()=>{const n=parseFloat(e.amount||0),a=parseFloat(e.round_off||0),t=S.value;return n>t?n-t-a:0}),V=n=>{let a=n.toFixed(2).toString(),[t,i]=a.split("."),r=t.substring(t.length-3),m=t.substring(0,t.length-3);return m!==""&&(r=","+r),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${i}`},E=n=>{const a=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",t)},Q=(n,a)=>{const t=_.value==="Yes"?parseFloat(k.value||0):parseFloat(e.amount||0)+parseFloat(e.round_off||0);if(!h.value[a].check){h.value[a].amount=0;return}let i=t;h.value.forEach((x,z)=>{x.check&&z!==a&&(i-=parseFloat(x.amount||0))});const r=parseFloat(h.value[a].pending_amount||0),m=Math.min(r,i);h.value[a].amount=m.toFixed(2)},h=b([]),F=()=>{h.value=w.value.map(n=>{const a=l.payment.invoice_data.some(i=>i.id===n.id),t=a?l.payment.invoice_data.find(i=>i.id===n.id).amount:0;return{id:n.id,date:n.customer_invoice_date,invoice_no:n.customer_invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.original_pending_amount||n.pending_amount||0).toFixed(2),check:a,amount:a?t.toString():"0.00"}})},_=b("No");O(w,()=>{F()}),O(_,()=>{F()}),O(()=>e.amount,()=>{_.value==="No"&&F()});const U=n=>{e.errors[n]=null,e.errors.settled_amount=null},W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}];return X(()=>{I(l.payment.organization_id);const n=l.invoices.filter(i=>i.purchase_order&&i.purchase_order.organization_id===l.payment.organization_id&&i.purchase_order.company_id===l.payment.company_id);w.value=n,F();const a=l.credit.filter(i=>i.organization_id===l.payment.organization_id&&i.company_id===l.payment.company_id);f.value=a,k.value=f.value.reduce((i,r)=>i+r.unused_amount,0);const t=l.bankinfo.filter(i=>i.organization_id===l.payment.organization_id);N.value=t}),(n,a)=>(c(),u(D,null,[d(s(ee),{title:"Edit Payment"}),d(ie,null,{default:$(()=>[o("div",_e,[o("div",pe,[o("div",ye,[ve,o("div",fe,[f.value.length>0?(c(),u("div",he," Credits Available: ₹"+p(V(k.value)),1)):y("",!0)])]),o("form",{onSubmit:te(G,["prevent"]),class:""},[o("div",xe,[o("div",be,[o("div",ke,[d(v,{for:"payment_type",value:"Organization"}),o("div",we,[d(T,{options:C.organization,modelValue:s(e).organization_id,"onUpdate:modelValue":a[0]||(a[0]=t=>s(e).organization_id=t),onOnchange:I,class:g({"error rounded-md":s(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",Ve,[d(v,{for:"payment_type",value:"Company"}),o("div",ze,[d(T,{options:C.companies,modelValue:s(e).company_id,"onUpdate:modelValue":a[1]||(a[1]=t=>s(e).company_id=t),onOnchange:J,class:g({"error rounded-md":s(e).errors.company_id})},null,8,["options","modelValue","class"])])]),o("div",Ce,[d(v,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Ne,[d(me,{modelValue:_.value,"onUpdate:modelValue":a[2]||(a[2]=t=>_.value=t),options:W},null,8,["modelValue"])])]),_.value=="No"?(c(),u("div",Ae,[d(v,{for:"payment_type",value:"Payment Type"}),o("div",Fe,[d(T,{options:C.paymentType,modelValue:s(e).payment_type,"onUpdate:modelValue":a[3]||(a[3]=t=>s(e).payment_type=t),onOnchange:H,class:g({"error rounded-md":s(e).errors.payment_type})},null,8,["options","modelValue","class"])])])):y("",!0),_.value=="No"?(c(),u("div",Ue,[d(v,{for:"round_off",value:"Round Off"}),d(P,{type:"text",onChange:a[4]||(a[4]=t=>U("round_off")),modelValue:s(e).round_off,"onUpdate:modelValue":a[5]||(a[5]=t=>s(e).round_off=t),class:g({"error rounded-md":s(e).errors.round_off})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(c(),u("div",$e,[d(v,{for:"amount",value:"Amount"}),d(P,{id:"amount",type:"text",onChange:a[6]||(a[6]=t=>U("amount")),modelValue:s(e).amount,"onUpdate:modelValue":a[7]||(a[7]=t=>s(e).amount=t),class:g({"error rounded-md":s(e).errors.amount})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(c(),u("div",Pe,[d(v,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Te,[o("p",Se,p(V(B.value)),1)])])):y("",!0),A.value=="check"&&_.value=="No"?(c(),u("div",Oe,[d(v,{for:"check_number",value:"Cheque Number"}),d(P,{id:"check_number",type:"text",modelValue:s(e).check_number,"onUpdate:modelValue":a[8]||(a[8]=t=>s(e).check_number=t),class:g({"error rounded-md":s(e).errors["data.check_number"]})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(c(),u("div",De,[d(v,{for:"date",value:"Payment Date"}),oe(o("input",{"onUpdate:modelValue":a[9]||(a[9]=t=>s(e).date=t),class:g(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":s(e).errors.date}]),type:"date",onChange:a[10]||(a[10]=t=>U("date"))},null,34),[[ae,s(e).date]])])):y("",!0),A.value=="cash"&&_.value=="No"?(c(),u("div",Ie)):y("",!0),A.value!="cash"&&_.value=="No"?(c(),u("div",Be,[d(v,{for:"org_bank_id",value:"Our Bank"}),o("div",Ee,[d(T,{options:N.value,modelValue:s(e).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=t=>s(e).org_bank_id=t),onOnchange:K,class:g({"error rounded-md":s(e).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):y("",!0),o("div",je,[o("table",Me,[Ye,o("div",Le,[o("tbody",Re,[(c(!0),u(D,null,q(h.value,(t,i)=>(c(),u("tr",{key:i},[o("td",qe,[o("div",Ze,[d(ue,{name:"check",checked:t.check,"onUpdate:checked":r=>t.check=r,onChange:r=>Q(r,i)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",Ge,p(t.invoice_no),1),o("td",He,p(t.total_amount),1),o("td",Je,p(t.pending_amount),1),o("td",Ke,[d(P,{id:"amount",type:"text",modelValue:t.amount,"onUpdate:modelValue":r=>t.amount=r,onChange:r=>U("invoice."+i+".amount"),class:g({error:s(e).errors[`invoice.${i}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),s(e).errors[`invoice.${i}.amount`]?(c(),u("p",Qe,p(s(e).errors[`invoice.${i}.amount`]),1)):y("",!0)]),o("td",We,p(E(t.date)),1)]))),128))])])])]),o("div",Xe,[d(v,{for:"note",value:"Total Settled Amount"}),o("div",et,[o("p",tt,p(V(S.value)),1)]),s(e).errors.settled_amount?(c(),u("p",ot,p(s(e).errors.settled_amount),1)):y("",!0)]),_.value=="No"?(c(),u("div",at,[d(v,{for:"note",value:"Note"}),d(de,{id:"note",type:"text",rows:2,modelValue:s(e).note,"onUpdate:modelValue":a[12]||(a[12]=t=>s(e).note=t)},null,8,["modelValue"])])):y("",!0)]),f.value.length>0&&_.value=="Yes"?(c(),u("table",nt,[st,o("tbody",it,[(c(!0),u(D,null,q(f.value,(t,i)=>{var r,m,x,z,j,M,Y,L;return c(),u("tr",{key:i},[o("td",lt,p(E(t.date)),1),o("td",rt,[o("div",dt,[o("div",mt,p((m=(r=t.paymentpaid)==null?void 0:r.bank_info)!=null&&m.bank_name?(z=(x=t.paymentpaid)==null?void 0:x.bank_info)==null?void 0:z.bank_name:"Cash")+" - "+p((M=(j=t.paymentpaid)==null?void 0:j.bank_info)!=null&&M.account_number?(L=(Y=t.paymentpaid)==null?void 0:Y.bank_info)==null?void 0:L.account_number:""),1)])]),o("td",ct,p(V(t.amount)),1),o("td",ut,p(V(t.unused_amount)),1)])}),128))])])):y("",!0)]),o("div",_t,[o("div",pt,[d(le,{href:n.route("payment.index")},{svg:$(()=>[yt]),_:1},8,["href"]),d(re,{disabled:s(e).processing},{default:$(()=>[ne("Update")]),_:1},8,["disabled"]),d(se,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:$(()=>[s(e).recentlySuccessful?(c(),u("p",vt,"Saved.")):y("",!0)]),_:1})])])],40,ge)])])]),_:1})],64))}};export{Ft as default};
