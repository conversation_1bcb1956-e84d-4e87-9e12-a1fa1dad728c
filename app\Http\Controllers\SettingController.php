<?php

namespace App\Http\Controllers;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Setting;
use App\Models\Organization;
use App\Models\Document;
use App\Models\SerialNumbers;
use App\Models\Product;
use App\Models\PurchaseTransaction;
use App\Models\PurchaseOrderReceives;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseOrder;
use App\Models\InvoiceDetail;
use App\Models\Invoice;
use App\Models\ChallanDetail;
use App\Models\Challan;
use App\Models\CreditDetails;
use App\Http\Requests\OrganizationRequest;
use App\Http\Requests\PrefixRequest;
use App\Models\Company;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Auth;
use Config;
use Spatie\Activitylog\Models\Activity;
use App\Exports\StockExport;
use App\Models\PurchaseOrderReceiveDetails;
use Maatwebsite\Excel\Facades\Excel;


class SettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:Manage Prefix')->only(['managePrefix']);
        $this->middleware('permission:Sales Stock')->only(['salesstock']);
        $this->middleware('permission:Service Stock')->only(['servicestock']);
        $this->middleware('permission:List Setting')->only(['index']);
        $this->middleware('permission:Activity Log')->only(['getAllLogs', 'loadMoreLogs']);
    }

    public function numberSetting(Request $request)
    {
        $data = Setting::with('organization')->get();

        $permissions = [
            'canEditRoles'        => auth()->user()->can('Edit Roles')
        ];

        // dd($data);
        return Inertia::render('Settings/NumberSetting', ['data' => $data, 'permissions' => $permissions]);
    }

    public function updateNumber(Request $request, $id)
    {

        DB::beginTransaction();
        try {
            $request->validate([
                'number' => 'required|numeric',
            ]);

            $setting = Setting::findOrFail($id);

            // dd($setting);

            if (!$setting) {
                return redirect()->back()->with('error', 'Setting not found.');
            }

            if ($setting->number == $request->number) {
                return redirect()->back()->with('info', 'No changes made.');
            }

            $orgName = 'Unknown Organization';
            if ($setting->organization_id) {
                $organization = \App\Models\Organization::find($setting->organization_id);
                if ($organization) {
                    $orgName = $organization->name;
                }
            }

            $description = "Setting Number Updated in <b>"
            . $setting->value . " (" . $setting->type . ")</b> for <b>"
            . $orgName . "</b> from "
            . "<span style='color: red; font-weight: bold;'>" . $setting->number . "</span> to "
            . "<span style='color: green; font-weight: bold;'>" . $request->number . "</span> by";

            DB::table('activity_log')->insert([
                'log_name'     => 'Number Setting Update',
                'causer_id'    => Auth::id(),
                'causer_type'  => 'App\\Models\\User',
                'description'  => $description,
                'properties'   => '',
                'subject_type' => 'App\\Models\\Setting',
                'subject_id'   => $id,
                'created_at'   => now(),
                'updated_at'   => now(),
            ]);

            $setting->number = $request->number;
            $setting->updated_by = Auth::id();
            $setting->save();
            DB::commit();
            Redirect::to('/number-setting')->with('success', 'Number updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/number-setting')->with('error', $e->getMessage());
        }
    }

    public function getLogo(Request $request)
    {
        $data = Organization::pluck('logo');
        return response()->json(['logoUrl' => $data[0]]);
    }

    public function managePrefix(Request $request)
    {
        $data = Setting::all();
        return Inertia::render('Settings/ManagePrefix', compact('data'));
    }

    public function savePrefixInfo(PrefixRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $organizations = Organization::get()->toArray();
                foreach ($data as $type => $value) {
                        foreach($organizations as $organization){
                        $data['value'] = $value;
                        $data['type'] = $type;
                        $data['organization_id'] = $organization['id'];
                        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                        $settings = Setting::updateOrCreate(['type' => $type, 'organization_id' => $organization['id']], $data);
                    }
                }
            DB::commit();
            return Redirect::to('/manage-prefix')->with('success','Prefix Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/manage-prefix')->with('error', $e->getMessage());
        }
    }

    public function removedocument($id, $name)
    {
        DB::beginTransaction();
        try {
            $document = Document::find($id);
            if ($document) {
                $filePath = Config::get('constants.uploadFilePath.'.$name);
                $filePathToDelete = $filePath['default']. $document->name;
                if (file_exists($filePathToDelete)) {
                    unlink($filePathToDelete); // Delete the file
                }
                $document->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Document Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removeProduct($id, $modal)
    {
        DB::beginTransaction();
        try {
            $model =  'App\\Models\\' . $modal;
            $modelName = new $model;
            $record = $modelName::find($id);
            if($modal == 'ChallanDetail' || $modal == 'InvoiceDetail'){
                $getQty = SerialNumbers::where('id',  $record->serial_number_id)->first();
                $updatedQty = $getQty->sell_qty - $record->qty;
                if($getQty){
                    $updateStatus  = SerialNumbers::where('id', $record->serial_number_id)->update(['sell_qty'=> $updatedQty]);
                }
            } else if($modal == 'PurchaseOrderReceiveDetails'){
                $purchaseOrderDetails = PurchaseOrderDetail::find($record->purchase_order_detail_id);
                $serialNumbers = SerialNumbers::where('purchase_order_receive_detail_id', $record->id)->first();

                $price = $serialNumbers->purchase_price;
                $gst = $purchaseOrderDetails->gst;
                $qty = $record->receive_qty;

                $total_price = $price * $qty;
                $total_gst_amount = $price * $qty * ($gst / 100);
                $total_amount = $total_price + $total_gst_amount;

                //purchase update
                $porId = $record->purchase_order_receive_id;
                $poReceiveData = PurchaseOrderReceives::find($porId);

                PurchaseTransaction::where([
                    'entity_type' => 'purchase_invoice',
                    'entity_id' => $porId]
                )->update([
                    'amount' => $poReceiveData->total_amount - $total_amount
                ]);

                $poReceiveData->pending_amount    = $poReceiveData->pending_amount - $total_amount;
                $poReceiveData->total_amount      = $poReceiveData->total_amount - $total_amount;
                $poReceiveData->total_gst_amount  = $poReceiveData->total_gst_amount - $total_gst_amount;
                $poReceiveData->total_price       = $poReceiveData->total_price - $total_price;
                $poReceiveData->save();

                //stock removed
                $serialNumbers = SerialNumbers::where('purchase_order_receive_detail_id', $record->id)->get();
                foreach($serialNumbers as $serialNumber){
                    if($serialNumber->sell_qty == 0){
                        $serialNumber->delete();
                    } else{
                        return Redirect::back()->with('error', 'Invoice Generated Product cannot be Remove');
                    }
                }
                //purchase order status update
                $purchaseOrderReceives = PurchaseOrderReceiveDetails::where('id', $id)->first();
                $purchaseOrderDetail = PurchaseOrderDetail::where('id', $record->purchase_order_detail_id)->update(['receive_qty' => $purchaseOrderDetails->receive_qty - $purchaseOrderReceives->receive_qty]);
                $purchaseOrderDetail = PurchaseOrderDetail::where('purchase_order_id', $purchaseOrderDetails->purchase_order_id);
                $totalQty = $purchaseOrderDetail->sum('qty');
                $totalReceiveQty = $purchaseOrderDetail->sum('receive_qty');
                $status = PurchaseOrder::STATUS_OPEN;
                if ($totalReceiveQty == $totalQty) {
                    $status = PurchaseOrder::STATUS_COMPLETE;
                } elseif ($totalReceiveQty > 0) {
                    $status = PurchaseOrder::STATUS_PARTIALLY;
                }
                PurchaseOrder::where('id', $purchaseOrderDetails->purchase_order_id)->update(['status' => $status]);
            }
            $record->delete();
            DB::commit();
            return Redirect::back()->with('success','Product Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removeInvoiceDetails($id)
    {
        DB::beginTransaction();
        try {
            $record = InvoiceDetail::find($id);
            $getInvoicedQty = ChallanDetail::where('id',  $record->challan_detail_id)->first();
            $updatedQty =  $getInvoicedQty->invoiced_qty - $record->qty;
            $updateInvoicedQty  = ChallanDetail::where('id',  $record->challan_detail_id)->update(['invoiced_qty'=> $updatedQty]);

            $challanDetail = ChallanDetail::where('challan_id', $getInvoicedQty->challan_id)->where('is_receive', NULL);
            $totalQty = $challanDetail->sum('qty');
            $totalInvoicedQty = $challanDetail->sum('invoiced_qty');
            if ($totalInvoicedQty == $totalQty) {
                $status = 'Close';
            } elseif ($totalInvoicedQty > 0) {
                $status = 'In-process';
            } else {
                $status = 'Open';
            }
            $updateChallan = Challan::where('id', $getInvoicedQty->challan_id)->update([
                'status' => $status
            ]);
            $record->delete();
            DB::commit();
            return Redirect::back()->with('success','Product Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removeTransferProduct($id)
    {
        DB::beginTransaction();
        try {
            $invoiceDetail = InvoiceDetail::find($id);
            $getQty = SerialNumbers::where('id',  $invoiceDetail->serial_number_id)->first();
            $updatedQty = $getQty->sell_qty - $invoiceDetail->qty;
            if($getQty){
                $updateStatus  = SerialNumbers::where('id', $invoiceDetail->serial_number_id)->update(['sell_qty'=> $updatedQty]);
            }
            $invoice = Invoice::where('id', $invoiceDetail->invoice_id)->first();

            $purchaseOrderDetail = PurchaseOrderDetail::where(['purchase_order_id' => $invoice->purchase_order_id, 'product_id' => $invoiceDetail->product_id, 'total_amount' => $invoiceDetail->total_amount])->first();
            $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::where(['purchase_order_receive_id' => $invoice->purchase_order_receive_id, 'purchase_order_detail_id' => $purchaseOrderDetail->id])->first();
            $serialNumbers = SerialNumbers::where('purchase_order_receive_detail_id', $purchaseOrderReceiveDetail->id)->get();

            foreach($serialNumbers as $serialNumber){
                if($serialNumber->sell_qty == 0){
                    $serialNumber->delete();
                } else{
                    return Redirect::back()->with('error', 'Invoice Generated Product cannot be Remove');
                }
            }
            $purchaseOrderReceiveDetail->delete();
            $purchaseOrderDetail->delete();
            $invoiceDetail->delete();
            DB::commit();
            return Redirect::back()->with('success','Product Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removeChallanTransferProduct($id)
    {
        DB::beginTransaction();
        try {
            $challanDetail = ChallanDetail::find($id);
            $getQty = SerialNumbers::where('id',  $challanDetail->serial_number_id)->first();
            $updatedQty = $getQty->sell_qty - $challanDetail->qty;
            if($getQty){
                $updateStatus  = SerialNumbers::where('id', $challanDetail->serial_number_id)->update(['sell_qty'=> $updatedQty]);
            }
            $invoice = Challan::where('id', $challanDetail->challan_id)->first();

            $purchaseOrderDetail = PurchaseOrderDetail::where(['purchase_order_id' => $invoice->purchase_order_id, 'product_id' => $challanDetail->product_id, 'total_amount' => $challanDetail->total_amount])->first();
            $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::where(['purchase_order_receive_id' => $invoice->purchase_order_receive_id, 'purchase_order_detail_id' => $purchaseOrderDetail->id])->first();
            $serialNumbers = SerialNumbers::where('purchase_order_receive_detail_id', $purchaseOrderReceiveDetail->id)->get();

            foreach($serialNumbers as $serialNumber){
                if($serialNumber->sell_qty == 0){
                    $serialNumber->delete();
                } else{
                    return Redirect::back()->with('error', 'Invoice Generated Product cannot be Remove');
                }
            }
            $purchaseOrderReceiveDetail->delete();
            $purchaseOrderDetail->delete();
            $challanDetail->delete();
            DB::commit();
            return Redirect::back()->with('success','Product Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function activityLogs(Request $request)
    {
        $search = $request->input('search');
        $query = Activity::query();
        if (!empty($search)) {
            $searchableFields = ['log_name', 'properties', 'event', 'properties'];
            $query->where(function ($subquery) use ($searchableFields, $search) {
                foreach ($searchableFields as $field) {
                    $subquery->orWhere($field, 'like', "%$search%");
                }
            });
        }
        $data = $query->paginate(10);
        return Inertia::render('Activity/List', compact('data'));
    }

    public function salesstock(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $query = Product::with(['salesProduct' => function ($query1) use ($organizationId) {
            if ($organizationId) {
                $query1->where('organization_id', $organizationId);
            }
        }, 'company'])->where('category', 'Sales')->orderByRaw('name');

        if($companyId) {
            $query->where('company_id', $companyId);
        }

        if(!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('item_code', 'like', "$search%")
                ->orWhere('name', 'like', "%$search%");
            });
        }

        $user = auth()->user();
        $stockdata = $query->orderBy('id', 'desc')->get();
        $data = $query->paginate(20);
        $organization  = Organization::select('id', 'name')->get();
        $company  = Company::select('id', 'name')->orderByRaw('name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allOption2 = ['id' => null, 'name' => 'ALL COMPANY'];
        $company->prepend($allOption2);
        $organization->prepend($allOption);
        $data->withQueryString()->links();

        $permissions = [
            'canProductHistory'  => auth()->user()->can('Product History'),
            'canStockAdd'        => auth()->user()->can('Stock Add'),
            'canStockEdit'       => auth()->user()->can('Stock Edit'),
        ];

        return Inertia::render('Settings/SalesStock', compact('data', 'permissions', 'user', 'stockdata', 'organization', 'company', 'organizationId', 'companyId'));
    }

    
    public function exportExcel(Request $request)
    {
        $data = $request->input();
    
        $organizationName = null;
        if (!empty($data['organizationId']) && $data['organizationId'] != "null") {
            $organization = Organization::find($data['organizationId']);
            $organizationName = $organization ? $organization->name : null;
        }
    
        $companyName = null;
        if (!empty($data['companyId']) && $data['companyId'] != "null") {
            $company = Company::find($data['companyId']);
            $companyName = $company ? $company->name : null;
        }
    
        $stockList = SerialNumbers::leftJoin('products', 'products.id', '=', 'serial_numbers.product_id')
            ->leftJoin('companies', 'companies.id', '=', 'products.company_id')
            ->select(
                'companies.name as company_name',
                'products.name as product_name',
                'products.item_code as product_code',
                'serial_numbers.mrp',
                'serial_numbers.purchase_price',
                DB::raw('(receive_qty - sell_qty) as stock'),
                'batch'
            )
            ->whereRaw('(receive_qty - sell_qty) > 0');
    
        if ($data['organizationId'] != "null") {
            $stockList->where('organization_id', $data['organizationId']);
        }
    
        if ($data['companyId'] != "null") {
            $stockList->where('products.company_id', $data['companyId']);
        }
    
        $stockList->where('products.category', $data['category']);
        $allData = $stockList->get()->toArray();
        return Excel::download(new StockExport($allData, $organizationName, $companyName), 'stock.xlsx');
    }

    public function servicestock(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');

        $query = Product::with(['salesProduct' => function ($query) use ($organizationId) {
            if ($organizationId) {
                $query->where('organization_id', $organizationId);
            }
        }, 'company'])->where('category', 'Service')->orderByRaw('name');

        if ($companyId) {
            $query->where('company_id', $companyId);
        }

        if(!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('name', 'like', "$search%")
                ->orWhere('item_code', 'like', "$search%");
            });
        }
        $user = auth()->user();
        $stockdata = $query->orderBy('id', 'desc')->get();
        $data = $query->orderByRaw('name')->paginate(20);
        $organization  = Organization::select('id', 'name')->get();
        $company  = Company::select('id', 'name')->orderByRaw('name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allOption2 = ['id' => null, 'name' => 'ALL COMPANY'];
        $company->prepend($allOption2);
        $organization->prepend($allOption);
        $data->withQueryString()->links();

        $permissions = [
            'canProductHistory'  => auth()->user()->can('Product History'),
            'canStockAdd'        => auth()->user()->can('Stock Add'),
            'canStockEdit'       => auth()->user()->can('Stock Edit'),
        ];

        return Inertia::render('Settings/ServiceStock', compact('data', 'permissions', 'user',  'stockdata', 'organization', 'company', 'organizationId', 'companyId'));
    }

    public function index(Request $request)
    {
        $permissions = [
            'canOrganizationAdd'  => auth()->user()->can('Organization'),
            'canPermissionsAdd'   => auth()->user()->can('Roles & Permissions'),
            'canPrefixAdd'        => auth()->user()->can('Manage Prefix'),
            'canBanksAdd'         => auth()->user()->can('Banks'),
            'canAccountTypeAdd'   => auth()->user()->can('Account Type'),
            'canJobCardChecklistAdd' => auth()->user()->can('Job Card Checklist'),
            'canSMPTAdd'          => auth()->user()->can('SMPT'),
            'canEmailTemplateAdd' => auth()->user()->can('Email Template'),
            'canEmailTagsAdd' => auth()->user()->can('Email Tags'),
        ];
        return Inertia::render('Settings/Index', compact('permissions'));
    }

    protected function buildLogQuery($request)
    {
        $search   = $request->input('search', '');
        $userId   = $request->input('causer_id', null);
        $fromDate = $request->input('from_date', null);
        $toDate   = $request->input('to_date', null);
        $logName  = $request->input('log_name', null);

        $query = Activity::with(['causer', 'subject'])
            ->select('activity_log.*')
            ->latest()
            ->whereDate('activity_log.created_at', '>', '2025-02-16'); // Added filter for logs after 14-02-2025

            if (!empty($search)) {
                // Remove HTML tags before searching (using MySQL's REGEXP_REPLACE)
                $query->where(function ($q) use ($search) {
                    $q->whereRaw("REGEXP_REPLACE(description, '<[^>]+>', '') LIKE ?", ['%' . $search . '%']);
                });
            }

        if (!empty($userId)) {
            $query->where('causer_id', $userId);
        }

        if ($fromDate != '' ) {
            $query->whereDate('activity_log.created_at', '>=', $fromDate);
        }
        if ($toDate != '') {
            $query->whereDate('activity_log.created_at', '<=', $toDate);
        }
        if ($logName != '') {
            $query->where('log_name', $logName);
        }

        return $query;
    }

    public function getAllLogs(Request $request)
    {
        $query = $this->buildLogQuery($request);

        // Retrieve extra data for filters
        $user = User::where(['status' => '1'])
            ->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'id')
            ->orderByRaw('first_name')
            ->get();
        $alluser = ['id' => null, 'name' => 'ALL USERS'];
        $user->prepend($alluser);

        $logNames = Activity::select('log_name')
            ->distinct()
            ->whereNotNull('log_name')
            ->orderBy('log_name', 'asc')
            ->pluck('log_name');

        $limit = 15;
        $activityLogs = $query->limit($limit)->get();

        return Inertia::render('Activity/ActivityLog', compact('activityLogs', 'user', 'logNames'));
    }

    public function loadMoreLogs(Request $request)
    {
        $offset = $request->input('offset', 0);
        $limit  = 15;

        $query = $this->buildLogQuery($request);

        $activityLogs = $query->offset($offset)->limit($limit)->get();

        return response()->json([
            'activityLogs' => $activityLogs,
            'hasMore'      => ($activityLogs->count() === $limit)
        ]);
    }
}
