import{o as e,c as o,f as n,b as s}from"./app-497d70e1.js";const i={key:0},r={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},l=s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1),c=[l],a={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},d=s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1),_=[d],p={__name:"ArrowIcon",props:{isSorted:{type:Boolean,default:!1},direction:{type:String,default:"asc"}},setup(t){return(h,k)=>t.isSorted?(e(),o("span",i,[t.direction==="asc"?(e(),o("svg",r,c)):(e(),o("svg",a,_))])):n("",!0)}};export{p as _};
