import{_ as o}from"./AdminLayout-42d5bb92.js";import i from"./DeleteUserForm-e2770d0d.js";import m from"./UpdatePasswordForm-435cc50c.js";import r from"./UpdateProfileInformationForm-93e99746.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-2ecbacfc.js";import"./DangerButton-3e1103de.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-aa79d601.js";import"./InputLabel-f62a278f.js";import"./Modal-54f7c77a.js";/* empty css                                                              */import"./SecondaryButton-be49842d.js";import"./TextInput-73b24943.js";import"./PrimaryButton-0d76f021.js";import"./TextArea-00d3f05d.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
