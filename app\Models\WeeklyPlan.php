<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WeeklyPlan extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'weekly_plan';

    protected static $logName = 'Weekly Plan';

    public function getLogDescription(string $event): string
    {

        return "Weekly plan has been {$event} for <strong>{$this->customer_name}</strong> by";
    }

    protected static $logAttributes = [
        'date',
        'customer_name',
        'dr_name',
        'place',
        'company',
        'product',
        'status',
        'close_date',
        'brief_discussion'
    ];

    protected $fillable = [
        'date',
        'customer_name',
        'dr_name',
        'place',
        'company',
        'product',
        'status',
        'close_date',
        'brief_discussion',
        'created_by',
        'updated_by'
    ];

    public function users(){
        return $this->belongsTo(User::class,'created_by','id');
    }
}
