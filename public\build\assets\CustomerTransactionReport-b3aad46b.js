import{h as M,r as m,l as P,j as R,o as x,c as y,a as d,u as k,w as N,F as S,Z as j,b as e,g as F,k as T,v as V,n as z,i as q,f as G,t as g}from"./app-2ecbacfc.js";import{_ as X}from"./AdminLayout-42d5bb92.js";import{_ as Z}from"./CreateButton-1fa2a774.js";import{_ as H}from"./SimpleDropdown-5dc59147.js";import{_ as J}from"./SearchableDropdown-6058bf5f.js";/* empty css                                                              */import{_ as v}from"./InputLabel-f62a278f.js";const K={class:"animate-top"},Q={class:"sm:flex sm:items-center"},W=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Customers Pending Amount")],-1),Y={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},ee={class:"flex items-center space-x-4"},te={class:"flex ml-6"},oe={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},se={class:"flex justify-between mb-2"},ae={class:"flex"},ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),re={class:"inline-flex items-center space-x-4 justify-end w-full"},le=["src"],ie={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},de={class:"sm:col-span-3"},ce={class:"relative mt-2"},ue={class:"sm:col-span-3"},me={class:"relative mt-2"},pe={class:"sm:col-span-3"},_e={class:"sm:col-span-3"},ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},fe={class:"shadow sm:rounded-lg"},ve=e("div",{class:"p-2 flex justify-end text-base font-semibold leading-6 text-gray-900"},null,-1),he={class:"w-full text-sm text-left rtl:text-right text-gray-500"},xe=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CUSTOMER NAME "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BILL NO "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")])],-1),ye={class:"px-4 py-2.5 font-medium text-gray-900 min-w-32"},we={class:"px-4 py-2.5 min-w-32"},be={class:"px-4 py-2.5 min-w-32"},De={class:"px-4 py-2.5 min-w-32"},ke={class:"px-4 py-2.5 min-w-44"},Ce={class:"px-4 py-2.5 min-w-32"},Ne={key:0,class:"bg-white"},Se=e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ",-1),Te=[Se],Ue={__name:"CustomerTransactionReport",props:["data","customers","organization","organizationId","customerId","creditdata"],setup(w){const p=w,b=M({}),A=m("customer tranasction"),E=m("ALL CUSTOMERS"),n=m(p.organizationId),i=m(p.customerId),f=m(""),r=m(p.fromDate||""),l=m(p.toDate||"");P([n,i,r,l],()=>{updateParams({organization_id:n.value,customer_id:i.value,from_date:r.value,to_date:l.value})});const h=(s,o,t,c,a)=>{f.value=s,b.get(route("customer-transaction.report",{search:s,organization_id:o,customer_id:t,from_date:c,to_date:a}),{preserveState:!0})},I=(s,o)=>{n.value=s,h(f.value,n.value,i.value,r.value,l.value)},B=(s,o)=>{i.value=s,E.value=o,h(f.value,n.value,i.value,r.value,l.value)},C=R(()=>{let s=p.data.filter(o=>{const t=new Date(o.date),c=new Date(r.value),a=new Date(l.value);if(r.value&&l.value)return t>=c&&t<=a;const _=!r.value||t>=c,u=!l.value||t<=a;return _&&u});return n.value&&(s=s.filter(o=>o.organization_id===n.value)),i.value&&(s=s.filter(o=>o.customer_id===i.value)),s}),D=s=>{if(s==null)return"-";let o=s.toFixed(2).toString(),[t,c]=o.split("."),a=t.substring(t.length-3),_=t.substring(0,t.length-3);return _!==""&&(a=","+a),`${_.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${c}`},L=()=>{const s=A.value.replace(/\s+/g,"_"),o={customer_id:p.customerId||"",organization_id:n.value||"",from_date:r.value||"",to_date:l.value||""},c=`/export/customer-report?${new URLSearchParams(o).toString()}`;fetch(c,{method:"GET"}).then(a=>{if(!a.ok)throw new Error("Network response was not ok");return a.blob()}).then(a=>{const _=window.URL.createObjectURL(new Blob([a])),u=document.createElement("a");u.href=_,u.setAttribute("download",`${s}.xlsx`),document.body.appendChild(u),u.click(),document.body.removeChild(u)}).catch(a=>{console.error("Error exporting data:",a)})},O=s=>{const o=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},U=()=>{h(f.value,n.value,i.value,r.value,l.value)},$=()=>{h(f.value,n.value,i.value,r.value,l.value)};return(s,o)=>(x(),y(S,null,[d(k(j),{title:"Customers Pending Amount"}),d(X,null,{default:N(()=>[e("div",K,[e("div",Q,[W,e("div",Y,[e("div",ee,[e("div",te,[d(Z,{href:s.route("reports")},{default:N(()=>[F(" Back ")]),_:1},8,["href"])])])])]),e("div",oe,[e("div",se,[e("div",ae,[ne,d(v,{for:"customer_id",value:"Filters"})]),e("div",re,[e("button",{onClick:L},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,le)])])]),e("div",ie,[e("div",de,[d(v,{for:"customer_id",value:"Organization Name"}),e("div",ce,[d(H,{options:w.organization,modelValue:n.value,"onUpdate:modelValue":o[0]||(o[0]=t=>n.value=t),onOnchange:I},null,8,["options","modelValue"])])]),e("div",ue,[d(v,{for:"customer_id",value:"Customer Name"}),e("div",me,[d(J,{options:w.customers,modelValue:i.value,"onUpdate:modelValue":o[1]||(o[1]=t=>i.value=t),onOnchange:B},null,8,["options","modelValue"])])]),e("div",pe,[d(v,{for:"date",value:"From Date"}),T(e("input",{"onUpdate:modelValue":o[2]||(o[2]=t=>r.value=t),class:z(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(b).errors.from_date}]),type:"date",onChange:U},null,34),[[V,r.value]])]),e("div",_e,[d(v,{for:"date",value:"To Date"}),T(e("input",{"onUpdate:modelValue":o[3]||(o[3]=t=>l.value=t),class:z(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(b).errors.to_date}]),type:"date",onChange:$},null,34),[[V,l.value]])])])]),e("div",ge,[e("div",fe,[ve,e("table",he,[xe,e("tbody",null,[(x(!0),y(S,null,q(C.value,(t,c)=>(x(),y("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",ye,g(t.customer?t.customer.customer_name:"-"),1),e("td",we,g(t.invoice_no?t.invoice_no:"-"),1),e("td",be,g(D(t.total_amount)),1),e("td",De,g(D(t.paid_amount)),1),e("td",ke,g(O(t.date)??"-"),1),e("td",Ce,g(D(t.pending_amount)),1)]))),128)),C.value.length===0?(x(),y("tr",Ne,Te)):G("",!0)])])])])])]),_:1})],64))}};export{Ue as default};
