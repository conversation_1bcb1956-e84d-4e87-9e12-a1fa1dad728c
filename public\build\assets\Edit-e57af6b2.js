import{K as z,r as b,C as Vt,j as U,o as m,c as p,a as i,u as l,w as h,F as B,Z as Ct,b as t,t as v,k as St,v as It,d as kt,n as _,f as y,i as st,g as k,e as $t,s as Pt,x as Tt}from"./app-8a557454.js";import{_ as Ut,a as Dt}from"./AdminLayout-301d54ca.js";import{_ as Gt}from"./InputError-ccd7f9dc.js";import{_ as F}from"./InputLabel-07f3a6e8.js";import{P as ot}from"./PrimaryButton-9d9bcdd8.js";import{_ as V}from"./TextInput-ab168ee4.js";import{_ as O}from"./TextArea-3588e81e.js";import{_ as D}from"./SearchableDropdown-51a69527.js";import{D as at}from"./DangerButton-41cc1b93.js";import{_ as E}from"./SecondaryButton-e65b5ab9.js";import{M as L}from"./Modal-3bbbc3d3.js";import{_ as qt}from"./MultipleFileUpload-e5c9465d.js";import{_ as lt}from"./FileViewer-c68b7823.js";import{u as Mt}from"./index-62ab7306.js";import{_ as jt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const c=f=>(Pt("data-v-82833d63"),f=f(),Tt(),f),At={class:"animate-top"},Nt={class:"sm:flex sm:items-center"},zt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Quotation")],-1)),Bt={class:"w-auto"},Ot={class:"flex space-x-2 items-center"},Et=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Quotation Number:",-1)),Lt={class:"text-sm font-semibold text-gray-900 leading-6"},Qt={class:"flex space-x-2 items-center"},Wt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Rt=["onSubmit"],Ht={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Kt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Zt={class:"sm:col-span-4"},Jt={class:"relative mt-2"},Xt={class:"sm:col-span-4"},Yt={class:"relative mt-2"},te={class:"sm:col-span-4"},ee={class:"relative mt-2"},se={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},oe={class:"overflow-x-auto w-full"},ae={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},le=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Model",-1)),ne=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Image",-1)),ie=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),re=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),de=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),ce=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ue=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),me={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},pe={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},_e={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ge={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ve={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ye=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),xe=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),he=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),fe={class:"divide-y divide-gray-300 bg-white"},we={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},be={class:"relative mt-2"},Fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},Ve={class:""},Ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ke={key:0,class:"text-red-500 text-xs absolute"},$e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Te={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},Ue={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},De={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ge={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},je={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ae={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},Ne={class:"flex space-x-2"},ze={class:"text-sm text-gray-900 leading-6 py-1.5"},Be=["onClick"],Oe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ee=[Oe],Le={class:"flex items-center justify-between"},Qe={class:"ml-auto flex items-center justify-end gap-x-6"},We={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Re={class:"min-w-full divide-y divide-gray-300"},He=c(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT "),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),Ke={class:"divide-y divide-gray-300 bg-white"},Ze={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Je={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Xe=["onClick"],Ye=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ts=[Ye],es=["onClick"],ss=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),os=[ss],as=["onClick"],ls=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ns=[ls],is={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},rs={class:"items-center justify-between"},ds={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},cs={class:"inline-flex items-center justify-end w-full space-x-3"},us=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),ms={class:"text-base font-semibold text-gray-900"},ps={class:"inline-flex items-center justify-end w-full space-x-3"},_s=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),gs={class:"w-40"},vs={class:"inline-flex items-center justify-end w-full space-x-3"},ys=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),xs={class:"text-base font-semibold text-gray-900 w-w-32"},hs={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},fs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),ws={class:"text-base font-semibold text-gray-900"},bs={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Fs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Vs={class:"text-base font-semibold text-gray-900"},Cs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Ss=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Is={class:"text-base font-semibold text-gray-900"},ks={class:"inline-flex items-center justify-end w-full space-x-3"},$s=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Ps={class:"text-base font-semibold text-gray-900"},Ts={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Us={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ds={class:"sm:col-span-10"},Gs={class:"flex space-x-4"},qs={class:"w-full"},Ms=c(()=>t("div",{class:"w-full"},null,-1)),js={class:"w-full"},As={class:"relative mt-2"},Ns={class:"sm:col-span-10"},zs={class:"flex space-x-4"},Bs={class:"w-full"},Os={class:"w-full"},Es={class:"w-full"},Ls={class:"sm:col-span-10"},Qs={class:"flex space-x-4"},Ws={class:"w-full"},Rs={class:"w-full"},Hs={class:"flex mt-6 items-center justify-between"},Ks={class:"ml-auto flex items-center justify-end gap-x-6"},Zs=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Js={class:"p-6"},Xs=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),Ys={class:"mt-6 flex justify-end"},to={class:"p-6"},eo=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),so={class:"mt-6 flex justify-end"},oo={class:"p-6"},ao={class:"mt-6 px-4 flex justify-end"},lo={__name:"Edit",props:["customers","salesuser","products","filepath","category","organization","productpath"],setup(f){const Q=f,W=z().props.filepath.view,nt=z().props.productpath.view,d=z().props.data[0],o=Mt("post","/quotation",{note:d.note,date:d.date,selectedProductItem:[],customer_id:d.customer_id,sales_user_id:d.sales_user_id,organization_id:d.organization_id,category:d.category,total_amount:"",quotation_number:d.quotation_number,quotation_id:d.id,document:d.documents,cgst:d.cgst,sgst:d.sgst,igst:d.igst,total_gst:d.total_gst,total_discount:d.total_discount,overall_discount:d.overall_discount,sub_total:d.sub_total,validity:d.validity,delivery:d.delivery,payment_terms:d.payment_terms,warranty:d.warranty}),it=()=>{o.sub_total=K.value,o.total_discount=Z.value,o.cgst=g.value=="CGST/SGST"?I.value/2:"0",o.sgst=g.value=="CGST/SGST"?I.value/2:"0",o.igst=g.value=="IGST"?I.value:"0",o.total_gst=I.value,o.total_amount=H.value,o.selectedProductItem=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},u=b([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",quotation_detail_id:"",image:""}]);Vt(()=>{u.value=d.quotation_detail.map(s=>{var a,e;return{product_id:s.product_id,quotation_detail_id:s.id,description:s.description,qty:s.qty,price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),mrp:(e=(a=s.product)==null?void 0:a.serial_numbers[0])!=null&&e.mrp?parseFloat(s.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),discount:parseFloat(s.discount).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0"}}),u.value=d.quotation_detail.map(s=>{var e,r;const a=Q.products.find(n=>n.id===s.product_id);return{product_id:s.product_id,quotation_detail_id:s.id,description:s.description,image:a?a.image:null,qty:s.qty,price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),mrp:(r=(e=s.product)==null?void 0:e.serial_numbers[0])!=null&&r.mrp?parseFloat(s.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),discount:parseFloat(s.discount).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)||"0"}})});const g=b(d.customers.gst_type),rt=(s,a,e,r)=>{var x;const n=Q.products.find(C=>C.id===s);n&&(u.value[e].product_id=n.id,u.value[e].price=parseFloat(n.price).toFixed(2),u.value[e].description=n.description,u.value[e].mrp=(x=n==null?void 0:n.serial_numbers[0])!=null&&x.mrp?parseFloat(n.serial_numbers[0].mrp).toFixed(2):"-",u.value[e].gst=parseFloat(n.gst).toFixed(2),u.value[e].sgst=parseFloat(n.gst/2).toFixed(2),u.value[e].image=n.image,u.value[e].discount="0.00",o.errors[`selectedProductItem.${e}.product_id`]=null,o.errors[`selectedProductItem.${e}.price`]=null,S(r))},dt=()=>{u.value.push({product_id:"",description:"",qty:"",price:"",gst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",quotation_detail_id:"",image:""})},G=b(!1),R=b(null),ct=b(null),q=()=>{G.value=!1},ut=()=>{o.get(route("removeproduct",{id:R.value,model:"QuotationDetail"}),{onSuccess:()=>{q(),u.value.splice(index,1)}})},mt=(s,a)=>{a!==void 0&&a!=""?(R.value=a,ct.value=s,G.value=!0):u.value.splice(s,1)},pt=(s,a)=>{const e=parseFloat(s.price),r=parseFloat(s.discount)||0,n=g.value=="IGST"?parseFloat(s.gst):parseFloat(s.sgst*2),x=parseFloat(s.qty);let C=0,P=0;r>0?C=e*x:C=e*x*(1+n/100);const T=C*(r/100)||0,tt=e*1*(n/100),N=(e*x-T)*(n/100);r>0?P=C-T+N:P=C-T;const et=e*x;return s.total_price=isNaN(et)?"":parseFloat(et).toFixed(2),s.gst_amount=isNaN(tt)?"":parseFloat(tt).toFixed(2),s.total_gst_amount=isNaN(N)?"":parseFloat(N).toFixed(2),s.discount_amount=isNaN(T)?"":parseFloat(T).toFixed(2),isNaN(P)?"":parseFloat(P).toFixed(2)},S=(s,a)=>{s.total_amount=pt(s)},H=U(()=>{const s=Math.round(u.value.reduce((e,r)=>e+(r.total_amount?parseFloat(r.total_amount):0),0)),a=o.overall_discount?parseFloat(o.overall_discount):0;return s-a}),I=U(()=>u.value.reduce((s,a)=>s+(a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),K=U(()=>u.value.reduce((s,a)=>s+(a.total_price?parseFloat(a.total_price):0),0)),Z=U(()=>{const s=u.value.reduce((e,r)=>e+(r.discount_amount?parseFloat(r.discount_amount):0),0),a=o.overall_discount?parseFloat(o.overall_discount):0;return s+a}),w=s=>{o.errors[s]=null};U(()=>{const s=new Date(d.date),a={year:"numeric",month:"long",day:"numeric"};return s.toLocaleDateString("en-US",a)});const _t=s=>{o.document=s},M=b(!1),J=b(null),gt=s=>{J.value=s,M.value=!0},vt=()=>{o.get(route("removedocument",{id:J.value,name:"quotationDocument"}),{onSuccess:()=>{j()}})},j=()=>{M.value=!1},yt=(s,a)=>{o.sales_user_id=s},A=b(!1),X=b(null),xt=b("custom"),ht=s=>{X.value=s,A.value=!0},Y=()=>{A.value=!1},ft=s=>{const a=window.location.origin+W+s,e=document.createElement("a");e.href=a,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},$=s=>{let a=s.toFixed(2).toString(),[e,r]=a.split("."),n=e.substring(e.length-3),x=e.substring(0,e.length-3);return x!==""&&(n=","+n),`${x.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${r}`},wt=(s,a)=>{o.customer_id=s,o.errors.customer_id=null},bt=(s,a)=>{o.category=s,o.errors.category=null},Ft=(s,a)=>{o.organization_id=s,o.errors.organization_id=null};return(s,a)=>(m(),p(B,null,[i(l(Ct),{title:"Quotation"}),i(Ut,null,{default:h(()=>[t("div",At,[t("div",Nt,[zt,t("div",Bt,[t("div",Ot,[Et,t("span",Lt,v(l(d).quotation_number),1)]),t("div",Qt,[Wt,St(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>l(o).date=e),onChange:a[1]||(a[1]=e=>l(o).validate("date"))},null,544),[[It,l(o).date]])])])]),t("form",{onSubmit:kt(it,["prevent"]),class:""},[t("div",Ht,[t("div",Kt,[t("div",Zt,[i(F,{for:"company_name",value:"Organization"}),t("div",Jt,[i(D,{options:f.organization,modelValue:l(o).organization_id,"onUpdate:modelValue":a[2]||(a[2]=e=>l(o).organization_id=e),onOnchange:Ft,class:_({"error rounded-md":l(o).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Xt,[i(F,{for:"customer_id",value:"Customer Name"}),t("div",Yt,[i(D,{options:f.customers,modelValue:l(o).customer_id,"onUpdate:modelValue":a[3]||(a[3]=e=>l(o).customer_id=e),onOnchange:wt,class:_({"error rounded-md":l(o).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",te,[i(F,{for:"company_name",value:"Category"}),t("div",ee,[i(D,{options:f.category,modelValue:l(o).category,"onUpdate:modelValue":a[4]||(a[4]=e=>l(o).category=e),onOnchange:bt,class:_({"error rounded-md":l(o).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",se,[t("div",oe,[t("table",ae,[t("thead",null,[t("tr",null,[le,ne,ie,re,de,ce,ue,g.value=="IGST"?(m(),p("th",me,"IGST (%)")):y("",!0),g.value=="IGST"?(m(),p("th",pe,"IGST (₹)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",_e,"CGST (%)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",ge,"SGST (%)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",ve,"Total GST (₹)")):y("",!0),ye,xe,he])]),t("tbody",fe,[(m(!0),p(B,null,st(u.value,(e,r)=>(m(),p("tr",{key:r},[t("td",we,[t("div",be,[i(D,{options:f.products,modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,x)=>rt(n,x,r,e),onChange:a[5]||(a[5]=n=>l(o).validate("product_id")),class:_({"error rounded-md":l(o).errors[`selectedProductItem.${r}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",Fe,[t("div",Ve,[i(lt,{fileUrl:l(nt)+e.image},null,8,["fileUrl"])])]),t("td",Ce,[i(O,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,autocomplete:"description",rows:2,onChange:n=>e.validate("description"),class:_({"error rounded-md":l(o).errors[`selectedProductItem.${r}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),t("td",Se,v(e.mrp??"-"),1),t("td",Ie,[i(V,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,autocomplete:"qty",onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".qty"),class:_({error:l(o).errors[`selectedProductItem.${r}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),l(o).errors[`selectedProductItem.${r}.qty1`]?(m(),p("p",ke,v(l(o).errors[`selectedProductItem.${r}.qty1`]),1)):y("",!0)]),t("td",$e,[i(V,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":n=>e.price=n,autocomplete:"price",onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".price"),class:_({error:l(o).errors[`selectedProductItem.${r}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Pe,[t("div",Te,v(e.total_price),1)]),g.value=="IGST"?(m(),p("td",Ue,[i(V,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(m(),p("td",De,[i(V,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(m(),p("td",Ge,[i(V,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),t("td",qe,v(e.total_gst_amount),1),t("td",Me,[i(V,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>S(e,r),onChange:n=>w("selectedProductItem."+r+".discount"),class:_({error:l(o).errors[`selectedProductItem.${r}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",je,v(e.discount_amount),1),t("td",Ae,[t("div",Ne,[t("div",ze,v(e.total_amount),1),t("button",{type:"button",class:"mt-1 flex",onClick:n=>mt(r,e.quotation_detail_id)},Ee,8,Be)])])]))),128))])])]),t("div",Le,[t("div",Qe,[i(ot,{onClick:dt,type:"button"},{default:h(()=>[k("Add Product")]),_:1})])])]),l(d).documents&&l(d).documents.length>0?(m(),p("div",We,[t("table",Re,[He,t("tbody",Ke,[(m(!0),p(B,null,st(l(d).documents,(e,r)=>(m(),p("tr",{key:l(d).id,class:""},[t("td",Ze,v(e.orignal_name),1),t("td",Je,[t("button",{type:"button",onClick:n=>gt(e.id)},ts,8,Xe),t("button",{type:"button",onClick:n=>ht(e.name)},os,8,es),t("button",{type:"button",onClick:n=>ft(e.name)},ns,8,as)])]))),128))])])])):y("",!0),t("div",is,[t("div",rs,[t("div",ds,[t("div",cs,[us,t("p",ms,v($(K.value)),1)]),t("div",ps,[_s,t("div",gs,[i(V,{id:"overall_discount",type:"text",modelValue:l(o).overall_discount,"onUpdate:modelValue":a[6]||(a[6]=e=>l(o).overall_discount=e)},null,8,["modelValue"])])]),t("div",vs,[ys,t("p",xs,v($(Z.value)),1)]),g.value=="IGST"?(m(),p("div",hs,[fs,t("p",ws,v($(I.value)),1)])):y("",!0),g.value=="CGST/SGST"?(m(),p("div",bs,[Fs,t("p",Vs,v($(I.value/2)),1)])):y("",!0),g.value=="CGST/SGST"?(m(),p("div",Cs,[Ss,t("p",Is,v($(I.value/2)),1)])):y("",!0),t("div",ks,[$s,t("p",Ps,v($(H.value)),1)])])])]),t("div",Ts,[t("div",Us,[t("div",Ds,[t("div",Gs,[t("div",qs,[i(F,{for:"note",value:"Upload Documents"}),i(qt,{inputId:"document",inputName:"document",onFiles:_t})]),Ms,t("div",js,[i(F,{for:"company_name",value:"Sales Person"}),t("div",As,[i(D,{options:f.salesuser,modelValue:l(o).sales_user_id,"onUpdate:modelValue":a[7]||(a[7]=e=>l(o).sales_user_id=e),onOnchange:yt,class:_({"error rounded-md":l(o).errors.sales_user_id})},null,8,["options","modelValue","class"])])])])]),t("div",Ns,[t("div",zs,[t("div",Bs,[i(F,{for:"Validity",value:"Validity"}),i(V,{id:"price",type:"text",modelValue:l(o).validity,"onUpdate:modelValue":a[8]||(a[8]=e=>l(o).validity=e),onChange:a[9]||(a[9]=e=>w(l(o).errors.validity)),class:_({"error rounded-md":l(o).errors.validity})},null,8,["modelValue","class"])]),t("div",Os,[i(F,{for:"delivery",value:"Delivery"}),i(V,{id:"price",type:"text",modelValue:l(o).delivery,"onUpdate:modelValue":a[10]||(a[10]=e=>l(o).delivery=e),onChange:a[11]||(a[11]=e=>w(l(o).errors.delivery)),class:_({"error rounded-md":l(o).errors.delivery})},null,8,["modelValue","class"])]),t("div",Es,[i(F,{for:"warranty",value:"Warranty"}),i(V,{id:"price",type:"text",modelValue:l(o).warranty,"onUpdate:modelValue":a[12]||(a[12]=e=>l(o).warranty=e),onChange:a[13]||(a[13]=e=>w(l(o).errors.warranty)),class:_({"error rounded-md":l(o).errors.warranty})},null,8,["modelValue","class"])])])]),t("div",Ls,[t("div",Qs,[t("div",Ws,[i(F,{for:"payment_terms",value:"Payment terms"}),i(O,{id:"price",type:"text",rows:4,modelValue:l(o).payment_terms,"onUpdate:modelValue":a[14]||(a[14]=e=>l(o).payment_terms=e),onChange:a[15]||(a[15]=e=>w(l(o).errors.payment_terms)),class:_({"error rounded-md":l(o).errors.payment_terms})},null,8,["modelValue","class"])]),t("div",Rs,[i(F,{for:"note",value:"Note"}),i(O,{id:"note",type:"text",rows:4,modelValue:l(o).note,"onUpdate:modelValue":a[16]||(a[16]=e=>l(o).note=e),autocomplete:"note",onChange:a[17]||(a[17]=e=>l(o).validate("note"))},null,8,["modelValue"]),l(o).invalid("note")?(m(),$t(Gt,{key:0,class:"",message:l(o).errors.note},null,8,["message"])):y("",!0)])])])])]),t("div",Hs,[t("div",Ks,[i(Dt,{href:s.route("quotation.index")},{svg:h(()=>[Zs]),_:1},8,["href"]),i(ot,{disabled:l(o).processing},{default:h(()=>[k("Submit")]),_:1},8,["disabled"])])])],40,Rt)]),i(L,{show:G.value,onClose:q},{default:h(()=>[t("div",Js,[Xs,t("div",Ys,[i(E,{onClick:q},{default:h(()=>[k(" Cancel")]),_:1}),i(at,{class:"ml-3",onClick:ut},{default:h(()=>[k(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(L,{show:M.value,onClose:j},{default:h(()=>[t("div",to,[eo,t("div",so,[i(E,{onClick:j},{default:h(()=>[k(" Cancel")]),_:1}),i(at,{class:"ml-3",onClick:vt},{default:h(()=>[k(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(L,{show:A.value,onClose:Y,maxWidth:xt.value},{default:h(()=>[t("div",oo,[i(lt,{fileUrl:l(W)+X.value},null,8,["fileUrl"]),t("div",ao,[i(E,{onClick:Y},{default:h(()=>[k(" Cancel")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Fo=jt(lo,[["__scopeId","data-v-82833d63"]]);export{Fo as default};
