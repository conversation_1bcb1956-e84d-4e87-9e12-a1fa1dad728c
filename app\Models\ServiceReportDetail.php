<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceReportDetail extends Model
{
    use HasFactory;

    use SoftDeletes;

    use ActivityTrait;

    protected $table = 'service_report_detail';

    protected static $logName = 'Service Report Detail';

    public function getLogDescription(string $event): string
    {
        return "Service report detail has been {$event} for <strong>{$this->type}</strong> by";
    }

    protected static $logAttributes = [
        'service_report_id',
        'date',
        'type',
        'document_name',
        'original_document_name',
        'service_engineer_id',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'service_report_id',
        'date',
        'type',
        'document_name',
        'original_document_name',
        'service_engineer_id',
        'created_by',
        'updated_by'
    ];

    public function engineer(){
        return $this->belongsTo(User::class,'service_engineer_id','id');
    }

}
