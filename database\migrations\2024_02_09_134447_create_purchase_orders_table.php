<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained( table: 'companies', indexName: 'poc_id')->onDelete('cascade')->onUpdate('no action');
            $table->string('po_number');
            $table->date('date');
            $table->enum('status', ['Open', 'Partially Received', 'Completed', 'Cancelled']);
            $table->double('total_amount', 16, 2);
            $table->longText('note')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
