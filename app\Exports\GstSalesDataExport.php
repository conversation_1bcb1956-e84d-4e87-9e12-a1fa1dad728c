<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class GstSalesDataExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;

    public function __construct(array $allData, string $organizationName = 'All Organization', $fromDate = null, $toDate = null)
    {
        $this->allData = $allData;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
    }

    public function title(): string
    {
        return 'GST Sales Data';
    }

    public function headings(): array
    {
        return [
            "S.No",
            "Desc",
            "GSTIN",
            "Invoice Date",
            "Invoice No",
            "Invoice Value (₹)",
            "Local/ Central",
            "Invoice Type",
            "HSN Code",
            "Quantity",
            "Amount (₹)",
            "Taxable Amount (₹)",
            "SGST %",
            "SGST Amount (₹)",
            "CGST %",
            "CGST Amount (₹)",
            "IGST %",
            "IGST Amount (₹)",
            "Cess",
            "Total GST (₹)"
        ];
    }

    public function collection()
    {
        $columns = [];
        $sNoTax = 1;
        $sNoRetail = 1;

        // Initialize totals for all required columns
        $totalAmount = 0;
        $totalQuantity = 0;
        $totalTaxableAmount = 0;
        $totalSgstAmount = 0;
        $totalCgstAmount = 0;
        $totalIgstAmount = 0;
        $totalCess = 0;
        $totalGst = 0;

        $columns[] = ["", "B2B"];

        $fromDate = $this->fromDate ? Carbon::parse($this->fromDate) : null;
        $toDate = $this->toDate ? Carbon::parse($this->toDate) : null;

        foreach ($this->allData as $d) {
            if ($d['invoice_type'] !== 'Tax') {
                continue;
            }

            $invoiceDate = Carbon::parse($d['date']);

            if (($fromDate && $invoiceDate->lt($fromDate)) || ($toDate && $invoiceDate->gt($toDate))) {
                continue;
            }

            $formattedInvoice = $this->formatInvoice($d, $sNoTax++, 'Inventory');
            $columns = array_merge($columns, $formattedInvoice);

            // Calculate totals from each invoice row
            foreach ($formattedInvoice as $row) {
                // Amount (₹) - index 10
                if (isset($row[10]) && is_numeric($row[10])) {
                    $totalAmount += $row[10];
                }

                // Quantity - index 9
                if (isset($row[9]) && is_numeric($row[9])) {
                    $totalQuantity += $row[9];
                }

                // Taxable Amount (₹) - index 11
                if (isset($row[11]) && is_numeric($row[11])) {
                    $totalTaxableAmount += $row[11];
                }

                // SGST Amount (₹) - index 13
                if (isset($row[13]) && is_numeric($row[13])) {
                    $totalSgstAmount += $row[13];
                }

                // CGST Amount (₹) - index 15
                if (isset($row[15]) && is_numeric($row[15])) {
                    $totalCgstAmount += $row[15];
                }

                // IGST Amount (₹) - index 17
                if (isset($row[17]) && is_numeric($row[17])) {
                    $totalIgstAmount += $row[17];
                }

                // Cess - index 18
                if (isset($row[18]) && is_numeric($row[18])) {
                    $totalCess += $row[18];
                }

                // Total GST (₹) - index 19
                if (isset($row[19]) && is_numeric($row[19])) {
                    $totalGst += $row[19];
                }
            }
        }

        // dd($columns);dd

        $columns[] = ["", "B2C"];

        foreach ($this->allData as $d) {
            if ($d['invoice_type'] !== 'Retail') {
                continue;
            }

            $invoiceDate = Carbon::parse($d['date']);

            if (($fromDate && $invoiceDate->lt($fromDate)) || ($toDate && $invoiceDate->gt($toDate))) {
                continue;
            }

            $formattedInvoice = $this->formatInvoice($d, $sNoRetail++, 'Retail');
            $columns = array_merge($columns, $formattedInvoice);

            // Calculate totals from each invoice row
            foreach ($formattedInvoice as $row) {
                // Amount (₹) - index 10
                if (isset($row[10]) && is_numeric($row[10])) {
                    $totalAmount += $row[10];
                }

                // Quantity - index 9
                if (isset($row[9]) && is_numeric($row[9])) {
                    $totalQuantity += $row[9];
                }

                // Taxable Amount (₹) - index 11
                if (isset($row[11]) && is_numeric($row[11])) {
                    $totalTaxableAmount += $row[11];
                }

                // SGST Amount (₹) - index 13
                if (isset($row[13]) && is_numeric($row[13])) {
                    $totalSgstAmount += $row[13];
                }

                // CGST Amount (₹) - index 15
                if (isset($row[15]) && is_numeric($row[15])) {
                    $totalCgstAmount += $row[15];
                }

                // IGST Amount (₹) - index 17
                if (isset($row[17]) && is_numeric($row[17])) {
                    $totalIgstAmount += $row[17];
                }

                // Cess - index 18
                if (isset($row[18]) && is_numeric($row[18])) {
                    $totalCess += $row[18];
                }

                // Total GST (₹) - index 19
                if (isset($row[19]) && is_numeric($row[19])) {
                    $totalGst += $row[19];
                }
            }
        }

        // Add additional rows before the total
        // Nil Rated/Exempted row
        $nilRatedRow = array_fill(0, 20, '');
        $nilRatedRow[1] = 'Nil Rated/Exempted';
        $nilRatedRow[9] = '0';
        $nilRatedRow[10] = '0.00';
        $nilRatedRow[11] = '0.00';
        $nilRatedRow[12] = '0.00';
        $nilRatedRow[13] = '0.00';
        $nilRatedRow[14] = '0.00';
        $nilRatedRow[15] = '0.00';
        $nilRatedRow[16] = '0.00';
        $nilRatedRow[17] = '0.00';
        $nilRatedRow[18] = '0.00';
        $nilRatedRow[19] = '0.00';
        $columns[] = $nilRatedRow;

        // Export Invoices row
        $exportInvoicesRow = array_fill(0, 20, '');
        $exportInvoicesRow[1] = 'Export Invoices';
        $exportInvoicesRow[9] = '0';
        $exportInvoicesRow[10] = '0.00';
        $exportInvoicesRow[11] = '0.00';
        $exportInvoicesRow[12] = '0.00';
        $exportInvoicesRow[13] = '0.00';
        $exportInvoicesRow[14] = '0.00';
        $exportInvoicesRow[15] = '0.00';
        $exportInvoicesRow[16] = '0.00';
        $exportInvoicesRow[17] = '0.00';
        $exportInvoicesRow[18] = '0.00';
        $exportInvoicesRow[19] = '0.00';
        $columns[] = $exportInvoicesRow;

        // Tax Liability on Advance row
        $taxLiabilityRow = array_fill(0, 20, '');
        $taxLiabilityRow[1] = 'Tax Liability on Advance';
        $taxLiabilityRow[9] = '0';
        $taxLiabilityRow[10] = '0.00';
        $taxLiabilityRow[11] = '0.00';
        $taxLiabilityRow[12] = '0.00';
        $taxLiabilityRow[13] = '0.00';
        $taxLiabilityRow[14] = '0.00';
        $taxLiabilityRow[15] = '0.00';
        $taxLiabilityRow[16] = '0.00';
        $taxLiabilityRow[17] = '0.00';
        $taxLiabilityRow[18] = '0.00';
        $taxLiabilityRow[19] = '0.00';
        $columns[] = $taxLiabilityRow;

        // Set/off Tax on Advance of prior period row
        $setOffTaxRow = array_fill(0, 20, '');
        $setOffTaxRow[1] = 'Set/off Tax on Advance of prior period';
        $setOffTaxRow[9] = '0';
        $setOffTaxRow[10] = '0.00';
        $setOffTaxRow[11] = '0.00';
        $setOffTaxRow[12] = '0.00';
        $setOffTaxRow[13] = '0.00';
        $setOffTaxRow[14] = '0.00';
        $setOffTaxRow[15] = '0.00';
        $setOffTaxRow[16] = '0.00';
        $setOffTaxRow[17] = '0.00';
        $setOffTaxRow[18] = '0.00';
        $setOffTaxRow[19] = '0.00';
        $columns[] = $setOffTaxRow;

        // Add total row with all calculated totals
        $totalRow = array_fill(0, 20, '');
        $totalRow[1] = ' Gross Total';
        $totalRow[9] = number_format($totalQuantity, 0);
        $totalRow[10] = number_format($totalAmount, 2, '.', ',');
        $totalRow[11] = number_format($totalTaxableAmount, 2, '.', ',');
        $totalRow[13] = number_format($totalSgstAmount, 2, '.', ',');
        $totalRow[15] = number_format($totalCgstAmount, 2, '.', ',');
        $totalRow[17] = number_format($totalIgstAmount, 2, '.', ',');
        $totalRow[18] = number_format($totalCess, 2, '.', ',');
        $totalRow[19] = number_format($totalGst, 2, '.', ',');
        $columns[] = $totalRow;

        return collect($columns);
    }

    private function formatInvoice($invoice, $sNo, $type)
    {
        $formattedData = [];
        $lastInvoiceNo = null;

        $customerName = $invoice['customers']['customer_name'] ?? 'N/A';
        $gstin = $invoice['customers']['gst_no'] ?? 'N/A';
        $gstType = $invoice['customers']['gst_type'] ?? 'N/A';
        $localOrCentral = ($gstType === 'IGST') ? 'Central' : 'Local';
        $invoiceDate = $invoice['date'] ?? 'N/A';
        $invoiceNo = $invoice['invoice_no'] ?? 'N/A';
        $invoiceValue = $invoice['total_amount'] ?? 0;
        // $taxableAmount = $invoice['sub_total'] ?? 0;
        // These variables are used in the invoice details processing
        // $sgst = $invoice['sgst'] ?? 0;
        // $cgst = $invoice['cgst'] ?? 0;
        // $igst = $invoice['igst'] ?? 0;
        // $totalGst = $invoice['total_gst'] ?? 0;

    // foreach ($invoice['invoice_details'] as $detail) {
    //     $hsnCode = $detail['product']['hsn_code'] ?? 'N/A';
    //     $quantity = $detail['qty'] ?? 0;

    //     $sgstPercent = $taxableAmount > 0 ? ($sgst / $taxableAmount) * 100 : 0;
    //     $cgstPercent = $taxableAmount > 0 ? ($cgst / $taxableAmount) * 100 : 0;
    //     $igstPercent = $taxableAmount > 0 ? ($igst / $taxableAmount) * 100 : 0;

    //     $formattedData[] = [
    //         $sNo,
    //         $customerName,
    //         $gstin,
    //         $invoiceDate,
    //         $invoiceNo,
    //         $invoiceValue,
    //         $localOrCentral,
    //         $type,
    //         $hsnCode,
    //         $quantity,
    //         $invoiceValue,
    //         $taxableAmount,
    //         round($sgstPercent, 2),
    //         $sgst,
    //         round($cgstPercent, 2),
    //         $cgst,
    //         round($igstPercent, 2),
    //         $igst,
    //         0,
    //         $totalGst
    //     ];
    // }

        $hsnGrouped = [];

        foreach ($invoice['invoice_details'] as $detail) {
            $hsnCode = $detail['product']['hsn_code'] ?? 'N/A';
            $quantity = $detail['qty'] ?? 0;
            $total_price = $detail['total_price'] ?? 0;
            if (!isset($hsnGrouped[$hsnCode])) {
                $hsnGrouped[$hsnCode] = [
                    'quantity' => 0,
                    'sub_total' => 0,
                    'sgst_amount' => 0,
                    'cgst_amount' => 0,
                    'igst_amount' => 0,
                    'sgst' => 0,
                    'cgst' => 0,
                    'igst' => 0,
                    'total_gst' => 0,
                ];
            }
            // Sum the quantities and other relevant details
            $hsnGrouped[$hsnCode]['quantity'] += $quantity;
            $hsnGrouped[$hsnCode]['sub_total'] += $total_price;
            $hsnGrouped[$hsnCode]['sgst_amount'] += ($invoice['sgst'] > 0) ? $detail['total_gst_amount']/2 : 0;
            $hsnGrouped[$hsnCode]['cgst_amount'] += ($invoice['cgst'] > 0) ? $detail['total_gst_amount']/2 : 0;
            $hsnGrouped[$hsnCode]['igst_amount'] += ($invoice['igst'] > 0) ? $detail['total_gst_amount'] : 0;
            $hsnGrouped[$hsnCode]['sgst'] = ($invoice['sgst'] > 0) ? $detail['gst']/2 : 0;
            $hsnGrouped[$hsnCode]['cgst'] = ($invoice['cgst'] > 0) ? $detail['gst']/2 : 0;
            $hsnGrouped[$hsnCode]['igst'] = ($invoice['igst'] > 0) ? $detail['gst'] : 0;
            $hsnGrouped[$hsnCode]['total_gst'] += $detail['total_gst_amount'] ?? 0;
        }



        // foreach ($hsnGrouped as $hsnCode => $group) {
        //     $formattedData[] = [
        //         $sNo,
        //         $customerName,
        //         $gstin,
        //         $invoiceDate,
        //         $invoiceNo,
        //         $invoiceValue,
        //         $localOrCentral,
        //         $type,
        //         $hsnCode,
        //         $group['quantity'],
        //         $invoiceValue,
        //         $group['sub_total'],
        //         // $taxableAmount,
        //         // round($sgstPercent, 2),
        //         $group['sgst'],
        //         $group['sgst_amount'],
        //         // round($cgstPercent, 2),
        //         $group['cgst'],
        //         $group['cgst_amount'],
        //         // round($igstPercent, 2),
        //         $group['igst'],
        //         $group['igst_amount'],
        //         0,  // This value seems to be a placeholder, make sure you define it if necessary
        //         $group['total_gst']
        //     ];
        // }


        // Now loop through the grouped HSN data
        foreach ($hsnGrouped as $hsnCode => $group) {

        // Check if this is the first row for this invoice
        if ($invoiceNo !== $lastInvoiceNo) {
                $formattedData[] = [
                    $sNo,
                    $customerName,
                    $gstin,
                    $invoiceDate,
                    $invoiceNo,
                    $invoiceValue,
                    $localOrCentral,
                    $type,
                    $hsnCode,
                    $group['quantity'],
                    $invoiceValue,
                    !empty($group['sub_total']) ? $group['sub_total'] : '0.00',
                    // $taxableAmount,
                    // round($sgstPercent, 2),
                    !empty($$group['sgst']) ? $group['sgst'] : '0.00',
                    !empty($$group['sgst_amount']) ? $group['sgst_amount'] : '0.00',
                    // round($cgstPercent, 2),
                    !empty($$group['cgst']) ? $group['cgst'] : '0.00',
                    !empty($$group['cgst_amount']) ? $group['cgst_amount'] : '0.00',
                    // round($igstPercent, 2),
                    !empty($group['igst']) ? $group['igst'] : '0.00',
                    !empty($group['igst_amount']) ? $group['igst_amount'] : '0.00',
                    '0.00',  // This value seems to be a placeholder, make sure you define it if necessary
                    !empty($group['total_gst']) ? $group['total_gst']  : '0.00'
                ];
                // Save the last invoice number, customer name, and invoice value
                $lastInvoiceNo = $invoiceNo;
            } else {
                // For subsequent rows with the same invoice number, set customer name and invoice value to null
                $formattedData[] = [
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    $hsnCode,
                    $group['quantity'],
                    '0.00',
                    !empty($group['sub_total']) ? $group['sub_total'] : '0.00',
                    // $taxableAmount,
                    // round($sgstPercent, 2),
                    !empty($group['sgst']) ? $group['sgst'] : '0.00',
                    !empty($group['sgst_amount']) ? $group['sgst_amount'] : '0.00',
                    // round($cgstPercent, 2),
                    !empty($group['cgst']) ? $group['cgst'] : '0.00',
                    !empty($group['cgst_amount']) ? $group['cgst_amount'] : '0.00',
                    // round($igstPercent, 2),
                    !empty($group['igst']) ? $group['igst'] : '0.00',
                    !empty($group['igst_amount']) ? $group['igst_amount'] : '0.00',
                    '0.00',  // This value seems to be a placeholder, make sure you define it if necessary
                    $group['total_gst']
                ];
            }
        }

        return $formattedData;

    }

    public function startCell(): string
    {
        return 'A5';
    }


    public function styles(Worksheet $sheet)
    {
        // Merge cells for the header information
        $sheet->mergeCells('A1:T1');
        $sheet->mergeCells('A2:T2');
        $sheet->mergeCells('A3:T3');
        $sheet->mergeCells('A4:T4');

        $sheet->setCellValue('A1', $this->organizationName);
        $sheet->setCellValue('A2', '1301/1302, 13TH FLOOR, LINK INSPIRED WORKSPACE, OPP.VISHNUDHARA GARDENS, NR.JLR SHOWROOM, OFF.S.G.ROAD, GOTA, AHMEDABAD-382481');
        $sheet->setCellValue('A3', 'GSTIN : 24AAGCV8377E1Z1');

        $dateRange = $this->fromDate && $this->toDate
            ? "FOR THE PERIOD " . Carbon::parse($this->fromDate)->format('d-m-Y') . " TO " . Carbon::parse($this->toDate)->format('d-m-Y')
            : '';

        $sheet->setCellValue('A4', 'GSTR1 DETAILS ' . $dateRange);

        $styleArray = [
            'font' => [
                'bold' => true,
                'size' => 14,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet->getStyle('A1')->applyFromArray($styleArray);

        $sheet->getStyle('A2:A3')->applyFromArray([
            'font' => [
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        $sheet->getStyle('A4')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        $sheet->getStyle('A5:T5')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'FFFFCC',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);

        $highestRow = $sheet->getHighestRow();

        foreach (range(6, $highestRow) as $row) {
            $sheet->getStyle("A$row:T$row")->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ]);

            if ($row % 2 == 0) {
                $sheet->getStyle("A$row:T$row")->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('F9F9F9');
            } else {
                $sheet->getStyle("A$row:T$row")->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('FFFFFF');
            }
        }

        // Style the additional rows (Nil Rated, Export Invoices, etc.)
        $additionalRowsStartRow = $highestRow - 4; // 4 additional rows before total

        for ($row = $additionalRowsStartRow; $row < $highestRow; $row++) {
            $sheet->getStyle("A{$row}:T{$row}")->applyFromArray([
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'F0F0F0', // Light gray background
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT,
                ],
            ]);

            // Format number cells and set right alignment for numeric values
            $sheet->getStyle("J{$row}:T{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle("J{$row}")->getNumberFormat()->setFormatCode('#,##0'); // Quantity as whole number

            // Set right alignment for all numeric cells
            $sheet->getStyle("J{$row}:T{$row}")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        }

        // Style the total row
        $sheet->getStyle("A{$highestRow}:T{$highestRow}")->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'FFFF00',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        // Format the total cells with proper number formatting
        // Quantity - column J
        $sheet->getStyle("J{$highestRow}")->getNumberFormat()->setFormatCode('#,##0');

        // Amount (₹) - column K
        $sheet->getStyle("K{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Taxable Amount (₹) - column L
        $sheet->getStyle("L{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // SGST Amount (₹) - column N
        $sheet->getStyle("N{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // CGST Amount (₹) - column P
        $sheet->getStyle("P{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // IGST Amount (₹) - column R
        $sheet->getStyle("R{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Cess - column S
        $sheet->getStyle("S{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        // Total GST (₹) - column T
        $sheet->getStyle("T{$highestRow}")->getNumberFormat()->setFormatCode('#,##0.00');

        return [];
    }
}
