import{o,c as i,a as d,u as p,w as x,F as r,Z as y,b as t,d as f,t as s,g as u,i as h,f as _}from"./app-8a557454.js";import{_ as g}from"./AdminLayout-301d54ca.js";import{_ as w}from"./CreateButton-d5560e12.js";const b={class:"animate-top h-screen"},v={class:"sm:flex sm:items-center"},j=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Challan Detail")],-1),C={class:"flex items-center space-x-4"},N={class:"text-sm font-semibold text-gray-900"},k={class:"flex justify-end w-20"},S={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},D={class:"inline-flex items-start space-x-6 justify-start w-full"},T={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},V={class:"inline-flex items-center justify-start w-full space-x-2"},B=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1),E={class:"text-sm leading-6 text-gray-700"},F={class:"inline-flex items-center justify-start w-full space-x-2"},I=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1),q={class:"text-sm leading-6 text-gray-700"},A={class:"inline-flex items-center justify-start w-full space-x-2"},L=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1),P={class:"text-sm leading-6 text-gray-700"},Q={class:"inline-flex items-center justify-start w-full space-x-2"},O=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1),U={class:"text-sm leading-6 text-gray-700"},$={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},z={class:"inline-flex items-center justify-start w-full space-x-2"},G=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Challan Number:",-1),H={class:"text-sm leading-6 text-gray-700"},M={class:"inline-flex items-center justify-start w-full space-x-2"},R=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Challan Date:",-1),Z={class:"text-sm leading-6 text-gray-700"},J={class:"inline-flex items-center justify-start w-full space-x-2"},K=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1),W={class:"text-sm leading-6 text-gray-700"},X={class:"inline-flex items-center justify-start w-full space-x-2"},Y=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Sales Person:",-1),tt={class:"text-sm leading-6 text-gray-700"},st={class:"mt-8 overflow-x-auto sm:rounded-lg"},et={class:"shadow sm:rounded-lg"},at={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ot=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Sr No"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Product Code"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Product Description"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"HSN Code"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Batch"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Qty"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Invoiced Qty"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"Return Qty")])],-1),it={class:"px-4 py-2.5 min-w-20"},lt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},nt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-normal min-w-60"},ct={class:"px-4 py-2.5 font-medium whitespace-nowrap min-w-32"},dt={class:"px-4 py-2.5 min-w-22"},rt={class:"px-4 py-2.5 min-w-24"},mt={class:"px-4 py-2.5 min-w-36"},xt={class:"px-4 py-2.5 min-w-32"},ht={class:"mt-6 sm:flex sm:items-center"},_t={key:0,class:"text-2xl font-semibold leading-7 text-gray-900"},pt={class:"flow-root"},yt={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},ft={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden",style:{"min-height":"500px"}},ut={key:0,class:"p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},gt={class:"min-w-full divide-y divide-gray-300"},wt=t("thead",{class:"bg-gray-50 border"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"INVOICE NO"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"STATUS"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"DATE")])],-1),bt={class:"divide-y divide-gray-300 bg-white"},vt={class:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},jt={class:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Ct={class:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Nt={class:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Tt={__name:"View",props:["data"],setup(e){const m=l=>{const n=new Date(l),a={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",a)};return(l,n)=>(o(),i(r,null,[d(p(y),{title:"Challan"}),d(g,null,{default:x(()=>[t("div",b,[t("form",{onSubmit:n[0]||(n[0]=f((...a)=>l.submit&&l.submit(...a),["prevent"])),class:""},[t("div",v,[j,t("div",C,[t("div",null,[t("p",N,s(e.data[0].organization.name),1)]),t("div",k,[d(w,{href:l.route("challan.index")},{default:x(()=>[u(" Back ")]),_:1},8,["href"])])])]),t("div",S,[t("div",D,[t("div",T,[t("div",V,[B,t("p",E,s(e.data[0].customers.customer_name??"-"),1)]),t("div",F,[I,t("p",q,s(e.data[0].customers.gst_no??"-"),1)]),t("div",A,[L,t("p",P,s(e.data[0].customers.email??"-"),1)]),t("div",Q,[O,t("p",U,s(e.data[0].customers.contact_no??"-"),1)])]),t("div",$,[t("div",z,[G,t("p",H,s(e.data[0].challan_number??"-"),1)]),t("div",M,[R,t("p",Z,s(m(e.data[0].date)??"-"),1)]),t("div",J,[K,t("p",W,s(e.data[0].category??"-"),1)]),t("div",X,[Y,t("p",tt,s(e.data[0].users.first_name??"-")+" "+s(e.data[0].users.last_name??"-"),1)])])])]),t("div",st,[t("div",et,[t("table",at,[ot,t("tbody",null,[(o(!0),i(r,null,h(e.data[0].challan_detail,(a,c)=>(o(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:c},[t("th",it,s(c+1),1),t("th",lt,s(a.viewserialnumbers.product.item_code??"-"),1),t("th",nt,s(a.viewserialnumbers.product.name??"-"),1),t("th",ct,s(a.viewserialnumbers.product.hsn_code??"-"),1),t("th",dt,s(a.viewserialnumbers.batch??"-"),1),t("th",rt,s(a.qty??"-"),1),t("th",mt,s(a.invoiced_qty??"-"),1),t("th",xt,s(a.return_qty??"-"),1)]))),128))])])])]),t("div",ht,[e.data[0].invoice&&e.data[0].invoice.length!=0?(o(),i("h1",_t,"Invoice List")):_("",!0)]),t("div",pt,[t("div",yt,[t("div",ft,[e.data[0].invoice&&e.data[0].invoice.length>0?(o(),i("div",ut,[t("table",gt,[wt,t("tbody",bt,[(o(!0),i(r,null,h(e.data[0].invoice,(a,c)=>(o(),i("tr",{key:c,class:""},[t("td",vt,s(a.invoice_no),1),t("td",jt,s(parseFloat(a.total_amount).toFixed(2)),1),t("td",Ct,s(a.status),1),t("td",Nt,s(m(a.date)),1)]))),128))])])])):_("",!0)])])])],32)])]),_:1})],64))}};export{Tt as default};
