<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductLog extends Model
{
    use HasFactory;

    protected $fillable = ['product_id', 'type', 'log_data', 'created_by', 'organization_id'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /*public function purchaseOrderDetail()
    {
        return $this->hasMany(PurchaseOrderDetail::class);
    }

    public function purchaseOrderReceives()
    {
        return $this->hasMany(PurchaseOrderReceives::class);
    }*/

}
