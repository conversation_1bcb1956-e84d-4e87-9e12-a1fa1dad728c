<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_cards', function (Blueprint $table) {
            $table->id();
            $table->string('job_card_number');
            $table->integer('engineer_id');
            $table->string('hospital_name');
            $table->string('address')->nullable();
            $table->string('city');
            $table->bigInteger('contact_no')->nullable();
            $table->string('product_name');
            $table->string('product_code')->nullable();
            $table->string('serial_no')->nullable();
            $table->string('accessories')->nullable();
            $table->longText('problem_description')->nullable();
            $table->date('date');
            $table->enum('job_status', ['Repaired', 'Not Repaired', 'Return As It Is'])->default('Not Repaired');
            $table->enum('status', ['Open', 'Close']);
            $table->longText('close_note')->nullable();
            $table->integer('close_by')->nullable();
            $table->date('close_date')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_cards');
    }
};
