<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\PaymentPaid;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseOrderReceives;
use App\Models\SerialNumbers;
use App\Models\BankInfo;
use App\Models\Company;
use App\Models\User;
use App\Models\PurchaseOrderReceiveDetails;
use App\Models\PurchaseTransaction;
use App\Repositories\CompanyRepository;
use App\Traits\CommonTrait;
use App\Traits\FileUploadTrait;
use Config;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use App\Traits\QueryTrait;
use PDF;

class CompanyService
{
    use CommonTrait;
    use FileUploadTrait;
    use QueryTrait;

    protected $companyRepository;

    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    /* List Company PO */
    public function getCompanyPurchaseOrderList($request)
    {
        $searchText = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $categoryId = $request->input('category');
        $salesUserId = $request->input('sales_user_id');
        $typeId = $request->input('type');

        $query = $this->companyRepository->getCompanyPurchaseOrderList($request,$searchText, $organizationId, $companyId, $categoryId, $salesUserId, $typeId);

        $searchableFields = ['po_number', 'sales_order_no', 'category', 'company.name', 'date', 'total_amount', 'status'];
        // $this->searchAndSort($query, $request, $searchableFields);
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $organization  = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();
        $category = Config::get('constants.quotationList');
        $types = Config::get('constants.purchaseTypeList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL COMPANY'];
        $companies->prepend($allOption2);
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $salesuser->prepend($allSalesuser);

        // $data->withQueryString()->links();
        $paymentType = Config::get('constants.paymentType');
        $bankinfo  = BankInfo::select('id', 'bank_name as name', 'organization_id')->get();

        $pagetypes = Config::get('constants.perPage');
        $data = $query->paginate($pagetypes)->withQueryString();

        $permissions = [
            'canCreateCompanypo'      => auth()->user()->can('Create Companypo'),
            'canEditCompanypo'        => auth()->user()->can('Edit Companypo'),
            'canDeleteCompanypo'      => auth()->user()->can('Delete Companypo'),
            'canViewCompanypo'        => auth()->user()->can('View Companypo'),
            'canReceiveCompanypo'     => auth()->user()->can('Receive Companypo'),
            'canPaymentCompanypo'     => auth()->user()->can('Payment Companypo'),
        ];

        return Inertia::render('CompanyPo/List', compact('data', 'types', 'typeId', 'permissions', 'filepath', 'organization','paymentType', 'bankinfo', 'companies', 'organizationId', 'companyId', 'category', 'categoryId', 'salesuser', 'salesUserId', 'pagetypes'));
    }

    /* Create Company PO */
    public function createCompanyPurchaseOrder()
    {
        $po_number = $this->generatePONumber();
        $companies = $this->companyRepository->getActiveCompanies();
        $products  = Product::selectRaw("id as id, IFNULL(CONCAT(item_code,' : ', name), name) AS name, description, gst, hsn_code, company_id, category, item_code, price")->get();
        $organization = $this->companyRepository->getOrganization();
        $category = Config::get('constants.productCategoryList');
        $type = Config::get('constants.purchaseTypeList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('CompanyPo/Add', compact('po_number', 'companies', 'organization', 'category', 'type', 'products', 'salesuser'));
    }

    /* Edit Company PO */
    public function editCompanyPurchaseOrder($id)
    {
        $data = $this->companyRepository->getCompanyPurchaseOrderData($id);
        $filepath = Config::get('constants.uploadFilePath.purchaseOrderDocument');
        $companies = $this->companyRepository->getActiveCompanies();
        $products  = Product::selectRaw("id as id, IFNULL(CONCAT(item_code,' : ', name), name) AS name, description, gst, hsn_code, company_id, category, item_code, price")->get();
        $organization = $this->companyRepository->getOrganization();
        $category = Config::get('constants.productCategoryList');
        $type = Config::get('constants.purchaseTypeList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('CompanyPo/Edit', compact('data', 'category', 'type', 'filepath', 'organization', 'companies', 'products', 'salesuser'));
    }

    /* Delete Company PO */
    public function deleteCompanyPurchaseOrder($id)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::find($id);
            if($purchaseOrder){
                $purchaseOrderDetail = PurchaseOrderDetail::where('purchase_order_id', $id)->get();
                foreach ($purchaseOrderDetail as $detail) {
                    $detail->delete();
                }
            }
            $purchaseOrder->delete();
            DB::commit();
            return Redirect::back()->with('success', 'Purchase Order Removed Successfully');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    /* View Company PO */
    public function viewCompanyPurchaseOrder($id)
    {
        $data = $this->companyRepository->getCompanyPurchaseOrderData($id);
        $filepath = Config::get('constants.uploadFilePath.purchaseOrderDocument');
        $purchaseFilepath = Config::get('constants.uploadFilePath.recivePoDocument');
        $receivedOrder = $this->companyRepository->getReceivedOrderData($id);
        return Inertia::render('CompanyPo/ViewPo', compact('data', 'receivedOrder', 'filepath', 'purchaseFilepath'));
    }

    /* Receive Company PO */
    public function receiveCompanyPurchaseOrder($id)
    {
        $data = $this->companyRepository->getCompanyPurchaseOrderReceiveData($id);
        $salesuser = $this->companyRepository->getSalesUserList();
        $filepath = Config::get('constants.uploadFilepath.recivePoDocument');
        $po_receive_number = $this->generatePOReceiveNumber();
        return Inertia::render('CompanyPo/ReceivePo', compact('data', 'po_receive_number', 'salesuser', 'filepath'));
    }

    /* Save Company PO */
    public function saveCompanyCompanyPurchaseData($request)
    {
        $data = (array) $request->DTO();
        $userId = Auth::id();

        try {
            DB::beginTransaction();
            if (isset($data['purchase_order_id'])) {
                $this->updatePurchaseOrder($data, $userId);
                $this->handleFileUpload($request, $data['purchase_order_id']);
            } else {
                $poNumbers = $this->generatePONumber();
                $data['po_number'] = $poNumbers[$data['organization_id']] ?? '';

                $this->updatePONumber($data['po_number'], $data['organization_id']);
                $purchaseOrder = $this->createPurchaseOrder($data, $userId);
                $this->handleFileUpload($request, $purchaseOrder->id);

            }
            //$this->handleFileUpload($request, $purchaseOrder->id ?? $data['purchase_order_id']);

            DB::commit();
            $message = isset($purchaseOrder) ? 'Company PO Created Successfully' : 'Company PO Updated Successfully';

            return redirect('/companypo')->with('success', $message);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }

    private function updatePurchaseOrder($data, $userId)
    {
        $poID = $data['purchase_order_id'];
        $poData = PurchaseOrder::findOrFail($poID);
        $poData->update([
            'igst'              => $data['igst'],
            'sgst'              => $data['sgst'],
            'cgst'              => $data['cgst'],
            'category'          => $data['category'],
            'type'              => $data['type'],
            'total_gst'         => $data['total_gst'],
            'sub_total'         => $data['sub_total'],
            'overall_discount'      => $data['overall_discount'],
            'total_discount'      => $data['total_discount'],
            'total_amount'      => $data['total_amount'],
            'note'              => $data['note'],
            'date'              => $data['po_date'],
            'sales_user_id'     => $data['sales_user_id'],
            'organization_id'   => $data['organization_id'],
            'sales_order_no'    => $data['sales_order_no'],
            'sales_order_date'  => $data['sales_order_date'],
        ]);
        foreach ($data['selectedProductItem'] as $orderDetails) {
            $this->updateOrCreatePurchaseOrderDetail($orderDetails, $userId, $poID);
        }
    }

    private function createPurchaseOrder($data, $userId)
    {
        $data['date'] = $data['po_date'];
        $data['status'] = "Open";
        $data['created_by'] = $data['updated_by'] = $userId;
        $data['overall_discount'] = $data['overall_discount'] ?? 0;
        $purchaseOrder = PurchaseOrder::create($data);
        if($purchaseOrder){
            foreach ($data['selectedProductItem'] as $orderDetails) {
                $this->updateOrCreatePurchaseOrderDetail($orderDetails, $userId, $purchaseOrder->id);
            }
        }
        return $purchaseOrder;
    }

    private function createPurchaseTransaction($data, $userId, $purchaseOrderReceiveId)
    {
        $data['entity_id'] = $purchaseOrderReceiveId->id;
        $data['entity_type'] = 'purchase_invoice';
        $data['created_by'] = $data['updated_by'] = $userId;
        $data['date'] = $purchaseOrderReceiveId->customer_invoice_date;
        $data['amount'] = $data['total_amount'];
        $data['payment_type'] = 'cr';
        $data['company_id'] = $data['receivedProduct'][0]['company_id'];
        $data['organization_id'] = $data['receivedProduct'][0]['organization_id'];
        $data['note'] = 'Bill Number: ' .$data['customer_invoice_no']. ' ' .'Date: ' .$data['customer_invoice_date'];
        PurchaseTransaction::create($data);
    }

    private function updateOrCreatePurchaseOrderDetail($orderDetails, $userId, $purchaseOrderId)
    {
        $orderDetails['purchase_order_id'] = $purchaseOrderId;
        $orderDetails['gst'] = isset($orderDetails['gst']) ? $orderDetails['gst'] : 0;
        if (!empty($orderDetails['purchase_order_detail_id'])) {
            $orderDetails['updated_by'] = $userId;
            $poDetail = PurchaseOrderDetail::find($orderDetails['purchase_order_detail_id']);
            $poDetail->update($orderDetails);
        } else {
            $orderDetails['receive_qty'] = 0;
            $orderDetails['created_by'] = $userId;
            $orderDetails['updated_by'] = $userId;
            PurchaseOrderDetail::create($orderDetails);
        }
    }

    private function handleFileUpload($request, $purchaseOrderId)
    {
        $files = $request->file('document');
        if ($files) {
            $documentTye = PurchaseOrder::DOCUMENT_TYPE;
            $filePath = Config::get('constants.uploadFilePath.purchaseOrderDocument');
            $this->uploadDocuments($files, $filePath, $purchaseOrderId, $documentTye);
        }
    }


    /* Received Company PO */
    public function receiveCompanyPurchaseOrderData($request){
        $data = $request->all();
        $userId = Auth::id();
        $receivedBy = $data['created_by'];
        $category = $data['category'];
        $purchaseOrderId = $data['receivedProduct'][0]['purchase_order_id'];
        $organizationId = $data['receivedProduct'][0]['organization_id'];

        $poReceiveNumbers = $this->generatePOReceiveNumber();
        $poReceiveNumber = $poReceiveNumbers[$organizationId] ?? '';

        // $poReceiveNumber = $data['receivedProduct'][0]['po_receive_number'];

        DB::beginTransaction();
        try {
            $this->updatePOReceiveNumber($poReceiveNumber, $organizationId);

            $data['purchase_order_id'] = $data['receivedProduct'][0]['purchase_order_id'];
            $data['po_receive_number'] = $data['receivedProduct'][0]['po_receive_number'];
            $data['updated_by'] = $data['created_by'] = $receivedBy;
            $data['po_receive_date'] = date('Y-m-d');
            $data['pending_amount'] = $data['total_amount'];
            $purchaseOrderReceive = PurchaseOrderReceives::create($data);
            $files = $request->file('document');
            if ($files) {
                $documentType = PurchaseOrderReceives::DOCUMENT_TYPE;
                $filePath = Config::get('constants.uploadFilePath.recivePoDocument');
                $this->uploadDocuments($files, $filePath, $purchaseOrderReceive->id, $documentType);
            }

            if($data['type'] == 'invoice'){
                $this->createPurchaseTransaction($data, $userId, $purchaseOrderReceive);
            }

            foreach ($data['receivedProduct'] as $receivedProduct) {
                if ($receivedProduct['receive_qty'] > 0) {
                    $this->processReceivedProduct($receivedProduct, $receivedBy, $userId, $category, $purchaseOrderReceive->id);
                }
            }

            $this->updatePurchaseOrderStatus($purchaseOrderId);
            DB::commit();
            return redirect('/companypo')->with('success', 'Successfully Product Received.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }


    public function purchaseOrderpaymentPayData($request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data = $data['data'];
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            $receivePayment = PaymentPaid::create($data);
            $data['payment_type'] = 'dr';
            $data['note'] = 'Cheque No :' .$data['check_number']. ' ' .'PO Number:' .$data['po_number'];
            PurchaseTransaction::create($data);
            DB::commit();
            return Redirect::to('/companypo')->with('success','Payment Paid Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/companypo')->with('error', $e->getMessage());
        }
    }

    private function processReceivedProduct($receivedProduct, $receivedBy, $userId, $category, $purchaseOrderReceiveId)
    {
        $receivedProduct['purchase_order_receive_id'] = $purchaseOrderReceiveId;
        $receivedProduct['updated_by'] = $receivedProduct['created_by'] = $receivedBy;
        $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);
        if($receivedProduct['total_batch'] != 0){ //to do check if null
            foreach ($receivedProduct['productDetails'] as $productDetails) {
                $productDetails['organization_id']  = $receivedProduct['organization_id'];
                $productDetails['product_id']       = $receivedProduct['product_id'];
                $productDetails['mrp']              = $receivedProduct['mrp'];
                $productDetails['purchase_price']   = $receivedProduct['purchase_price'];
                $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                $productDetails['receive_qty']      = $productDetails['qty'];
                $productDetails['sell_qty']         = 0;
                $productDetails['created_by']       = $userId;
                $productDetails['updated_by']       = $userId;
                $productDetails['unique_id']        = $productDetails['batch'];
                $createProduct = SerialNumbers::create($productDetails);
                Product::addProductLog($createProduct, $productDetails, $receivedProduct['product_id'], $receivedProduct['organization_id'], 'received');
            }
        } else {
            $productDetails['organization_id']  = $receivedProduct['organization_id'];
            $productDetails['product_id']       = $receivedProduct['product_id'];
            $productDetails['mrp']              = $receivedProduct['mrp'];
            $productDetails['purchase_price']   = $receivedProduct['purchase_price'];
            $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
            $productDetails['receive_qty']      = $receivedProduct['receive_qty'];
            $productDetails['sell_qty']         = 0;
            $productDetails['created_by']       = $userId;
            $productDetails['updated_by']       = $userId;
            $productDetails['unique_id']        = "(₹) " .$receivedProduct['purchase_price'];
            $createProduct = SerialNumbers::create($productDetails);
            Product::addProductLog($createProduct, $productDetails, $receivedProduct['product_id'], $receivedProduct['organization_id'], 'received');
        }
        $purchaseOrderDetail = PurchaseOrderDetail::find($receivedProduct['purchase_order_detail_id']);
        $purchaseOrderDetail->update([
            'receive_qty' => $purchaseOrderDetail->receive_qty + $receivedProduct['receive_qty']
        ]);
    }

    private function generateUniqueId($productDetails)
    {
        $fields = [$productDetails['batch']];
        return implode('_', array_filter($fields));
    }

    private function updatePurchaseOrderStatus($purchaseOrderId)
    {
        $purchaseOrderDetails = PurchaseOrderDetail::where('purchase_order_id', $purchaseOrderId);
        $totalQty = $purchaseOrderDetails->sum('qty');
        $totalReceiveQty = $purchaseOrderDetails->sum('receive_qty');

        $status = PurchaseOrder::STATUS_OPEN;
        if ($totalReceiveQty == $totalQty) {
            $status = PurchaseOrder::STATUS_COMPLETE;
        } elseif ($totalReceiveQty > 0) {
            $status = PurchaseOrder::STATUS_PARTIALLY;
        }
        PurchaseOrder::where('id', $purchaseOrderId)->update(['status' => $status]);
    }

    public function downloadPurchaseOrder($id, $type)
    {
        $data = PurchaseOrder::where('id', $id)->with('purchaseOrderDetail.product', 'documents', 'company', 'organization')->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $pdf = PDF::loadView('pdf.companypo', compact('data', 'filepath'))->setPaper('A4', $type);
        $sanitizedFilename = $this->sanitizeFilename($data[0]->company->name);
        return $pdf->download("Purchase_Order_{$sanitizedFilename}.pdf");
        // return $pdf->download("purchase_order_{$id}.pdf");
        // return $pdf->stream();
    }
}
