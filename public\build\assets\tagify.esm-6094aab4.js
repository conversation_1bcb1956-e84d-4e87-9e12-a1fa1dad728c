import{l as xt,m as Dt,p as St,j as It,o as Mt,e as Et,a as X,w as J,k as G,b as z,T as $,E as Q,n as Nt,B as _t,f as At,L as Ct,s as kt,x as Lt}from"./app-497d70e1.js";import{_ as Pt}from"./_plugin-vue_export-helper-c27b6911.js";const jt=t=>(kt("data-v-ea97d804"),t=t(),Lt(),t),Vt={class:"fixed inset-0 overflow-y-auto px-4 sm:px-0 z-50 right-0","scroll-region":""},Rt=jt(()=>z("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)),Ft=[Rt],Bt={__name:"NewModal",props:{show:{type:Boolean,default:!1},closeable:{type:Boolean,default:!0}},emits:["close"],setup(t,{emit:e}){const i=t;xt(()=>i.show,()=>{i.show?document.body.style.overflow="hidden":document.body.style.overflow=null});const n=()=>{i.closeable&&e("close")},s=o=>{o.key==="Escape"&&i.show&&n()};Dt(()=>document.addEventListener("keydown",s)),St(()=>{document.removeEventListener("keydown",s),document.body.style.overflow=null});const a=It(()=>({"right-slide":"w-[40%]"})[i.maxWidth]);return(o,r)=>(Mt(),Et(Ct,{to:"body"},[X($,{"leave-active-class":"duration-200"},{default:J(()=>[G(z("div",Vt,[X($,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:J(()=>[G(z("div",{class:"fixed inset-0 transform transition-all",onClick:n},Ft,512),[[Q,t.show]])]),_:1}),X($,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0 translate-x-full","enter-to-class":"opacity-100 translate-x-0","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100 translate-x-0","leave-to-class":"opacity-0 translate-x-full"},{default:J(()=>[G(z("div",{class:Nt(["bg-white overflow-hidden shadow-xl transform transition-all sm:mx-auto sm:max-w-xl right-slide",a.value])},[t.show?_t(o.$slots,"default",{key:0},void 0,!0):At("",!0)],2),[[Q,t.show]])]),_:3})],512),[[Q,t.show]])]),_:3})]))}},ee=Pt(Bt,[["__scopeId","data-v-ea97d804"]]);var lt="&#8203;";function Y(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function dt(t){return function(e){if(Array.isArray(e))return Y(e)}(t)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(t)||function(e,i){if(e){if(typeof e=="string")return Y(e,i);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Y(e,i)}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var C={isEnabled:function(){var t;return(t=window.TAGIFY_DEBUG)===null||t===void 0||t},log:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var n;this.isEnabled()&&(n=console).log.apply(n,["[Tagify]:"].concat(dt(e)))},warn:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var n;this.isEnabled()&&(n=console).warn.apply(n,["[Tagify]:"].concat(dt(e)))}},B=function(t,e,i,n){return t=""+t,e=""+e,n&&(t=t.trim(),e=e.trim()),i?t==e:t.toLowerCase()==e.toLowerCase()},ct=function(t,e){return t&&Array.isArray(t)&&t.map(function(i){return ot(i,e)})};function ot(t,e){var i,n={};for(i in t)e.indexOf(i)<0&&(n[i]=t[i]);return n}function Z(t){return new DOMParser().parseFromString(t.trim(),"text/html").body.firstElementChild}function ut(t,e){for(e=e||"previous";t=t[e+"Sibling"];)if(t.nodeType==3)return t}function j(t){return typeof t=="string"?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/`|'/g,"&#039;"):t}function E(t){var e=Object.prototype.toString.call(t).split(" ")[1].slice(0,-1);return t===Object(t)&&e!="Array"&&e!="Function"&&e!="RegExp"&&e!="HTMLUnknownElement"}function y(t,e,i){var n,s;function a(o,r){for(var l in r)if(r.hasOwnProperty(l)){if(E(r[l])){E(o[l])?a(o[l],r[l]):o[l]=Object.assign({},r[l]);continue}if(Array.isArray(r[l])){o[l]=Object.assign([],r[l]);continue}o[l]=r[l]}}return n=t,((s=Object)!=null&&typeof Symbol<"u"&&s[Symbol.hasInstance]?s[Symbol.hasInstance](n):n instanceof s)||(t={}),a(t,e),i&&a(t,i),t}function gt(){var t=[],e={},i=!0,n=!1,s=void 0;try{for(var a,o=arguments[Symbol.iterator]();!(i=(a=o.next()).done);i=!0){var r=a.value,l=!0,d=!1,c=void 0;try{for(var u,g=r[Symbol.iterator]();!(l=(u=g.next()).done);l=!0){var p=u.value;E(p)?e[p.value]||(t.push(p),e[p.value]=1):t.includes(p)||t.push(p)}}catch(h){d=!0,c=h}finally{try{l||g.return==null||g.return()}finally{if(d)throw c}}}}catch(h){n=!0,s=h}finally{try{i||o.return==null||o.return()}finally{if(n)throw s}}return t}function W(t){return String.prototype.normalize?typeof t=="string"?t.normalize("NFD").replace(/[\u0300-\u036f]/g,""):void 0:t}var ht=function(){return/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent)};function pt(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,function(t){return(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16)})}function V(t){var e;return bt.call(this,t)&&(t==null||(e=t.classList)===null||e===void 0?void 0:e.contains(this.settings.classNames.tag))}function ft(t){return bt.call(this,t)&&(t==null?void 0:t.closest(this.settings.classNames.tagSelector))}function bt(t){var e;return(t==null||(e=t.closest)===null||e===void 0?void 0:e.call(t,this.settings.classNames.namespaceSelector))===this.DOM.scope}function Tt(t,e){var i=window.getSelection();return e=e||i.getRangeAt(0),typeof t=="string"&&(t=document.createTextNode(t)),e&&(e.deleteContents(),e.insertNode(t)),t}function v(t,e,i){return t?(e&&(t.__tagifyTagData=i?e:y({},t.__tagifyTagData||{},e)),t.__tagifyTagData):(C.warn("tag element doesn't exist",{tagElm:t,data:e}),e)}function A(t){if(t&&t.parentNode){var e=t,i=window.getSelection(),n=i.getRangeAt(0);i.rangeCount&&(n.setStartAfter(e),n.collapse(!0),i.removeAllRanges(),i.addRange(n))}}function Ot(t,e){t.forEach(function(i){if(v(i.previousSibling)||!i.previousSibling){var n=document.createTextNode("​");i.before(n),e&&A(n)}})}var tt={delimiters:",",pattern:null,tagTextProp:"value",maxTags:1/0,callbacks:{},addTagOnBlur:!0,addTagOn:["blur","tab","enter"],onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,focusable:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\.|\:|\s/,mixTagsInterpolator:["[[","]]"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:function(){},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:" "},autoComplete:{enabled:!0,rightKey:!1,tabKey:!1},classNames:{namespace:"tagify",mixMode:"tagify--mix",selectMode:"tagify--select",input:"tagify__input",focus:"tagify--focus",tagNoAnimation:"tagify--noAnim",tagInvalid:"tagify--invalid",tagNotAllowed:"tagify--notAllowed",scopeLoading:"tagify--loading",hasMaxTags:"tagify--hasMaxTags",hasNoTags:"tagify--noTags",empty:"tagify--empty",inputInvalid:"tagify__input--invalid",dropdown:"tagify__dropdown",dropdownWrapper:"tagify__dropdown__wrapper",dropdownHeader:"tagify__dropdown__header",dropdownFooter:"tagify__dropdown__footer",dropdownItem:"tagify__dropdown__item",dropdownItemActive:"tagify__dropdown__item--active",dropdownItemHidden:"tagify__dropdown__item--hidden",dropdownItemSelected:"tagify__dropdown__item--selected",dropdownInital:"tagify__dropdown--initial",tag:"tagify__tag",tagText:"tagify__tag-text",tagX:"tagify__tag__removeBtn",tagLoading:"tagify__tag--loading",tagEditing:"tagify__tag--editable",tagFlash:"tagify__tag--flash",tagHide:"tagify__tag--hide"},dropdown:{classname:"",enabled:2,maxItems:10,searchKeys:["value","searchBy"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,escapeHTML:!0,highlightFirst:!0,closeOnSelect:!0,clearOnSelect:!0,position:"all",appendTarget:null},hooks:{beforeRemoveTag:function(){return Promise.resolve()},beforePaste:function(){return Promise.resolve()},suggestionClick:function(){return Promise.resolve()},beforeKeyDown:function(){return Promise.resolve()}}};function Ht(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Wt(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{},n=Object.keys(i);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(s){return Object.getOwnPropertyDescriptor(i,s).enumerable}))),n.forEach(function(s){Ht(t,s,i[s])})}return t}function Ut(t,e){return e=e??{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):function(i,n){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);n&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),s.push.apply(s,a)}return s}(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}),t}function et(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function qt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function zt(t){return function(e){if(Array.isArray(e))return et(e)}(t)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(t)||function(e,i){if(e){if(typeof e=="string")return et(e,i);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return et(e,i)}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kt(){for(var t in this.dropdown={},this._dropdown)this.dropdown[t]=typeof this._dropdown[t]=="function"?this._dropdown[t].bind(this):this._dropdown[t];this.dropdown.refs(),this.DOM.dropdown.__tagify=this}var U,R,Xt=(U=function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{},n=Object.keys(i);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(s){return Object.getOwnPropertyDescriptor(i,s).enumerable}))),n.forEach(function(s){qt(t,s,i[s])})}return t}({},{events:{binding:function(){var t=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],e=this.dropdown.events.callbacks,i=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:e.onKeyDown.bind(this),onMouseOver:e.onMouseOver.bind(this),onMouseLeave:e.onMouseLeave.bind(this),onClick:e.onClick.bind(this),onScroll:e.onScroll.bind(this)},n=t?"addEventListener":"removeEventListener";this.settings.dropdown.position!="manual"&&(document[n]("scroll",i.position,!0),window[n]("resize",i.position),window[n]("keydown",i.onKeyDown)),this.DOM.dropdown[n]("mouseover",i.onMouseOver),this.DOM.dropdown[n]("mouseleave",i.onMouseLeave),this.DOM.dropdown[n]("mousedown",i.onClick),this.DOM.dropdown.content[n]("scroll",i.onScroll)},callbacks:{onKeyDown:function(t){var e=this;if(this.state.hasFocus&&!this.state.composing){var i=this.settings,n=i.dropdown.includeSelectedTags,s=this.DOM.dropdown.querySelector(i.classNames.dropdownItemActiveSelector),a=this.dropdown.getSuggestionDataByNode(s),o=i.mode=="mix",r=i.mode=="select";i.hooks.beforeKeyDown(t,{tagify:this}).then(function(l){switch(t.key){case"ArrowDown":case"ArrowUp":case"Down":case"Up":t.preventDefault();var d=e.dropdown.getAllSuggestionsRefs(),c=t.key=="ArrowUp"||t.key=="Up";s&&(s=e.dropdown.getNextOrPrevOption(s,!c)),s&&s.matches(i.classNames.dropdownItemSelector)||(s=d[c?d.length-1:0]),e.dropdown.highlightOption(s,!0);break;case"PageUp":case"PageDown":var u;t.preventDefault();var g=e.dropdown.getAllSuggestionsRefs(),p=Math.floor(e.DOM.dropdown.content.clientHeight/((u=g[0])===null||u===void 0?void 0:u.offsetHeight))||1,h=t.key==="PageUp";if(s){var f=g.indexOf(s),m=h?Math.max(0,f-p):Math.min(g.length-1,f+p);s=g[m]}else s=g[0];e.dropdown.highlightOption(s,!0);break;case"Home":case"End":t.preventDefault();var b=e.dropdown.getAllSuggestionsRefs();s=b[t.key==="Home"?0:b.length-1],e.dropdown.highlightOption(s,!0);break;case"Escape":case"Esc":e.dropdown.hide();break;case"ArrowRight":if(e.state.actions.ArrowLeft||i.autoComplete.rightKey)return;case"Tab":var O=!i.autoComplete.rightKey||!i.autoComplete.tabKey;if(!o&&!r&&s&&O&&!e.state.editing&&a){t.preventDefault();var S=e.dropdown.getMappedValue(a);return e.state.autoCompleteData=a,e.input.autocomplete.set.call(e,S),!1}return!0;case"Enter":t.preventDefault(),e.state.actions.selectOption=!0,setTimeout(function(){return e.state.actions.selectOption=!1},100),i.hooks.suggestionClick(t,{tagify:e,tagData:a,suggestionElm:s}).then(function(){if(s){var w=n?s:e.dropdown.getNextOrPrevOption(s,!c);e.dropdown.selectOption(s,t,function(){if(w){var _=w.getAttribute("value");w=e.dropdown.getSuggestionNodeByValue(_),e.dropdown.highlightOption(w)}})}else e.dropdown.hide(),o||e.addTags(e.state.inputText.trim(),!0)}).catch(function(w){return C.warn(w)});break;case"Backspace":if(o||e.state.editing.scope)return;var M=e.input.raw.call(e);M!=""&&M.charCodeAt(0)!=8203||(i.backspace===!0?e.removeTags():i.backspace=="edit"&&setTimeout(e.editTag.bind(e),0))}})}},onMouseOver:function(t){var e=t.target.closest(this.settings.classNames.dropdownItemSelector);this.dropdown.highlightOption(e)},onMouseLeave:function(t){this.dropdown.highlightOption()},onClick:function(t){var e=this;if(t.button==0&&t.target!=this.DOM.dropdown&&t.target!=this.DOM.dropdown.content){var i=t.target.closest(this.settings.classNames.dropdownItemSelector),n=this.dropdown.getSuggestionDataByNode(i);this.state.actions.selectOption=!0,setTimeout(function(){return e.state.actions.selectOption=!1},100),this.settings.hooks.suggestionClick(t,{tagify:this,tagData:n,suggestionElm:i}).then(function(){i?e.dropdown.selectOption(i,t):e.dropdown.hide()}).catch(function(s){return C.warn(s)})}},onScroll:function(t){var e=t.target,i=e.scrollTop/(e.scrollHeight-e.parentNode.clientHeight)*100;this.trigger("dropdown:scroll",{percentage:Math.round(i)})}}},refilter:function(t){t=t||this.state.dropdown.query||"",this.suggestedListItems=this.dropdown.filterListItems(t),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger("dropdown:updated",this.DOM.dropdown)},getSuggestionDataByNode:function(t){for(var e,i=t&&t.getAttribute("value"),n=this.suggestedListItems.length;n--;){if(E(e=this.suggestedListItems[n])&&e.value==i)return e;if(e==i)return{value:e}}},getSuggestionNodeByValue:function(t){return this.dropdown.getAllSuggestionsRefs().find(function(e){return e.getAttribute("value")===t})},getNextOrPrevOption:function(t){var e=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=this.dropdown.getAllSuggestionsRefs(),n=i.findIndex(function(s){return s===t});return e?i[n+1]:i[n-1]},highlightOption:function(t,e){var i,n=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(n),this.state.ddItemElm.removeAttribute("aria-selected")),!t)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);i=this.dropdown.getSuggestionDataByNode(t),this.state.ddItemData=i,this.state.ddItemElm=t,t.classList.add(n),t.setAttribute("aria-selected",!0),e&&(t.parentNode.scrollTop=t.clientHeight+t.offsetTop-t.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,i),this.dropdown.position())},selectOption:function(t,e,i){var n=this,s=this.settings,a=s.dropdown.includeSelectedTags,o=s.dropdown,r=o.clearOnSelect,l=o.closeOnSelect;if(!t)return this.addTags(this.state.inputText,!0),void(l&&this.dropdown.hide());e=e||{};var d=t.getAttribute("value"),c=d=="noMatch",u=s.mode=="mix",g=this.suggestedListItems.find(function(h){var f;return((f=h.value)!==null&&f!==void 0?f:h)==d});if(this.trigger("dropdown:select",{data:g,elm:t,event:e}),g||c){if(this.state.editing){var p=this.normalizeTags([g])[0];g=s.transformTag.call(this,p)||p,this.onEditTagDone(null,y({__isValid:!0},g))}else this[u?"addMixTags":"addTags"]([g||this.input.raw.call(this)],r);(u||this.DOM.input.parentNode)&&(setTimeout(function(){n.DOM.input.focus(),n.toggleFocusClass(!0)}),l&&setTimeout(this.dropdown.hide.bind(this)),a?i&&i():(t.addEventListener("transitionend",function(){n.dropdown.fillHeaderFooter(),setTimeout(function(){t.remove(),n.dropdown.refilter(),i&&i()},100)},{once:!0}),t.classList.add(this.settings.classNames.dropdownItemHidden)))}else l&&setTimeout(this.dropdown.hide.bind(this))},selectAll:function(t){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems("");var e=this.dropdown.filterListItems("");return t||(e=this.state.dropdown.suggestions),this.addTags(e,!0),this},filterListItems:function(t,e){var i,n,s,a,o,r,l=function(){var w,_,k=void 0,L=void 0;w=h[S],n=((_=Object)!=null&&typeof Symbol<"u"&&_[Symbol.hasInstance]?_[Symbol.hasInstance](w):w instanceof _)?h[S]:{value:h[S]};var T,x=Object.keys(n).some(function(D){return O.includes(D)})?O:["value"];u.fuzzySearch&&!e.exact?(a=x.reduce(function(D,I){return D+" "+(n[I]||"")},"").toLowerCase().trim(),u.accentedSearch&&(a=W(a),r=W(r)),k=a.indexOf(r)==0,L=a===r,T=a,s=r.toLowerCase().split(" ").every(function(D){return T.includes(D.toLowerCase())})):(k=!0,s=x.some(function(D){var I=""+(n[D]||"");return u.accentedSearch&&(I=W(I),r=W(r)),u.caseSensitive||(I=I.toLowerCase()),L=I===r,e.exact?I===r:I.indexOf(r)==0})),o=!u.includeSelectedTags&&i.isTagDuplicate(E(n)?n.value:n),s&&!o&&(L&&k?p.push(n):u.sortby=="startsWith"&&k?g.unshift(n):g.push(n))},d=this,c=this.settings,u=c.dropdown,g=(e=e||{},[]),p=[],h=c.whitelist,f=u.maxItems>=0?u.maxItems:1/0,m=u.includeSelectedTags,b=typeof u.sortby=="function",O=u.searchKeys,S=0;if(!(t=c.mode=="select"&&this.value.length&&this.value[0][c.tagTextProp]==t?"":t)||!O.length){g=m?h:h.filter(function(w){return!d.isTagDuplicate(E(w)?w.value:w)});var M=b?u.sortby(g,r):g.slice(0,f);return this.state.dropdown.suggestions=M,M}for(r=u.caseSensitive?""+t:(""+t).toLowerCase();S<h.length;S++)i=this,l();return this.state.dropdown.suggestions=p.concat(g),M=b?u.sortby(p.concat(g),r):p.concat(g).slice(0,f),this.state.dropdown.suggestions=M,M},getMappedValue:function(t){var e=this.settings.dropdown.mapValueTo;return e?typeof e=="function"?e(t):t[e]||t.value:t.value},createListHTML:function(t){var e=this;return y([],t).map(function(i,n){typeof i!="string"&&typeof i!="number"||(i={value:i});var s=e.dropdown.getMappedValue(i);return s=typeof s=="string"&&e.settings.dropdown.escapeHTML?j(s):s,e.settings.templates.dropdownItem.apply(e,[Ut(Wt({},i),{mappedValue:s}),e])}).join("")}}),R=(R={refs:function(){this.DOM.dropdown=this.parseTemplate("dropdown",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-wrapper']")},getHeaderRef:function(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-header']")},getFooterRef:function(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-footer']")},getAllSuggestionsRefs:function(){return zt(this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector))},show:function(t){var e,i,n,s=this,a=this.settings,o=a.mode=="mix"&&!a.enforceWhitelist,r=!a.whitelist||!a.whitelist.length,l=a.dropdown.position=="manual";if(t=t===void 0?this.state.inputText:t,!(r&&!o&&!a.templates.dropdownItemNoMatch||a.dropdown.enabled===!1||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(t),t&&!this.suggestedListItems.length&&(this.trigger("dropdown:noMatch",t),a.templates.dropdownItemNoMatch&&(n=a.templates.dropdownItemNoMatch.call(this,{value:t}))),!n){if(this.suggestedListItems.length)t&&o&&!this.state.editing.scope&&!B(this.suggestedListItems[0].value,t)&&this.suggestedListItems.unshift({value:t});else{if(!t||!o||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:t}]}i=""+(E(e=this.suggestedListItems[0])?e.value:e),a.autoComplete&&i&&i.indexOf(t)==0&&this.input.autocomplete.suggest.call(this,e)}this.dropdown.fill(n),a.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(a.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=t||!0,this.state.dropdown.query=t,this.setStateSelection(),l||setTimeout(function(){s.dropdown.position(),s.dropdown.render()}),setTimeout(function(){s.trigger("dropdown:show",s.DOM.dropdown)})}},hide:function(t){var e=this,i=this.DOM,n=i.scope,s=i.dropdown,a=this.settings.dropdown.position=="manual"&&!t;if(s&&document.body.contains(s)&&!a)return window.removeEventListener("resize",this.dropdown.position),this.dropdown.events.binding.call(this,!1),n.setAttribute("aria-expanded",!1),s.parentNode.removeChild(s),setTimeout(function(){e.state.dropdown.visible=!1},100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger("dropdown:hide",s),this},toggle:function(t){this.dropdown[this.state.dropdown.visible&&!t?"hide":"show"]()},getAppendTarget:function(){var t=this.settings.dropdown;return typeof t.appendTarget=="function"?t.appendTarget():t.appendTarget},render:function(){var t,e,i,n=this,s=(t=this.DOM.dropdown,(i=t.cloneNode(!0)).style.cssText="position:fixed; top:-9999px; opacity:0",document.body.appendChild(i),e=i.clientHeight,i.parentNode.removeChild(i),e),a=this.settings,o=this.dropdown.getAppendTarget();return a.dropdown.enabled===!1||(this.DOM.scope.setAttribute("aria-expanded",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(s),o.appendChild(this.DOM.dropdown),setTimeout(function(){return n.DOM.dropdown.classList.remove(a.classNames.dropdownInital)}))),this},fill:function(t){t=typeof t=="string"?t:this.dropdown.createListHTML(t||this.suggestedListItems);var e,i=this.settings.templates.dropdownContent.call(this,t);this.DOM.dropdown.content.innerHTML=(e=i)?e.replace(/\>[\r\n ]+\</g,"><").split(/>\s+</).join("><").trim():""},fillHeaderFooter:function(){var t=this.dropdown.filterListItems(this.state.dropdown.query),e=this.parseTemplate("dropdownHeader",[t]),i=this.parseTemplate("dropdownFooter",[t]),n=this.dropdown.getHeaderRef(),s=this.dropdown.getFooterRef();e&&(n==null||n.parentNode.replaceChild(e,n)),i&&(s==null||s.parentNode.replaceChild(i,s))},position:function(t){var e=this.settings.dropdown,i=this.dropdown.getAppendTarget();if(e.position!="manual"&&i){var n,s,a,o,r,l,d,c,u,g,p=this.DOM.dropdown,h=e.RTL,f=i===document.body,m=i===this.DOM.scope,b=f?window.pageYOffset:i.scrollTop,O=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,S=O.clientHeight,M=Math.max(O.clientWidth||0,window.innerWidth||0),w=M>480?e.position:"all",_=this.DOM[w=="input"?"input":"scope"];if(t=t||p.clientHeight,this.state.dropdown.visible){if(w=="text"?(a=(n=function(){var T=document.getSelection();if(T.rangeCount){var x,D,I=T.getRangeAt(0),H=I.startContainer,K=I.startOffset;if(K>0)return(D=document.createRange()).setStart(H,K-1),D.setEnd(H,K),{left:(x=D.getBoundingClientRect()).right,top:x.top,bottom:x.bottom};if(H.getBoundingClientRect)return H.getBoundingClientRect()}return{left:-9999,top:-9999}}()).bottom,s=n.top,o=n.left,r="auto"):(l=function(T){var x=0,D=0;for(T=T.parentNode;T&&T!=O;)x+=T.offsetTop||0,D+=T.offsetLeft||0,T=T.parentNode;return{top:x,left:D}}(i),n=_.getBoundingClientRect(),s=m?-1:n.top-l.top,a=(m?n.height:n.bottom-l.top)-1,o=m?-1:n.left-l.left,r=n.width+"px"),!f){var k=function(){for(var T=0,x=e.appendTarget.parentNode;x;)T+=x.scrollTop||0,x=x.parentNode;return T}();s+=k,a+=k}var L;s=Math.floor(s),a=Math.ceil(a),c=M-o<120,u=((d=(L=e.placeAbove)!==null&&L!==void 0?L:S-n.bottom<t)?s:a)+b,g=o+(h&&n.width||0)+window.pageXOffset,g=w=="text"&&c?"right: 0;":"left: ".concat(g,"px;"),p.style.cssText="".concat(g," top: ").concat(u,"px; min-width: ").concat(r,"; max-width: ").concat(r),p.setAttribute("placement",d?"top":"bottom"),p.setAttribute("position",w)}}}})!=null?R:{},Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(R)):function(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),i.push.apply(i,n)}return i}(Object(R)).forEach(function(t){Object.defineProperty(U,t,Object.getOwnPropertyDescriptor(R,t))}),U),F="@yaireo/tagify/",Jt={empty:"empty",exceed:"number of tags exceeded",pattern:"pattern mismatch",duplicate:"already exists",notAllowed:"not allowed"},Gt={wrapper:function(t,e){return'<tags class="'.concat(e.classNames.namespace," ").concat(e.mode?"".concat(e.classNames[e.mode+"Mode"]):""," ").concat(t.className,`"
                    `).concat(e.readonly?"readonly":"",`
                    `).concat(e.disabled?"disabled":"",`
                    `).concat(e.required?"required":"",`
                    `).concat(e.mode==="select"?"spellcheck='false'":"",`
                    tabIndex="-1">
                    `).concat(this.settings.templates.input.call(this),`
                `).concat(lt,`
        </tags>`)},input:function(){var t=this.settings,e=t.placeholder||lt;return"<span ".concat(!t.readonly&&t.userInput?"contenteditable":"",' tabIndex="0" data-placeholder="').concat(e,'" aria-placeholder="').concat(t.placeholder||"",`"
                    class="`).concat(t.classNames.input,`"
                    role="textbox"
                    autocapitalize="false"
                    autocorrect="off"
                    aria-autocomplete="both"
                    aria-multiline="`).concat(t.mode=="mix",'"></span>')},tag:function(t,e){var i=e.settings;return'<tag title="'.concat(t.title||t.value,`"
                    contenteditable='false'
                    tabIndex="`).concat(i.a11y.focusableTags?0:-1,`"
                    class="`).concat(i.classNames.tag," ").concat(t.class||"",`"
                    `).concat(this.getAttributes(t),`>
            <x title='' tabIndex="`).concat(i.a11y.focusableTags?0:-1,'" class="').concat(i.classNames.tagX,`" role='button' aria-label='remove tag'></x>
            <div>
                <span `).concat(i.mode==="select"&&i.userInput?"contenteditable='true'":"",` autocapitalize="false" autocorrect="off" spellcheck='false' class="`).concat(i.classNames.tagText,'">').concat(t[i.tagTextProp]||t.value,`</span>
            </div>
        </tag>`)},dropdown:function(t){var e=t.dropdown,i=e.position=="manual";return'<div class="'.concat(i?"":t.classNames.dropdown," ").concat(e.classname,'" role="listbox" aria-labelledby="dropdown" dir="').concat(e.RTL?"rtl":"",`">
                    <div data-selector='tagify-suggestions-wrapper' class="`).concat(t.classNames.dropdownWrapper,`"></div>
                </div>`)},dropdownContent:function(t){var e=this.settings.templates,i=this.state.dropdown.suggestions;return`
            `.concat(e.dropdownHeader.call(this,i),`
            `).concat(t,`
            `).concat(e.dropdownFooter.call(this,i),`
        `)},dropdownItem:function(t){return"<div ".concat(this.getAttributes(t),`
                    class='`).concat(this.settings.classNames.dropdownItem," ").concat(this.isTagDuplicate(t.value)?this.settings.classNames.dropdownItemSelected:""," ").concat(t.class||"",`'
                    tabindex="0"
                    role="option">`).concat(t.mappedValue||t.value,"</div>")},dropdownHeader:function(t){return`<header data-selector='tagify-suggestions-header' class="`.concat(this.settings.classNames.dropdownHeader,'"></header>')},dropdownFooter:function(t){var e=t.length-this.settings.dropdown.maxItems;return e>0?`<footer data-selector='tagify-suggestions-footer' class="`.concat(this.settings.classNames.dropdownFooter,`">
                `).concat(e,` more items. Refine your search.
            </footer>`):""},dropdownItemNoMatch:null};function mt(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function vt(t,e){return e!=null&&typeof Symbol<"u"&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function $t(t,e){return function(i){if(Array.isArray(i))return i}(t)||function(i,n){var s=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s!=null){var a,o,r=[],l=!0,d=!1;try{for(s=s.call(i);!(l=(a=s.next()).done)&&(r.push(a.value),!n||r.length!==n);l=!0);}catch(c){d=!0,o=c}finally{try{l||s.return==null||s.return()}finally{if(d)throw o}}return r}}(t,e)||function(i,n){if(i){if(typeof i=="string")return mt(i,n);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(s);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return mt(i,n)}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function it(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function P(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function wt(t,e){return e!=null&&typeof Symbol<"u"&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function Qt(t,e){return e=e??{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):function(i,n){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);n&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),s.push.apply(s,a)}return s}(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}),t}function nt(t){return function(e){if(Array.isArray(e))return it(e)}(t)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(t)||function(e,i){if(e){if(typeof e=="string")return it(e,i);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return it(e,i)}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Yt={customBinding:function(){var t=this;this.customEventsList.forEach(function(e){t.on(e,t.settings.callbacks[e])})},binding:function(){var t,e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],i=this.settings,n=this.events.callbacks,s=e?"addEventListener":"removeEventListener";if(!this.state.mainEvents||!e){for(var a in this.state.mainEvents=e,e&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on("tagify.removeAllTags",this.removeAllTags.bind(this))),t=this.listeners.main=this.listeners.main||{keydown:["input",n.onKeydown.bind(this)],click:["scope",n.onClickScope.bind(this)],dblclick:i.mode!="select"&&["scope",n.onDoubleClickScope.bind(this)],paste:["input",n.onPaste.bind(this)],drop:["input",n.onDrop.bind(this)],compositionstart:["input",n.onCompositionStart.bind(this)],compositionend:["input",n.onCompositionEnd.bind(this)]})t[a]&&this.DOM[t[a][0]][s](a,t[a][1]);var o=this.listeners.main.inputMutationObserver||new MutationObserver(n.onInputDOMChange.bind(this));o.disconnect(),i.mode=="mix"&&o.observe(this.DOM.input,{childList:!0}),this.events.bindOriginaInputListener.call(this)}},bindOriginaInputListener:function(t){var e=(t||0)+500;this.listeners.main&&(clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(this.events.callbacks.observeOriginalInputValue.bind(this),e))},bindGlobal:function(t){var e,i=this.events.callbacks,n=t?"removeEventListener":"addEventListener";if(this.listeners&&(t||!this.listeners.global)){this.listeners.global=this.listeners.global||[{type:this.isIE?"keydown":"input",target:this.DOM.input,cb:i[this.isIE?"onInputIE":"onInput"].bind(this)},{type:"keydown",target:window,cb:i.onWindowKeyDown.bind(this)},{type:"focusin",target:this.DOM.scope,cb:i.onFocusBlur.bind(this)},{type:"focusout",target:this.DOM.scope,cb:i.onFocusBlur.bind(this)},{type:"click",target:document,cb:i.onClickAnywhere.bind(this),useCapture:!0}];var s=!0,a=!1,o=void 0;try{for(var r,l=this.listeners.global[Symbol.iterator]();!(s=(r=l.next()).done);s=!0)(e=r.value).target[n](e.type,e.cb,!!e.useCapture)}catch(d){a=!0,o=d}finally{try{s||l.return==null||l.return()}finally{if(a)throw o}}}},unbindGlobal:function(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur:function(t){var e,i,n=this.settings,s=ft.call(this,t.relatedTarget),a=V.call(this,t.relatedTarget),o=t.target.classList.contains(n.classNames.tagX),r=t.type=="focusin",l=t.type=="focusout";o&&n.mode!="mix"&&this.DOM.input.focus(),s&&r&&!a&&!o&&this.toggleFocusClass(this.state.hasFocus=+new Date);var d=t.target?this.trim(this.DOM.input.textContent):"",c=(i=this.value)===null||i===void 0||(e=i[0])===null||e===void 0?void 0:e[n.tagTextProp],u=n.dropdown.enabled>=0,g={relatedTarget:t.relatedTarget},p=this.state.actions.selectOption&&(u||!n.dropdown.closeOnSelect),h=this.state.actions.addNew&&u;if(l){if(t.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),n.onChangeAfterBlur&&this.triggerChangeEvent()}if(!(p||h||o))if(this.state.hasFocus=!(!r&&!s)&&+new Date,this.toggleFocusClass(this.state.hasFocus),n.mode!="mix"){if(r){if(!n.focusable)return;var f=n.dropdown.enabled===0&&!this.state.dropdown.visible,m=!a||n.mode==="select",b=this.DOM.scope.querySelector(this.settings.classNames.tagTextSelector);return this.trigger("focus",g),void(f&&m&&(this.dropdown.show(this.value.length?"":void 0),this.setRangeAtStartEnd(!1,b)))}if(l){if(this.trigger("blur",g),this.loading(!1),n.mode=="select"){if(this.value.length){var O=this.getTagElms()[0];d=this.trim(O.textContent)}c===d&&(d="")}d&&!this.state.actions.selectOption&&n.addTagOnBlur&&n.addTagOn.includes("blur")&&this.addTags(d,!0)}s||(this.DOM.input.removeAttribute("style"),this.dropdown.hide())}else r?this.trigger("focus",g):l&&(this.trigger("blur",g),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart:function(t){this.state.composing=!0},onCompositionEnd:function(t){this.state.composing=!1},onWindowKeyDown:function(t){var e,i=this.settings,n=document.activeElement,s=ft.call(this,n)&&this.DOM.scope.contains(n),a=n===this.DOM.input,o=s&&n.hasAttribute("readonly"),r=this.DOM.scope.querySelector(this.settings.classNames.tagTextSelector),l=this.state.dropdown.visible;if((t.key==="Tab"&&l||this.state.hasFocus||s&&!o)&&!a){e=n.nextElementSibling;var d=t.target.classList.contains(i.classNames.tagX);switch(t.key){case"Backspace":i.readonly||this.state.editing||(this.removeTags(n),(e||this.DOM.input).focus());break;case"Enter":if(d)return void this.removeTags(t.target.parentNode);i.a11y.focusableTags&&V.call(this,n)&&setTimeout(this.editTag.bind(this),0,n);break;case"ArrowDown":this.state.dropdown.visible||i.mode=="mix"||this.dropdown.show();break;case"Tab":r==null||r.focus()}}},onKeydown:function(t){var e=this,i=this.settings;if(!this.state.composing&&i.userInput){i.mode=="select"&&i.enforceWhitelist&&this.value.length&&t.key!="Tab"&&t.preventDefault();var n=this.trim(t.target.textContent);this.trigger("keydown",{event:t}),i.hooks.beforeKeyDown(t,{tagify:this}).then(function(s){if(i.mode=="mix"){switch(t.key){case"Left":case"ArrowLeft":e.state.actions.ArrowLeft=!0;break;case"Delete":case"Backspace":if(e.state.editing)return;var a=document.getSelection(),o=t.key=="Delete"&&a.anchorOffset==(a.anchorNode.length||0),r=a.anchorNode.previousSibling,l=a.anchorNode.nodeType==1||!a.anchorOffset&&r&&r.nodeType==1&&a.anchorNode.previousSibling;(function(b){var O=document.createElement("div");b.replace(/\&#?[0-9a-z]+;/gi,function(S){return O.innerHTML=S,O.innerText})})(e.DOM.input.innerHTML);var d,c,u,g=e.getTagElms(),p=a.anchorNode.length===1&&a.anchorNode.nodeValue==String.fromCharCode(8203);if(i.backspace=="edit"&&l)return d=a.anchorNode.nodeType==1?null:a.anchorNode.previousElementSibling,setTimeout(e.editTag.bind(e),0,d),void t.preventDefault();if(ht()&&wt(l,Element))return u=ut(l),l.hasAttribute("readonly")||l.remove(),e.DOM.input.focus(),void setTimeout(function(){A(u),e.DOM.input.click()});if(a.anchorNode.nodeName=="BR")return;if((o||l)&&a.anchorNode.nodeType==1?c=a.anchorOffset==0?o?g[0]:null:g[Math.min(g.length,a.anchorOffset)-1]:o?c=a.anchorNode.nextElementSibling:wt(l,Element)&&(c=l),a.anchorNode.nodeType==3&&!a.anchorNode.nodeValue&&a.anchorNode.previousElementSibling&&t.preventDefault(),(l||o)&&!i.backspace||a.type!="Range"&&!a.anchorOffset&&a.anchorNode==e.DOM.input&&t.key!="Delete")return void t.preventDefault();if(a.type!="Range"&&c&&c.hasAttribute("readonly"))return void A(ut(c));t.key=="Delete"&&p&&v(a.anchorNode.nextSibling)&&e.removeTags(a.anchorNode.nextSibling)}return!0}var h=i.dropdown.position=="manual";switch(t.key){case"Backspace":i.mode=="select"&&i.enforceWhitelist&&e.value.length?e.removeTags():e.state.dropdown.visible&&i.dropdown.position!="manual"||t.target.textContent!=""&&n.charCodeAt(0)!=8203||(i.backspace===!0?e.removeTags():i.backspace=="edit"&&setTimeout(e.editTag.bind(e),0));break;case"Esc":case"Escape":if(e.state.dropdown.visible)return;t.target.blur();break;case"Down":case"ArrowDown":e.state.dropdown.visible||e.dropdown.show();break;case"ArrowRight":var f=e.state.inputSuggestion||e.state.ddItemData;if(f&&i.autoComplete.rightKey)return void e.addTags([f],!0);break;case"Tab":return!0;case"Enter":if(e.state.dropdown.visible&&!h)return;t.preventDefault();var m=e.state.autoCompleteData||n;setTimeout(function(){e.state.dropdown.visible&&!h||e.state.actions.selectOption||!i.addTagOn.includes(t.key.toLowerCase())||(e.addTags([m],!0),e.state.autoCompleteData=null)})}}).catch(function(s){return s})}},onInput:function(t){this.postUpdate();var e=this.settings;if(e.mode=="mix")return this.events.callbacks.onMixTagsInput.call(this,t);var i=this.input.normalize.call(this,void 0,{trim:!1}),n=i.length>=e.dropdown.enabled,s={value:i,inputElm:this.DOM.input},a=this.validateTag({value:i});e.mode=="select"&&this.toggleScopeValidation(a),s.isValid=a,this.state.inputText!=i&&(this.input.set.call(this,i,!1),i.search(e.delimiters)!=-1?this.addTags(i)&&this.input.set.call(this):e.dropdown.enabled>=0&&this.dropdown[n?"show":"hide"](i),this.trigger("input",s))},onMixTagsInput:function(t){var e,i,n,s,a,o,r,l,d=this,c=this.settings,u=this.value.length,g=this.getTagElms(),p=document.createDocumentFragment(),h=window.getSelection().getRangeAt(0),f=[].map.call(g,function(m){return v(m).value});if(t.inputType=="deleteContentBackward"&&ht()&&this.events.callbacks.onKeydown.call(this,{target:t.target,key:"Backspace"}),Ot(this.getTagElms()),this.value.slice().forEach(function(m){m.readonly&&!f.includes(m.value)&&p.appendChild(d.createTagElem(m))}),p.childNodes.length&&(h.insertNode(p),this.setRangeAtStartEnd(!1,p.lastChild)),g.length!=u)return this.value=[].map.call(this.getTagElms(),function(m){return v(m)}),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(o=window.getSelection()).rangeCount>0&&o.anchorNode.nodeType==3){if((h=o.getRangeAt(0).cloneRange()).collapse(!0),h.setStart(o.focusNode,0),n=(e=h.toString().slice(0,h.endOffset)).split(c.pattern).length-1,(i=e.match(c.pattern))&&(s=e.slice(e.lastIndexOf(i[i.length-1]))),s){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:s.match(c.pattern)[0],value:s.replace(c.pattern,"")},this.state.tag.baseOffset=o.baseOffset-this.state.tag.value.length,l=this.state.tag.value.match(c.delimiters))return this.state.tag.value=this.state.tag.value.replace(c.delimiters,""),this.state.tag.delimiters=l[0],this.addTags(this.state.tag.value,c.dropdown.clearOnSelect),void this.dropdown.hide();a=this.state.tag.value.length>=c.dropdown.enabled;try{r=(r=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&r.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch{}(r||n<this.state.mixMode.matchedPatternCount)&&(a=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=n}setTimeout(function(){d.update({withoutChangeEvent:!0}),d.trigger("input",y({},d.state.tag,{textContent:d.DOM.input.textContent})),d.state.tag&&d.dropdown[a?"show":"hide"](d.state.tag.value)},10)},onInputIE:function(t){var e=this;setTimeout(function(){e.events.callbacks.onInput.call(e,t)})},observeOriginalInputValue:function(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickAnywhere:function(t){if(t.target!=this.DOM.scope&&!this.DOM.scope.contains(t.target)){this.toggleFocusClass(!1),this.state.hasFocus=!1;var e=t.target.closest(this.settings.classNames.dropdownSelector);(e==null?void 0:e.__tagify)!=this&&this.dropdown.hide()}},onClickScope:function(t){var e=this.settings,i=t.target.closest("."+e.classNames.tag),n=t.target===this.DOM.scope,s=+new Date-this.state.hasFocus;if(n&&e.mode!="select")this.DOM.input.focus();else{if(!t.target.classList.contains(e.classNames.tagX))return i&&!this.state.editing?(this.trigger("click",{tag:i,index:this.getNodeIndex(i),data:v(i),event:t}),void(e.editTags!==1&&e.editTags.clicks!==1&&e.mode!="select"||this.events.callbacks.onDoubleClickScope.call(this,t))):void(t.target==this.DOM.input&&(e.mode=="mix"&&this.fixFirefoxLastTagNoCaret(),s>500||!e.focusable)?this.state.dropdown.visible?this.dropdown.hide():e.dropdown.enabled===0&&e.mode!="mix"&&this.dropdown.show(this.value.length?"":void 0):e.mode!="select"||e.dropdown.enabled!==0||this.state.dropdown.visible||(this.events.callbacks.onDoubleClickScope.call(this,Qt(function(a){for(var o=1;o<arguments.length;o++){var r=arguments[o]!=null?arguments[o]:{},l=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),l.forEach(function(d){P(a,d,r[d])})}return a}({},t),{target:this.getTagElms()[0]})),!e.userInput&&this.dropdown.show()));this.removeTags(t.target.parentNode)}},onPaste:function(t){var e=this;t.preventDefault();var i,n,s,a=this.settings;if(!a.userInput)return!1;a.readonly||(n=t.clipboardData||window.clipboardData,s=n.getData("Text"),a.hooks.beforePaste(t,{tagify:this,pastedText:s,clipboardData:n}).then(function(o){o===void 0&&(o=s),o&&(e.injectAtCaret(o,window.getSelection().getRangeAt(0)),e.settings.mode=="mix"?e.events.callbacks.onMixTagsInput.call(e,t):e.settings.pasteAsTags?i=e.addTags(e.state.inputText+o,!0):(e.state.inputText=o,e.dropdown.show(o))),e.trigger("paste",{event:t,pastedText:s,clipboardData:n,tagsElems:i})}).catch(function(o){return o}))},onDrop:function(t){t.preventDefault()},onEditTagInput:function(t,e){var i,n=t.closest("."+this.settings.classNames.tag),s=this.getNodeIndex(n),a=v(n),o=this.input.normalize.call(this,t),r=(P(i={},this.settings.tagTextProp,o),P(i,"__tagId",a.__tagId),i),l=this.validateTag(r);this.editTagChangeDetected(y(a,r))||t.originalIsValid!==!0||(l=!0),n.classList.toggle(this.settings.classNames.tagInvalid,l!==!0),a.__isValid=l,n.title=l===!0?a.title||a.value:l,o.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=o),this.dropdown.show(o)),this.trigger("edit:input",{tag:n,index:s,data:y({},this.value[s],{newValue:o}),event:e})},onEditTagPaste:function(t,e){var i=(e.clipboardData||window.clipboardData).getData("Text");e.preventDefault();var n=Tt(i);this.setRangeAtStartEnd(!1,n)},onEditTagClick:function(t,e){this.events.callbacks.onClickScope.call(this,e)},onEditTagFocus:function(t){this.state.editing={scope:t,input:t.querySelector("[contenteditable]")}},onEditTagBlur:function(t,e){var i=V.call(this,e.relatedTarget);if(this.settings.mode=="select"&&i&&e.relatedTarget.contains(e.target))this.dropdown.hide();else if(this.state.editing&&(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(document.activeElement)||this.trigger("blur",{}),this.DOM.scope.contains(t))){var n,s,a,o=this.settings,r=t.closest("."+o.classNames.tag),l=v(r),d=this.input.normalize.call(this,t),c=(P(n={},o.tagTextProp,d),P(n,"__tagId",l.__tagId),n),u=l.__originalData,g=this.editTagChangeDetected(y(l,c)),p=this.validateTag(c);if(d)if(g){var h;if(s=this.hasMaxTags(),a=y({},u,(P(h={},o.tagTextProp,this.trim(d)),P(h,"__isValid",p),h)),o.transformTag.call(this,a,u),(p=(!s||u.__isValid===!0)&&this.validateTag(a))!==!0){if(this.trigger("invalid",{data:a,tag:r,message:p}),o.editTags.keepInvalid)return;o.keepInvalidTags?a.__isValid=p:a=u}else o.keepInvalidTags&&(delete a.title,delete a["aria-invalid"],delete a.class);this.onEditTagDone(r,a)}else this.onEditTagDone(r,u);else this.onEditTagDone(r)}},onEditTagkeydown:function(t,e){if(!this.state.composing)switch(this.trigger("edit:keydown",{event:t}),t.key){case"Esc":case"Escape":this.state.editing=!1,e.__tagifyTagData.__originalData.value?e.parentNode.replaceChild(e.__tagifyTagData.__originalHTML,e):e.remove();break;case"Enter":case"Tab":t.preventDefault(),setTimeout(function(){return t.target.blur()},0)}},onDoubleClickScope:function(t){var e=t.target.closest("."+this.settings.classNames.tag);if(e){var i,n,s=v(e),a=this.settings;(s==null?void 0:s.editable)!==!1&&(i=e.classList.contains(this.settings.classNames.tagEditing),n=e.hasAttribute("readonly"),a.readonly||i||n||!this.settings.editTags||!a.userInput||(this.events.callbacks.onEditTagFocus.call(this,e),this.editTag(e)),this.toggleFocusClass(!0),a.mode!="select"&&this.trigger("dblclick",{tag:e,index:this.getNodeIndex(e),data:v(e)}))}},onInputDOMChange:function(t){var e=this;t.forEach(function(n){n.addedNodes.forEach(function(s){if(s.outerHTML=="<div><br></div>")s.replaceWith(document.createElement("br"));else if(s.nodeType==1&&s.querySelector(e.settings.classNames.tagSelector)){var a,o=document.createTextNode("");s.childNodes[0].nodeType==3&&s.previousSibling.nodeName!="BR"&&(o=document.createTextNode(`
`)),(a=s).replaceWith.apply(a,nt([o].concat(nt(nt(s.childNodes).slice(0,-1))))),A(o)}else if(V.call(e,s)){var r;if(((r=s.previousSibling)===null||r===void 0?void 0:r.nodeType)!=3||s.previousSibling.textContent||s.previousSibling.remove(),s.previousSibling&&s.previousSibling.nodeName=="BR"){s.previousSibling.replaceWith(`
​`);for(var l=s.nextSibling,d="";l;)d+=l.textContent,l=l.nextSibling;d.trim()&&A(s.previousSibling)}else s.previousSibling&&!v(s.previousSibling)||s.before("​")}}),n.removedNodes.forEach(function(s){s&&s.nodeName=="BR"&&V.call(e,i)&&(e.removeTags(i),e.fixFirefoxLastTagNoCaret())})});var i=this.DOM.input.lastChild;i&&i.nodeValue==""&&i.remove(),i&&i.nodeName=="BR"||this.DOM.input.appendChild(document.createElement("br"))}}};function st(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function rt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function N(t,e){return e!=null&&typeof Symbol<"u"&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function yt(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{},n=Object.keys(i);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(i).filter(function(s){return Object.getOwnPropertyDescriptor(i,s).enumerable}))),n.forEach(function(s){rt(t,s,i[s])})}return t}function q(t){return function(e){if(Array.isArray(e))return st(e)}(t)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(t)||function(e,i){if(e){if(typeof e=="string")return st(e,i);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(e,i)}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(t,e){if(!t){C.warn("input element not found",t);var i=new Proxy(this,{get:function(){return function(){return i}}});return i}if(t.__tagify)return C.warn("input element is already Tagified - Same instance is returned.",t),t.__tagify;var n;y(this,function(s){var a=document.createTextNode(""),o={};function r(l,d,c){c&&d.split(/\s+/g).forEach(function(u){return a[l+"EventListener"].call(a,u,c)})}return{removeAllCustomListeners:function(){Object.entries(o).forEach(function(l){var d=$t(l,2),c=d[0];d[1].forEach(function(u){return r("remove",c,u)})}),o={}},off:function(l,d){return l&&(d?r("remove",l,d):l.split(/\s+/g).forEach(function(c){var u;(u=o[c])===null||u===void 0||u.forEach(function(g){return r("remove",c,g)}),delete o[c]})),this},on:function(l,d){return d&&typeof d=="function"&&(l.split(/\s+/g).forEach(function(c){Array.isArray(o[c])?o[c].push(d):o[c]=[d]}),r("add",l,d)),this},trigger:function(l,d,c){var u;if(c=c||{cloneData:!0},l)if(s.settings.isJQueryPlugin)l=="remove"&&(l="removeTag"),jQuery(s.DOM.originalInput).triggerHandler(l,[d]);else{try{var g=typeof d=="object"?d:{value:d};if((g=c.cloneData?y({},g):g).tagify=this,d.event&&(g.event=this.cloneEvent(d.event)),vt(d,Object))for(var p in d)vt(d[p],HTMLElement)&&(g[p]=d[p]);u=new CustomEvent(l,{detail:g})}catch(h){C.warn(h)}a.dispatchEvent(u)}}}}(this)),this.isFirefox=/firefox|fxios/i.test(navigator.userAgent)&&!/seamonkey/i.test(navigator.userAgent),this.isIE=window.document.documentMode,e=e||{},this.getPersistedData=(n=e.id,function(s){var a;if(n){var o,r="/"+s;if(((a=localStorage)===null||a===void 0?void 0:a.getItem(F+n+"/v"))===1)try{o=JSON.parse(localStorage[F+n+r])}catch{}return o}}),this.setPersistedData=function(s){var a;return s?((a=localStorage)===null||a===void 0||a.setItem(F+s+"/v",1),function(o,r){var l,d="/"+r,c=JSON.stringify(o);o&&r&&((l=localStorage)===null||l===void 0||l.setItem(F+s+d,c),dispatchEvent(new Event("storage")))}):function(){}}(e.id),this.clearPersistedData=function(s){return function(a){var o=F+"/"+s+"/";if(a)localStorage.removeItem(o+a);else for(var r in localStorage)r.includes(o)&&localStorage.removeItem(r)}}(e.id),this.applySettings(t,e),this.state={inputText:"",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(t),Kt.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),t.autofocus&&this.DOM.input.focus(),t.__tagify=this}at.prototype={_dropdown:Xt,placeCaretAfterNode:A,getSetTagData:v,helpers:{sameStr:B,removeCollectionProp:ct,omit:ot,isObject:E,parseHTML:Z,escapeHTML:j,extend:y,concatWithoutDups:gt,getUID:pt,isNodeTag:V},customEventsList:["change","add","remove","invalid","input","paste","click","keydown","focus","blur","edit:input","edit:beforeUpdate","edit:updated","edit:start","edit:keydown","dropdown:show","dropdown:hide","dropdown:select","dropdown:updated","dropdown:noMatch","dropdown:scroll"],dataProps:["__isValid","__removed","__originalData","__originalHTML","__tagId"],trim:function(t){return this.settings.trim&&t&&typeof t=="string"?t.trim():t},parseHTML:Z,templates:Gt,parseTemplate:function(t,e){return Z((t=this.settings.templates[t]||t).apply(this,e))},set whitelist(t){var e=t&&Array.isArray(t);this.settings.whitelist=e?t:[],this.setPersistedData(e?t:[],"whitelist")},get whitelist(){return this.settings.whitelist},set userInput(t){this.settings.userInput=!!t,this.setContentEditable(!!t)},get userInput(){return this.settings.userInput},generateClassSelectors:function(t){var e=function(n){var s=n;Object.defineProperty(t,s+"Selector",{get:function(){return"."+this[s].split(" ")[0]}})};for(var i in t)e(i)},applySettings:function(t,e){var i,n;tt.templates=this.templates;var s=y({},tt,e.mode=="mix"?{dropdown:{position:"text"}}:{}),a=this.settings=y({},s,e);if(a.disabled=t.hasAttribute("disabled"),a.readonly=a.readonly||t.hasAttribute("readonly"),a.placeholder=j(t.getAttribute("placeholder")||a.placeholder||""),a.required=t.hasAttribute("required"),this.generateClassSelectors(a.classNames),this.isIE&&(a.autoComplete=!1),["whitelist","blacklist"].forEach(function(r){var l=t.getAttribute("data-"+r);l&&N(l=l.split(a.delimiters),Array)&&(a[r]=l)}),"autoComplete"in e&&!E(e.autoComplete)&&(a.autoComplete=tt.autoComplete,a.autoComplete.enabled=e.autoComplete),a.mode=="mix"&&(a.pattern=a.pattern||/@/,a.autoComplete.rightKey=!0,a.delimiters=e.delimiters||null,a.tagTextProp&&!a.dropdown.searchKeys.includes(a.tagTextProp)&&a.dropdown.searchKeys.push(a.tagTextProp)),t.pattern)try{a.pattern=new RegExp(t.pattern)}catch{}if(a.delimiters){a._delimiters=a.delimiters;try{a.delimiters=new RegExp(this.settings.delimiters,"g")}catch{}}a.disabled&&(a.userInput=!1),this.TEXTS=yt({},Jt,a.texts||{}),a.mode=="select"&&(a.dropdown.includeSelectedTags=!0),(a.mode!="select"||!((i=e.dropdown)===null||i===void 0)&&i.enabled)&&a.userInput||(a.dropdown.enabled=0),a.dropdown.appendTarget=((n=e.dropdown)===null||n===void 0?void 0:n.appendTarget)||document.body,a.dropdown.includeSelectedTags===void 0&&(a.dropdown.includeSelectedTags=a.duplicates);var o=this.getPersistedData("whitelist");Array.isArray(o)&&(this.whitelist=Array.isArray(a.whitelist)?gt(a.whitelist,o):o)},getAttributes:function(t){var e,i=this.getCustomAttributes(t),n="";for(e in i)n+=" "+e+(t[e]!==void 0?'="'.concat(i[e],'"'):"");return n},getCustomAttributes:function(t){if(!E(t))return"";var e,i={};for(e in t)e.slice(0,2)!="__"&&e!="class"&&t.hasOwnProperty(e)&&t[e]!==void 0&&(i[e]=j(t[e]));return i},setStateSelection:function(){var t=window.getSelection(),e={anchorOffset:t.anchorOffset,anchorNode:t.anchorNode,range:t.getRangeAt&&t.rangeCount&&t.getRangeAt(0)};return this.state.selection=e,e},getCSSVars:function(){var t,e,i,n=getComputedStyle(this.DOM.scope,null);this.CSSVars={tagHideTransition:(t=function(s){if(!s)return{};var a=(s=s.trim().split(" ")[0]).split(/\d+/g).filter(function(o){return o}).pop().trim();return{value:+s.split(a).filter(function(o){return o})[0].trim(),unit:a}}((i="tag-hide-transition",n.getPropertyValue("--"+i))),e=t.value,t.unit=="s"?1e3*e:e)}},build:function(t){var e=this.DOM,i=t.closest("label");this.settings.mixMode.integrated?(e.originalInput=null,e.scope=t,e.input=t):(e.originalInput=t,e.originalInput_tabIndex=t.tabIndex,e.scope=this.parseTemplate("wrapper",[t,this.settings]),e.input=e.scope.querySelector(this.settings.classNames.inputSelector),t.parentNode.insertBefore(e.scope,t),t.tabIndex=-1),i&&i.setAttribute("for","")},destroy:function(){var t;this.events.unbindGlobal.call(this),(t=this.DOM.scope.parentNode)===null||t===void 0||t.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),this.removeAllCustomListeners(),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues:function(t){var e,i=this.settings;if(this.state.blockChangeEvent=!0,t===void 0){var n=this.getPersistedData("value");t=n&&!this.DOM.originalInput.value?n:i.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),t)if(i.mode=="mix")this.parseMixTags(t),(e=this.DOM.input.lastChild)&&e.tagName=="BR"||this.DOM.input.insertAdjacentHTML("beforeend","<br>");else{try{N(JSON.parse(t),Array)&&(t=JSON.parse(t))}catch{}this.addTags(t,!0).forEach(function(s){return s&&s.classList.add(i.classNames.tagNoAnimation)})}else this.postUpdate();this.state.lastOriginalValueReported=i.mixMode.integrated?"":this.DOM.originalInput.value},cloneEvent:function(t){var e={};for(var i in t)i!="path"&&(e[i]=t[i]);return e},loading:function(t){return this.state.isLoading=t,this.DOM.scope.classList[t?"add":"remove"](this.settings.classNames.scopeLoading),this},tagLoading:function(t,e){return t&&t.classList[e?"add":"remove"](this.settings.classNames.tagLoading),this},toggleClass:function(t,e){typeof t=="string"&&this.DOM.scope.classList.toggle(t,e)},toggleScopeValidation:function(t){var e=t===!0||t===void 0;!this.settings.required&&t&&t===this.TEXTS.empty&&(e=!0),this.toggleClass(this.settings.classNames.tagInvalid,!e),this.DOM.scope.title=e?"":t},toggleFocusClass:function(t){this.toggleClass(this.settings.classNames.focus,!!t)},setPlaceholder:function(t){var e=this;["data","aria"].forEach(function(i){return e.DOM.input.setAttribute("".concat(i,"-placeholder"),t)})},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var t=this.DOM.originalInput,e=this.state.lastOriginalValueReported!==t.value,i=new CustomEvent("change",{bubbles:!0});e&&(this.state.lastOriginalValueReported=t.value,i.simulated=!0,t._valueTracker&&t._valueTracker.setValue(Math.random()),t.dispatchEvent(i),this.trigger("change",this.state.lastOriginalValueReported),t.value=this.state.lastOriginalValueReported)}},events:Yt,fixFirefoxLastTagNoCaret:function(){},setRangeAtStartEnd:function(t,e){if(e){t=typeof t=="number"?t:!!t,e=e.lastChild||e;var i=document.getSelection();if(N(i.focusNode,Element)&&!this.DOM.input.contains(i.focusNode))return!0;try{i.rangeCount>=1&&["Start","End"].forEach(function(n){return i.getRangeAt(0)["set"+n](e,t||e.length)})}catch(n){console.warn(n)}}},insertAfterTag:function(t,e){if(e=e||this.settings.mixMode.insertAfterTag,t&&t.parentNode&&e)return e=typeof e=="string"?document.createTextNode(e):e,t.parentNode.insertBefore(e,t.nextSibling),e},editTagChangeDetected:function(t){var e=t.__originalData;for(var i in e)if(!this.dataProps.includes(i)&&t[i]!=e[i])return!0;return!1},getTagTextNode:function(t){return t.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode:function(t,e){this.getTagTextNode(t).innerHTML=j(e)},editTag:function(t,e){var i=this;t=t||this.getLastTag(),e=e||{};var n=this.settings,s=this.getTagTextNode(t),a=this.getNodeIndex(t),o=v(t),r=this.events.callbacks,l=!0,d=n.mode=="select";if(!d&&this.dropdown.hide(),s){if(!N(o,Object)||!("editable"in o)||o.editable)return o=v(t,{__originalData:y({},o),__originalHTML:t.cloneNode(!0)}),v(o.__originalHTML,o.__originalData),s.setAttribute("contenteditable",!0),t.classList.add(n.classNames.tagEditing),this.events.callbacks.onEditTagFocus.call(this,t),s.addEventListener("click",r.onEditTagClick.bind(this,t)),s.addEventListener("blur",r.onEditTagBlur.bind(this,this.getTagTextNode(t))),s.addEventListener("input",r.onEditTagInput.bind(this,s)),s.addEventListener("paste",r.onEditTagPaste.bind(this,s)),s.addEventListener("keydown",function(c){return r.onEditTagkeydown.call(i,c,t)}),s.addEventListener("compositionstart",r.onCompositionStart.bind(this)),s.addEventListener("compositionend",r.onCompositionEnd.bind(this)),e.skipValidation||(l=this.editTagToggleValidity(t)),s.originalIsValid=l,this.trigger("edit:start",{tag:t,index:a,data:o,isValid:l}),s.focus(),!d&&this.setRangeAtStartEnd(!1,s),n.dropdown.enabled===0&&!d&&this.dropdown.show(),this.state.hasFocus=!0,this}else C.warn("Cannot find element in Tag template: .",n.classNames.tagTextSelector)},editTagToggleValidity:function(t,e){var i;if(e=e||v(t))return(i=!("__isValid"in e)||e.__isValid===!0)||this.removeTagsFromValue(t),this.update(),t.classList.toggle(this.settings.classNames.tagNotAllowed,!i),e.__isValid=i,e.__isValid;C.warn("tag has no data: ",t,e)},onEditTagDone:function(t,e){t=t||this.state.editing.scope,e=e||{};var i,n,s=this.settings,a={tag:t,index:this.getNodeIndex(t),previousData:v(t),data:e};this.trigger("edit:beforeUpdate",a,{cloneData:!1}),this.state.editing=!1,delete e.__originalData,delete e.__originalHTML,t&&t.parentNode&&(((n=e[s.tagTextProp])!==void 0?!((i=(n+="").trim)===null||i===void 0)&&i.call(n):!(s.tagTextProp in e)&&e.value)?(t=this.replaceTag(t,e),this.editTagToggleValidity(t,e),s.a11y.focusableTags?t.focus():s.mode!="select"&&A(t)):this.removeTags(t)),this.trigger("edit:updated",a),s.dropdown.closeOnSelect&&this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag:function(t,e){e&&e.value!==""&&e.value!==void 0||(e=t.__tagifyTagData),e.__isValid&&e.__isValid!=1&&y(e,this.getInvalidTagAttrs(e,e.__isValid));var i=this.createTagElem(e);return t.parentNode.replaceChild(i,t),this.updateValueByDOMTags(),i},updateValueByDOMTags:function(){var t=this;this.value.length=0;var e=this.settings.classNames,i=[e.tagNotAllowed.split(" ")[0],e.tagHide];[].forEach.call(this.getTagElms(),function(n){q(n.classList).some(function(s){return i.includes(s)})||t.value.push(v(n))}),this.update(),this.dropdown.refilter()},injectAtCaret:function(t,e){var i;if(e=e||((i=this.state.selection)===null||i===void 0?void 0:i.range),typeof t=="string"&&(t=document.createTextNode(t)),!e&&t)return this.appendMixTags(t),this;var n=Tt(t,e);return this.setRangeAtStartEnd(!1,n),this.updateValueByDOMTags(),this.update(),this},input:{set:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=this.settings,n=i.dropdown.closeOnSelect;this.state.inputText=t,e&&(this.DOM.input.innerHTML=j(""+t),t&&this.toggleClass(i.classNames.empty,!this.DOM.input.innerHTML)),!t&&n&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw:function(){return this.DOM.input.textContent},validate:function(){var t=!this.state.inputText||this.validateTag({value:this.state.inputText})===!0;return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!t),t},normalize:function(t,e){var i=t||this.DOM.input,n=[];i.childNodes.forEach(function(s){return s.nodeType==3&&n.push(s.nodeValue)}),n=n.join(`
`);try{n=n.replace(/(?:\r\n|\r|\n)/g,this.settings.delimiters.source.charAt(0))}catch{}return n=n.replace(/\s/g," "),e!=null&&e.trim?this.trim(n):n},autocomplete:{suggest:function(t){if(this.settings.autoComplete.enabled){typeof(t=t||{value:""})!="object"&&(t={value:t});var e=this.dropdown.getMappedValue(t);if(typeof e!="number"){var i=this.state.inputText.toLowerCase(),n=e.substr(0,this.state.inputText.length).toLowerCase(),s=e.substring(this.state.inputText.length);e&&this.state.inputText&&n==i?(this.DOM.input.setAttribute("data-suggest",s),this.state.inputSuggestion=t):(this.DOM.input.removeAttribute("data-suggest"),delete this.state.inputSuggestion)}}},set:function(t){var e=this.DOM.input.getAttribute("data-suggest"),i=t||(e?this.state.inputText+e:null);return!!i&&(this.settings.mode=="mix"?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+i)):(this.input.set.call(this,i),this.setRangeAtStartEnd(!1,this.DOM.input)),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx:function(t){return this.value.findIndex(function(e){return e.__tagId==(t||{}).__tagId})},getNodeIndex:function(t){var e=0;if(t)for(;t=t.previousElementSibling;)e++;return e},getTagElms:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var n="."+q(this.settings.classNames.tag.split(" ")).concat(q(e)).join(".");return[].slice.call(this.DOM.scope.querySelectorAll(n))},getLastTag:function(){var t=this.settings.classNames,e=this.DOM.scope.querySelectorAll("".concat(t.tagSelector,":not(.").concat(t.tagHide,"):not([readonly])"));return e[e.length-1]},isTagDuplicate:function(t,e,i){var n=0,s=!0,a=!1,o=void 0;try{for(var r,l=this.value[Symbol.iterator]();!(s=(r=l.next()).done);s=!0){var d=r.value;B(this.trim(""+t),d.value,e)&&i!=d.__tagId&&n++}}catch(c){a=!0,o=c}finally{try{s||l.return==null||l.return()}finally{if(a)throw o}}return n},getTagIndexByValue:function(t){var e=this,i=[],n=this.settings.dropdown.caseSensitive;return this.getTagElms().forEach(function(s,a){s.__tagifyTagData&&B(e.trim(s.__tagifyTagData.value),t,n)&&i.push(a)}),i},getTagElmByValue:function(t){var e=this.getTagIndexByValue(t)[0];return this.getTagElms()[e]},flashTag:function(t){var e=this;t&&(t.classList.add(this.settings.classNames.tagFlash),setTimeout(function(){t.classList.remove(e.settings.classNames.tagFlash)},100))},isTagBlacklisted:function(t){return t=this.trim(t.toLowerCase()),this.settings.blacklist.filter(function(e){return(""+e).toLowerCase()==t}).length},isTagWhitelisted:function(t){return!!this.getWhitelistItem(t)},getWhitelistItem:function(t,e,i){e=e||"value";var n,s=this.settings;return(i=i||s.whitelist).some(function(a){var o=typeof a=="object"?a[e]||a.value:a;if(B(o,t,s.dropdown.caseSensitive,s.trim))return n=typeof a=="object"?a:{value:a},!0}),n||e!="value"||s.tagTextProp=="value"||(n=this.getWhitelistItem(t,s.tagTextProp,i)),n},validateTag:function(t){var e=this.settings,i="value"in t?"value":e.tagTextProp,n=this.trim(t[i]+"");return(t[i]+"").trim()?e.mode!="mix"&&e.pattern&&N(e.pattern,RegExp)&&!e.pattern.test(n)?this.TEXTS.pattern:!e.duplicates&&this.isTagDuplicate(n,e.dropdown.caseSensitive,t.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(n)||e.enforceWhitelist&&!this.isTagWhitelisted(n)?this.TEXTS.notAllowed:!e.validate||e.validate(t):this.TEXTS.empty},getInvalidTagAttrs:function(t,e){return{"aria-invalid":!0,class:"".concat(t.class||""," ").concat(this.settings.classNames.tagNotAllowed).trim(),title:e}},hasMaxTags:function(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly:function(t,e){var i=this.settings;this.DOM.scope.contains(document.activeElement)&&document.activeElement.blur(),i[e||"readonly"]=t,this.DOM.scope[(t?"set":"remove")+"Attribute"](e||"readonly",!0),this.settings.userInput=!0,this.setContentEditable(!t)},setContentEditable:function(t){this.DOM.input.contentEditable=t,this.DOM.input.tabIndex=t?0:-1},setDisabled:function(t){this.setReadonly(t,"disabled")},normalizeTags:function(t){var e=this,i=this.settings,n=i.whitelist,s=i.delimiters,a=i.mode,o=i.tagTextProp,r=[],l=!!n&&N(n[0],Object),d=Array.isArray(t),c=d&&t[0].value,u=function(g){return(g+"").split(s).reduce(function(p,h){var f,m=e.trim(h);return m&&p.push((rt(f={},o,m),rt(f,"value",m),f)),p},[])};if(typeof t=="number"&&(t=t.toString()),typeof t=="string"){if(!t.trim())return[];t=u(t)}else d&&(t=t.reduce(function(g,p){if(E(p)){var h=y({},p);o in h||(o="value"),h[o]=e.trim(h[o]),(h[o]||h[o]===0)&&g.push(h)}else if(p!=null&&p!==""&&p!==void 0){var f;(f=g).push.apply(f,q(u(p)))}return g},[]));return l&&!c&&(t.forEach(function(g){var p=r.map(function(m){return m.value}),h=e.dropdown.filterListItems.call(e,g[o],{exact:!0});e.settings.duplicates||(h=h.filter(function(m){return!p.includes(m.value)}));var f=h.length>1?e.getWhitelistItem(g[o],o,h):h[0];f&&N(f,Object)?r.push(f):a!="mix"&&(g.value==null&&(g.value=g[o]),r.push(g))}),r.length&&(t=r)),t},parseMixTags:function(t){var e=this,i=this.settings,n=i.mixTagsInterpolator,s=i.duplicates,a=i.transformTag,o=i.enforceWhitelist,r=i.maxTags,l=i.tagTextProp,d=[];t=t.split(n[0]).map(function(u,g){var p,h,f,m=u.split(n[1]),b=m[0],O=d.length==r;try{if(b==+b)throw Error;h=JSON.parse(b)}catch{h=e.normalizeTags(b)[0]||{value:b}}if(a.call(e,h),O||!(m.length>1)||o&&!e.isTagWhitelisted(h.value)||!s&&e.isTagDuplicate(h.value)){if(u)return g?n[0]+u:u}else h[p=h[l]?l:"value"]=e.trim(h[p]),f=e.createTagElem(h),d.push(h),f.classList.add(e.settings.classNames.tagNoAnimation),m[0]=f.outerHTML,e.value.push(h);return m.join("")}).join(""),this.DOM.input.innerHTML=t,this.DOM.input.appendChild(document.createTextNode("")),this.DOM.input.normalize();var c=this.getTagElms();return c.forEach(function(u,g){return v(u,d[g])}),this.update({withoutChangeEvent:!0}),Ot(c,this.state.hasFocus),t},replaceTextWithNode:function(t,e){if(this.state.tag||e){e=e||this.state.tag.prefix+this.state.tag.value;var i,n,s=this.state.selection||window.getSelection(),a=s.anchorNode,o=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return a.splitText(s.anchorOffset-o),(i=a.nodeValue.lastIndexOf(e))==-1||(n=a.splitText(i),t&&a.parentNode.replaceChild(t,n)),!0}},prepareNewTagNode:function(t,e){e=e||{};var i=this.settings,n=[],s={},a=Object.assign({},t,{value:t.value+""});if(t=Object.assign({},a),i.transformTag.call(this,t),t.__isValid=this.hasMaxTags()||this.validateTag(t),t.__isValid!==!0){if(e.skipInvalid)return;if(y(s,this.getInvalidTagAttrs(t,t.__isValid),{__preInvalidData:a}),t.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(t.value)),!i.createInvalidTags)return void n.push(t.value)}return"readonly"in t&&(t.readonly?s["aria-readonly"]=!0:delete t.readonly),{tagElm:this.createTagElem(t,s),tagData:t,aggregatedInvalidInput:n}},postProcessNewTagNode:function(t,e){var i=this,n=this.settings,s=e.__isValid;s&&s===!0?this.value.push(e):(this.trigger("invalid",{data:e,index:this.value.length,tag:t,message:s}),n.keepInvalidTags||setTimeout(function(){return i.removeTags(t,!0)},1e3)),this.dropdown.position()},selectTag:function(t,e){var i=this;if(!this.settings.enforceWhitelist||this.isTagWhitelisted(e.value)){this.state.actions.selectOption&&setTimeout(function(){return i.setRangeAtStartEnd(!1,i.DOM.input)});var n=this.getLastTag();return n?this.replaceTag(n,e):this.appendTag(t),this.value[0]=e,this.update(),this.trigger("add",{tag:t,data:e}),[t]}},addEmptyTag:function(t){var e=y({value:""},t||{}),i=this.createTagElem(e);v(i,e),this.appendTag(i),this.editTag(i,{skipValidation:!0}),this.toggleFocusClass(!0)},addTags:function(t,e,i){var n=this,s=[],a=this.settings,o=[],r=document.createDocumentFragment(),l=[];if(!t||t.length==0)return s;switch(t=this.normalizeTags(t),a.mode){case"mix":return this.addMixTags(t);case"select":e=!1,this.removeAllTags()}return this.DOM.input.removeAttribute("style"),t.forEach(function(d){var c=n.prepareNewTagNode(d,{skipInvalid:i||a.skipInvalid});if(c){var u=c.tagElm;if(d=c.tagData,o=c.aggregatedInvalidInput,s.push(u),a.mode=="select")return n.selectTag(u,d);r.appendChild(u),n.postProcessNewTagNode(u,d),l.push({tagElm:u,tagData:d})}}),this.appendTag(r),l.forEach(function(d){var c=d.tagElm,u=d.tagData;return n.trigger("add",{tag:c,index:n.getTagIdx(u),data:u})}),this.update(),t.length&&e&&(this.input.set.call(this,a.createInvalidTags?"":o.join(a._delimiters)),this.setRangeAtStartEnd(!1,this.DOM.input)),this.dropdown.refilter(),s},addMixTags:function(t){var e=this;if((t=this.normalizeTags(t))[0].prefix||this.state.tag)return this.prefixedTextToTag(t[0]);var i=document.createDocumentFragment();return t.forEach(function(n){var s=e.prepareNewTagNode(n);i.appendChild(s.tagElm),e.insertAfterTag(s.tagElm),e.postProcessNewTagNode(s.tagElm,s.tagData)}),this.appendMixTags(i),i.children},appendMixTags:function(t){var e=!!this.state.selection;e?this.injectAtCaret(t):(this.DOM.input.focus(),(e=this.setStateSelection()).range.setStart(this.DOM.input,e.range.endOffset),e.range.setEnd(this.DOM.input,e.range.endOffset),this.DOM.input.appendChild(t),this.updateValueByDOMTags(),this.update())},prefixedTextToTag:function(t){var e,i,n,s=this,a=this.settings,o=(e=this.state.tag)===null||e===void 0?void 0:e.delimiters;if(t.prefix=t.prefix||this.state.tag?this.state.tag.prefix:(a.pattern.source||a.pattern)[0],n=this.prepareNewTagNode(t),i=n.tagElm,this.replaceTextWithNode(i)||this.DOM.input.appendChild(i),setTimeout(function(){return i.classList.add(s.settings.classNames.tagNoAnimation)},300),this.update(),!o){var r=this.insertAfterTag(i)||i;setTimeout(A,0,r)}return this.state.tag=null,this.postProcessNewTagNode(i,n.tagData),i},appendTag:function(t){var e=this.DOM,i=e.input;e.scope.insertBefore(t,i)},createTagElem:function(t,e){t.__tagId=pt();var i,n=y({},t,yt({value:j(t.value+"")},e));return function(s){for(var a,o=document.createNodeIterator(s,NodeFilter.SHOW_TEXT,null,!1);a=o.nextNode();)a.textContent.trim()||a.parentNode.removeChild(a)}(i=this.parseTemplate("tag",[n,this])),v(i,t),i},reCheckInvalidTags:function(){var t=this,e=this.settings;this.getTagElms(e.classNames.tagNotAllowed).forEach(function(i,n){var s=v(i),a=t.hasMaxTags(),o=t.validateTag(s),r=o===!0&&!a;if(e.mode=="select"&&t.toggleScopeValidation(o),r)return s=s.__preInvalidData?s.__preInvalidData:{value:s.value},t.replaceTag(i,s);i.title=a||o})},removeTags:function(t,e,i){var n,s=this,a=this.settings;if(t=t&&N(t,HTMLElement)?[t]:N(t,Array)?t:t?[t]:[this.getLastTag()].filter(function(o){return o}),n=t.reduce(function(o,r){r&&typeof r=="string"&&(r=s.getTagElmByValue(r));var l=v(r);return r&&l&&!l.readonly&&o.push({node:r,idx:s.getTagIdx(l),data:v(r,{__removed:!0})}),o},[]),i=typeof i=="number"?i:this.CSSVars.tagHideTransition,a.mode=="select"&&(i=0,this.input.set.call(this)),n.length==1&&a.mode!="select"&&n[0].node.classList.contains(a.classNames.tagNotAllowed)&&(e=!0),n.length)return a.hooks.beforeRemoveTag(n,{tagify:this}).then(function(){var o=function(r){r.node.parentNode&&(r.node.parentNode.removeChild(r.node),e?a.keepInvalidTags&&this.trigger("remove",{tag:r.node,index:r.idx}):(this.trigger("remove",{tag:r.node,index:r.idx,data:r.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),a.keepInvalidTags&&this.reCheckInvalidTags()))};i&&i>10&&n.length==1?(function(r){r.node.style.width=parseFloat(window.getComputedStyle(r.node).width)+"px",document.body.clientTop,r.node.classList.add(a.classNames.tagHide),setTimeout(o.bind(this),i,r)}).call(s,n[0]):n.forEach(o.bind(s)),e||(s.removeTagsFromValue(n.map(function(r){return r.node})),s.update(),a.mode=="select"&&a.userInput&&s.setContentEditable(!0))}).catch(function(o){})},removeTagsFromDOM:function(){this.getTagElms().forEach(function(t){return t.remove()})},removeTagsFromValue:function(t){var e=this;(t=Array.isArray(t)?t:[t]).forEach(function(i){var n=v(i),s=e.getTagIdx(n);s>-1&&e.value.splice(s,1)})},removeAllTags:function(t){var e=this;t=t||{},this.value=[],this.settings.mode=="mix"?this.DOM.input.innerHTML="":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout(function(){e.DOM.input.focus()}),this.settings.mode=="select"&&(this.input.set.call(this),this.settings.userInput&&this.setContentEditable(!0)),this.update(t)},postUpdate:function(){this.state.blockChangeEvent=!1;var t,e,i=this.settings,n=i.classNames,s=i.mode=="mix"?i.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(n.hasMaxTags,this.value.length>=i.maxTags),this.toggleClass(n.hasNoTags,!this.value.length),this.toggleClass(n.empty,!s),i.mode=="select"&&this.toggleScopeValidation((e=this.value)===null||e===void 0||(t=e[0])===null||t===void 0?void 0:t.__isValid)},setOriginalInputValue:function(t){var e=this.DOM.originalInput;this.settings.mixMode.integrated||(e.value=t,e.tagifyValue=e.value,this.setPersistedData(t,"value"))},update:function(t){clearTimeout(this.debouncedUpdateTimeout),this.debouncedUpdateTimeout=setTimeout((function(){var e=this.getInputValue();this.setOriginalInputValue(e),this.settings.onChangeAfterBlur&&(t||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent(),this.postUpdate()}).bind(this),100),this.events.bindOriginaInputListener.call(this,100)},getInputValue:function(){var t=this.getCleanValue();return this.settings.mode=="mix"?this.getMixedTagsAsString(t):t.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(t):JSON.stringify(t):""},getCleanValue:function(t){return ct(t||this.value,this.dataProps)},getMixedTagsAsString:function(){var t="",e=this,i=this.settings,n=i.originalInputValueFormat||JSON.stringify,s=i.mixTagsInterpolator;return function a(o){o.childNodes.forEach(function(r){if(r.nodeType==1){var l=v(r);if(r.tagName=="BR"&&(t+=`\r
`),l&&V.call(e,r)){if(l.__removed)return;t+=s[0]+n(ot(l,e.dataProps))+s[1]}else r.getAttribute("style")||["B","I","U"].includes(r.tagName)?t+=r.textContent:r.tagName!="DIV"&&r.tagName!="P"||(t+=`\r
`,a(r))}else t+=r.textContent})}(this.DOM.input),t}},at.prototype.removeTag=at.prototype.removeTags;export{ee as N,at as Q};
