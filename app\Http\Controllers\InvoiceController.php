<?php

namespace App\Http\Controllers;

use App\Mail\InvoiceEmail;
use Illuminate\Support\Facades\Mail;
use App\Models\Invoice;
use App\Models\Quotation;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\PaymentReceive;
use App\Models\SerialNumbers;
use App\Models\InvoiceDetail;
use App\Models\Product;
use App\Models\EmailTemplate;
use App\Models\MailConfig;
use App\Models\SendEmail;
use App\Models\BankInfo;
use App\Models\CustomerTransaction;
use App\Models\User;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseTransaction;
use App\Models\PurchaseOrderReceives;
use App\Models\BankTransaction;
use App\Models\Company;
use App\Models\CustomerCreditDetails;
use App\Models\CustomerCredit;
use App\Models\Challan;
use App\Models\ChallanDetail;
use App\Models\CreditNote;
use App\Models\Document;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Requests\invoiceStoreRequest;
use App\Http\Requests\invoicePaymentRequest;
use App\Http\Requests\ChallanInvoiceRequest;
use App\Http\Requests\CreditNoteRequest;
use App\Models\PurchaseOrderReceiveDetails;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Traits\CommonTrait;
use App\Mail\SendInvoiceEmail;  // You'll create this email class later
use App\Traits\QueryTrait;
use Illuminate\Support\Facades\Validator;
use Config;
use PDF;

class InvoiceController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Invoice')->only(['index']);
        $this->middleware('permission:Create Invoice')->only(['create', 'store']);
        $this->middleware('permission:Edit Invoice')->only(['edit', 'store']);
        $this->middleware('permission:Delete Invoice')->only('destroy');
        $this->middleware('permission:View Invoice')->only('view');
        $this->middleware('permission:Create Stock Transfer')->only(['stockTransfer', 'store']);
        $this->middleware('permission:Edit Stock Transfer')->only(['stockTransferEdit', 'store']);
        $this->middleware('permission:Receive Payment')->only(['receivePayment']);
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $bankId = $request->input('bank_id');

        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $salesUserId = $request->input('sales_user_id');
        $categoryId = $request->input('category');
        $invoice_type = $request->input('invoice_type');
        $query = Invoice::with('invoiceDetail.serialnumbers.product', 'credit.paymentreceive.bankInfo', 'customers', 'documents', 'organization', 'challan', 'invoiceDetail.product');
        if ($customerId) {
            $query->where('customer_id', $customerId);
        }
        if ($invoice_type) {
            $query->where('invoice_type', $invoice_type);
        }
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }
        if($categoryId) {
            $query->where('category', $categoryId);
        }
        if(!empty($search)){
            $query->where(function ($query) use ($search) {
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })
                ->orWhere('invoice_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            });
        }

        $searchableFields = ['invoice_no', 'invoice_type', 'category', 'customers.customer_name','date', 'total_amount', 'paid_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        // $data = $query->orderBy('id', 'desc')->paginate(20);
        $data = $query
            ->orderByRaw("
                CAST(SUBSTRING_INDEX(invoice_no, '/', -1) AS CHAR) DESC,
                CAST(SUBSTRING_INDEX(SUBSTRING(invoice_no, 3), '/', 1) AS UNSIGNED) DESC
            ")
            ->paginate(20);
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, email')->orderByRaw('customer_name')->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $invoicetypes = Config::get('constants.invoiceType');
        $category = Config::get('constants.quotationList');
        $emailTemplates = EmailTemplate::select('template_name as name', 'id', 'email_subject', 'content')->get();
        $email = MailConfig::select('email as name','id')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allCustomers);
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $salesuser->prepend($allSalesuser);
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $invoiceMCbank = Config::get('constants.invoiceMCBankinfo');
        $invoiceHCbank = Config::get('constants.invoiceHCBankinfo');
        $invoiceNOXbank = Config::get('constants.invoiceNOXBankinfo');
        $terms = Config::get('constants.invoiceTermsAndConditions');
        $pagetypes = collect(Config::get('constants.pageTypes'));
        $data->withQueryString()->links();
        $user_id = Auth::user()->id;
        $permissions = [
            'canCreateInvoice' => auth()->user()->can('Create Invoice'),
            'canEditInvoice'   => auth()->user()->can('Edit Invoice'),
            'canDeleteInvoice' => auth()->user()->can('Delete Invoice'),
            'canViewInvoice'   => auth()->user()->can('View Invoice'),
            'canStockTransfer'   => auth()->user()->can('Create Stock Transfer'),
            'canStockTransferEdit'=> auth()->user()->can('Edit Stock Transfer'),
            'canReceivePayment'   => auth()->user()->can('Receive Payment')
        ];
        return Inertia::render('Invoice/List', compact('data', 'user_id', 'permissions', 'emailTemplates', 'email', 'filepath', 'organization', 'customers', 'paymentType', 'bankinfo', 'terms', 'invoiceMCbank', 'invoiceHCbank', 'invoiceNOXbank', 'pagetypes', 'organizationId', 'customerId', 'invoicetypes', 'invoice_type', 'salesuser', 'salesUserId', 'category', 'categoryId'));
    }

    public function create()
    {
        $invoice_no = $this->generateInvoiceNo();
        $retail_invoice_no = $this->generateRetailInvoiceNo();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', null)->orderByRaw('customer_name')->get();
        // $invoice_details = InvoiceDetail::with('invoice', 'product')->orderBy('created_at', 'desc')->get()->take(400);
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date,  receive_qty,  sell_qty, organization_id')->whereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('salesProducts')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.productCategoryList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('Invoice/Add', compact('invoice_no', 'retail_invoice_no', 'customers', 'serialno', 'products', 'organization', 'category', 'salesuser'));
    }

    public function getPreviousInvoice(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|integer',
            'organization_id' => 'required|integer',
            'product_id' => 'required|integer',
        ]);

        $previousInvoice = InvoiceDetail::with('invoice', 'product')
            ->whereHas('invoice', function ($query) use ($request) {
                $query->where('customer_id', $request->customer_id)
                    ->where('organization_id', $request->organization_id);
            })
            ->where('product_id', $request->product_id)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($previousInvoice) {
            return response()->json([
                'success' => true,
                'data' => [
                    'product_name' => $previousInvoice->product->name,
                    'hsn_code' => $previousInvoice->product->hsn_code,
                    'qty' => $previousInvoice->qty,
                    'price' => $previousInvoice->price,
                    'gst' => $previousInvoice->gst,
                    'total_amount' => $previousInvoice->total_amount,
                ],
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No previous invoice found for the selected criteria.',
        ]);
    }

    public function stockTransfer()
    {
        $invoice_no = $this->generateInvoiceNo();
        $retail_invoice_no = $this->generateRetailInvoiceNo();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', '!=', '')->orderByRaw('customer_name')->get();
        // $invoice_details = InvoiceDetail::with('invoice', 'product')->orderBy('created_at', 'desc')->get()->take(400);
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date,  receive_qty,  sell_qty, organization_id')->whereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('salesProducts')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $organization  = Organization::select('id', 'name')->get();
        $category = Config::get('constants.productCategoryList');
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('Invoice/TransferStockAdd', compact('invoice_no', 'retail_invoice_no', 'customers', 'serialno', 'products', 'organization', 'category', 'salesuser'));
    }

    public function edit(string $id)
    {
        $data      = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'customers', 'documents', 'organization', 'invoiceDetail.product')->get()->toArray();
        $getSelectedNo = InvoiceDetail::where('invoice_id', $id)->with('serialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['serialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['serialnumbers']['id'];
            }
        }
        $filepath  = Config::get('constants.uploadFilePath.Invoice');
        $category = Config::get('constants.productCategoryList');
        $customers =  Customer::where(['status' => '1', 'customer_type' => $data[0]['invoice_type']])->where('organization_id', null)->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->orderByRaw('customer_name')->get();
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('Invoice/Edit', compact('data', 'category','filepath', 'customers', 'products', 'serialno', 'salesuser'));
    }

    public function stockTransferEdit(string $id)
    {
        $data      = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'customers', 'documents', 'organization', 'invoiceDetail.product')->get()->toArray();
        $getSelectedNo = InvoiceDetail::where('invoice_id', $id)->with('serialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['serialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['serialnumbers']['id'];
            }
        }
        $filepath  = Config::get('constants.uploadFilePath.Invoice');
        $customers =  Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id, person_name, id, gst_type, customer_type')->where('organization_id', '!=', '')->orderByRaw('customer_name')->get();
        // $invoice_details = InvoiceDetail::with('invoice', 'product')->orderBy('created_at', 'desc')->get();
        // $invoice_details = InvoiceDetail::with('invoice', 'product')->orderBy('created_at', 'desc')->get()->take(400);
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        return Inertia::render('Invoice/TransferStockEdit', compact('data', 'filepath', 'customers', 'products', 'serialno', 'salesuser'));
    }

    public function creditNoteAdd(string $id)
    {
        $credit_no = $this->generateCreditNo();
        $data      = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'customers', 'documents', 'organization', 'invoiceDetail.product')->get()->toArray();
        $getSelectedNo = InvoiceDetail::where('invoice_id', $id)->with('serialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['serialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['serialnumbers']['id'];
            }
        }
        $filepath  = Config::get('constants.uploadFilePath.Invoice');
        $category = Config::get('constants.productCategoryList');
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        return Inertia::render('Invoice/CreditNoteAdd', compact('credit_no', 'data', 'category','filepath', 'products', 'serialno'));
    }

    public function store(invoiceStoreRequest $request)
    {
        DB::beginTransaction();
        $data = $request->all();
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        try {
            if(isset($data['invoice_id'])){
                $customerTransaction = CustomerTransaction::where(['entity_type' => 'invoice', 'entity_id' => $data['invoice_id']])->first();
                $customerTransaction->update(['amount' => $data['total_amount'], 'customer_id' => $data['customer_id']]);
                $updateInvoice = Invoice::where('id', $data['invoice_id'])->update([
                    'customer_id' => $data['customer_id'],
                    'note'        => $data['note'],
                    'date'        => $data['date'],
                    'category'        => $data['category'],
                    'sales_user_id'        => $data['sales_user_id'],
                    'igst'        => $data['igst'],
                    'sgst'        => $data['sgst'],
                    'cgst'        => $data['cgst'],
                    'total_discount' => $data['total_discount'],
                    'overall_discount' => $data['overall_discount'],
                    'discount_before_tax' => $data['discount_before_tax'],
                    'total_gst'   => $data['total_gst'],
                    'sub_total'   => $data['sub_total'],
                    'total_amount'=> $data['total_amount'],
                    'pending_amount'=> $data['total_amount'],
                    'dispatch' => $data['dispatch'],
                    'transport' => $data['transport'],
                    'patient_name' => $data['patient_name'],
                    'customer_po_date' => $data['customer_po_date'],
                    'customer_po_number' => $data['customer_po_number'],
                    'eway_bill' => $data['eway_bill'],
                    'due_days' => $data['due_days'],
                    'cr_dr_note' => $data['cr_dr_note']
                ]);

                if(isset($data['stock_transfer']) && $data['stock_transfer'] == 'yes'){
                    $updatePurchaseOrder = PurchaseOrder::where('id', $data['purchase_order_id'])->update([
                        'igst'        => $data['igst'],
                        'sgst'        => $data['sgst'],
                        'cgst'        => $data['cgst'],
                        'date'        => $data['date'],
                        'total_gst'   => $data['total_gst'],
                        'sub_total'   => $data['sub_total'],
                        'total_amount'=> $data['total_amount'],
                        'total_discount' => $data['total_discount'],
                        'overall_discount' => $data['overall_discount']
                    ]);

                    $updatePurchaseOrderReceives = PurchaseOrderReceives::where('id', $data['purchase_order_receive_id'])->update([
                        'total_price'      => $data['sub_total'],
                        'total_gst_amount' => $data['total_gst'],
                        'total_amount'     => $data['total_amount'],
                        'pending_amount'   => $data['total_amount'],
                        'po_receive_date'  => $data['date'],
                        'customer_invoice_date' => $data['date']
                    ]);

                    $purchaseTransaction = PurchaseTransaction::where(['entity_type' => 'purchase_invoice', 'entity_id' => $data['purchase_order_receive_id']])->first();
                    $purchaseTransaction->update(['amount' => $data['total_amount']]);

                    if($updatePurchaseOrder){
                        foreach ($data['selectedProductItem'] as $invoiceDetails) {

                            if(empty($invoiceDetails['invoice_detail_id'])) {
                                $customer = Customer::where('id', $data['customer_id'])->first();
                                $invoiceDetails['purchase_order_id'] = $data['purchase_order_id'];
                                $invoiceDetails['receive_qty'] = $invoiceDetails['qty'];
                                $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                                $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                                $purchaseOrderDetail = PurchaseOrderDetail::create($invoiceDetails);
                                $receivedProduct['purchase_order_receive_id'] = $data['purchase_order_receive_id'];
                                $receivedProduct['purchase_order_detail_id'] = $purchaseOrderDetail->id;
                                $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                                $receivedProduct['organization_id'] = $customer->organization_id;
                                $receivedProduct['product_id'] = $invoiceDetails['product_id'];
                                $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                                $receivedProduct['updated_by'] = $receivedProduct['created_by'] = Auth::user()->id;
                                $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);

                                $serialNo = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                                $productDetails['batch']            = $serialNo->batch;
                                $productDetails['organization_id']  = $customer->organization_id;
                                $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                                $productDetails['product_id']       = $invoiceDetails['product_id'];
                                $productDetails['mrp']              = $serialNo->mrp;
                                $productDetails['expiry_date']      = $serialNo->expiry_date;
                                $productDetails['purchase_price']   = $invoiceDetails['sell_price'];
                                $productDetails['receive_qty']      = $invoiceDetails['qty'];
                                $productDetails['sell_qty']         = 0;
                                $productDetails['created_by']       = $data['created_by'];
                                $productDetails['updated_by']       = $data['created_by'];
                                $productDetails['unique_id']        = $serialNo->unique_id;
                                $createProduct =  SerialNumbers::create($productDetails);
                            }
                        }
                    }
                }

                foreach ($data['selectedProductItem'] as $invoiceDetails) {
                    $invoiceDetails['invoice_id'] = $data['invoice_id'];
                    $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                    if (!empty($invoiceDetails['invoice_detail_id'])) {
                        $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetail = InvoiceDetail::find($invoiceDetails['invoice_detail_id']);
                        $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                        $updatedQty =  $getSellQty->sell_qty + $invoiceDetails['qty'] - $invoiceDetail->qty;
                        $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                        $invoiceDetail->update($invoiceDetails);
                    } else {
                        $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetail = InvoiceDetail::create($invoiceDetails);
                        Product::addProductLog($invoiceDetail, $invoiceDetails, $invoiceDetails['product_id'], $data['organization_id'], 'payment');
                        $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                        $updatedQty = $getSellQty->sell_qty + $invoiceDetails['qty'] ;
                        $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                    }
                }

                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['invoice_id']);
                }
                DB::commit();
                return Redirect::to('/invoice')->with('success','Invoice Updated Successfully');
            } else {
                $data['overall_discount'] = $data['overall_discount'] ?? 0;
                $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
                $data['status'] = "Unpaid";
                $data['entity_id'] = null;
                $data['entity_type'] = "invoice";
                // if($data['invoice_type'] == 'Tax'){
                //     $this->updateInvoiceNo($data['invoice_no'],  $data['organization_id']);
                // } else {
                //     $this->updateRetailInvoiceNo($data['invoice_no'], $data['organization_id']);
                // }
                $data['pending_amount'] = $data['total_amount'];// TO DO CHECK WHILE EDIT

                if ($data['invoice_type'] == 'Tax') {
                    $invoiceNumbers = $this->generateInvoiceNo();
                    $invoice_no = $invoiceNumbers[$data['organization_id']] ?? '';
                    $data['invoice_no'] = $invoice_no;
                    $this->updateInvoiceNo($invoice_no, $data['organization_id']);
                } else {
                    $invoiceNumbers = $this->generateRetailInvoiceNo();
                    $invoice_no = $invoiceNumbers[$data['organization_id']] ?? '';
                    $data['invoice_no'] = $invoice_no;
                    $this->updateRetailInvoiceNo($invoice_no, $data['organization_id']);
                }

                $invoice = Invoice::create($data);

                $data['amount'] = $data['total_amount'];
                $data['payment_type'] = 'dr';
                $data['entity_id'] = $invoice->id;
                $data['entity_type'] = 'invoice';
                $data['note'] = 'Invoice No :' .$data['invoice_no'];
                CustomerTransaction::create($data);

                if(isset($data['stock_transfer']) && $data['stock_transfer'] == 'yes'){
                    //purchase order
                    $customer = Customer::where('id', $data['customer_id'])->first();
                    $organization = Organization::where('id', $data['organization_id'])->first();
                    $company  = Company::where('name', 'like', $organization->name)->first();
                    $po_number = $this->generatePONumber();
                    $data['po_number'] = $po_number[$customer->organization_id];
                    $data['organization_id'] = $customer->organization_id;
                    $data['company_id'] = $company->id;
                    $data['status'] = "Completed";
                    $data['note'] = "stock transfer";
                    $purchaseOrder = PurchaseOrder::create($data);

                    //purchase order receive
                    $po_receive_number = $this->generatePOReceiveNumber();
                    $data['po_receive_number'] = $po_receive_number[$customer->organization_id];
                    $data['po_receive_date'] =  $data['customer_invoice_date'] = $data['date'];
                    $data['customer_invoice_no'] = $data['invoice_no'];
                    $data['purchase_order_id'] = $purchaseOrder->id;
                    $data['total_price'] = $data['sub_total'];
                    $data['total_gst_amount'] = $data['total_gst'];
                    $data['status'] = "Unpaid";
                    $purchaseOrderReceiveId = PurchaseOrderReceives::create($data);

                    $invoiceUpdate = Invoice::where('id',  $invoice->id);
                    $invoiceUpdate->update(['purchase_order_id'=> $purchaseOrder->id, 'purchase_order_receive_id' => $purchaseOrderReceiveId->id]);

                    //purchase transaction
                    $data['amount'] = $data['total_amount'];
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'Bill Number: ' .$data['customer_invoice_no']. ' ' .'Date: ' .$data['customer_invoice_date'];
                    $data['entity_id'] = $purchaseOrderReceiveId->id;
                    $data['date'] = $purchaseOrderReceiveId->customer_invoice_date;
                    $data['entity_type'] = 'purchase_invoice';
                    PurchaseTransaction::create($data);

                    $this->updatePONumber($data['po_number'], $data['organization_id']);
                    $this->updatePOReceiveNumber($data['po_receive_number'], $data['organization_id']);

                    if($purchaseOrder){
                        foreach ($data['selectedProductItem'] as $invoiceDetails) {
                            $invoiceDetails['purchase_order_id'] = $purchaseOrder->id;
                            $invoiceDetails['receive_qty'] = $invoiceDetails['qty'];
                            $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                            $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                            $purchaseOrderDetail = PurchaseOrderDetail::create($invoiceDetails);

                            $receivedProduct['purchase_order_receive_id'] = $purchaseOrderReceiveId->id;
                            $receivedProduct['purchase_order_detail_id'] = $purchaseOrderDetail->id;
                            $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                            // $receivedProduct['organization_id'] = $data['organization_id'];
                            $receivedProduct['product_id'] = $invoiceDetails['product_id'];
                            $receivedProduct['receive_qty'] = $invoiceDetails['qty'];
                            $receivedProduct['organization_id'] = $customer->organization_id;
                            $receivedProduct['updated_by'] = $receivedProduct['created_by'] = Auth::user()->id;
                            $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);

                            $serialNo = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                            $productDetails['batch']            = $serialNo->batch;
                            $productDetails['organization_id']  = $customer->organization_id;
                            $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                            $productDetails['product_id']       = $invoiceDetails['product_id'];
                            $productDetails['mrp']              = $serialNo->mrp;
                            $productDetails['expiry_date']      = $serialNo->expiry_date;
                            $productDetails['purchase_price']   = $invoiceDetails['sell_price'];
                            $productDetails['receive_qty']      = $invoiceDetails['qty'];
                            $productDetails['sell_qty']         = 0;
                            $productDetails['created_by']       = $data['created_by'];
                            $productDetails['updated_by']       = $data['created_by'];
                            $productDetails['unique_id']        = $serialNo->unique_id;
                            $createProduct =  SerialNumbers::create($productDetails);
                            Product::addProductLog($createProduct, $productDetails, $invoiceDetails['product_id'], $data['organization_id'], 'received');
                        }
                    }
                }

                if($invoice){
                    foreach ($data['selectedProductItem'] as $invoiceDetails) {
                        $invoiceDetails['invoice_id'] = $invoice->id;
                        $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = $data['created_by'];
                        $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                        $invoiceDetail = InvoiceDetail::create($invoiceDetails);

                        Product::addProductLog($invoiceDetail, $invoiceDetails, $invoiceDetails['product_id'],  $invoice->organization_id, 'payment');

                        $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                        $updatedQty = $getSellQty->sell_qty + $invoiceDetails['qty'];
                        if($getSellQty){
                            $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                        }
                    }
                }

                $files = $request->file('document');
                if($files){
                    $this->uploadInvoiceDocuments($files, $invoice->id);
                }
                DB::commit();
                return Redirect::to('/invoice')->with('success','Invoice Created Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/invoice')->with('error', $e->getMessage());
        }
    }

    public function view(Request $request, $id)
    {
       $data = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers', 'customers', 'documents', 'organization', 'paymentReceive.bankInfo', 'invoiceDetail.product', 'users')->get()->toArray();
       return Inertia::render('Invoice/View', compact('data'));
    }

    public function update(Request $request, string $id)
    {
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $data = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers')->first();
            if($data->entity_type == 'invoice'){
                foreach ($data->invoiceDetail as $detail) {
                    $serialnumbers = $detail->serialnumbers;
                    $serialnumbers->sell_qty = $serialnumbers->sell_qty - $detail->qty;
                    $serialnumbers->save();
                }

            } else {
                $challanIds = explode(',',$data->challan_ids);
                foreach ($data->invoiceDetail as $detail) {
                    if(empty($detail->challan_detail_id)){
                        $serialnumbers = $detail->serialnumbers;
                        $serialnumbers->sell_qty = $serialnumbers->sell_qty - $detail->qty;
                        $serialnumbers->save();
                    } else {
                        $challanDetail = ChallanDetail::find($detail->challan_detail_id);
                        $updateInvoicedQty['invoiced_qty'] = $challanDetail->invoiced_qty - $detail->qty;
                        $challanDetail->update($updateInvoicedQty);
                    }
                }
                foreach($challanIds as $key => $value){
                    $challanDetail = ChallanDetail::where('challan_id', $value)->where('is_receive', NULL);
                    $totalQty = $challanDetail->sum('qty');
                    $totalInvoicedQty = $challanDetail->sum('invoiced_qty') + $challanDetail->sum('return_qty');
                    if ($totalInvoicedQty == $totalQty) {
                        $status = 'Close';
                    } elseif ($totalInvoicedQty > 0) {
                        $status = 'In-process';
                    } else {
                        $status = 'Open';
                    }
                    $updateChallan = Challan::where('id', $value)->update([
                        'status' => $status
                    ]);
                }
            }
            $customerTransaction = CustomerTransaction::where(['customer_id' => $data->customer_id, 'organization_id' => $data->organization_id, 'entity_id' => $data->id, 'entity_type' => 'invoice'])->first();
            $customerTransaction->delete();
            $deleteInvoice = Invoice::find($id);
            $deleteInvoice->delete();
            $deleteInvoiceDetail = InvoiceDetail::where('invoice_id', $id)->delete();
            DB::commit();
            return Redirect::back()->with('success', 'Invoice Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function destroyStockTransfer($id)
    {
        DB::beginTransaction();
        try {
            $invoice = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers')->first();
            foreach ($invoice->invoiceDetail as $detail) {
                $serialnumbers = $detail->serialnumbers;
                $serialnumbers->sell_qty = $serialnumbers->sell_qty - $detail->qty;
                $serialnumbers->save();
            }

            $purchaseOrderReceiveDetails = PurchaseOrderReceiveDetails::where(['purchase_order_receive_id' => $invoice->purchase_order_receive_id])->get();
            foreach($purchaseOrderReceiveDetails as $purchaseOrderReceiveDetail){
                $serialNumbers = SerialNumbers::where('purchase_order_receive_detail_id', $purchaseOrderReceiveDetail->id)->get();
                foreach($serialNumbers as $serialNumber){
                    if($serialNumber->sell_qty == 0){
                        $serialNumber->delete();
                    } else{
                        return Redirect::back()->with('error', 'Invoice Generated Product cannot be Remove');
                    }
                }
            }

            $purchseTransection = PurchaseTransaction::where(['entity_id' => $invoice->purchase_order_receive_id, 'entity_type' => 'purchase_invoice'])->first();
            $purchseTransection->delete();

            $purchaseOrderReceive = PurchaseOrderReceives::find($invoice->purchase_order_receive_id);
            $purchaseOrderReceive->purchaseOrderReceiveDetails()->delete();
            $purchaseOrderReceive->delete();

            $purchaseOrder = PurchaseOrder::find($invoice->purchase_order_id);
            $purchaseOrder->purchaseOrderDetail()->delete();
            $purchaseOrder->delete();

            $customerTransaction = CustomerTransaction::where(['entity_id' => $invoice->id, 'entity_type' => 'invoice'])->first();
            $customerTransaction->delete();

            $invoice->invoiceDetail()->delete();
            $invoice->delete();

            DB::commit();
            return Redirect::back()->with('success', 'Transferred Invoice deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->with('error',$e->getMessage());
        }
    }

    //temperory close same functionality given from receipt
    // public function receivePayment(invoicePaymentRequest $request)
    // {
    //     $requestData = $request->all();
    //     DB::beginTransaction();
    //     try {
    //         $data = $requestData['form'];
    //         if($requestData['is_credit'] == 'Yes'){
    //             foreach($requestData['credit_data'] as $credit){
    //                 if(isset($credit['amount_to_credit'])){
    //                     $InvoiceDetail = Invoice::find($data['invoice_id']);
    //                     $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $credit['amount_to_credit'];
    //                     $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $credit['amount_to_credit'];
    //                     if ($updateData['pending_amount'] <= 0) {
    //                         $updateData['status'] = 'Paid';
    //                     } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
    //                         $updateData['status'] = 'Partially Paid';
    //                     } else {
    //                         $updateData['status'] = 'Unpaid';
    //                     }
    //                     $InvoiceDetail->update($updateData);

    //                     $credit['invoice_id'] = $data['invoice_id'];
    //                     $credit['credit_id'] = $credit['id'];
    //                     $credit['amount'] = $credit['amount_to_credit'];
    //                     $credit['date'] = date('Y-m-d');
    //                     $credit['created_by'] = $credit['updated_by'] = Auth::user()->id;
    //                     $creditDetails = CustomerCreditDetails::create($credit);
    //                     if($creditDetails){
    //                         $creditInfo = CustomerCredit::with('paymentreceive')->where('id', $credit['id'])->first();
    //                         $data['created_by'] = $data['updated_by'] = Auth::user()->id;
    //                         $data['entity_id'] = $creditDetails->id;
    //                         $data['entity_type'] = 'credit_details';
    //                         $data['organization_id'] = $data['organization_id'];
    //                         $data['customer_id'] = $data['customer_id'];
    //                         $data['payment_type'] = 'cr';
    //                         $data['amount'] = $credit['amount_to_credit'];
    //                         $data['date'] = date('Y-m-d');
    //                         if($creditInfo->paymentreceive->payment_type == 'check'){
    //                             $data['note'] = 'Cheque No:' .$creditInfo->paymentreceive->check_number .' '.'Invoice No:' .$data['invoice_no'];
    //                         } else if($creditInfo->paymentreceive->payment_type == 'NEFT'){
    //                             $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$data['invoice_no'];
    //                         } else if($creditInfo->paymentreceive->payment_type == 'cash'){
    //                             $data['note'] = $data['note'].' '.'Invoice No:' .$data['invoice_no'];
    //                         }
    //                         $creditInfo->update(['unused_amount' => $creditInfo->unused_amount -  $credit['amount_to_credit']]);
    //                         $customerTransaction = CustomerTransaction::create($data);
    //                         DB::commit();
    //                         return Redirect::to('/invoice')->with('success','Payment Received Successfully');
    //                     }
    //                 }

    //             }
    //         } else {
    //             $data['created_by'] = $data['updated_by'] = Auth::user()->id;
    //             $receivePayment = PaymentReceive::create($data);
    //             if($receivePayment){
    //                 $InvoiceDetail = Invoice::find($data['invoice_id']);
    //                 $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $data['amount'];
    //                 $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $data['amount'];
    //                 if ($updateData['pending_amount'] <= 0) {
    //                     $updateData['status'] = 'Paid';
    //                 } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
    //                     $updateData['status'] = 'Partially Paid';
    //                 } else {
    //                     $updateData['status'] = 'Unpaid';
    //                 }
    //                 $InvoiceDetail->update($updateData);
    //             }

    //             //customer Transaction
    //             $data['entity_id'] = $receivePayment->id;
    //             $data['entity_type'] = 'payment_receive';

    //             if($data['payment_type'] == 'check'){
    //                 $data['payment_type'] = 'cr';
    //                 $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Invoice No:' .$data['invoice_no'];
    //                 $bankTransaction = BankTransaction::create($data);
    //             } else if($data['payment_type'] == 'NEFT'){
    //                 $data['payment_type'] = 'cr';
    //                 $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$data['invoice_no'];
    //                 $bankTransaction = BankTransaction::create($data);
    //             } else if($data['payment_type'] == 'cash'){
    //                 $data['payment_type'] = 'cr';
    //                 $data['note'] = $data['note'].' '.'Invoice No:' .$data['invoice_no'];
    //             }
    //             CustomerTransaction::create($data);
    //             DB::commit();
    //             return Redirect::to('/invoice')->with('success','Payment Received Successfully');
    //         }

    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         return Redirect::to('/invoice')->with('error', $e->getMessage());
    //     }
    // }

    // public function removeTransaction($id)
    // {
    //     DB::beginTransaction();
    //     try {
    //         $paymentReceive = PaymentReceive::find($id);
    //         $InvoiceDetail = Invoice::find($paymentReceive->invoice_id);
    //         $updateData['paid_amount'] = $InvoiceDetail->paid_amount - $paymentReceive->amount;
    //         $updateData['pending_amount'] = $InvoiceDetail->pending_amount + $paymentReceive->amount;
    //         if ($updateData['pending_amount'] <= 0) {
    //             $updateData['status'] = 'Paid';
    //         } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
    //             $updateData['status'] = 'Partially Paid';
    //         } else {
    //             $updateData['status'] = 'Unpaid';
    //         }
    //         $InvoiceDetail->update($updateData);
    //         $paymentReceive->delete();
    //         BankTransaction::where(['entity_id' => $id, 'entity_type' => 'payment_receive'])->delete();
    //         CustomerTransaction::where(['entity_id' => $id, 'entity_type' => 'payment_receive'])->delete();
    //         DB::commit();
    //         return Redirect::back()->with('success','Transaction Removed Successfully');
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         return Redirect::back()->with('error', $e->getMessage());
    //     }
    // }

    private function uploadInvoiceDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.Invoice');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "invoice";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    // function resetCustomerTransactions()
    // {
    //     try {
    //         CustomerTransaction::truncate();
    //         $invoices = Invoice::all();

    //         foreach ($invoices as $invoice) {

    //             $customerTransaction = new CustomerTransaction([
    //                 'organization_id' => $invoice->organization_id,
    //                 'customer_id' => $invoice->customer_id,
    //                 'entity_id' => $invoice->id,
    //                 'entity_type' => 'invoice',
    //                 'payment_type' => 'dr',
    //                 'amount' => $invoice->total_amount,
    //                 'date' => $invoice->date,
    //                 'note' => 'Invoice No: ' . $invoice->invoice_no,
    //                 'created_by' => $invoice->created_by,
    //                 'updated_by' => $invoice->updated_by,
    //             ]);
    //             $customerTransaction->created_at = $invoice->created_at;
    //             $customerTransaction->updated_at = $invoice->updated_at;
    //             $customerTransaction->save();
    //         }
    //         return response()->json(['message' => 'Customer transactions re-set successfully.']);
    //     } catch (\Exception $e) {
    //         \Log::error('Error inserting customer transactions: ' . $e->getMessage());
    //         return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
    //     }
    // }

    // public function resetPurchaseTransactions()
    // {
    //     try {
    //         PurchaseTransaction::truncate();
    //         $purchaseOrders = PurchaseOrderReceives::with('purchaseOrder')->get();

    //         foreach ($purchaseOrders as $purchaseOrder) {

    //             $purchaseTransaction = new PurchaseTransaction([
    //                 'organization_id' => $purchaseOrder->purchaseOrder->organization_id,
    //                 'company_id' => $purchaseOrder->purchaseOrder->company_id,
    //                 'entity_id' => $purchaseOrder->id,
    //                 'entity_type' => 'purchase_invoice',
    //                 'payment_type' => 'cr',
    //                 'amount' => $purchaseOrder->total_amount,
    //                 'date' => $purchaseOrder->customer_invoice_date,
    //                 'note' =>  'Bill Number: ' .$purchaseOrder->customer_invoice_no. ' ' .'Date: ' .$purchaseOrder->customer_invoice_date,
    //                 'created_by' => $purchaseOrder->created_by,
    //                 'updated_by' => $purchaseOrder->updated_by,
    //             ]);

    //             $purchaseTransaction->created_at = $purchaseOrder->created_at;
    //             $purchaseTransaction->updated_at = $purchaseOrder->updated_at;

    //             $purchaseTransaction->save();
    //         }

    //         return response()->json(['message' => 'Purchase transactions re-set successfully.']);
    //     } catch (\Exception $e) {
    //         \Log::error('Error inserting purchase transactions: ' . $e->getMessage());
    //         return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
    //     }
    // }

    public function downloadInvoice($id, $type)
    {
        $data = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'credit.paymentreceive.bankInfo', 'customers', 'documents', 'organization', 'challan', 'invoiceDetail.product')->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $invoiceMCbank = Config::get('constants.invoiceMCBankinfo');
        $invoiceHCbank = Config::get('constants.invoiceHCBankinfo');
        $invoiceNOXbank = Config::get('constants.invoiceNOXBankinfo');
        $terms = Config::get('constants.invoiceTermsAndConditions');
        $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
        $pdf = PDF::loadView('pdf.invoice', compact('data', 'invoiceMCbank', 'invoiceHCbank', 'invoiceNOXbank', 'terms', 'filepath'))->setPaper('A4', $type);
    //    return $pdf->stream();
        return $pdf->download("Invoice_{$sanitizedFilename}.pdf");
    }

    public function downloadTempInvoice($id, $type)
    {
        $data = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'credit.paymentreceive.bankInfo', 'customers', 'documents', 'organization', 'challan', 'invoiceDetail.product')->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $invoiceMCbank = Config::get('constants.invoiceMCBankinfo');
        $invoiceHCbank = Config::get('constants.invoiceHCBankinfo');
        $invoiceNOXbank = Config::get('constants.invoiceNOXBankinfo');
        $terms = Config::get('constants.invoiceTermsAndConditions');
        $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
        $pdf = PDF::loadView('pdf.invoice', compact('data', 'invoiceMCbank', 'invoiceHCbank', 'invoiceNOXbank', 'terms', 'filepath'))->setPaper('A4', $type);
        $tempPath =  public_path('uploads/product-images/Invoice_' . $sanitizedFilename . '.pdf');
        $pdf->save($tempPath);
        return $tempPath;
    }

    public function downloadTempQuotation($id , $type){
        $data = Quotation::where('id', $id)->with('quotationDetail.product.serialNumbers', 'customers', 'documents', 'organization')->get();
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $productpath = Config::get('constants.uploadFilePath.productImages');
        $quotationbank = Config::get('constants.quotationBankinfo');
        $quotationHealthCareBankinfo = Config::get('constants.quotationHealthCareBankinfo');
        $quotationNoxBank = Config::get('constants.quotationNoxBankinfo');
        $sanitizedFilename = $this->sanitizeFilename($data[0]->customers->customer_name);
        $pdf = PDF::loadView('pdf.quotation', compact('data', 'filepath', 'productpath', 'quotationbank', 'quotationHealthCareBankinfo', 'quotationNoxBank'))->setPaper('A4', $type);
        $tempPath =  public_path('uploads/product-images/Quotation_' . $sanitizedFilename . '.pdf');
        $pdf->save($tempPath);
        return $tempPath;
    }

    public function editCHLInvoice($id)
    {

        $invoicedata   = Invoice::where('id', $id)->with('invoiceDetail.serialnumbers.product', 'customers', 'documents', 'organization', 'invoiceDetail.product')->get()->toArray();
        $getSelectedNo = InvoiceDetail::where('invoice_id', $id)->with('serialnumbers')->get()->toArray();
        $selectedSerialNumbersIds = [];
        foreach ($getSelectedNo as $item) {
            if (isset($item['serialnumbers']['id'])) {
                $selectedSerialNumbersIds[] = $item['serialnumbers']['id'];
            }
        }
        $serialno  = SerialNumbers::with('product')->selectRaw('CONCAT(unique_id, " - ", "(",(receive_qty - sell_qty), " Qty)") as name, id, product_id, purchase_price, mrp, expiry_date, organization_id')->whereIn('id',$selectedSerialNumbersIds)->orwhereRaw('(receive_qty - sell_qty) > 0')->get();
        $products  = Product::with('serialNumbers')->selectRaw("id as id,  CONCAT(IFNULL(item_code, ''), CASE WHEN item_code IS NOT NULL THEN ' : ' ELSE '' END, name, CASE WHEN prefix IS NOT NULL THEN CONCAT(' - ', prefix) ELSE '' END) AS name, hsn_code, gst, category, item_code")->get();
        $invoice = Invoice::findOrFail($id);
        $challanIds = explode(',',$invoice->challan_ids);

        $data = ChallanDetail::whereIn('challan_id', $challanIds)->where('is_receive', NULL)->with(['viewserialnumbers.product', 'product', 'challan.customers', 'challan.documents', 'challan.organization'])->whereRaw('qty - (invoiced_qty + return_qty) > 0')->get()->toArray();
        $challanData = Challan::whereIn('id', $challanIds)->get()->toArray();
        $filepath  = Config::get('constants.uploadFilePath.challanDocument');
        $customers = Customer::where('status', '1')->select('customer_name as name', 'person_name', 'id')->orderByRaw('customer_name')->get();
        return Inertia::render('Invoice/CHLInvoiceEdit', compact('data', 'invoicedata', 'serialno', 'products' ,'filepath', 'customers', 'challanData'));
    }

    public function editChallanInvoice(ChallanInvoiceRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $updateInvoice = Invoice::where('id', $data['invoice_id'])->update([
                'customer_id' => $data['customer_id'],
                'note'        => $data['note'],
                'date'        => $data['date'],
                'category'        => $data['category'],
                'igst'        => $data['igst'],
                'sgst'        => $data['sgst'],
                'cgst'        => $data['cgst'],
                'total_discount' => $data['total_discount'],
                'overall_discount' => $data['overall_discount'],
                'discount_before_tax' => $data['discount_before_tax'],
                'total_gst'   => $data['total_gst'],
                'sub_total'   => $data['sub_total'],
                'total_amount'=> $data['total_amount'],
                'pending_amount'=> $data['total_amount'],
                'dispatch' => $data['dispatch'],
                'transport' => $data['transport'],
                'patient_name' => $data['patient_name'],
                'customer_po_date' => $data['customer_po_date'],
                'customer_po_number' => $data['customer_po_number'],
                'eway_bill' => $data['eway_bill'],
                'due_days' => $data['due_days'],
                'cr_dr_note' => $data['cr_dr_note']
            ]);

            // $challanIds = array_column($data['challans'], 'id');
            // $challanIdsString = implode(',', $challanIds);
            // $data['challan_ids'] = $challanIdsString;
            if($updateInvoice){
                foreach ($data['invoicedProduct'] as $invoiceDetails) {
                    $invoiceDetails['invoice_id'] = $data['invoice_id'];
                    $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                    if (!empty($invoiceDetails['invoice_detail_id'])) {
                        $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetail = InvoiceDetail::find($invoiceDetails['invoice_detail_id']);
                        if (!empty($invoiceDetails['challan_detail_id'])) {
                            $invoiceDetail->update($invoiceDetails);
                        } else {
                            //create crone for old records challan_detail_id
                            $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                            $updatedQty =  $getSellQty->sell_qty + $invoiceDetails['qty'] - $invoiceDetail->qty;
                            $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                            $invoiceDetail->update($invoiceDetails);
                        }
                    } else {
                        $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetail = InvoiceDetail::create($invoiceDetails);
                        $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                        $updatedQty = $getSellQty->sell_qty + $invoiceDetails['qty'] ;
                        $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                    }
                }

                foreach ($data['selectedProductItem'] as $invoiceDetails) {
                    if($invoiceDetails['check'] == true){
                        $invoiceDetails['invoice_id'] = $data['invoice_id'];
                        $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                        $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                        $invoiceDetail = InvoiceDetail::create($invoiceDetails);

                        //invoiced Qty
                        $challanDetail = ChallanDetail::find($invoiceDetails['challan_detail_id']);
                        $updateInvoicedQty['invoiced_qty'] = $challanDetail->invoiced_qty + $invoiceDetails['qty'];
                        $challanDetail->update($updateInvoicedQty);
                    }
                }
            }

            $files = $request->file('document');
            if($files){
                $this->uploadDocuments($files, $data['invoice_id']);
            }
            //update challan status
            foreach($data['challans'] as $challans){
                $challanDetail = ChallanDetail::where('challan_id', $challans['id'])->where('is_receive', NULL);
                $totalQty = $challanDetail->sum('qty');
                $totalInvoicedQty = $challanDetail->sum('invoiced_qty') + $challanDetail->sum('return_qty');
                if ($totalInvoicedQty == $totalQty) {
                    $status = 'Close';
                } elseif ($totalInvoicedQty > 0) {
                    $status = 'In-process';
                } else {
                    $status = 'Open';
                }
                $updateChallan = Challan::where('id', $challans['id'])->update([
                    'status' => $status
                ]);
            }
            //customer Transaction update
            $customerTransaction = CustomerTransaction::where(['entity_type' => 'invoice', 'entity_id' => $data['invoice_id']])->first();
            $customerTransaction->update(['amount' => $data['total_amount']]);

            DB::commit();
            return Redirect::to('/invoice')->with('success','Challan Invoice Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/invoice')->with('error', $e->getMessage());
        }
    }

    public function creditNoteSave(CreditNoteRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
            $data['pending_amount'] = $data['total_amount'];// TO DO CHECK WHILE EDIT

            $creditNumbers = $this->generateCreditNo();
            $data['credit_note_no'] = $creditNumbers[$data['organization_id']];

            while (CreditNote::where('credit_note_no', $data['credit_note_no'])->exists()) {
                $this->updateCreditNo($data['credit_note_no'], $data['organization_id']);
                $creditNumbers = $this->generateCreditNo();
                $data['credit_note_no'] = $creditNumbers[$data['organization_id']];
            }

            $creditNote = CreditNote::create($data);
            $this->updateCreditNo($data['credit_note_no'], $data['organization_id']);

            $data['amount'] = $data['total_amount'];
            $data['payment_type'] = 'cr';
            $data['entity_id'] = $creditNote->id;
            $data['entity_type'] = 'credit_note';
            $data['note'] = 'CR Note:'. $data['debit_note_number'] . ' Invoice No :' .$data['invoice_no'];
            CustomerTransaction::create($data);
                $invoice = Invoice::findOrFail($data['invoice_id']);
                if($invoice->status == 'Unpaid'){
                    $invoice->cr_dr_note = $data['debit_note_number'];
                    $invoice->pending_amount -= $data['total_amount'];
                    $invoice->paid_amount += $data['total_amount'];
                    if ($invoice->pending_amount <= 0) {
                        $invoice->status = 'Paid';
                    } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                        $invoice->status = 'Partially Paid';
                    } else {
                        $invoice->status = 'Unpaid';
                    }
                    $invoice->save();
                } else {
                    $data['payment_type'] = 'cash';
                    $data['unused_amount'] = $data['amount'] = $data['total_amount'];
                    $data['note'] = $creditNote->id.' '.$data['debit_note_number'].' '.'Credit Note Amount';
                    $receivePayment = PaymentReceive::create($data);
                    $data['payment_receive_id'] = $receivePayment->id;
                    CustomerCredit::create($data);
                }
                foreach ($data['selectedProductItem'] as $invoiceDetails) {
                    $invoiceDetails['invoice_id'] = $data['invoice_id'];
                    $invoiceDetails['credit_note_id'] = $creditNote->id;
                    $invoiceDetails['price'] = $invoiceDetails['sell_price'];
                    $invoiceDetails['created_by'] = $invoiceDetails['updated_by'] = Auth::user()->id;
                    $invoiceDetails['is_receive'] = 'yes';
                    $invoiceDetail = InvoiceDetail::create($invoiceDetails);
                    $getSellQty = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->first();
                    $updatedQty = $getSellQty->sell_qty - $invoiceDetails['qty'] ;
                    $updateStatus  = SerialNumbers::where('id',  $invoiceDetails['serial_number_id'])->update(['sell_qty'=> $updatedQty]);
                }

            DB::commit();
            return Redirect::to('/invoice')->with('success','Credit Note Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::to('/invoice')->with('error', $e->getMessage());
        }
    }

    public function sendInvoiceEmail(Request $request)
    {
        $request->validate([
            'to' => 'required|email',
            'subject' => 'required|string',
            'content' => 'required|string',
            'cc' => 'nullable|string',
            'id' => 'required|integer',
            'pagetype' => 'required',
            'template' => 'required'
        ]);

        $ccEmails = [];
        if (!empty($request->cc)) {
            $ccEmails = array_filter(
                explode(',', $request->cc),
                fn($email) => filter_var(trim($email), FILTER_VALIDATE_EMAIL)
            );
        }

        $contentData = $this->getContent($request->id, $request->subject, $request->content, $request->user_id, $request->template);

        $smtpConfig = MailConfig::findOrFail($request->from);

        $emailData = [
            'to' => $request->to,
            'from' => $smtpConfig->username,
            'subject' => $contentData['subject'],
            'content' => $contentData['content'],
            'pdf' => $contentData['pdf'],
            'cc' => $ccEmails,
            'user_id' => $request->user_id, // Ensure user_id is validated/passed
            'subject_id' => $request->id,
            'subject_type' => $request->template == 'Invoice'
                                ? 'App\Models\Invoice'
                                : 'App\Models\Quotation',
        ];

        if (!$smtpConfig) {
            return response()->json(['error' => 'Invalid SMTP configuration.'], 400);
        }

        config([
            'mail.mailers.smtp.host' => $smtpConfig->host,
            'mail.mailers.smtp.port' => $smtpConfig->port,
            'mail.mailers.smtp.username' => $smtpConfig->username,
            'mail.mailers.smtp.password' => $smtpConfig->password,
            'mail.mailers.smtp.encryption' => $smtpConfig->encryption,
            'mail.from.name' => $smtpConfig->name,
        ]);

        if($request->template == 'Invoice'){
            $pdfPath = $this->downloadTempInvoice($request->id, $request->pagetype);
        } else if ($request->template == 'Quotation'){
            $pdfPath = $this->downloadTempQuotation($request->id, $request->pagetype);
        }

        // Mail::to($request->to);->send(new SendInvoiceEmail($emailData, $pdfPath));
        try {
            $mail = Mail::to($emailData['to']);
            if (!empty($emailData['cc'])) {
                $mail->cc($emailData['cc']);
            }
            $mail->send(new SendInvoiceEmail($emailData, $pdfPath));
            unlink($pdfPath);
            return response()->json(['success' => 'Email sent successfully.'], 200);
        } catch (\Exception $e) {
            unlink($pdfPath);
            return response()->json(['error' => 'Failed to send email: ' . $e->getMessage()], 500);
        }
    }

    private function getContent($id, $subject, $content, $userId, $template)
    {
        if($template == 'Invoice'){
            $invoice = Invoice::with('customers', 'organization')->findOrFail($id);
            $customer_name = $invoice->customers->customer_name;
            $company_name = $invoice->organization->name;
            $invoice_number = $invoice->invoice_no;
            $invoice_date = date('d-m-Y', strtotime($invoice->date));
            $total_amount = $invoice->total_amount;
            $pdf = 'Invoice.pdf';
        } else if ($template == 'Quotation'){
            $quotation = Quotation::with('customers', 'organization')->findOrFail($id);
            $customer_name = $quotation->customers->customer_name;
            $company_name = $quotation->organization->name;
            $quotation_number = $quotation->quotation_number;
            $quotation_date = date('d-m-Y', strtotime($quotation->date));
            $pdf = 'Quotation.pdf';
        }


        $user = User::with('roles')->findOrFail($userId);
        $user_name = $user->first_name . ' ' . $user->last_name;
        $user_role = $user->roles[0]->name;
        $contact_no = $user->contact_no;

        $tagArray = [
            "{user_name}" => $user_name,
            "{user_role}" => $user_role,
            "{company_name}" => $company_name,
            "{number}" => $contact_no,
            "{customer_name}" => $customer_name,
            "{invoice_number}" => $invoice_number ?? '',
            "{invoice_date}" => $invoice_date ?? '',
            "{invoice_amount}" => $total_amount ?? '',
            "{quotation_number}" => $quotation_number ?? '',
            "{quotation_date}" => $quotation_date ?? '',

        ];

        foreach ($tagArray as $key => $value) {
            $content = str_replace($key, $value, $content);
        }

        foreach ($tagArray as $key => $value) {
            $subject = str_replace($key, $value, $subject);
        }

        return ['subject' => $subject, 'content' => $content, 'pdf' => $pdf];
    }
}
