<script setup>
import { ref, onMounted, watch } from 'vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , useForm} from '@inertiajs/vue3';
import ArrowIcon from '@/Components/ArrowIcon.vue';

const props = defineProps(['data', 'user', 'userId', 'inquiry_type', 'inquiryTypes', 'userInfo', 'permissions']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('funnel.index', {
    user_id: props.userId,
    inquiry_type: props.inquiry_type,
});

const searchValue = ref('');
const userId = ref(props.userId);
const inquiry_type = ref(props.inquiry_type)

watch([userId, inquiry_type ], () => {
    updateParams({
        user_id: userId.value,
        inquiry_type: inquiry_type.value,
    });
});

const columns = [
    { field: 'users.first_name',   label: 'ENGINEER',         sortable: true, visible: props.userInfo.role_id == '1' },
    { field: 'customer_name',   label: 'CUSTOMER NAME',      sortable: true, multiFieldSort: ['customer_name', 'dr_name'], visible: true },
    { field: 'place',           label: 'CITY',             sortable: true, visible: true },
    { field: 'mobile_number',   label: 'NUMBER',           sortable: false, visible: true },
    { field: 'company',         label: 'COMPANY',          sortable: true, visible: true },
    { field: 'product',         label: 'PRODUCT',          sortable: true, visible: true },
    { field: 'order_value',     label: 'ORDER VALUE (₹)',  sortable: true, visible: true },
    { field: 'order_month',     label: 'E.M. OF ORDER',    sortable: true, visible: true },
    { field: 'status',          label: 'STATUS',           sortable: false, visible: true },
    { field: 'inquiry_type',    label: 'INQUIRY',          sortable: true, visible: true },
    { field: 'action',          label: 'ACTION',           sortable: false, visible: true}
];

const handleSearchChange = (value, userId, inquiry_type) => {
    searchValue.value = value;
    form.get(route('funnel.index', { search: value, user_id: userId, inquiry_type: inquiry_type }), {
        preserveState: true,
        // replace: true,
    });
};

const setSalesUser = (id, name) => {
    userId.value = id;
    handleSearchChange(searchValue.value, userId.value, inquiry_type.value);
};

const modalVisible = ref(false);
const selectedUserId = ref(null);

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('funnel.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const changeStatusModal = ref(false);

const changeStatus = (userId) => {
  selectedUserId.value = userId;
  changeStatusModal.value = true;
};

const closeChangeStatusModal = () => {
    changeStatusModal.value = false;
};

const changePlanStatus = () => {
    const currentUrl = window.location.href;

    form.post(route('funnel.changeStatus', { id: selectedUserId.value }), {
        onSuccess: () => {
            window.location.href = currentUrl;
        }
    });
};

const setInquiryType = (id, name) => {
    inquiry_type.value = id;
    handleSearchChange(searchValue.value, userId.value, inquiry_type.value);
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Cold':
            return 'bg-green-100';
        case 'Warm':
            return 'bg-yellow-100';
        case 'Close':
            return 'bg-red-100';
        default:
            return 'bg-blue-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Cold':
            return 'text-green-600';
        case 'Warm':
            return 'text-yellow-600';
        case 'Close':
            return 'text-red-600';
        default:
            return 'text-blue-600';
    }
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};


</script>

<template>
    <Head title="Funnel"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Funnel</h1>
                </div>
                <div class="flex justify-end items-center">
                    <div class="ml-6 flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                            </svg>
                            <input id="search-field" @input="handleSearchChange($event.target.value, userId, inquiry_type)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                            <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateFunnel">
                        <div class="flex justify-end">
                            <CreateButton :href="route('funnel.create')">
                                Add Funnel
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div  class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center">
                    <div v-if="permissions.canFilterFunnel" class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Engineer Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="user"
                            v-model="userId"
                            @onchange="setSalesUser"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Inquiry Type" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="inquiryTypes"
                                v-model="inquiry_type"
                                @onchange="setInquiryType"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th v-for="(column, index) in columns" v-show="column.visible" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                {{ column.label }}
                                <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(userData, index) in data.data" :key="userData.id">
                            <td v-if="userInfo.role_id == '1'" class="px-4 py-2.5 min-w-44">
                                {{ userData.users.first_name ?? '' }} {{ userData.users.last_name ?? '' }}
                            </td>
                            <th scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                <div class="">{{ userData.customer_name ?? '' }} </div>
                                <div class="">{{ userData.dr_name ?? '' }}</div>
                            </th>
                            <td class="px-4 py-2.5">
                                {{ userData.place ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5">
                                {{ userData.number ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5">
                                {{ userData.company ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52">
                                {{ userData.product ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-40">
                                {{ formatAmount(userData.order_value) ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-40">
                                {{ userData.order_month ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52">
                                {{ userData.status ?? '-' }}
                            </td>
                            <td class="flex flex-1 items-center px-4 py-2.5">
                                <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(userData.inquiry_type)">
                                <span class="text-sm font-semibold" :class="getStatusClass(userData.inquiry_type)">{{ userData.inquiry_type }}</span>
                                </div>
                            </td>
                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                    <Dropdown :align="'right'" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink v-if="permissions.canEditFunnel && userData.inquiry_type != 'Close'" :href="route('funnel.edit',{id:userData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"/>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button v-if="permissions.canDeleteFunnel" type="button" @click="openDeleteModal(userData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">

                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <button v-if="userData.inquiry_type != 'Close'" type="button" @click="changeStatus(userData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Close Funnel
                                                </span>
                                            </button>

                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="12" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>

        <Modal :show="changeStatusModal" @close="closeChangeStatusModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you want to Close this Funnel?
                </h2>
                <div class="mt-6 flex justify-end space-x-3">
                    <SecondaryButton @click="closeChangeStatusModal"> Cancel </SecondaryButton>
                    <div class="w-32">
                        <PrimaryButton @click="changePlanStatus" type="button">Approve</PrimaryButton>
                    </div>
                </div>
            </div>
        </Modal>

    </AdminLayout>
</template>
