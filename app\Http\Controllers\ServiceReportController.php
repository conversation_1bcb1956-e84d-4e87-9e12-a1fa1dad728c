<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\ServiceReport;
use App\Models\Customer;
use App\Models\Company;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\ServiceReportRequest;
use App\Http\Requests\UploadServiceReportRequest;
use App\Models\ServiceReportDetail;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Config;

class ServiceReportController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:List Bank Transaction')->only(['index']);
        // $this->middleware('permission:Create Bank Transaction')->only(['create', 'store']);
        // $this->middleware('permission:Edit Bank Transaction')->only(['edit', 'update']);
        // $this->middleware('permission:Delete Bank Transaction')->only('destroy');
    }

    public function show(Request $request, $id)
    {
        $search = $request->input('search');
        $query = ServiceReport::with('company', 'reportDetail.engineer')->where('customer_id', $id);
        $filepath = Config::get('constants.uploadFilePath.ServiceReports');
        if(!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('company', function ($subquery) use ($search) {
                    $subquery->where('name', 'like', "%$search%");
                })->orWhere('product_code', 'like', "%$search%")
                    ->orWhere('product_name', 'like', "%$search%")
                    ->orWhere('serial_no', 'like', "%$search%");
            });
        }
        $perPage = Config::get('constants.perPage');
        $data = $query->orderBy('id', 'desc')->paginate($perPage);
        $customer = Customer::find($id);
        $permissions = [
            // 'canCreateBankTransaction'      => auth()->user()->can('Create Bank Transaction'),
            // 'canEditBankTransaction'        => auth()->user()->can('Edit Bank Transaction'),
            // 'canDeleteBankTransaction'      => auth()->user()->can('Delete Bank Transaction')
        ];
        return Inertia::render('Role/TempShow', compact('data', 'customer', 'permissions', 'filepath'));
    }

    public function create(Request $request)
    {
        $customer = Customer::find($request->id);
        $reporttype = Config::get('constants.reportType');
        $company  = Company::select('id', 'name')->get();
        $serviceperson  = User::where(['status' => '1', 'role_id' => ['7', '8']])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->get();
        return Inertia::render('Role/TempAdd', compact('customer', 'reporttype', 'serviceperson', 'company'));
    }

    public function uploadServiceReport(Request $request, $id)
    {
        $serviceReport = ServiceReport::with('customers')->find($id);
        $reporttype = Config::get('constants.reportType');
        $serviceperson  = User::where(['status' => '1', 'role_id' => ['7', '8']])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->get();
        return Inertia::render('Role/TempUploadReport', compact('serviceReport', 'reporttype', 'serviceperson'));
    }

    public function store(ServiceReportRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $data['updated_by'] = auth()->id();
            $files = $request->file('document');
            if($files){
                $filePath = Config::get('constants.uploadFilePath.ServiceReports');
                foreach ($files as $file){
                    $originalName = $file->getClientOriginalName();
                    $fileName = time().str_replace(' ', '-', $originalName);
                    $path = $filePath['default'];
                    if(!is_dir($path)) {
                        mkdir($path, 0777, true);
                    }
                    $upload_success = $file->move($path, $fileName);
                    if($upload_success){
                        foreach ($data['serial_no'] as $serial) {
                            $data['serial_no'] = $serial;
                            $createServiceReport = ServiceReport::create($data);
                            $reportData['date'] = $data['date'];
                            $reportData['type'] = $data['type'];
                            $reportData['service_report_id'] = $createServiceReport->id;
                            $reportData['service_engineer_id'] = $data['service_engineer_id'];
                            $reportData['document_name'] = $fileName;
                            $reportData['original_document_name'] = $originalName;
                            $reportData['created_by'] = $reportData['updated_by'] = auth()->id();
                            ServiceReportDetail::create($reportData);
                        }
                    }
                }
            }
            DB::commit();
            return Redirect::to("/service-reports/$request->customer_id")->with('success', 'Report Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function saveUploadServiceReport(UploadServiceReportRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $files = $request->file('document');
            if($files){
                $filePath = Config::get('constants.uploadFilePath.ServiceReports');
                foreach ($files as $file){
                    $originalName = $file->getClientOriginalName();
                    $fileName = time().str_replace(' ', '-', $originalName);
                    $path = $filePath['default'];
                    if(!is_dir($path)) {
                        mkdir($path, 0777, true);
                    }
                    $upload_success = $file->move($path, $fileName);
                    if($upload_success){
                        $reportData['date'] = $data['date'];
                        $reportData['type'] = $data['type'];
                        $reportData['service_report_id'] = $data['service_report_id'];
                        $reportData['service_engineer_id'] = $data['service_engineer_id'];
                        $reportData['document_name'] = $fileName;
                        $reportData['original_document_name'] = $originalName;
                        $reportData['created_by'] = $reportData['updated_by'] = auth()->id();
                        ServiceReportDetail::create($reportData);
                    }
                }
            }
            DB::commit();
            return Redirect::to("/service-reports/$request->customer_id")->with('success', 'Report Uploaded Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = ServiceReport::where('id', $id)->get();
        return Inertia::render('Role/TempEdit', compact('data'));
    }

    public function view(Request $request, $id)
    {
        $search = $request->input('search');
        $filepath = Config::get('constants.uploadFilePath.ServiceReports');
        $query = ServiceReport::with('reportDetail.engineer')->where('id', $id);

        $data = $query->orderBy('id', 'asc')->paginate(20);
        $customer = Customer::find($id);
        return Inertia::render('Role/TempHistory', compact('data', 'customer', 'filepath'));
    }

    public function update(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = auth()->id();;
            $user = ServiceReport::findOrFail($request->id);
            $user->update($data);
            DB::commit();
            return Redirect::to("service-reports/$request->customer_id")->with('success', 'Report Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $serviceReport = ServiceReport::find($id);
            $serviceReport->delete();
            $ServiceReportDetail = ServiceReportDetail::where('service_report_id', $id)->delete();
            // if ($ServiceReportDetail) {
            //     $filePath = Config::get('constants.uploadFilePath.ServiceReports');
            //     $filePathToDelete = $filePath['default']. $ServiceReportDetail->document_name;
            //     if (file_exists($filePathToDelete)) {
            //         unlink($filePathToDelete); // Delete the file
            //     }
            //     $ServiceReportDetail->delete();
            // }
            DB::commit();
            return Redirect::back()->with('success','Report Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
