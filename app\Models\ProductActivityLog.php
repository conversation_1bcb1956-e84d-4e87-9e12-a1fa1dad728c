<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductActivityLog extends Model
{
    use HasFactory;

    protected $table = 'product_activity_log';

    protected $fillable = [
        'log_name',
        'model',
        'model_id',
        'product_id',
        'action',
        'description',
        'properties',
        'created_by',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
