<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challan', function (Blueprint $table) {
            $table->dropColumn('total_amount');
        });

        Schema::table('challan_detail', function (Blueprint $table) {
            $table->dropColumn('price');
            $table->dropColumn('total_price');
            $table->dropColumn('gst');
            $table->dropColumn('gst_amount');
            $table->dropColumn('total_gst_amount');
            $table->dropColumn('total_amount');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
