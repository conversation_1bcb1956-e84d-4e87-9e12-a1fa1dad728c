import{r as x,j as M,l as O,o as r,c as d,a as l,u as a,w as F,F as I,Z as ee,b as s,t as v,f as c,d as te,n as f,k as oe,v as se,i as Z,g as ae,T as ne,s as le,x as ie}from"./app-8a557454.js";import{_ as re,a as de}from"./AdminLayout-301d54ca.js";import{_ as p}from"./InputLabel-07f3a6e8.js";import{P as ue}from"./PrimaryButton-9d9bcdd8.js";import{_ as k}from"./TextInput-ab168ee4.js";import{_ as me}from"./TextArea-3588e81e.js";import{_ as ce}from"./RadioButton-c5ab2b34.js";import{_ as U}from"./SearchableDropdown-51a69527.js";import{u as _e}from"./index-62ab7306.js";/* empty css                                                                          */import{_ as pe}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ve}from"./Checkbox-b528928a.js";const z=g=>(le("data-v-d02b5de6"),g=g(),ie(),g),fe={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ge={class:"sm:flex sm:items-center"},he=z(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),xe={class:"flex items-center justify-between"},be={key:0,class:"text-base font-semibold leading-6 text-gray-900"},ke=["onSubmit"],we={class:"border-b border-gray-900/10 pb-12"},Ve={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Ae={class:"sm:col-span-3"},$e={class:"relative mt-2"},Fe={class:"sm:col-span-2"},Ue={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},Se={class:"relative mt-2"},Te={key:1,class:"sm:col-span-3"},Pe={key:2,class:"sm:col-span-2"},De={key:3,class:"sm:col-span-1"},Oe={key:4,class:"sm:col-span-1"},Ie={key:5,class:"sm:col-span-1"},Be={key:6,class:"sm:col-span-3"},je={key:7,class:"sm:col-span-2"},Ee={class:"mt-4 flex justify-start"},Re={class:"text-base font-semibold"},Ye={key:8,class:"sm:col-span-2"},qe={key:9,class:"sm:col-span-2"},Le={key:10,class:"sm:col-span-2"},Me={class:"relative mt-2"},Ze={key:11,class:"sm:col-span-3"},Ge={class:"sm:col-span-6"},He={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Je=z(()=>s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:""}),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Ke={style:{"overflow-y":"auto","max-height":"318px"}},Qe={class:"divide-y divide-gray-300 bg-white"},We={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Xe={class:"text-sm text-gray-900 leading-6 py-1.5"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},st={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},at={key:0,class:"text-red-500 text-xs absolute"},nt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},lt={class:"sm:col-span-2"},it={class:"mt-4 flex justify-start"},rt={class:"text-base font-semibold"},dt={key:0,class:"text-red-500 text-xs absolute"},ut={key:12,class:"sm:col-span-6"},mt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ct=z(()=>s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),_t={class:"divide-y divide-gray-300 bg-white"},pt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ft={class:"flex flex-col"},yt={class:"text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"flex mt-6 items-center justify-between"},bt={class:"ml-auto flex items-center justify-end gap-x-6"},kt=z(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),wt={key:0,class:"text-sm text-gray-600"},Vt={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(g){const V=g;x([]);const e=_e("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),C=x(""),G=()=>{e.settled_amount=S.value,e.advance_amount=j.value,e.total_unused_amount=N.value,e.is_credit=u.value,e.invoice=y.value,e.credit_data=h.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},H=(n,o)=>{C.value=o,e.payment_type=n,e.errors.payment_type=null,o==="Cash"?e.note="Cash":e.note==="Cash"&&(e.note="")},$=x([]),h=x([]),N=x(""),B=x([]),J=(n,o)=>{const t=V.bankinfo.filter(m=>m.organization_id===n);B.value=t;const _=V.invoices.filter(m=>m.organization_id===n&&m.customer_id===e.customer_id);$.value=_;const i=V.credit.filter(m=>m.organization_id===n&&m.customer_id===e.customer_id);h.value=i,N.value=h.value.reduce((m,b)=>m+b.unused_amount,0),e.organization_id=n,e.errors.organization_id=null},K=(n,o)=>{const t=V.invoices.filter(i=>i.customer_id===n&&i.organization_id===e.organization_id);$.value=t;const _=V.credit.filter(i=>i.customer_id===n&&i.organization_id===e.organization_id);h.value=_,N.value=h.value.reduce((i,m)=>i+m.unused_amount,0),e.customer_id=n,e.errors.customer_id=null},Q=(n,o)=>{e.org_bank_id=n,e.errors.org_bank_id=null},S=M(()=>y.value.reduce((n,o)=>n+(o.check&&o.amount?parseFloat(o.amount):0),0)),j=M(()=>{const n=parseFloat(e.amount||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0),o=parseFloat(e.round_off||0),t=S.value;return n-t-o}),T=()=>{},A=n=>{let o=n.toFixed(2).toString(),[t,_]=o.split("."),i=t.substring(t.length-3),m=t.substring(0,t.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${_}`},E=n=>{const o=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},u=x("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],X=n=>{const o=u.value==="Yes"?parseFloat(N.value||0):parseFloat(e.amount||0)+parseFloat(e.round_off||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0);if(!y.value[n].check){y.value[n].amount=0;return}let t=o;y.value.forEach((m,b)=>{m.check&&b!==n&&(t-=parseFloat(m.amount||0))});const _=parseFloat(y.value[n].pending_amount||0),i=Math.min(_,t);y.value[n].amount=i.toFixed(2)},y=x([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),P=()=>{y.value=$.value.map(n=>({id:n.id,date:n.date,invoice_no:n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.pending_amount||0).toFixed(2),check:!1,amount:"0.00"}))};O($,()=>{P()}),O(u,()=>{P()}),O(()=>e.amount,()=>{u.value==="No"&&P()});const w=n=>{e.errors[n]=null,e.errors.settled_amount=null};return(n,o)=>(r(),d(I,null,[l(a(ee),{title:"Receipt"}),l(re,null,{default:F(()=>[s("div",fe,[s("div",ye,[s("div",ge,[he,s("div",xe,[h.value.length>0?(r(),d("div",be," Credits Available: ₹"+v(A(N.value)),1)):c("",!0)])]),s("form",{onSubmit:te(G,["prevent"]),class:""},[s("div",we,[s("div",Ve,[s("div",Ce,[l(p,{for:"payment_type",value:"Organization"}),s("div",Ne,[l(U,{options:g.organization,modelValue:a(e).organization_id,"onUpdate:modelValue":o[0]||(o[0]=t=>a(e).organization_id=t),onOnchange:J,class:f({"error rounded-md":a(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),s("div",Ae,[l(p,{for:"payment_type",value:"Customer"}),s("div",$e,[l(U,{options:g.customers,modelValue:a(e).customer_id,"onUpdate:modelValue":o[1]||(o[1]=t=>a(e).customer_id=t),onOnchange:K,class:f({"error rounded-md":a(e).errors.customer_id})},null,8,["options","modelValue","class"])])]),s("div",Fe,[l(p,{for:"role_id",value:"Payment Through Credit ?"}),s("div",Ue,[l(ce,{modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=t=>u.value=t),options:W},null,8,["modelValue"])])]),u.value=="No"?(r(),d("div",ze,[l(p,{for:"payment_type",value:"Payment Type"}),s("div",Se,[l(U,{options:g.paymentType,modelValue:a(e).payment_type,"onUpdate:modelValue":o[3]||(o[3]=t=>a(e).payment_type=t),onOnchange:H,class:f({"error rounded-md":a(e).errors.payment_type})},null,8,["options","modelValue","class"])])])):c("",!0),u.value=="No"?(r(),d("div",Te,[l(p,{for:"date",value:"Payment Date"}),oe(s("input",{"onUpdate:modelValue":o[4]||(o[4]=t=>a(e).date=t),class:f(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":a(e).errors.date}]),type:"date",onChange:o[5]||(o[5]=t=>w("date"))},null,34),[[se,a(e).date]])])):c("",!0),u.value=="No"?(r(),d("div",Pe)):c("",!0),u.value=="No"?(r(),d("div",De,[l(p,{for:"tds_amount",value:"TDS Amount"}),l(k,{type:"text",onChange:o[6]||(o[6]=t=>w("tds_amount")),onInput:o[7]||(o[7]=t=>T()),modelValue:a(e).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=t=>a(e).tds_amount=t),class:f({"error rounded-md":a(e).errors.tds_amount})},null,8,["modelValue","class"])])):c("",!0),u.value=="No"?(r(),d("div",Oe,[l(p,{for:"discount_amount",value:"Discount Amount"}),l(k,{type:"text",onChange:o[9]||(o[9]=t=>w("discount_amount")),onInput:o[10]||(o[10]=t=>T()),modelValue:a(e).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=t=>a(e).discount_amount=t),class:f({"error rounded-md":a(e).errors.discount_amount})},null,8,["modelValue","class"])])):c("",!0),u.value=="No"?(r(),d("div",Ie,[l(p,{for:"round_off",value:"Round Off"}),l(k,{type:"text",onChange:o[12]||(o[12]=t=>w("round_off")),modelValue:a(e).round_off,"onUpdate:modelValue":o[13]||(o[13]=t=>a(e).round_off=t),class:f({"error rounded-md":a(e).errors.round_off})},null,8,["modelValue","class"])])):c("",!0),u.value=="No"?(r(),d("div",Be,[l(p,{for:"amount",value:"Amount"}),l(k,{id:"amount",type:"text",onChange:o[14]||(o[14]=t=>w("amount")),onInput:o[15]||(o[15]=t=>T()),modelValue:a(e).amount,"onUpdate:modelValue":o[16]||(o[16]=t=>a(e).amount=t),class:f({"error rounded-md":a(e).errors.amount})},null,8,["modelValue","class"])])):c("",!0),u.value=="No"?(r(),d("div",je,[l(p,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Ee,[s("p",Re,v(A(j.value)),1)])])):c("",!0),C.value=="Cheque"&&u.value=="No"?(r(),d("div",Ye,[l(p,{for:"check_number",value:"Cheque Number"}),l(k,{id:"check_number",type:"text",modelValue:a(e).check_number,"onUpdate:modelValue":o[17]||(o[17]=t=>a(e).check_number=t),class:f({"error rounded-md":a(e).errors["data.check_number"]})},null,8,["modelValue","class"])])):c("",!0),C.value=="Cheque"&&u.value=="No"?(r(),d("div",qe,[l(p,{for:"bank_name",value:"Bank Name"}),l(k,{id:"bank_name",type:"text",modelValue:a(e).bank_name,"onUpdate:modelValue":o[18]||(o[18]=t=>a(e).bank_name=t),class:f({"error rounded-md":a(e).errors["data.bank_name"]})},null,8,["modelValue","class"])])):c("",!0),C.value!="Cash"&&u.value=="No"?(r(),d("div",Le,[l(p,{for:"org_bank_id",value:"Our Bank"}),s("div",Me,[l(U,{options:B.value,modelValue:a(e).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=t=>a(e).org_bank_id=t),onOnchange:Q,class:f({"error rounded-md":a(e).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):c("",!0),C.value!="Cash"&&u.value=="No"?(r(),d("div",Ze)):c("",!0),s("div",Ge,[s("table",He,[Je,s("div",Ke,[s("tbody",Qe,[(r(!0),d(I,null,Z(y.value,(t,_)=>(r(),d("tr",{key:_},[s("td",We,[s("div",Xe,[l(ve,{name:"check",checked:t.check,"onUpdate:checked":i=>t.check=i,onChange:i=>X(_)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",et,v(t.invoice_no),1),s("td",tt,v(t.total_amount),1),s("td",ot,v(t.pending_amount),1),s("td",st,[l(k,{id:"amount",type:"text",modelValue:t.amount,"onUpdate:modelValue":i=>t.amount=i,onChange:i=>w("invoice."+_+".amount"),class:f({error:a(e).errors[`invoice.${_}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),a(e).errors[`invoice.${_}.amount`]?(r(),d("p",at,v(a(e).errors[`invoice.${_}.amount`]),1)):c("",!0)]),s("td",nt,v(E(t.date)),1)]))),128))])])])]),s("div",lt,[l(p,{for:"note",value:"Total Settled Amount"}),s("div",it,[s("p",rt,v(A(S.value)),1)]),a(e).errors.settled_amount?(r(),d("p",dt,v(a(e).errors.settled_amount),1)):c("",!0)]),u.value=="No"?(r(),d("div",ut,[l(p,{for:"note",value:"Note"}),l(me,{id:"note",type:"text",rows:2,modelValue:a(e).note,"onUpdate:modelValue":o[20]||(o[20]=t=>a(e).note=t)},null,8,["modelValue"])])):c("",!0)]),h.value.length>0&&u.value=="Yes"?(r(),d("table",mt,[ct,s("tbody",_t,[(r(!0),d(I,null,Z(h.value,(t,_)=>{var i,m,b,D,R,Y,q,L;return r(),d("tr",{key:_},[s("td",pt,v(E(t.date)),1),s("td",vt,[s("div",ft,[s("div",yt,v((m=(i=t.paymentreceive)==null?void 0:i.bank_info)!=null&&m.bank_name?(D=(b=t.paymentreceive)==null?void 0:b.bank_info)==null?void 0:D.bank_name:"Cash")+" - "+v((Y=(R=t.paymentreceive)==null?void 0:R.bank_info)!=null&&Y.account_number?(L=(q=t.paymentreceive)==null?void 0:q.bank_info)==null?void 0:L.account_number:""),1)])]),s("td",gt,v(A(t.amount)),1),s("td",ht,v(A(t.unused_amount)),1)])}),128))])])):c("",!0)]),s("div",xt,[s("div",bt,[l(de,{href:n.route("receipt.index")},{svg:F(()=>[kt]),_:1},8,["href"]),l(ue,{disabled:a(e).processing},{default:F(()=>[ae("Save")]),_:1},8,["disabled"]),l(ne,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:F(()=>[a(e).recentlySuccessful?(r(),d("p",wt,"Saved.")):c("",!0)]),_:1})])])],40,ke)])])]),_:1})],64))}},It=pe(Vt,[["__scopeId","data-v-d02b5de6"]]);export{It as default};
