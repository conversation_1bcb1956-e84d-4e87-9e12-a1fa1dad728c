<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proforma_invoice_details', function (Blueprint $table) {
            $table->id();
            $table->integer('pi_id');
            $table->integer('product_id');
            $table->integer('qty');;
            $table->integer('delivered_qty')->default(0);
            $table->longText('description');
            $table->double('price',16,2);
            $table->double('total_price',16,2);
            $table->integer('gst');
            $table->double('gst_amount',16,2);
            $table->double('total_gst_amount',16,2);
            $table->double('discount',16,2)->nullable()->default(0);
            $table->double('discount_amount',16,2)->nullable()->default(0);
            $table->double('total_amount',16,2);
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('proforma_invoice_details');
    }
};
