<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\DTO\MailConfigDTO;
use Support\Contracts\HasDTO;

class MailConfigRequest extends FormRequest implements HasDTO
{
    public function authorize(): bool
    {
        return true; // Update if authorization logic is needed
    }

    public function rules(): array
    {
        return [
            'host' => 'required|string',
            'port' => 'required|numeric',
            'username' => 'required|string',
            'password' => 'required|string',
            'email' => 'required|email',
            'encryption' => 'nullable|string',
            'name' => 'required|string',
        ];
    }

    public function DTO()
    {
        return MailConfigDTO::LazyFromArray($this->input());
    }

}
