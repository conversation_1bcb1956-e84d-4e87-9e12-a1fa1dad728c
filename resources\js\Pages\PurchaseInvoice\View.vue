<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import NoRecordsFound from '@/Components/NoRecordsFound.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import { Head , usePage} from '@inertiajs/vue3';

const props = defineProps(['data']);
const poData = usePage().props.data[0];
// const file = usePage().props.filepath.view;

const totalAmountSum = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_amount, 0);
    }
    return 0;
});

const totalQty = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.qty, 0);
    }
    return 0;
});

const totalRecQty = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.receive_qty, 0);
    }
    return 0;
});

const totalPrice = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_price, 0);
    }
    return 0;
});

const totalGst = computed(() => {
    if (props.data[0].purchase_order_detail) {
        return props.data[0].purchase_order_detail.reduce((acc, poData) => acc + poData.total_gst_amount, 0);
    }
    return 0;
});

const totalReceivedProductAmount = computed(() => {
    if (props.receivedOrder) {
        return props.receivedOrder.reduce((acc, poData) => {
            const productAmount = poData.total_amount;
            return acc + productAmount;
        }, 0);
    }
    return 0;
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

// const formatAmount = (amount) => {
//     const [integerPart, decimalPart] = amount.toFixed(2).toString().split('.');
//     const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
//     return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
//     // return '\u20B9' + formattedIntegerPart + (decimalPart ? '.' + decimalPart : '');
// };

const showDetails = ref([]);

const toggleDetails = (index) => {
    showDetails.value[index] = !showDetails.value[index];
};

const documentPreviewModal = ref(false);
const selectedDocument = ref(null);
const modalMaxWidth = ref('custom');

const openPreviewModal = (name) => {
  selectedDocument.value = name;
  documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
};

const downloadDocument = (name) => {
    const documentURL = window.location.origin+ file+ name;
    const link = document.createElement('a');
    link.href = documentURL;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};


const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const backRoute = computed(() => {
    const source = new URLSearchParams(window.location.search).get('source');
    if (source === 'dashboard') {
        return route('dashboard');
    }
    return route('purchaseinvoice.index');
});

</script>

<template>
    <Head title="Purchase Invoice"/>

    <AdminLayout>
        <div class="animate-top h-screen">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Purchase Invoice</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].purchase_order.organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="backRoute">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Company Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.company.name  ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  data[0].purchase_order.company.gst_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Invoice No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].customer_invoice_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Invoice Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].customer_invoice_date) ?? '-' }}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].purchase_order.po_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  formatDate(data[0].purchase_order.date) ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">PO Received By</p>
                        <p class="text-sm leading-6 text-gray-700">{{data[0].users.first_name }} {{ data[0].users.last_name}}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden">
                <div class="p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50 border">
                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10">
                                <th scope="col" class="py-3.5 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">Product Code</th>
                                <th scope="col" class="py-3.5 sm:col-span-3 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">Product Description</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900 sm:pl-6">HSN</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">Qty</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">GST</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">Price</th>
                                <th scope="col" class="py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold  text-gray-900">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">

                            <no-records-found :data="data[0].purchase_order_receive_details" :colspan="7"></no-records-found>
<!--                            <template v-if="data[0].purchase_order_receive_details.length === 0">
                                <no-records-found :colspan="7"></no-records-found>
                            </template>-->

                            <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-10" v-for="(receive, index) in data[0].purchase_order_receive_details" :key="index">
                                <td class="whitespace-nowrap sm:col-span-2 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.item_code ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-3 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.name ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.product.hsn_code ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.receive_qty ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.purchase_order_detail.gst ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">{{ receive.serial_numbers[0].purchase_price ?? '-' }}</td>
                                <td class="whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6">
                                    <button @click="toggleDetails(index)">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                </td>
                                <div v-if="showDetails[index] && receive.serial_numbers.length != 0" class="divide-y divide-gray-300 sm:col-span-10 product-details border mx-6 mb-4">
                                    <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50">
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Batch</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Expiry Date</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">MRP (₹)</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Purchase Price (₹)</th>
                                        <th scope="col" class="py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Qty</th>
                                    </tr>
                                     <tbody class="divide-y divide-gray-300 bg-white grid grid-cols-1" >
                                        <tr class="grid grid-cols-1 gap-x-6 sm:grid-cols-5" v-for="(productinfo, index) in receive.serial_numbers" :key="index">
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.batch ?? '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.expiry_date != null) ? formatDate(productinfo.expiry_date) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.mrp) ? formatAmount(productinfo.mrp) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ (productinfo.purchase_price) ? formatAmount(productinfo.purchase_price) : '-' }}</td>
                                            <td class="py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ productinfo.receive_qty ?? '-' }}</td>
                                        </tr>
                                     </tbody>
                                </div>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-2">
                    <div class="w-full">
                        <p class="text-sm font-semibold text-gray-700">Note:</p>
                        <p class="text-sm text-gray-900 break-words">
                            {{ data[0]?.purchase_order_receives?.note || data[0]?.note || '-' }}
                        </p>
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(data[0].total_price) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total GST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(data[0].total_gst_amount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(data[0].total_amount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </form>
        </div>
         <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                 <FileViewer :fileUrl="file+ selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal"> Cancel </SecondaryButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>
