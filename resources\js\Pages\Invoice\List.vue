<script setup>
import { ref, nextTick, onMounted, watch ,reactive, computed} from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import NewModal from '@/Components/NewModal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import Dropdown from '@/Components/Dropdown.vue';
import RadioButton from '@/Components/RadioButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import { QuillEditor } from "@vueup/vue-quill";
import Tagify from '@yaireo/tagify';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'user_id', 'permissions', 'organization', 'emailTemplates', 'email',  'customers', 'paymentType', 'terms', 'invoiceMCbank', 'invoiceHCbank', 'invoiceNOXbank', 'pagetypes', 'bankinfo', 'organizationId', 'customerId', 'invoicetypes', 'invoice_type', 'salesuser', 'salesUserId', 'category', 'categoryId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('invoice.index', {
    customer_id: props.customerId,
    invoice_type: props.invoiceType,
    organization_id: props.organizationId,
    sales_user_id: props.salesUserId,
    category: props.categoryId,
});

const filePath = usePage().props.filepath.view;
const invoiceData = ref([]);
// const form = useForm({});
const modalVisible = ref(false);
const paymentReceiveModal = ref(false);
const modalMaxWidth = ref('custom');
const paymentReceiveWidth = ref('custom2');
const payment_type = ref('');
const emailData = ref({}); // Define emailData using

const columns = [
    { field: 'invoice_no',                  label: 'INVOICE NUMBER',   sortable: true },
    { field: 'invoice_type',                label: 'TYPE',             sortable: true },
    { field: 'category',                    label: 'CATEGORY',         sortable: true },
    { field: 'customers.customer_name',     label: 'CUSTOMER NAME',    sortable: true },
    { field: 'date',                        label: 'DATE',             sortable: true },
    { field: 'total_amount',                label: 'AMOUNT (₹)',       sortable: true },
    { field: 'paid_amount',                 label: 'PAID AMOUNT (₹)',  sortable: true },
    { field: 'status',                      label: 'STATUS',           sortable: true },
    { field: 'action',                      label: 'ACTION',           sortable: false },
];

const page = ref('portrait');
const sendingEmail = ref(false);
const sendInvoiceEmailModal = ref(false);
const notification = ref({
    show: false,
    message: '',
    type: 'success'
});

const errors = ref({
    to: '',
    from: '',
    template: '',
    pagetype: '',
    subject: '',
    content: ''
});

const emailTemplate = ref({
    to: '',
    from: '',
    cc: '',
    template: '',
    subject: '',
    content: '',
    id: '',
    user_id: '',
    pagetype: 'portrait'
});

const openInvoiceSendEmailModal = (id) => {
    const invoice = props.data.data.find(invoice => invoice.id === id);
    emailTemplate.value = {
        to: invoice ? invoice.customers.email : '',
        cc: '',
        id: id,
        from: '',
        template: '',
        subject: '',
        content: '',
        user_id: props.user_id,
        pagetype: 'portrait'
    };

    errors.value = {
        to: '',
        from: '',
        template: '',
        pagetype: '',
        subject: '',
        content: ''
    };

    if (Array.isArray(props.email) && props.email.length > 0) {
        emailTemplate.value.from = props.email[0].id;
    }

    sendInvoiceEmailModal.value = true;
    nextTick(() => {
        const input = document.getElementById("cc");
        if (input) {
            const tagify = new Tagify(input, {
                delimiters: ",",
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                placeholder: "Add CC emails...",
            });

            tagify.on('change', (e) => {
                emailTemplate.value.cc = tagify.value.map(tag => tag.value).join(',');
            });
        }
    });
};

const closeInvoiceSendEmailModal = () => {
    sendInvoiceEmailModal.value = false;
};

const setTemplate = (id, name) => {
    const selectedTemplate = props.emailTemplates.find(template => template.id === id);
    if (selectedTemplate) {
        emailTemplate.value.template = name;
        emailTemplate.value.subject = selectedTemplate.email_subject;
        emailTemplate.value.content = selectedTemplate.content;

        errors.value.template = '';
        errors.value.subject = '';
        errors.value.content = '';
    }
};

const setFrom = (id, name) => {
    emailTemplate.value.from = id;
    errors.value.from = '';
};

const setPDFPageType = (id, name) => {
    emailTemplate.value.pagetype = id;
    errors.value.pagetype = '';
};

const sendEmail = () => {
    let isValid = true;

    errors.value = {
        to: '',
        from: '',
        template: '',
        pagetype: '',
        subject: '',
        content: ''
    };

    if (!emailTemplate.value.to || emailTemplate.value.to.trim() === '') {
        errors.value.to = 'This to field is required.';
        isValid = false;
    }
    if (!emailTemplate.value.from) {
        errors.value.from = 'This from field is required.';
        isValid = false;
    }
    if (!emailTemplate.value.template) {
        errors.value.template = 'This template field is required.';
        isValid = false;
    }
    if (!emailTemplate.value.pagetype) {
        errors.value.pagetype = 'This field is required.';
        isValid = false;
    }
    if (!emailTemplate.value.subject || emailTemplate.value.subject.trim() === '') {
        errors.value.subject = 'This subject field is required.';
        isValid = false;
    }
    if (!emailTemplate.value.content || emailTemplate.value.content.trim() === '') {
        errors.value.content = 'This content field is required.';
        isValid = false;
    }

    if (!isValid) return;

    sendingEmail.value = true;

    const formData = new FormData();
    formData.append('to', emailTemplate.value.to);
    formData.append('from', emailTemplate.value.from);
    formData.append('cc', emailTemplate.value.cc);
    formData.append('subject', emailTemplate.value.subject);
    formData.append('content', emailTemplate.value.content);
    formData.append('id', emailTemplate.value.id);
    formData.append('user_id', emailTemplate.value.user_id);
    formData.append('pagetype', emailTemplate.value.pagetype);
    formData.append('template', emailTemplate.value.template);

    fetch(route('send-invoice-email'), {
        method: 'POST',
        body: formData,
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
    })
    .then(async (response) => {
        const data = await response.json();
        if (!response.ok) {
            notification.value = {
                show: true,
                message: data.error || 'Failed to send email',
                type: 'error'
            };
            throw new Error(data.error || 'Failed to send email');
        }
        notification.value = {
            show: true,
            message: data.success || 'Email sent successfully',
            type: 'success'
        };
        sendInvoiceEmailModal.value = false;
        setTimeout(() => {
            notification.value.show = false;
        }, 3000);
    })
    .catch(error => {
        notification.value = {
            show: true,
            message: error.message || 'An unexpected error occurred',
            type: 'error'
        };
    })
    .finally(() => {
        sendingEmail.value = false;
        setTimeout(() => {
            notification.value.show = false;
        }, 5000);
    });
};

const bankinfo = ref([]);
const creditData = ref([]);
const totalUnusedAmount = ref('');

const openPreviewModal = (id) => {
    const invoice = props.data.data.find(invoice  => invoice.id === id);
    invoiceData.value = invoice
    modalVisible.value = true;
};

const openPaymentReceiveModal = (id, organization_id) => {
    paymentform.value.organization_id = '';
    paymentform.value.customer_id = '';
    paymentform.value.org_bank_id = '';
    paymentform.value.invoice_id = '';
    paymentform.value.invoice_no = '';
    paymentform.value.payment_type = '';
    paymentform.value.amount = '';
    paymentform.value.check_number = '';
    paymentform.value.bank_name = '';
    paymentform.value.date = '';
    paymentform.value.note = '';
    paymentform.value.invoice_amount = '';
    const invoice = props.data.data.find(invoice  => invoice.id === id);
    invoiceData.value = invoice
    const credit = invoiceData.value.credit.filter(customer => customer.organization_id === organization_id);
    creditData.value = credit
    paymentReceiveModal.value = true;
    paymentform.value.customer_id = invoiceData.value.customer_id;
    paymentform.value.invoice_id = invoiceData.value.id;
    paymentform.value.invoice_no = invoiceData.value.invoice_no;
    paymentform.value.organization_id = invoiceData.value.organization_id;
    paymentform.value.invoice_amount = invoiceData.value.total_amount;
    const bank = props.bankinfo.filter(bank  => bank.organization_id === organization_id);
    bankinfo.value = bank;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
};

const totalAmountToCredit = computed(() => {
    return creditData.value.reduce((total, product) => {
        return total +  (product.amount_to_credit ? parseFloat(product.amount_to_credit) : 0);
    }, 0);
});

const paymentReceiveCloseModal = () => {
    paymentReceiveModal.value = false;
};

const closeModal = () => {
    modalVisible.value = false;
};

const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const invoiceType = ref(props.invoice_type);
const salesUserId = ref(props.salesUserId);
const categoryId = ref(props.categoryId);
const searchValue = ref('');

watch([organizationId, customerId, invoiceType, salesUserId, categoryId ], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
        invoice_type: invoiceType.value,
        sales_user_id: salesUserId.value,
        category: categoryId.value
    });
});

const handleSearchChange = (value, organizationId, customerId, invoiceType, salesUserId, categoryId) => {
    searchValue.value = value;
    form.get(route('invoice.index',{search:value,  organization_id: organizationId,  customer_id: customerId, invoice_type: invoiceType,  sales_user_id: salesUserId ,  category: categoryId}),  {
        preserveState: true,
        // replace: true,
    });
};

const deleteModal = ref(false);
const selectedInvoiceId = ref(null);


const openDeleteModal = (userId) => {
    selectedInvoiceId.value = userId;
    deleteModal.value = true;
};

const closeDeleteModal = () => {
    deleteModal.value = false;
};

const deleteInvoice = () => {
    form.delete(route('invoice.destroy',{id:selectedInvoiceId.value}), {
        onSuccess: () => closeDeleteModal()
    });
};

const STdeleteModal = ref(false);
const selectedStockTransferId = ref(null);

const openSTDeleteModal = (userId) => {
    selectedStockTransferId.value = userId;
    STdeleteModal.value = true;
};

const closeSTDeleteModal = () => {
    STdeleteModal.value = false;
};

const deleteStockTransfer = () => {
    form.delete(route('stock-transfer.destroy', {id: selectedStockTransferId.value}), {
        onSuccess: () => closeSTDeleteModal()
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value, invoiceType.value, salesUserId.value, categoryId.value);
};

const setCustomers = (id, name) => {
    customerId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, invoiceType.value, salesUserId.value, categoryId.value);
};

const setInvoiceType = (id, name) => {
    invoiceType.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, invoiceType.value, salesUserId.value, categoryId.value);
};

const setType = (id, name) => {
    categoryId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, invoiceType.value, salesUserId.value, categoryId.value);
};

const setSalesUser = (id, name) => {
    salesUserId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value, invoiceType.value, salesUserId.value, categoryId.value);
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Unpaid':
            return 'bg-blue-100';
        case 'Partially Paid':
            return 'bg-yellow-100';
        case 'Paid':
            return 'bg-green-100';
        default:
            return 'bg-gray-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Unpaid':
            return 'text-blue-600';
        case 'Partially Paid':
            return 'text-yellow-600';
        case 'Paid':
            return 'text-green-600';
        default:
            return 'text-gray-600';
    }
};

const generatePDF = (filename) => {
    const sanitizedFilename = filename.replace(/\s+/g, '_').replace(/[<>:"./\\|?*]+/g, '');
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');
    html2canvas(pdfContent, { scale: 2 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 196;
        const pageHeight = 297;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let position = 0;
        if (imgHeight <= pageHeight) {
            doc.addImage(imgData, 'PNG', 7, 10, imgWidth, imgHeight);
        } else {
            while (position < imgHeight) {
                doc.addImage(imgData, 'PNG', 7, -position +10 , imgWidth, imgHeight);
                position += pageHeight - 20;
                if (position < imgHeight) {
                    doc.addPage();
                }
            }
        }
        doc.save(sanitizedFilename);
    });
};


const setPageType = (id, name) => {
    page.value = id;
};


const downloadPDF = (id, page) => {
    window.open(`/invoice/download/${id}/${page}`, '_blank');
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    if (amount == null || isNaN(amount)) {
        return "0.00";  // Default value
    }
    let amountStr = Number(amount).toFixed(2);
    // let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const creditOption = ref('No');

const radioOptions = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
];

const paymentform = computed(() => ({
    organization_id: '',
    customer_id: '',
    org_bank_id: '',
    invoice_id: '',
    invoice_no: '',
    payment_type: '',
    amount: '',
    check_number: '',
    bank_name: '',
    date: '',
    note: '',
    invoice_amount: '',
}));

const setPaymentType = (id, name) => {
    paymentform.value.payment_type = id;
    payment_type.value = name;
    form.errors[`form.payment_type`] = null;
    if (name === "Cash") {
        paymentform.value.note = "Cash";
    } else if (paymentform.value.note === "Cash") {
        paymentform.value.note = "";
    }
};
const setBankInfo = (id, name) => {
    paymentform.value.org_bank_id = id;
    form.errors[`form.org_bank_id`] = null;
};

const acceptPayment = () => {
    form.post(route('invoice.paymentreceive',{ form : paymentform.value, is_credit: creditOption.value, credit_data: creditData.value}), {
        onSuccess: () => {
            form.reset();
            paymentReceiveModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};


</script>

<template>
    <Head title="Invoice"/>
    <div v-if="notification.show"  :class="['notification', notification.type]" @click="notification.show = false">
        {{ notification.message }}
    </div>
    <AdminLayout>
        <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="items-start">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Invoice</h1>
            </div>
            <div class="flex justify-end">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                     <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, customerId, invoiceType, salesUserId, categoryId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                            <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                </div>
                <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none" v-if="permissions.canCreateInvoice">
                    <div class="flex justify-end">
                        <CreateButton :href="route('invoice.create')">
                            Create Invoice
                        </CreateButton>
                    </div>
                </div>
                <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                    <div class="flex justify-end" v-if="permissions.canStockTransfer">
                        <CreateButton :href="route('stocktransfer')">
                            Stock Transfer
                        </CreateButton>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                </svg>
                <InputLabel for="customer_id" value="Filters" />
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Organization Name" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="organization"
                         v-model="organizationId"
                        @onchange="setOrganization"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Customer Name" />
                    <div class="relative mt-2">
                        <SearchableDropdownNew :options="customers"
                         v-model="customerId"
                        @onchange="setCustomers"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Invoice Type" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="invoicetypes"
                         v-model="invoiceType"
                        @onchange="setInvoiceType"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Sales Person" />
                    <div class="relative mt-2">
                        <SearchableDropdownNew :options="salesuser"
                         v-model="salesUserId"
                        @onchange="setSalesUser"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Category" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="category"
                         v-model="categoryId"
                        @onchange="setType"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                {{ column.label }}
                                <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                            <td class="px-4 py-2.5 min-w-40">
                                {{ poData.invoice_no }}
                            </td>
                            <td class="px-4 py-2.5">
                                {{ poData.invoice_type }}
                            </td>
                            <td class="px-4 py-2.5">
                               {{ poData.category }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52  font-medium text-gray-900 whitespace-nowrap truncate">
                                {{ poData.customers.customer_name }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatDate(poData.date) }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatAmount(poData.total_amount)}}
                            </td>
                            <td class="px-4 py-2.5 min-w-40">
                                {{ formatAmount(poData.paid_amount)}}
                            </td>
                            <td class="flex flex-1 items-center px-4 py-2.5">
                                <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                    <span class="text-sm font-semibold whitespace-nowrap" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                </div>
                            </td>
                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                    <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink v-if="poData.status == 'Unpaid' && poData.entity_type == 'invoice' && poData.customers.organization_id == null && permissions.canEditInvoice" :href="route('invoice.edit',{id:poData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink v-if="poData.status == 'Unpaid' && poData.entity_type == 'challan' && poData.customers.organization_id == null && permissions.canEditInvoice" :href="route('challaninvoice.edit',{id:poData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink v-if="poData.status == 'Unpaid' &&  poData.customers.organization_id != null && permissions.canStockTransferEdit" :href="route('stocktransfer.edit',{id:poData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button v-if="poData.status == 'Unpaid' && poData.customers.organization_id == null" type="button" @click="openDeleteModal(poData.id) && permissions.canDeleteInvoice" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <!-- to do changes in api -->
                                            <button v-if="poData.status === 'Unpaid' && poData.customers.organization_id != null && permissions.canDeleteInvoice" type="button" @click="openSTDeleteModal(poData.id)" class="flex space-x-2 items-center px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" :disabled="!permissions.canDeleteInvoice">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <ActionLink v-if="poData.customers.organization_id == null  && permissions.canEditInvoice" :href="route('creditnote.add',{id:poData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M3 10h18M3 14h18M3 18h18" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17 3v18a2 2 0 01-2 2H9a2 2 0 01-2-2V3a2 2 0 012-2h6a2 2 0 012 2z" />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Credit Note
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <ActionLink :href="route('invoice.view',{id:poData.id, source: 'invoice .index'})" v-if="permissions.canViewInvoice">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        View Invoice
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <!-- <button type="button" v-if="permissions.canReceivePayment"  @click="openPaymentReceiveModal(poData.id, poData.organization_id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Receive Payment
                                                </span>
                                            </button> -->
                                            <button type="button"  @click="openPreviewModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                        Generate PDF
                                                </span>
                                            </button>
                                            <button v-if="permissions.canCreateInvoice" type="button" @click="openInvoiceSendEmailModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 8l7 5 7-5M3 8v8m14-8v8M3 8l7 5 7-5"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">Send Email</span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <NewModal :show="sendInvoiceEmailModal">
        <div class="flex w-full justify-start bg-indigo-600 px-6 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
            <h2 class="text-2xl font-bold">Send Email</h2>
        </div>

        <div class="p-6 overflow-y-auto max-h-screen">
            <div class="space-y-4">
                <div>
                    <InputLabel for="to" value="To:" />
                    <TextInput
                        id="to"
                        type="email"
                        v-model="emailTemplate.to"
                        @input="errors.to = ''"
                    />
                    <span v-if="errors.to" class="text-red-500 text-sm">{{ errors.to }}</span>
                </div>

                <div>
                    <InputLabel for="cc" value="CC:" />
                    <TextInput id="cc" type="text" placeholder="Enter multiple emails" ref="ccInput" />
                </div>

                <div>
                    <InputLabel for="from" value="From:" />
                    <div class="relative mt-2">
                        <SearchableDropdown
                            :options="email"
                            v-model="emailTemplate.from"
                            @onchange="setFrom"
                        />
                    </div>
                    <span v-if="errors.from" class="text-red-500 text-sm">{{ errors.from }}</span>
                </div>

                <div class="flex space-x-4">
                    <div class="w-1/2">
                        <InputLabel value="Template:" />
                        <div class="relative mt-2">
                            <SearchableDropdown
                                :options="emailTemplates"
                                v-model="emailTemplate.template"
                                @onchange="setTemplate"
                            />
                        </div>
                        <span v-if="errors.template" class="text-red-500 text-sm">{{ errors.template }}</span>
                    </div>
                    <div class="w-1/2">
                        <InputLabel value="PDF Page:" />
                        <div class="relative mt-2">
                            <SearchableDropdown
                                :options="pagetypes"
                                v-model="emailTemplate.pagetype"
                                @onchange="setPDFPageType"
                            />
                        </div>
                        <span v-if="errors.pagetype" class="text-red-500 text-sm">{{ errors.pagetype }}</span>
                    </div>
                </div>

                <div>
                    <InputLabel for="subject" value="Subject:" />
                    <TextInput
                        id="subject"
                        type="text"
                        v-model="emailTemplate.subject"
                        @input="errors.subject = ''"
                    />
                    <span v-if="errors.subject" class="text-red-500 text-sm">{{ errors.subject }}</span>
                </div>

                <div class="mb-20">
                    <InputLabel for="content" value="Content:" />
                    <quill-editor
                        v-model:content="emailTemplate.content"
                        contentType="html"
                        theme="snow"
                        toolbar="essential"
                        @update:content="errors.content = ''"
                    ></quill-editor>
                    <span v-if="errors.content" class="text-red-500 text-sm">{{ errors.content }}</span>
                </div>
            </div>
        </div>

        <div class="px-10 py-4 bg-white flex justify-end absolute w-full right-0 bottom-0">
            <SecondaryButton @click="closeInvoiceSendEmailModal">Cancel</SecondaryButton>
            <div class="w-36">
                <PrimaryButton class="ml-3 w-20" @click="sendEmail" :disabled="sendingEmail">
                    <span v-if="sendingEmail">
                        <svg class="animate-spin h-5 w-5 mr-2 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
                        </svg>
                        Sending...
                    </span>
                    <span v-else>Send Email</span>
                </PrimaryButton>
            </div>
        </div>
    </NewModal>

        <Modal :show="deleteModal" @close="closeDeleteModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this Invoice?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeDeleteModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteInvoice"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="STdeleteModal" @close="closeSTDeleteModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this Transferred stock?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeSTDeleteModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteStockTransfer"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="modalVisible" @close="closeModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                <div class="container1" id="pdf-content">
                    <div v-if="invoiceData.organization.id == '3'" class="header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                        <img class="w-20 h-20" :src="filePath + invoiceData.organization.logo" alt="logo">
                        <p><strong style="font-size: 20px;">{{invoiceData.customers.customer_type}} Invoice</strong></p>
                        <h1><div style="width: 120px;">
                        </div></h1>
                    </div>
                    <div v-if="invoiceData.organization.id == '1' || invoiceData.organization.id == '2'" class="header" style="align-items: start; justify-content: center; text-align: center;">
                        <img class="w-full h-10" :src="filePath + invoiceData.organization.logo" alt="logo">
                        <div style="align-items: center; justify-content: space-between; margin-bottom: 10px;">
                            <p style="font-size: 20px;"><strong>{{invoiceData.customers.customer_type}} Invoice</strong></p>
                        </div>
                    </div>
                    <div style="display:flex; justify-content: space-between;">
                        <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <p><strong style="font-size: 14px; margin-top: 10px">{{ invoiceData.organization.name }}</strong> </p>
                            <p style="margin-bottom: 4px;"><strong></strong></p>
                            <p>{{ invoiceData.organization.address_line_1}}</p>
                            <p>{{ invoiceData.organization.address_line_2}}</p>
                            <p>{{ invoiceData.organization.pincode}} , {{ invoiceData.organization.city}}</p>
                             <p style="margin-bottom: 4px;"><strong></strong></p>
                            <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ invoiceData.organization.contact_no }}</p></div>
                            <div style="display:flex;"><p style="width: 40px;"><strong>Email</strong></p><p>: {{ invoiceData.organization.email }}</p></div>
                            <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ invoiceData.organization.gst_no }}</p></div>
                        </div>
                        <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 320px;">
                            <div style="display:flex;"><p style="width: 100px;"><strong>Invoice Number</strong></p><p>: {{ invoiceData.invoice_no }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Invoice Date</strong></p><p>: {{ formatDate(invoiceData.date) }}</p></div>
                            <div v-if="invoiceData.entity_type == 'challan'" style="display:flex;"><p style="width: 100px;"><strong>Challan Number</strong></p><p>: {{ invoiceData.challan.challan_number }}</p></div>
                            <div v-if="invoiceData.customer_po_number != null" style="display:flex;"><p style="width: 100px;"><strong>PO Number</strong></p><p>: {{ invoiceData.customer_po_number }}</p></div>
                            <div v-if="invoiceData.customer_po_date != null" style="display:flex;"><p style="width: 100px;"><strong>PO Date</strong></p><p>: {{ invoiceData.customer_po_date }}</p></div>
                            <div v-if="invoiceData.patient_name != null" style="display:flex;"><p style="width: 100px;"><strong>Patient Name</strong></p><p>: {{ invoiceData.patient_name }}</p></div>
                            <div v-if="invoiceData.eway_bill != null" style="display:flex;"><p style="width: 100px;"><strong>Eway Bill</strong></p><p>: {{ invoiceData.eway_bill }}</p></div>
                            <div v-if="invoiceData.due_days != null" style="display:flex;"><p style="width: 100px;"><strong>Due Days</strong></p><p>: {{ invoiceData.due_days }} Days</p></div>
                             <p style="margin-bottom: 4px;"><strong></strong></p>
                            <p><strong style="font-size: 14px; margin-top: 10px">{{ invoiceData.customers.customer_name}} </strong></p>
                            <p>{{ invoiceData.customers.address}}</p>
                            <!-- <p>{{ invoiceData.customers.city}}</p> -->
                             <p style="margin-bottom: 4px;"><strong></strong></p>
                            <div style="display:flex;"><p style="width: 40px;"><strong>Phone</strong></p><p>: {{ invoiceData.customers.contact_no }}</p></div>
                            <div style="display:flex;"><p style="width: 40px;"><strong>Email</strong></p><p>: {{ invoiceData.customers.email }}</p></div>
                            <div style="display:flex;"><p style="width: 40px;"><strong>GST</strong></p><p>: {{ invoiceData.customers.gst_no }}</p></div>
                            <div style="display:flex; justify-content: space-between">
                                <div v-if="invoiceData.transport != null" style="display:flex;"><p style=""><strong>Transport</strong></p><p>: {{ invoiceData.transport }}</p></div>
                                <div v-if="invoiceData.dispatch != null" style="display:flex;"><p style=""><strong>Dispatch</strong></p><p>: {{ invoiceData.dispatch }}</p></div>
                            </div>
                        </div>
                    </div>
                    <div style="overflow-x:auto;">
                        <table>
                            <thead>
                                <tr>
                                    <th>SN</th>
                                    <th v-if="invoiceData.category == 'Service'">PART NO</th>
                                    <th v-if="invoiceData.category == 'Sales'">CODE</th>
                                    <th>PRODUCT NAME</th>
                                    <th>HSN</th>
                                    <th>QTY</th>
                                    <th>BATCH</th>
                                    <th>EXP</th>
                                    <th>MRP</th>
                                    <th>RATE</th>
                                    <th v-if="invoiceData.customers.gst_type =='IGST'">IGST(%)</th>
                                    <th v-if="invoiceData.customers.gst_type =='CGST/SGST'">CGST(%)</th>
                                    <th v-if="invoiceData.customers.gst_type =='CGST/SGST'">SGST(%)</th>
                                    <th>DIS.(₹)</th>
                                    <th>AMOUNT</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(poData, index) in  invoiceData.invoice_detail" :key="poData.id" class="">
                                    <td>{{ index + 1 }}</td>
                                    <td v-if="invoiceData.category == 'Service'">{{ poData.serialnumbers.product.item_code }}</td>
                                    <td v-if="invoiceData.category == 'Sales'">{{ poData.serialnumbers.product.item_code }}</td>
                                    <td>{{ poData.serialnumbers.product.name }}
                                        <span style="display: flex">{{ poData.description }}</span>
                                    </td>
                                    <td>{{ poData.serialnumbers.product.hsn_code }}</td>
                                    <td>{{ poData.qty }}</td>
                                    <td>{{ poData.serialnumbers.batch ?? '-' }}</td>
                                    <td>{{ poData.serialnumbers.expiry_date ?? '-' }}</td>
                                    <td>{{ poData.serialnumbers.mrp ? formatAmount(poData.serialnumbers.mrp) : '-' }}</td>
                                    <td>{{ formatAmount(poData.price)}}</td>
                                    <td v-if="invoiceData.customers.gst_type =='IGST'">{{ formatAmount(poData.gst) ?? '-' }}</td>
                                    <td v-if="invoiceData.customers.gst_type =='CGST/SGST'">{{ formatAmount(poData.gst/2) ?? '-' }}</td>
                                    <td v-if="invoiceData.customers.gst_type =='CGST/SGST'">{{ formatAmount(poData.gst/2) ?? '-' }}</td>
                                    <td>{{ formatAmount(poData.discount_amount) ?? '-' }}</td>
                                    <td>{{ formatAmount(poData.total_amount) ?? '-' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="" style="margin-bottom: 10px; justify-items: start;">
                        <p>{{ invoiceData.note }}</p>
                    </div>
                    <div style="display:flex; justify-content: space-between;">
                        <div class="" style="margin-bottom: 20px; justify-items: start; width: 400px;">
                            <p><strong>OUR BANK DETAILS</strong></p>
                            <div v-if="invoiceData.organization.id == '1'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ invoiceMCbank.bank_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '1'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ invoiceMCbank.branch_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '1'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ invoiceMCbank.account_no }}</p></div>
                            <div v-if="invoiceData.organization.id == '1'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ invoiceMCbank.ifsc_code }}</p></div>

                            <div v-if="invoiceData.organization.id == '2'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ invoiceHCbank.bank_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '2'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ invoiceHCbank.branch_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '2'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ invoiceHCbank.account_no }}</p></div>
                            <div v-if="invoiceData.organization.id == '2'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ invoiceHCbank.ifsc_code }}</p></div>


                            <div v-if="invoiceData.organization.id == '3'" style="display:flex;"><p><strong>Bank Name</strong></p><p>:  {{ invoiceNOXbank.bank_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '3'" style="display:flex;"><p><strong>Branch Name</strong></p><p>:  {{ invoiceNOXbank.branch_name }}</p></div>
                            <div v-if="invoiceData.organization.id == '3'" style="display:flex;"><p><strong>Account No</strong></p><p>:  {{ invoiceNOXbank.account_no }}</p></div>
                            <div v-if="invoiceData.organization.id == '3'" style="display:flex;"><p><strong>IFSC Code</strong></p><p>:  {{ invoiceNOXbank.ifsc_code }}</p></div>
                        </div>
                        <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <div style="display:flex;"><p style="width: 100px;"><strong>Sub Total (₹)</strong></p><p>: {{  formatAmount(invoiceData.sub_total) }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Total Discount (₹)</strong></p><p>: {{  formatAmount(invoiceData.total_discount) }}</p></div>
                            <div v-if="invoiceData.customers.gst_type =='IGST'" style="display:flex;"><p style="width: 100px;"><strong>Total IGST (₹):</strong></p><p>: {{  formatAmount(invoiceData.igst) }}</p></div>
                            <div v-if="invoiceData.customers.gst_type =='CGST/SGST'" style="display:flex;"><p style="width: 100px;"><strong>Total CGST (₹):</strong></p><p>: {{  formatAmount(invoiceData.cgst) }}</p></div>
                            <div v-if="invoiceData.customers.gst_type =='CGST/SGST'" style="display:flex;"><p style="width: 100px;"><strong>Total SGST (₹):</strong></p><p>: {{  formatAmount(invoiceData.sgst) }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Total Amount (₹)</strong></p><p>: {{ formatAmount(invoiceData.total_amount) }}</p></div>
                        </div>
                    </div>
                    <div style="display:flex; justify-content: space-between;">
                        <div class="invoiceMCbank" style="margin-bottom: 20px; justify-items: start; width: 405px;">
                            <p><strong>TERMS & CONDITIONS</strong></p>
                            <div style="display:flex;"><li> {{ terms.term1 }}</li></div>
                            <div style="display:flex;"><li> {{ terms.term2 }}</li></div>
                            <div style="display:flex;"><li> {{ terms.term3 }}</li></div>
                        </div>
                        <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <p><strong>FOR,</strong></p>
                            <p><strong>{{ invoiceData.organization.name }}</strong></p>
                                <img class="h-28" :src="filePath + invoiceData.organization.signature" alt="logo">
                        </div>
                    </div>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <div class="flex flex-col justify-end space-y-6">
                        <div class="flex items-center space-x-2">
                            <InputLabel for="customer_id" value="Page Type :" />
                            <SearchableDropdown :options="pagetypes"
                            v-model="page"
                            @onchange="setPageType"
                        />
                        </div>
                        <div class="flex justify-end">
                            <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                                <div class="w-36">
                                    <PrimaryButton
                                        class="ml-3 w-20"
                                        @click="downloadPDF(invoiceData.id, page)"
                                    >
                                        Generate Pdf
                                    </PrimaryButton>
                                </div>
                        </div>
                    </div>
                </div>

            </div>
        </Modal>
        <Modal :show="paymentReceiveModal" @close="paymentReceiveCloseModal" :maxWidth="paymentReceiveWidth">
             <div class="p-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold leading-7 text-gray-900">Receive Payment</h2>
                        <div class="text-base font-semibold leading-6 text-gray-900" v-if="creditData.length > 0">
                            Credits Available:  ₹{{ formatAmount(totalUnusedAmount) }}
                        </div>
                    </div>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-4 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <div class="inline-flex items-center justify-start w-full space-x-3">
                                    <InputLabel for="role_id" value="Invoice No:" />
                                    <p class="text-sm font-semibold text-gray-700">
                                    {{ paymentform.invoice_no }}
                                    </p>
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <div class="inline-flex items-center justify-start w-full space-x-3">
                                    <InputLabel for="role_id" value="Total Amount (₹):" />
                                    <p class="text-sm font-semibold text-gray-700">
                                    {{ formatAmount(paymentform.invoice_amount) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2 sm:col-span-4">
                            <InputLabel for="role_id" value="Payment Through Credit ?" />
                            <div class="relative mt-2">
                                <RadioButton
                                    v-model="creditOption"
                                    :options="radioOptions"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                        </div>
                        <div v-if="creditOption =='No'" class="mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Payment Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="paymentType"
                                    v-model="paymentform.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors[`form.payment_type`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    @change="clearError('form.amount')"
                                    v-model="paymentform.amount"
                                    :class="{ 'error rounded-md': form.errors[`form.amount`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="check_number" value="Cheque Number" />
                                <TextInput
                                    id="check_number"
                                    type="text"
                                    v-model="paymentform.check_number"
                                    :class="{ 'error rounded-md': form.errors[`form.check_number`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="bank_name" value="Bank Name" />
                                <TextInput
                                    id="bank_name"
                                    type="text"
                                    v-model="paymentform.bank_name"
                                    :class="{ 'error rounded-md': form.errors[`form.bank_name`] }"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="paymentform.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                     @change="clearError('form.date')"
                                    :class="{ 'error rounded-md': form.errors[`form.date`] }"
                                />
                            </div>
                            <div v-if="payment_type != 'Cash'" class="sm:col-span-3">
                                <InputLabel for="org_bank_id" value="Our Bank" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="bankinfo"
                                    v-model="paymentform.org_bank_id"
                                    @onchange="setBankInfo"
                                    :class="{ 'error rounded-md': form.errors[`form.org_bank_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    :rows="2"
                                    v-model="paymentform.note"
                                />
                            </div>
                        </div>
                        <div v-else>
                            <table class="mt-5 overflow-x-auto divide-y divide-gray-300 w-full"  v-if="creditData.length > 0">
                                <thead>
                                    <tr>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Date</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Bank</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Credit Amount (₹)</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Available Credit (₹)</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Amount To Credit (₹)</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-300 bg-white">
                                    <tr v-for="(product, index)  in creditData" :key="index">
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">
                                            <div class="flex flex-col">
                                                <div class="text-sm text-gray-900">
                                                    {{ product.paymentreceive?.bank_info?.bank_name ? product.paymentreceive?.bank_info?.bank_name : 'Cash' }}
                                                </div>
                                                <div class="text-sm text-gray-900">
                                                    {{ product.paymentreceive?.bank_info?.account_number ? product.paymentreceive?.bank_info?.account_number : '' }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.amount) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.unused_amount) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900" style="width: 22%;">
                                            <TextInput
                                                id="amount_to_credit"
                                                type="text"
                                                v-model="product.amount_to_credit"
                                                @change="clearError('creditData.' + index + '.amount_to_credit')"
                                                :class="{ 'error': form.errors[`creditData.${index}.amount_to_credit`] }"
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="mt-4 flex justify-end">
                                <p class="text-base font-semibold">Total Amount To Credit: {{ formatAmount(totalAmountToCredit) }}</p>
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="paymentReceiveCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="acceptPayment"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>
</template>

<style scoped>
    .error {
        border: 1px solid red;
    }
    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 4px !important;
        text-align: left;
        font-size: 11px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 6px 4px !important;
        text-align: left;
        font-size: 11px;
        white-space: nowrap;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .modal {
        z-index: 1000;
    }

    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 64px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        z-index: 1000;
        cursor: pointer;
        transition: opacity 0.3s ease;
    }

    .notification.success {
        background-color: rgb(177 237 199);
        color: rgb(21 128 61);
        border: 1px solid rgb(21 128 61);
        font-size: 0.875rem;
    }

    .notification.error {
        background-color: rgb(243 202 202);
        color: rgb(185 28 28);
        border: 1px solid rgb(185 28 28);
        font-size: 0.875rem;
    }

</style>


