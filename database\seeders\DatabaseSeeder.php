<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\DB;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        if (DB::table('roles')->count() == 0) {
            DB::table('roles')->insert([
                [
                    'name'  => 'Admin',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Accountant',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Sales',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Service',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'HR',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }

        if (DB::table('settings')->count() == 0) {
            DB::table('settings')->insert([
                [
                    'type'   => 'po_number',
                    'value'  => 'PO',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'po_receive_number',
                    'value'  => 'POR',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'quotation_number',
                    'value'  => 'QT',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'order_number',
                    'value'  => 'OR',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'order_deliver_number',
                    'value'  => 'ORD',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'challan_number',
                    'value'  => 'CHL',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'invoice_number',
                    'value'  => 'R',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'type'   => 'invoice_number',
                    'value'  => 'R',
                    'created_by'  => '1',
                    'updated_by'  => '1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);
        }

        if (DB::table('roles')->count() > 0 && DB::table('permissions')->count() > 0) {

            foreach (User::all() as $user){
                $role = Role::findById($user->role_id);
                $user->assignRole($role->name);
            }

            $permissions = Permission::all();
             foreach (Role::all() as $role){
                 if($role->name == 'Admin'){
                     $role->givePermissionTo($permissions);
                 }else{
                     //$role->givePermissionTo('Dashboard');
                 }
             }
        }
    }
}
