import{r as C,C as Q,j as V,A as G,o as v,c as f,a as u,u as c,w as k,F,Z as L,b as e,d as Y,t as _,n as g,e as I,f as w,k as Z,v as H,i as T,g as J,s as K,x as W}from"./app-8a557454.js";import{_ as X}from"./AdminLayout-301d54ca.js";import{P as ee}from"./PrimaryButton-9d9bcdd8.js";import{_ as b}from"./TextInput-ab168ee4.js";import{_ as te}from"./DateInput-e2056bbf.js";import{_ as q}from"./InputLabel-07f3a6e8.js";import{_ as se}from"./SearchableDropdown-51a69527.js";import{_ as oe}from"./MultipleFileUpload-e5c9465d.js";import{u as ae}from"./index-62ab7306.js";import{_ as le}from"./_plugin-vue_export-helper-c27b6911.js";const i=m=>(K("data-v-7f173b24"),m=m(),W(),m),ce={class:"animate-top"},ne=["onSubmit"],re={class:"sm:flex sm:items-center"},ie=i(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Product")],-1)),de={class:"text-sm font-semibold text-gray-900"},_e={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},ue={class:"inline-flex items-start space-x-6 justify-start w-full"},me={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},pe={class:"inline-flex items-center justify-start w-full space-x-2"},ve=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1)),ge={class:"text-sm leading-6 text-gray-700"},ye={class:"inline-flex items-center justify-start w-full space-x-2"},fe=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),he={class:"text-sm leading-6 text-gray-700"},xe={class:"inline-flex items-center justify-start w-full space-x-2"},be=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),we={class:"text-sm leading-6 text-gray-700"},Ve={class:"inline-flex items-center justify-start w-full space-x-2"},Pe=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),$e={class:"text-sm leading-6 text-gray-700"},qe={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Ce={class:"inline-flex items-center justify-start w-full space-x-2"},Fe=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Receive No:",-1)),De={class:"text-sm leading-6 text-gray-700"},Ne={class:"inline-flex items-center justify-start w-full space-x-2"},Ue=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1)),Se={class:"text-sm leading-6 text-gray-700"},je={class:"inline-flex items-center justify-start w-full space-x-2"},ke=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1)),Ie={class:"text-sm leading-6 text-gray-700"},Te={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Ae={class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2"},Oe={class:"sm:col-span-4"},Re={class:"sm:col-span-4"},Be={class:"sm:col-span-4"},ze={class:"relative mt-2"},Ee={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto"},Me={class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2",style:{width:"140%"}},Qe=i(()=>e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")],-1)),Ge={class:"sm:col-span-1"},Le={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Ye={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},Ze=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price (₹)")],-1)),He=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"GST %")],-1)),Je=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),Ke=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Received QTY")],-1)),We=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"MRP (₹)")],-1)),Xe=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Purchase Price (₹)")],-1)),et=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),tt=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Batch")],-1)),st=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Amount")],-1)),ot={class:"sm:col-span-2"},at={class:"text-sm leading-5 text-gray-700"},lt={class:"sm:col-span-1"},ct={class:"text-sm leading-5 text-gray-700"},nt={class:"sm:col-span-1"},rt={class:"text-sm leading-5 text-gray-700"},it={class:"sm:col-span-1"},dt={class:"text-sm leading-5 text-gray-700"},_t={class:"sm:col-span-1"},ut={class:"text-sm leading-5 text-gray-700"},mt={class:"sm:col-span-1"},pt={class:"text-sm leading-5 text-gray-700"},vt={class:"sm:col-span-1 mb-2"},gt={class:"sm:col-span-1 mb-2"},yt={class:"sm:col-span-1 mb-2"},ft={key:0,class:"text-red-500 text-xs absolute"},ht={class:"flex sm:col-span-1 mb-2"},xt={class:"sm:col-span-1"},bt={class:"text-sm leading-5 text-gray-700"},wt={key:0,class:"sm:col-span-9 mb-2"},Vt={class:"sm:col-span-3"},Pt={class:"sm:col-span-3"},$t={class:"sm:col-span-3"},qt={key:0,class:"text-red-500 text-xs absolute"},Ct={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ft={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Dt={class:"sm:col-span-3 space-y-2"},Nt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ut={class:"sm:col-span-3 space-y-4"},St={class:"flex space-x-4"},jt={class:"w-full"},kt={class:"sm:col-span-3"},It={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Tt={class:"inline-flex items-center justify-end w-full space-x-3"},At=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Ot={class:"text-base font-semibold text-gray-900 w-32"},Rt={class:"inline-flex items-center justify-end w-full space-x-3"},Bt=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1)),zt={class:"text-base font-semibold text-gray-900 w-32"},Et={class:"inline-flex items-center justify-end w-full space-x-3"},Mt=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Round Off (₹):",-1)),Qt={class:"w-32"},Gt={class:"inline-flex items-center justify-end w-full space-x-3"},Lt={key:0,class:"text-sm text-red-600 mt-2"},Yt={class:"inline-flex items-center justify-end w-full space-x-3"},Zt=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Ht={class:"text-base font-semibold text-gray-900 w-32"},Jt={class:"flex mt-6 items-center justify-between"},Kt={class:"ml-auto flex items-center justify-end gap-x-6"},Wt={__name:"ReceivePo",props:["data","po_receive_number","salesuser"],setup(m){const h=m,r=C([{organization_id:"",company_id:"",product_id:"",purchase_order_detail_id:"",purchase_order_id:"",qty:"",po_receive_number:"",total_qty:"",received_qty:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}]),D=C();D.value=h.po_receive_number[h.data[0].organization.id],Q(()=>{r.value=h.data[0].purchase_order_detail_for_receive.map(a=>({organization_id:h.data[0].organization_id,company_id:h.data[0].company_id,product_id:a.product_id,purchase_order_detail_id:a.id,purchase_order_id:h.data[0].id,po_receive_number:h.po_receive_number[h.data[0].organization_id],total_qty:a.qty,received_qty:a.receive_qty,receive_qty:"",total_batch:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}))});const s=ae("post","/savereceivepo",{receivedProduct:[],created_by:"",total_price:"",total_gst_amount:"",total_amount:"",customer_invoice_no:"",customer_invoice_date:"",category:h.data[0].category,type:h.data[0].type,round_off:"0.00",document:null}),A=a=>{s.document=a},O=()=>{parseFloat(s.round_off)>.99||(s.total_amount=U.value,s.total_gst_amount=S.value,s.total_price=j.value,s.receivedProduct=r.value.map((o,d)=>({...o,productDetails:P.value[d]||[]})),s.submit({preserveScroll:!0,onSuccess:()=>s.reset()}))},x=a=>{s.errors[a]=null},P=C([]),R=a=>{s.errors["receivedProduct."+a+".receive_qty"]=null;const o=r.value[a].total_batch,d=r.value[a].total_qty,l=r.value[a].received_qty,t=[];let n;if(o&&!isNaN(o)){o>d-l?n=d-l:n=o;for(let p=0;p<n;p++)t.push({batch:"",expiry_date:"",qty:""})}r.value[a].total_batch=n,P.value[a]=t};V(()=>{const a=new Date,o={year:"numeric",month:"long",day:"numeric"};return a.toLocaleDateString("en-US",o)});const B=(a,o)=>{s.created_by=a,s.errors.created_by=null},z=(a,o)=>{const d=parseFloat(r.value[o].purchase_price),l=parseFloat(a.gst)||0,t=parseFloat(r.value[o].receive_qty),n=d*t*(1+l/100),p=d*t,y=d*t*(l/100);return r.value[o].total_price=isNaN(p)?"":parseFloat(p).toFixed(2),r.value[o].total_gst_amount=isNaN(y)?"":parseFloat(y).toFixed(2),isNaN(n)?"":parseFloat(n).toFixed(2)},N=(a,o)=>{r.value[o].total_amount=z(a,o)},U=V(()=>{const a=r.value.reduce((l,t)=>l+(t.total_amount?parseFloat(t.total_amount):0),0),o=parseFloat(s.round_off)||0,d=a+o;return parseFloat(d.toFixed(2))}),E=V(()=>{const a=parseFloat(s.round_off);return a>=0&&a<=.99}),S=V(()=>r.value.reduce((a,o)=>a+(o.total_gst_amount?parseFloat(o.total_gst_amount):0),0)),j=V(()=>r.value.reduce((a,o)=>a+(o.total_price?parseFloat(o.total_price):0),0)),M=a=>{const o=new Date(a),d={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",d)},$=a=>{let d=Math.round(a).toString(),l=d.substring(d.length-3),t=d.substring(0,d.length-3);return t!==""&&(l=","+l),`${t.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.00`};return(a,o)=>{const d=G("InputError");return v(),f(F,null,[u(c(L),{title:"Company PO"}),u(X,null,{default:k(()=>[e("div",ce,[e("form",{onSubmit:Y(O,["prevent"]),class:""},[e("div",re,[ie,e("p",de,_(m.data[0].organization.name),1)]),e("div",_e,[e("div",ue,[e("div",me,[e("div",pe,[ve,e("p",ge,_(m.data[0].company.name??"-"),1)]),e("div",ye,[fe,e("p",he,_(m.data[0].company.gst_no??"-"),1)]),e("div",xe,[be,e("p",we,_(m.data[0].company.email??"-"),1)]),e("div",Ve,[Pe,e("p",$e,_(m.data[0].company.contact_no??"-"),1)])]),e("div",qe,[e("div",Ce,[Fe,e("p",De,_(D.value??"-"),1)]),e("div",Ne,[Ue,e("p",Se,_(m.data[0].po_number??"-"),1)]),e("div",je,[ke,e("p",Ie,_(M(m.data[0].date)??"-"),1)])])])]),e("div",Te,[e("div",Ae,[e("div",Oe,[u(q,{for:"customer_invoice_no",value:"Company Invoice No"}),u(b,{id:"customer_invoice_no",type:"text",modelValue:c(s).customer_invoice_no,"onUpdate:modelValue":o[0]||(o[0]=l=>c(s).customer_invoice_no=l),onChange:o[1]||(o[1]=l=>x("customer_invoice_no")),class:g({"error rounded-md":c(s).errors.customer_invoice_no})},null,8,["modelValue","class"]),c(s).invalid("customer_invoice_no")?(v(),I(d,{key:0,class:"",message:c(s).errors.customer_invoice_no},null,8,["message"])):w("",!0)]),e("div",Re,[u(q,{for:"customer_invoice_date",value:"Company Invoice Date"}),Z(e("input",{class:g(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":c(s).errors.customer_invoice_date}]),type:"date","onUpdate:modelValue":o[2]||(o[2]=l=>c(s).customer_invoice_date=l),onChange:o[3]||(o[3]=l=>x("customer_invoice_date"))},null,34),[[H,c(s).customer_invoice_date]]),c(s).invalid("customer_invoice_date")?(v(),I(d,{key:0,class:"",message:c(s).errors.customer_invoice_date},null,8,["message"])):w("",!0)]),e("div",Be,[u(q,{for:"company_name",value:"Received By:"}),e("div",ze,[u(se,{options:m.salesuser,modelValue:c(s).created_by,"onUpdate:modelValue":o[4]||(o[4]=l=>c(s).created_by=l),onOnchange:B,class:g({"error rounded-md":c(s).errors.created_by})},null,8,["options","modelValue","class"])])])])]),e("div",Ee,[e("div",Me,[Qe,e("div",Ge,[m.data[0].category=="Service"?(v(),f("p",Le,"Part No")):(v(),f("p",Ye,"Product Code"))]),Ze,He,Je,Ke,We,Xe,et,tt,st]),(v(!0),f(F,null,T(m.data[0].purchase_order_detail_for_receive,(l,t)=>(v(),f("div",{class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center",style:{width:"140%"},key:t},[e("div",ot,[e("p",at,_(l.product.name??"-"),1)]),e("div",lt,[e("p",ct,_(l.product.item_code??"-"),1)]),e("div",nt,[e("p",rt,_($(l.price)??"-"),1)]),e("div",it,[e("p",dt,_($(l.gst)??"-"),1)]),e("div",_t,[e("p",ut,_(l.qty??"-"),1)]),e("div",mt,[e("p",pt,_(l.receive_qty??"-"),1)]),e("div",vt,[u(b,{id:"gst",type:"text",modelValue:r.value[t].mrp,"onUpdate:modelValue":n=>r.value[t].mrp=n,class:g({error:c(s).errors[`receivedProduct.${t}.mrp`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","class"])]),e("div",gt,[u(b,{id:"gst",type:"text",modelValue:r.value[t].purchase_price,"onUpdate:modelValue":n=>r.value[t].purchase_price=n,onInput:n=>N(l,t),onChange:n=>x("receivedProduct."+t+".purchase_price"),class:g({error:c(s).errors[`receivedProduct.${t}.purchase_price`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("div",yt,[u(b,{id:"gst",type:"text",modelValue:r.value[t].receive_qty,"onUpdate:modelValue":n=>r.value[t].receive_qty=n,numeric:!0,onChange:n=>x(`receivedProduct.${t}.receive_qty`),onInput:n=>N(l,t),class:g({error:c(s).errors[`receivedProduct.${t}.receive_qty`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","onInput","class"]),c(s).errors[`receivedProduct.${t}.receive_qty`]?(v(),f("p",ft,_(c(s).errors[`receivedProduct.${t}.receive_qty`]),1)):w("",!0)]),e("div",ht,[u(b,{id:"gst",type:"text",numeric:!0,modelValue:r.value[t].total_batch,"onUpdate:modelValue":n=>r.value[t].total_batch=n,onChange:n=>R(t),class:g({error:c(s).errors[`receivedProduct.${t}.total_batch`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",xt,[e("p",bt,_(r.value[t].total_amount),1)]),P.value[t]?(v(),f("div",wt,[(v(!0),f(F,null,T(P.value[t],(n,p)=>(v(),f("div",{key:p,class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 items-center"},[e("div",Vt,[u(b,{type:"text",modelValue:n.batch,"onUpdate:modelValue":y=>n.batch=y,placeholder:"Batch",onChange:y=>x("receivedProduct."+t+".productDetails."+p+".batch"),class:g({error:c(s).errors[`receivedProduct.${t}.productDetails.${p}.batch`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",Pt,[u(te,{modelValue:n.expiry_date,"onUpdate:modelValue":y=>n.expiry_date=y,onChange:y=>x("receivedProduct."+t+".productDetails."+p+".expiry_date"),class:g({error:c(s).errors[`receivedProduct.${t}.productDetails.${p}.expiry_date`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",$t,[u(b,{type:"text",modelValue:n.qty,"onUpdate:modelValue":y=>n.qty=y,placeholder:"Qty",onChange:y=>x("receivedProduct."+t+".productDetails."+p+".qty"),class:g({error:c(s).errors[`receivedProduct.${t}.productDetails.${p}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128)),c(s).errors[`receivedProduct.${t}.productDetails`]?(v(),f("p",qt,_(c(s).errors[`receivedProduct.${t}.productDetails`]),1)):w("",!0)])):w("",!0)]))),128))]),e("div",Ct,[e("div",Ft,[e("div",Dt,[e("div",Nt,[e("div",Ut,[e("div",St,[e("div",jt,[u(q,{for:"note",value:"Upload Documents"}),u(oe,{inputId:"document",inputName:"document",onFiles:A})])])])])]),e("div",kt,[e("div",It,[e("div",Tt,[At,e("p",Ot,_($(j.value)),1)]),e("div",Rt,[Bt,e("p",zt,_($(S.value)),1)]),e("div",Et,[Mt,e("div",Qt,[u(b,{id:"round_off",type:"text",modelValue:c(s).round_off,"onUpdate:modelValue":o[5]||(o[5]=l=>c(s).round_off=l),onChange:o[6]||(o[6]=l=>x("round_off")),class:g({"error rounded-md":c(s).errors.round_off}),min:"0"},null,8,["modelValue","class"])])]),e("div",Gt,[E.value?w("",!0):(v(),f("p",Lt,"Round Off should be between 0.01 and 0.99."))]),e("div",Yt,[Zt,e("p",Ht,_(U.value),1)])])])])]),e("div",Jt,[e("div",Kt,[u(ee,{class:g(["",{"opacity-25":c(s).processing}]),disabled:c(s).processing},{default:k(()=>[J(" Submit ")]),_:1},8,["class","disabled"])])])],40,ne)])]),_:1})],64)}}},is=le(Wt,[["__scopeId","data-v-7f173b24"]]);export{is as default};
