<?php

namespace App\Http\Requests;

use App\Models\InvoiceDetail;
use App\Models\SerialNumbers;
use App\Models\Customer;
use App\DTO\InvoiceDTO;
use Support\Contracts\HasDTO;

use Illuminate\Foundation\Http\FormRequest;

class invoiceStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'note'          => 'nullable|string',
            'selectedProductItem.*.product_id'  => 'required|integer',
            'selectedProductItem.*.serial_number_id'  => 'required|integer',
            'selectedProductItem.*.sell_price'  => 'required',
            'selectedProductItem.*.qty'  => 'required|integer',
            'customer_id'   => 'required|integer',
        ];

        if (empty($this->input('invoice_id'))) {
            $rules['organization_id'] = 'required|integer';
            $rules['category'] = 'required|string';
            $rules['sales_user_id'] = 'required|integer';
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        if (empty($this->input('invoice_id'))) {
            $validator->after(function ($validator) {
                if($this->input('stock_transfer')) {
                    $customer = Customer::where('id', $this->input('customer_id'))->first();
                    if ($customer &&  ($this->input('organization_id') == $customer->organization_id)) {
                        $validator->errors()->add("customer_id", __('Can not transfer to same organization'));
                    }
                }

                $selectedProductItems = $this->input('selectedProductItem');
                $serialNumberQuantities = [];

                foreach ($selectedProductItems as $key => $product) {
                    $serialNumberId = $product['serial_number_id'];
                    $qty = $product['qty'];
                    if (!isset($serialNumberQuantities[$serialNumberId])) {
                        $serialNumberQuantities[$serialNumberId] = 0;
                    }
                    $serialNumberQuantities[$serialNumberId] += $qty;

                    $productDetails = SerialNumbers::find($product['serial_number_id']);
                    if ($productDetails) {
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                        if ($productDetails && ($productDetails->mrp != '' || $productDetails->mrp != 0) && $product['sell_price'] > ($productDetails->mrp)) {
                            $validator->errors()->add("selectedProductItem.$key.sell_price", __('Price exceeds mrp'));
                        }
                    }
                }
            });

        } else {
            $validator->after(function ($validator) {
                $selectedProductItems = $this->input('selectedProductItem');
                $serialNumberQuantities = [];
                // dd($selectedProductItems);
                foreach ($selectedProductItems as $key => $product) {
                    $serialNumberId = $product['serial_number_id'];
                    $qty = $product['qty'];

                    if (!isset($serialNumberQuantities[$serialNumberId])) {
                        $serialNumberQuantities[$serialNumberId] = 0;
                    }
                    $serialNumberQuantities[$serialNumberId] += $qty;
                    if (empty($product['invoice_detail_id'])) {
                        $productDetails = SerialNumbers::find($product['serial_number_id']);
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                    } else {
                        $productDetails = SerialNumbers::find($product['serial_number_id']);
                        $invoiceDetails = InvoiceDetail::find($product['invoice_detail_id']);
                        $availableQty = $productDetails->receive_qty - $productDetails->sell_qty + $invoiceDetails->qty;
                        if ($serialNumberQuantities[$serialNumberId] > $availableQty && $product['qty'] != $invoiceDetails->qty) {
                            $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not available for this batch.'));
                        }
                    }
                    if ($productDetails && ($productDetails->mrp != '' || $productDetails->mrp != 0) && $product['sell_price'] > ($productDetails->mrp)) {
                        $validator->errors()->add("selectedProductItem.$key.sell_price", __('Price exceeds mrp'));
                    }
                }
            });
        }
    }
        
    public function DTO()
    {
        return InvoiceDTO::LazyFromArray($this->input());
    }
}
