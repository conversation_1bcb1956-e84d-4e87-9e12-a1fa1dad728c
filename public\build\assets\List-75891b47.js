import{_ as F,b as Y,a as K}from"./AdminLayout-42d5bb92.js";import{_ as q}from"./CreateButton-1fa2a774.js";import{_ as H}from"./SecondaryButton-be49842d.js";import{D as Z}from"./DangerButton-3e1103de.js";import{M as G}from"./Modal-54f7c77a.js";import{_ as J}from"./Pagination-56593f88.js";import{_ as Q}from"./SearchableDropdownNew-ade24f0e.js";import{_ as R}from"./SimpleDropdown-5dc59147.js";import{_ as b}from"./InputLabel-f62a278f.js";import{r as h,l as W,o as l,c as _,a,u,w as i,F as w,Z as X,b as t,g,i as $,e as k,f as C,t as m}from"./app-2ecbacfc.js";import{_ as D}from"./ArrowIcon-0ab7616b.js";import{s as tt}from"./sortAndSearch-94154e09.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const et={class:"animate-top"},ot={class:"flex justify-between items-center"},st=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Paid Payments")],-1),at={class:"flex justify-end"},nt=t("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[t("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),t("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),lt={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},it={class:"flex justify-end"},dt={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},rt={class:"flex mb-2"},ct=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),mt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},_t={class:"sm:col-span-4"},ht={class:"relative mt-2"},pt={class:"sm:col-span-4"},ft={class:"relative mt-2"},ut={class:"mt-8 overflow-x-auto sm:rounded-lg"},gt={class:"shadow sm:rounded-lg"},yt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},vt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},xt={class:"border-b-2"},bt=["onClick"],wt={key:0},kt={class:"px-4 py-2.5 min-w-32"},Ct={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 min-w-52"},Mt={class:"px-4 py-2.5 min-w-36"},zt={class:"px-4 py-2.5 min-w-52"},Nt={class:"px-4 py-2.5 min-w-36"},It={class:"px-4 py-2.5 min-w-32"},$t={class:"items-center px-4 py-2.5"},At={class:"flex items-center justify-start gap-4"},Vt=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Bt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),St=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Pt=["onClick"],Ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),jt=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Lt=[Ot,jt],Et={key:1},Tt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ut=[Tt],Ft={class:"p-6"},Yt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this transaction? ",-1),Kt={class:"mt-6 flex justify-end"},de={__name:"List",props:["data","organization","companies","organizationId","companyId"],setup(n){const p=n,{form:M,search:qt,sort:A,fetchData:Ht,sortKey:V,sortDirection:B,updateParams:S}=tt("payment.index",{organization_id:p.organizationId,company_id:p.companyId}),y=h(!1),z=h(null),P=[{field:"date",label:"DATE",sortable:!0},{field:"company.name",label:"COMPANY NAME",sortable:!0},{field:"payment_type",label:"PAYMENT TYPE",sortable:!0},{field:"bank_info.bank_name",label:"BANK",sortable:!0},{field:"invoice_no",label:"INVOICE NO",sortable:!1},{field:"amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],O=o=>{z.value=o,y.value=!0},v=()=>{y.value=!1},j=()=>{M.delete(route("payment.destroy",{id:z.value}),{onSuccess:()=>v()})},L=o=>{const s=new Date(o),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},E=o=>{let s=o.toFixed(2).toString(),[e,f]=s.split("."),c=e.substring(e.length-3),I=e.substring(0,e.length-3);return I!==""&&(c=","+c),`${I.replace(/\B(?=(\d{2})+(?!\d))/g,",")+c}.${f}`},d=h(p.organizationId),r=h(p.companyId),x=h("");W([d,r],()=>{S({organization_id:d.value,company_id:r.value})});const N=(o,s,e)=>{x.value=o,M.get(route("payment.index",{search:o,organization_id:s,company_id:e}),{preserveState:!0})},T=(o,s)=>{d.value=o,N(x.value,d.value,r.value)},U=(o,s)=>{r.value=o,N(x.value,d.value,r.value)};return(o,s)=>(l(),_(w,null,[a(u(X),{title:"Payment"}),a(F,null,{default:i(()=>[t("div",et,[t("div",ot,[st,t("div",at,[nt,t("div",lt,[t("div",it,[a(q,{href:o.route("payment.create")},{default:i(()=>[g(" Make payment ")]),_:1},8,["href"])])])])]),t("div",dt,[t("div",rt,[ct,a(b,{for:"company_id",value:"Filters"})]),t("div",mt,[t("div",_t,[a(b,{for:"company_id",value:"Organization Name"}),t("div",ht,[a(R,{options:n.organization,modelValue:d.value,"onUpdate:modelValue":s[0]||(s[0]=e=>d.value=e),onOnchange:T},null,8,["options","modelValue"])])]),t("div",pt,[a(b,{for:"company_id",value:"Company Name"}),t("div",ft,[a(Q,{options:n.companies,modelValue:r.value,"onUpdate:modelValue":s[1]||(s[1]=e=>r.value=e),onOnchange:U},null,8,["options","modelValue"])])])])]),t("div",ut,[t("div",gt,[t("table",yt,[t("thead",vt,[t("tr",xt,[(l(),_(w,null,$(P,(e,f)=>t("th",{key:f,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:c=>u(A)(e.field,e.sortable)},[g(m(e.label)+" ",1),e.sortable?(l(),k(D,{key:0,isSorted:u(V)===e.field,direction:u(B)},null,8,["isSorted","direction"])):C("",!0)],8,bt)),64))])]),n.data.data&&n.data.data.length>0?(l(),_("tbody",wt,[(l(!0),_(w,null,$(n.data.data,(e,f)=>(l(),_("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",kt,m(L(e.date)??"-"),1),t("td",Ct,m(e.company.name??"-"),1),t("td",Mt,m(e.payment_type=="check"?"Cheque":e.payment_type??"-"),1),t("td",zt,m(e!=null&&e.bank_info?(e==null?void 0:e.bank_info.bank_name)+"-"+(e==null?void 0:e.bank_info.account_number):"-"),1),t("td",Nt,m(e!=null&&e.invoice_data?e.invoice_data.map(c=>c.invoice_no).join(", "):e.invoice_no),1),t("td",It,m(E(e.amount)??"-"),1),t("td",$t,[t("div",At,[a(Y,{align:"right",width:"48"},{trigger:i(()=>[Vt]),content:i(()=>[e.invoice_data.length!=0?(l(),k(K,{key:0,href:o.route("payment.edit",e.id)},{svg:i(()=>[Bt]),text:i(()=>[St]),_:2},1032,["href"])):C("",!0),t("button",{type:"button",onClick:c=>O(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Lt,8,Pt)]),_:2},1024)])])]))),128))])):(l(),_("tbody",Et,Ut))])])]),n.data.data&&n.data.data.length>0?(l(),k(J,{key:0,class:"mt-6",links:n.data.links},null,8,["links"])):C("",!0)]),a(G,{show:y.value,onClose:v},{default:i(()=>[t("div",Ft,[Yt,t("div",Kt,[a(H,{onClick:v},{default:i(()=>[g(" Cancel ")]),_:1}),a(Z,{class:"ml-3",onClick:j},{default:i(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{de as default};
