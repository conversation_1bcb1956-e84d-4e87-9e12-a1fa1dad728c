<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::rename('administration_info', 'organizations');

        Schema::table('challan', function (Blueprint $table) {
            $table->integer('organization_id')->nullable()->after('id');
            $table->string('transport')->nullable()->after('note');
            $table->string('dispatch')->nullable()->after('note');
        });

        Schema::table('challan_detail', function (Blueprint $table) {
            $table->longText('description')->nullable()->after('qty');
            $table->double('discount', 16, 2)->nullable()->default(0)->change();
            $table->double('discount_amount', 16, 2)->nullable()->default(0)->change();
        });

        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->integer('organization_id')->nullable()->after('id');
        });

        Schema::table('purchase_order_details', function (Blueprint $table) {
            $table->longText('description')->nullable()->after('total_amount');
        });


        Schema::table('invoice', function (Blueprint $table) {
            $table->integer('organization_id')->nullable()->after('id');
            $table->string('transport')->nullable()->after('note');
            $table->string('dispatch')->nullable()->after('note');
        });

        Schema::table('invoice_details', function (Blueprint $table) {
            $table->longText('description')->nullable()->after('total_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('all', function (Blueprint $table) {
            //
        });
    }
};
