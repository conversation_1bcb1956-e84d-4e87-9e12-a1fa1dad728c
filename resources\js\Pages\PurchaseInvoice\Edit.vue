<script setup>
import { ref, onMounted, watch, computed, onBeforeMount } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import DateInput from '@/Components/DateInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['data', 'purchase_order_receives', 'salesuser']);

const receivedProduct = ref([
    {
        organization_id: '',
        company_id: '',
        product_id: '',
        purchase_order_detail_id:'',
        purchase_order_id: '',
        qty: '',
        po_receive_number: '',
        total_qty: '',
        received_qty: '',
        mrp: '',
        purchase_price: '',
        total_amount: '',
        total_price: '',
        total_gst_amount: ''
    }
])

onBeforeMount(() => {
    receivedProduct.value = props.data[0].purchase_order_detail_for_receive.map(item => ({
        organization_id: props.data[0].organization_id,
        company_id: props.data[0].company_id,
        product_id: item.product_id,
        purchase_order_detail_id: item.id,
        purchase_order_id:  props.data[0].id,
        po_receive_number: props.purchase_order_receives[0].po_receive_number,
        total_qty: item.qty,
        received_qty: item.receive_qty,
        receive_qty: '',
        total_batch: '',
        mrp: '',
        purchase_price: '',
        total_amount: '',
        total_price: '',
        total_gst_amount: ''
    }));
});

const form = useForm('post', '/purchaseinvoice', {
    purchase_order_receive_id: props.purchase_order_receives[0].id,
    purchase_order_id: props.purchase_order_receives[0].purchase_order_id,
    receivedProduct: [],
    created_by: props.purchase_order_receives[0].created_by,
    total_price: '',
    total_gst_amount: '',
    total_amount: '',
    round_off: "0.00",
    customer_invoice_no: props.purchase_order_receives[0].customer_invoice_no,
    customer_invoice_date: props.purchase_order_receives[0].customer_invoice_date,
    category: props.data[0].category,
    type: props.data[0].type
});

const submit = () => {
    const roundOffValue = parseFloat(form.round_off);
    if (roundOffValue > 0.99) {
        return;
    }
    form.total_amount = totalAmount.value;
    form.total_gst_amount = totalGstAmount.value;
    form.total_price = totalPrice.value;
    form.receivedProduct = receivedProduct.value.map((item, index) => ({
        ...item,
        productDetails: productDetails.value[index] || []
    }));
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const productDetails = ref([]);

const addFields = (index) => {
    // if(props.data[0].category == 'Sales'){
        form.errors['receivedProduct.' + index + '.receive_qty'] = null;
            const qty = receivedProduct.value[index].total_batch;
            const total_qty = receivedProduct.value[index].total_qty;
            const received_qty = receivedProduct.value[index].received_qty;
            const otherFields = [];
            let actualQty;
            if (qty && !isNaN(qty)){
                if( qty > (total_qty - received_qty)){
                    actualQty = total_qty - received_qty;
                } else {
                    actualQty = qty;
                }
                for (let i = 0; i < actualQty; i++) {
                    otherFields.push({
                        batch: '',
                        expiry_date: '',
                        qty: ''
                    });
                }
            }
            receivedProduct.value[index].total_batch = actualQty;
            productDetails.value[index] = otherFields;
    // }
};

const currentDate = computed(() => {
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return today.toLocaleDateString('en-US', options);
});

const setReceivedBy = (id, name) => {
    form.created_by = id;
    form.errors.created_by = null;
};

const calculateAmount = (product, index) => {
    const price = parseFloat(receivedProduct.value[index].purchase_price)  ;
    const gst = parseFloat(product.gst) || 0;
    const qty = parseFloat(receivedProduct.value[index].receive_qty);
    const amount = price * qty * (1 + gst / 100)
    const total_price = price * qty;
    const total_gst_amount = price * qty * (gst / 100);
    receivedProduct.value[index].total_price      = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    receivedProduct.value[index].total_gst_amount = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    return isNaN(amount) ? '' :  parseFloat(amount).toFixed(2);
};

const updateAmount = (product, index) => {
    receivedProduct.value[index].total_amount= calculateAmount(product, index);
};

const totalAmount = computed(() => {
    const totalProductAmount = receivedProduct.value.reduce((total, product) => {
        return total + (product.total_amount ? parseFloat(product.total_amount) : 0);
    }, 0);
    const roundOffAmount = parseFloat(form.round_off) || 0;
    const total = totalProductAmount + roundOffAmount;
    return parseFloat(total.toFixed(2));
});

const validateRoundOff = computed(() => {
    const roundOffValue = parseFloat(form.round_off);
    return roundOffValue >= 0.00 && roundOffValue <= 0.99;
});

const totalGstAmount = computed(() => {
    return receivedProduct.value.reduce((total, product) => {
        return total +  (product.total_gst_amount ? parseFloat(product.total_gst_amount) : 0);
    }, 0);
});

const totalPrice = computed(() => {
    return receivedProduct.value.reduce((total, product) => {
        return total +  (product.total_price ? parseFloat(product.total_price) : 0);
    }, 0);
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

</script>

<template>
    <Head title="Receive Purchase"/>

    <AdminLayout>
        <div class="animate-top">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Receive Product</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('purchaseinvoice.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Company Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.name ?? '-'  }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.gst_no ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.email ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].company.contact_no ?? '-' }}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Receive No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ props.purchase_order_receives[0].po_receive_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].po_number ?? '-' }}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">PO Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{  formatDate(data[0].date) ?? '-'}}</p>
                    </div>
                 </div>
            </div>
        </div>
        <div v-if="data[0].purchase_order_detail_for_receive.length > 0" class="mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_invoice_no" value="Company Invoice No" />
                    <TextInput
                        id="customer_invoice_no"
                        type="text"
                        v-model="form.customer_invoice_no"
                        @change="clearError('customer_invoice_no')"
                        :class="{ 'error rounded-md': form.errors.customer_invoice_no }"
                    />
                    <InputError  v-if="form.invalid('customer_invoice_no')" class="" :message="form.errors.customer_invoice_no" />
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="customer_invoice_date" value="Company Invoice Date" />
                    <input
                        class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    type="date"  v-model="form.customer_invoice_date"
                    @change="clearError('customer_invoice_date')"
                      :class="{ 'error rounded-md': form.errors.customer_invoice_date }"
                    />
                    <InputError v-if="form.invalid('customer_invoice_date')" class="" :message="form.errors.customer_invoice_date" />
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="company_name" value="Received By:" />
                    <div class="relative mt-2">
                        <SearchableDropdown :options="salesuser"
                            v-model="form.created_by"
                            @onchange="setReceivedBy"
                            :class="{ 'error rounded-md': form.errors.created_by }"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div v-if="data[0].purchase_order_detail_for_receive.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto">
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2" style="width: 140%;">
                <div class="sm:col-span-2">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Product</p>
                </div>
                <div class="sm:col-span-1">
                    <p v-if="data[0].category == 'Service'" class="text-sm font-semibold text-gray-900 leading-6">Part No</p>
                    <p v-else class="text-sm font-semibold text-gray-900 leading-6">Product Code</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">GST %</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Received QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">MRP (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Purchase Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Batch</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Amount</p>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center" style="width: 140%;" v-for="(product, index)  in data[0].purchase_order_detail_for_receive" :key="index">
                <div class="sm:col-span-2">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.name ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.item_code ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ formatAmount(product.price) ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ formatAmount(product.gst) ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.qty  ?? '-'}}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.receive_qty ?? '-'}}</p>
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].mrp"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.mrp`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].purchase_price"
                        @input="updateAmount(product, index)"
                        @change="clearError('receivedProduct.' + index + '.purchase_price')"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.purchase_price`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        v-model="receivedProduct[index].receive_qty"
                        :numeric="true"
                        @change="clearError(`receivedProduct.${index}.receive_qty`)"
                        @input="updateAmount(product, index)"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.receive_qty`] }"
                        min="1"
                    />
                    <p v-if="form.errors[`receivedProduct.${index}.receive_qty`]" class="text-red-500 text-xs absolute">
                        {{ form.errors[`receivedProduct.${index}.receive_qty`] }}
                    </p>
                </div>

                <div class="flex sm:col-span-1 mb-2">
                    <TextInput
                        id="gst"
                        type="text"
                        :numeric="true"
                        v-model="receivedProduct[index].total_batch"
                        @change="addFields(index)"
                        :class="{ 'error': form.errors[`receivedProduct.${index}.total_batch`] }"
                        min="1"
                    />
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ receivedProduct[index].total_amount }}</p>
                </div>
                <div v-if="productDetails[index]" class="sm:col-span-9 mb-2">
                    <div v-for="(field, index2) in productDetails[index]" :key="index2" class="grid grid-cols-1 gap-x-6  sm:grid-cols-12 items-center">
                        <div class="sm:col-span-3">
                            <TextInput
                                type="text"
                                v-model="field.batch"
                                placeholder="Batch"
                                @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.batch')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.batch`] }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <DateInput
                                v-model="field.expiry_date"
                                  @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.expiry_date')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.expiry_date`] }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <TextInput
                                type="text"
                                v-model="field.qty"
                                placeholder="Qty"
                                  @change="clearError('receivedProduct.' + index + '.productDetails.'+ index2 + '.qty')"
                                :class="{ 'error': form.errors[`receivedProduct.${index}.productDetails.${index2}.qty`] }"
                            />
                        </div>
                    </div>
                    <p v-if="form.errors[`receivedProduct.${index}.productDetails`]" class="text-red-500 text-xs absolute">
                        {{ form.errors[`receivedProduct.${index}.productDetails`] }}
                    </p>
                </div>
            </div>
        </div>
        <!-- <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto">
            <table class="overflow-x-auto divide-y divide-gray-300" style="width: 110%">
                <thead>
                    <tr>
                        <th v-if="data[0].category == 'Sales'" scope="col" class="py-3.5 text-left text-sm font-semibold text-gray-900">Product Code</th>
                        <th v-if="data[0].category == 'Service'" scope="col" class="py-3.5 text-left text-sm font-semibold text-gray-900">Part No</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Product Description</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">HSN</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Pkg Of Qty</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">QTY</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Rec. QTY</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Price (₹)</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Total Price (₹)</th>
                        <th v-if="data[0].company.gst_type =='IGST'" scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">IGST (%)</th>
                        <th v-if="data[0].company.gst_type =='IGST'" scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">IGST (₹)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">CGST (%)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">SGST (%)</th>
                        <th v-if="data[0].company.gst_type =='CGST/SGST'" scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Total GST (₹)</th>
                        <th scope="col" class="py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900">Total Amount (₹)</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-300 bg-white">
                    <tr v-for="(product, index)  in purchase_order_receives[0].purchase_order_receive_details" :key="index">
                        <td v-if="data[0].category == 'Sales'" class="whitespace-nowrap pr-3 py-3 text-sm text-gray-700 min-w-32">{{ product.product.item_code ?? '-' }}</td>
                        <td v-if="data[0].category == 'Service'" class="whitespace-nowrap pr-3 py-3 text-sm text-gray-700 min-w-32" :class="{ 'text-red-700': (product.qty - product.receive_qty > 0) }">{{ product.product.item_code ?? '-' }}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-36" :class="{ 'text-red-700': (product.qty - product.receive_qty > 0) }">{{ product.product.name ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-28">{{ product.product.hsn_code ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.pkg_of_qty ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.qty ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.receive_qty ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-28">{{ formatAmount(product.serial_numbers[0].purchase_price) ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-32">{{ formatAmount(product.total_price) ?? '-'}}</td>
                        <td v-if="data[0].company.gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.gst ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='IGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ formatAmount(product.total_gst_amount) ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.gst/2 ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-24">{{ product.purchase_order_detail.gst/2 ?? '-' }}</td>
                        <td v-if="data[0].company.gst_type =='CGST/SGST'" class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-28">{{ formatAmount(product.total_gst_amount) ?? '-'}}</td>
                        <td class="whitespace-nowrap px-3 py-3 text-sm text-gray-700 min-w-36">{{ formatAmount(product.total_amount) ?? '-' }}</td>
                    </tr>
                </tbody>
            </table>
        </div> -->
        <div  v-if="data[0].purchase_order_detail_for_receive.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-2">
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total GST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Round Off (₹):</p>
                            <div class="w-32">
                                <TextInput
                                    id="round_off"
                                    type="text"
                                    v-model="form.round_off"
                                    @change="clearError('round_off')"
                                    :class="{ 'error rounded-md': form.errors.round_off }"
                                    min="0"
                                />
                            </div>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm font-semibold text-gray-700">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalAmount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div  v-if="data[0].purchase_order_detail_for_receive.length > 0" class="flex mt-6 items-center justify-between">
            <div class="ml-auto flex items-center justify-end gap-x-6">
                <PrimaryButton class="" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Submit
                </PrimaryButton>
            </div>
        </div>
        </form>
        </div>
    </AdminLayout>

</template>

<style scoped>
.error {
  border: 1px solid red;
}
</style>
